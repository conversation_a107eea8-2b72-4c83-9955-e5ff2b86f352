import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.PaperQuestion;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.enums.ListeningAudioSourceEnum;
import com.sure.question.enums.ListeningVoiceGenderEnum;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.mapper.PaperQuestionMapper;
import com.sure.question.mapper.SmallQuestionMapper;
import com.sure.question.service.ListeningService;
import com.sure.question.vo.question.QuestionListeningVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jsoup.Jsoup;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class ListeningTest {
    @Resource
    private PaperMainMapper paperMainMapper;
    @Resource
    private PaperQuestionMapper paperQuestionMapper;
    @Resource
    private SmallQuestionMapper smallQuestionMapper;
    @Resource
    private ListeningService listeningService;

    /**
     * 给小学英语题目添加听力，其题目解析中包含听力材料
     * 试卷范围：小学英语编辑2022-09-01之后组卷试卷
     */
    @Test
    public void addPrimaryEnglishListening() {
        List<PaperMain> needAddListeningPapers = getNeedAddEnglishPapers();
        if (needAddListeningPapers.isEmpty()) {
            log.info("无需添加听力的试卷");
        } else {
            log.info("需添加听力的试卷共 {} 份", needAddListeningPapers.size());
            int index = 1;
            for (PaperMain paper : needAddListeningPapers) {
                log.info("处理试卷: {} / {}, {}", index++, needAddListeningPapers.size(), paper.getPaperName());
                this.addPaperListening(paper);
            }
            log.info("处理完成");
        }
    }

    private List<PaperMain> getNeedAddEnglishPapers() {
        final int gradeLevel = 1;
        final int subjectId = 3;
        final String userId = "bf300d14-ed9a-4910-b148-b42c1e3e1c03";
        final int uploadType = 2;
        final long minCreateTime = DateUtil.parseDate("2022-09-01").getTime();
        return paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getGradeLevel, gradeLevel)
                .eq(PaperMain::getSubjectId, subjectId)
                .eq(PaperMain::getUserId, userId)
                .eq(PaperMain::getUploadType, uploadType)
                .gt(PaperMain::getCreateTime, minCreateTime)
                .orderByAsc(PaperMain::getCreateTime)
                .select(PaperMain::getId, PaperMain::getPaperName));
    }

    private void addPaperListening(PaperMain paper) {
        List<List<SmallQuestion>> questions = getPaperQuestions(Long.parseLong(paper.getId()));
        if (questions.isEmpty()) {
            log.info("无题目");
            return;
        }
        int index = 1;
        for (List<SmallQuestion> smallQuestions : questions) {
            log.info("处理题目: {} / {}, {}", index++, questions.size(), smallQuestions.get(0).getQuestionId());
            addQuestionListening(smallQuestions);
        }
    }

    private List<List<SmallQuestion>> getPaperQuestions(long paperId) {
        // 查试卷内所有题目Id
        List<PaperQuestion> paperQuestions = paperQuestionMapper.selectList(new LambdaQueryWrapper<PaperQuestion>()
                .eq(PaperQuestion::getPaperId, paperId)
                .select(PaperQuestion::getQuestionId)
                .orderByAsc(PaperQuestion::getOrderId));
        if (paperQuestions.isEmpty()) {
            return Collections.emptyList();
        }
        List<Long> questionIds = paperQuestions.stream().map(PaperQuestion::getQuestionId).map(Long::valueOf).collect(Collectors.toList());

        // 查所有小题
        List<SmallQuestion> smallQuestions = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                .in(SmallQuestion::getQuestionId, questionIds)
                .select(SmallQuestion::getId, SmallQuestion::getQuestionId, SmallQuestion::getQuestionNo, SmallQuestion::getExplanation));

        // 按题分组
        return questionIds.stream().map(questionId -> {
            String qId = questionId.toString();
            return smallQuestions.stream()
                    .filter(sq -> sq.getQuestionId().equals(qId))
                    .sorted(Comparator.comparing(SmallQuestion::getQuestionNo))
                    .collect(Collectors.toList());
        }).filter(list -> !list.isEmpty()).collect(Collectors.toList());
    }

    private void addQuestionListening(List<SmallQuestion> smallQuestions) {
        boolean questionHasMaterial = false;
        for (SmallQuestion sq : smallQuestions) {
            String material = extractListeningMaterial(sq.getExplanation());
            if (StringUtils.isNotBlank(material)) {
                questionHasMaterial = true;
                log.info("生成听力: smallQuestionNo = {}, smallQuestionId = {}, material = {}, explanation = {}", sq.getQuestionNo(), sq.getId(), material, sq.getExplanation());
                QuestionListeningVo saveVo = new QuestionListeningVo();
                saveVo.setQuestionId(sq.getQuestionId());
                saveVo.setSmallQuestionId(smallQuestions.size() > 1 ? sq.getId() : "0");
                saveVo.setMaterial(material);
                saveVo.setAudioSource(ListeningAudioSourceEnum.TTS.getCode());
                saveVo.setVoiceGender(ListeningVoiceGenderEnum.WOMAN.getCode());
                saveVo.setSpeechRate(100);
                saveVo.setRepeatCount(1);
                try {
                    listeningService.saveOneListening(saveVo);
                    log.info("生成听力成功");
                } catch (Exception ex) {
                    log.info("生成听力失败\n{}", ExceptionUtils.getStackTrace(ex));
                    System.out.println("生成听力失败, questionId = " + sq.getQuestionId() + ", smallQuestionId = " + sq.getId());
                }
            }
        }
        if (!questionHasMaterial) {
            log.info("所有小题无听力材料");
        }
    }

    private String extractListeningMaterial(String explanation) {
        if (StringUtils.isBlank(explanation)) {
            return StringUtils.EMPTY;
        }
        String cleanText = Jsoup.parse(explanation).text();
        Pattern pattern = Pattern.compile("[【\\[]\\s*材\\s*料\\s*[】\\]]\\s*(.+)\\s*$");
        Matcher matcher = pattern.matcher(cleanText);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return StringUtils.EMPTY;
    }
}
