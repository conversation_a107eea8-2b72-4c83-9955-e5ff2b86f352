import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.dto.paper.structure.*;
import com.sure.question.entity.*;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.PaperStatusEnum;
import com.sure.question.mapper.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 检查试卷结构
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class BindPaperStructureTest {
    @Resource
    private PaperMainMapper paperMainMapper;
    @Resource
    private PaperQuestionMapper paperQuestionMapper;
    @Resource
    private SmallQuestionMapper smallQuestionMapper;
    @Resource
    private CoachBookPaperMapper coachBookPaperMapper;
    @Resource
    private CoachBookCustomPaperMapper coachBookCustomPaperMapper;

    @Test
    public void checkCoachBook() {
        Function<String, List<PaperMain>> paperMainQuery = lastPaperId -> {
            List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                    .gt(lastPaperId != null, CoachBookPaper::getPaperId, lastPaperId)
                    .orderByAsc(CoachBookPaper::getPaperId)
                    .select(CoachBookPaper::getPaperId)
                    .last("limit 100"));
            List<String> paperIds = coachBookPapers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());
            if (paperIds.isEmpty()) {
                return Collections.emptyList();
            }
            return paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                    .in(PaperMain::getId, paperIds)
                    .orderByAsc(PaperMain::getId)
                    .select(PaperMain::getId, PaperMain::getPaperStyleJson));
        };

        System.out.println("检查教辅试卷");
        check(paperMainQuery);
    }

    @Test
    public void checkCoachBookCustom() {
        Function<String, List<PaperMain>> paperMainQuery = lastPaperId -> {
            List<CoachBookCustomPaper> coachBookPapers = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>()
                    .gt(lastPaperId != null, CoachBookCustomPaper::getPaperId, lastPaperId)
                    .orderByAsc(CoachBookCustomPaper::getPaperId)
                    .select(CoachBookCustomPaper::getPaperId)
                    .last("limit 100"));
            List<String> paperIds = coachBookPapers.stream().map(CoachBookCustomPaper::getPaperId).collect(Collectors.toList());
            if (paperIds.isEmpty()) {
                return Collections.emptyList();
            }
            return paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                    .in(PaperMain::getId, paperIds)
                    .orderByAsc(PaperMain::getId)
                    .select(PaperMain::getId, PaperMain::getPaperStyleJson));
        };

        System.out.println("检查教辅定制卷");
        check(paperMainQuery);
    }

    @Test
    public void checkPublicBank() {
        Function<String, List<PaperMain>> paperMainQuery = lastPaperId -> paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .gt(lastPaperId != null, PaperMain::getId, lastPaperId)
                .eq(PaperMain::getStatus, PaperStatusEnum.Status0.getCode())
                .eq(PaperMain::getPaperBank, PaperBankEnum.Public.getCode())
                .orderByAsc(PaperMain::getId)
                .select(PaperMain::getId, PaperMain::getPaperStyleJson)
                .last("limit 100"));

        System.out.println("检查公共库");
        check(paperMainQuery);
    }

    private void check(Function<String, List<PaperMain>> paperMainQuery) {
        int paperCount = 0;
        List<String> errorPaperIdList = new ArrayList<>();
        List<PaperMain> paperMainList;

        String lastPaperId = null;
        while(!(paperMainList = paperMainQuery.apply(lastPaperId)).isEmpty()) {
            for (PaperMain paperMain : paperMainList) {
                paperCount++;
                List<String> questionIds = paperQuestionMapper.selectList(new LambdaQueryWrapper<PaperQuestion>()
                                .eq(PaperQuestion::getPaperId, paperMain.getId())
                                .select(PaperQuestion::getQuestionId))
                        .stream()
                        .map(PaperQuestion::getQuestionId)
                        .collect(Collectors.toList());
                if (questionIds.isEmpty()) {
                    errorPaperIdList.add(paperMain.getId());
                    continue;
                }
                List<SmallQuestion> smallQuestionList = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                        .in(SmallQuestion::getQuestionId, questionIds)
                        .select(SmallQuestion::getQuestionId, SmallQuestion::getId, SmallQuestion::getQuestionNo));

                Map<String, List<SmallQuestion>> groupByQuestionId = smallQuestionList.stream().collect(
                        Collectors.groupingBy(SmallQuestion::getQuestionId));
                Map<String, List<String>> questionIdBranchIdsMap = new HashMap<>();
                groupByQuestionId.forEach((questionId, smallQuestions) -> {
                    List<String> branchIds = smallQuestions.stream()
                            .sorted(Comparator.comparing(SmallQuestion::getQuestionNo).thenComparing(SmallQuestion::getId))
                            .map(SmallQuestion::getId).collect(Collectors.toList());
                    questionIdBranchIdsMap.put(questionId, branchIds);
                });
                if (isPaperStructureError(paperMain.getPaperStyleJson(), questionIdBranchIdsMap)) {
                    errorPaperIdList.add(paperMain.getId());
                }
            }
            lastPaperId = paperMainList.get(paperMainList.size() - 1).getId();
            System.out.println("已检查 " + paperCount + " 份试卷，其中 " + errorPaperIdList.size() + " 份试卷结构有误");
        }
        if (errorPaperIdList.isEmpty()) {
            System.out.println("检查完毕，所有试卷结构无误");
        }
        else {
            System.out.println("检查完毕，以下试卷结构有误：");
            System.out.println(StringUtils.join(errorPaperIdList, "\n"));
        }
    }

    private boolean isPaperStructureError(String paperStructureJson, Map<String, List<String>> questionIdBranchIdsMap) {
        if (StringUtils.isBlank(paperStructureJson)) {
            return true;
        }
        PaperStructure structure;
        try {
            structure = JSON.parseObject(paperStructureJson, PaperStructure.class);
        }
        catch (Exception exception) {
            return true;
        }
        for (StructureVolume volume : structure.getVolumes()) {
            for (StructureTopic topic : volume.getTopics()) {
                for (StructureQuestion question : topic.getQuestions()) {
                    List<String> branchIds = questionIdBranchIdsMap.get(question.getId());
                    if (branchIds == null || branchIds.isEmpty()) {
                        return true;
                    }
                    if (branchIds.size() != question.getBranches().size()) {
                        return true;
                    }
                    for (int idx = 0; idx < question.getBranches().size(); idx++) {
                        StructureBranch branch = question.getBranches().get(idx);
                        if (!StringUtils.equals(branch.getId(), branchIds.get(idx))) {
                            return true;
                        }
                    }
                    questionIdBranchIdsMap.remove(question.getId());
                }
            }
        }
        return !questionIdBranchIdsMap.isEmpty();
    }
}
