import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.entity.CoachBook;
import com.sure.question.entity.CoachBookPaper;
import com.sure.question.entity.RenderedQuestion;
import com.sure.question.mapper.CoachBookMapper;
import com.sure.question.mapper.CoachBookPaperMapper;
import com.sure.question.mapper.RenderedQuestionMapper;
import com.sure.question.service.QuestionBuildService;
import com.sure.question.service.RenderedQuestionService;
import com.sure.question.vo.question.QuestionVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 渲染公式
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class RenderQuestionTest {
    @Resource
    private RenderedQuestionService renderedQuestionService;
    @Resource
    private RenderedQuestionMapper renderedQuestionMapper;
    @Resource
    private CoachBookMapper coachBookMapper;
    @Resource
    private CoachBookPaperMapper coachBookPaperMapper;
    @Resource
    private QuestionBuildService questionBuildService;

    final int year = 2023;
    final int term = 1;

    @Test
    public void renderCoachBookQuestions() {
        List<CoachBook> coachBooks = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                .eq(CoachBook::getYear, year)
                .eq(CoachBook::getTerm, term)
                .select(CoachBook::getId));
        List<Integer> coachBookIds = coachBooks.stream().map(CoachBook::getId).collect(Collectors.toList());
        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .in(CoachBookPaper::getCoachBookId, coachBookIds)
                .select(CoachBookPaper::getPaperId));
        System.out.println("共 " + coachBookPapers.size() + " 份教辅试卷");
        int count = 0;
        for (CoachBookPaper paper : coachBookPapers) {
            if ((count++) % 100 == 1) {
                System.out.println("第 " + count + " 份试卷");
            }
            List<QuestionVo> paperQuestions = questionBuildService.selectPaperQuestionVos(paper.getPaperId());
            Set<String> paperQuestionIds = paperQuestions.stream().map(QuestionVo::getId).collect(Collectors.toSet());
            renderedQuestionMapper.selectList(new LambdaQueryWrapper<RenderedQuestion>()
                            .in(RenderedQuestion::getQuestionId, paperQuestionIds)
                            .select(RenderedQuestion::getQuestionId))
                    .forEach(renderedQuestion -> paperQuestionIds.remove(renderedQuestion.getQuestionId()));
            for (QuestionVo q : paperQuestions) {
                if (paperQuestionIds.contains(q.getId())) {
                    renderedQuestionService.renderQuestion(q);
                }
            }
        }
    }
}
