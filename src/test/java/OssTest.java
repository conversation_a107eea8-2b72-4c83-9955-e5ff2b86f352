import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.sure.question.QuestionApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.FileInputStream;
import java.io.FileNotFoundException;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class OssTest {
    @Value("${aliyun.ossTkImg.endpoint}")
    private String ossEndPoint2;
    @Value("${aliyun.ossTkImg.accessKeyId}")
    private String ossAccessKeyId2;
    @Value("${aliyun.ossTkImg.accessKeySecret}")
    private String ossAccessKetSecret2;
    @Value("${aliyun.ossTkImg.bucketName}")
    private String ossBucketName2;

    @Test
    public void replaceFile() throws FileNotFoundException {
        String name = "I:/sureFile/question/img/1798520264782077952_1798520264814145548.png";
        String filePath = "C:\\Users\\<USER>\\Desktop\\水印\\QQ图片20240606103011.png";
        OSS oss = new OSSClientBuilder().build(ossEndPoint2, ossAccessKeyId2, ossAccessKetSecret2);
        oss.putObject(ossBucketName2, name, new FileInputStream(filePath));
    }
}
