import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.entity.PaperMain;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.service.PaperMainService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.util.*;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class WaterMarkTest {

    @Autowired
    private PaperMainService paperMainService;

    @Autowired
    private PaperMainMapper paperMainMapper;

    @Test
    public void testRollback() {
        paperMainService.rollback("1442785824196091904");
    }

    @Test
    public void testPaperRemoveAndAddWaterMark() {
        paperMainService.paperRemoveAndAddWaterMark("1442785824196091904");
    }

    @Test
    public void testFileRemoveAndAddWaterMark() throws IOException {
        paperMainService.fileRemoveAddAddWaterMark("D:\\sureFile\\question\\file\\html\\1554343848827510784.html");
    }

    //移除和添加水印
    @Test
    public void removeAndAddWaterMark() {
        int limit = 0;
        int count = 1000;
        int totalCount = 0;
        while (true) {
            String last = " limit ".concat(String.valueOf(limit)).concat(",").concat(String.valueOf(count));
            List<PaperMain> paperMains = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>().last(last));
            if (paperMains.size() == 0) {
                break;
            }
            for (PaperMain paperMain : paperMains) {
                paperMainService.paperRemoveAndAddWaterMark(paperMain.getId());
            }
            totalCount += paperMains.size();
            System.out.println("当前偏移量：" + limit);
            limit += 10;
        }
        System.out.println(totalCount);

    }
}
