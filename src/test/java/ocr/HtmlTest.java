package ocr;

import com.sure.question.service.OCRService;
import com.sure.question.service.impl.OCRServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class HtmlTest {
    public static void main(String[] args) throws IOException {
        String paperId = "1834172527898615808";
        int subjectId = 3;
        String htmlPath = "D:\\sureFile\\question\\file\\word\\" + paperId + ".html";
        String tempFolder = "D:\\sureFile\\question\\file\\uploadFilePackage\\" + paperId + "-images";

        // 检查图片文件、识别结果json文件
        File folder = new File(tempFolder);
        File[] files = folder.listFiles();
        if (files == null) {
            System.out.println("文件夹为空");
            return;
        }

        Map<Integer, String> imgPathMap = new HashMap<>();
        Map<Integer, String> ocrResponsePathMap = new HashMap<>();
        for (File file : files) {
            if (file.isDirectory()) {
                continue;
            }
            Matcher matcher = Pattern.compile("(\\d+)\\.(png|json)").matcher(file.getName());
            if (matcher.find()) {
                Integer page = Integer.valueOf(matcher.group(1));
                if (matcher.group(2).equals("png")) {
                    imgPathMap.put(page, file.getAbsolutePath());
                } else if (matcher.group(2).equals("json")) {
                    ocrResponsePathMap.put(page, file.getAbsolutePath());
                }
            }
        }

        if (imgPathMap.isEmpty()) {
            System.out.println("无图片");
            return;
        } else if (ocrResponsePathMap.isEmpty()) {
            System.out.println("无json");
            return;
        } else if (imgPathMap.size() != ocrResponsePathMap.size() || !imgPathMap.keySet().stream().allMatch(ocrResponsePathMap::containsKey)) {
            System.out.println("图片与json不对应");
            return;
        }

        // 提取图片路径、json文本
        List<String> imgPathList = new ArrayList<>();
        List<String> ocrResponsePathList = new ArrayList<>();
        List<Integer> pages = imgPathMap.keySet().stream().sorted(Comparator.naturalOrder()).collect(Collectors.toList());
        for (Integer page : pages) {
            imgPathList.add(imgPathMap.get(page));
            String ocrResponsePath = ocrResponsePathMap.get(page);
            ocrResponsePathList.add(ocrResponsePath);
        }

        // 解析
        OCRService ocrService = new OCRServiceImpl();
        String html = ocrService.parseOCRResponse(imgPathList, ocrResponsePathList, subjectId);
        FileUtils.writeStringToFile(new File(htmlPath), html, StandardCharsets.UTF_8);
    }
}
