import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.QuestionApplication;
import com.sure.question.common.IdWorker;
import com.sure.question.dto.paper.structure.*;
import com.sure.question.dto.question.CheckQuestionOption;
import com.sure.question.entity.*;
import com.sure.question.mapper.*;
import com.sure.question.service.QuestionService;
import com.sure.question.util.CacheUtil;
import com.sure.question.util.GrapeUtil;
import com.sure.question.vo.grapeVo.*;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.SmallQuestionKnowledgeVo;
import com.sure.question.vo.question.SmallQuestionOptionVo;
import com.sure.question.vo.question.SmallQuestionVo;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.HttpCookie;
import java.util.*;
import java.util.concurrent.TimeUnit;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class TikuService {

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private QuestionService questionService;

    @Autowired
    private IdWorker idWorker;
    @Autowired
    private RegionMapper regionMapper;
    @Autowired
    private SubjectMapper subjectMapper;
    @Autowired
    private NewPaperMapper newPaperMapper;
    @Autowired
    private PaperMainMapper paperMainMapper;
    @Autowired
    private PaperTypeMapper paperTypeMapper;
    @Autowired
    private KnowledgeMainMapper knowledgeMainMapper;
    @Autowired
    private QuestionTypeMainMapper questionTypeMainMapper;
    @Autowired
    private BasketPaperStyleMapper basketPaperStyleMapper;


    private Logger logger = LoggerFactory.getLogger("appInfo");

    private static long livingTime = 60 * 60 * 24 * 7;

    //    ********
//    880
//    104,139,142,186,
    String str = // 17 * 4 + 12 = 80
            "192,203,206,210,211,217,219,220,221,239,240,241," +
                    "246,249,251,253,254,257,273,282,283,284,298,299,305,312,316,339,342," +
                    "347,348,349,350,351,353,361,362,365,366,368,369,370,372,373,374,375," +
//                    "376,377,379,380,381,383,384,385,388,391,392,395,401,403,410,412,419," +
//                    "421,436,438,439,440,443,445,447,453,461,466,467,485,487,492,509,512," +
//                    "513,516,521,527,528,531,535,538,542,543,544,546,547,551,552,553,554," +
//                    "555,556,558,559,562,569,570,571,575,590,592,593,595,596,597,600,603," +
                    "611,617,624,628,634,637,639,641,645,653,659,661,665,667,669,675,679," +
                    "688,692,700,701,702,703,704,706,707,723,724,728,729,730,731,734,735," +
                    "737,744,748,749,752,754,757,762,763,764,765,767";
    private String tokenRedisKey = "yunxiaotoken";

    private Integer index = 0;

    @Before
    public void before() {
        // 年级
        Object grade = redisTemplate.opsForValue().get("grape_grade");
        if (grade == null) {
            HashMap<String, Integer> gradeMap = new HashMap<>();
            gradeMap.put("一年级", 1);
            gradeMap.put("二年级", 2);
            gradeMap.put("三年级", 3);
            gradeMap.put("四年级", 4);
            gradeMap.put("五年级", 5);
            gradeMap.put("六年级", 6);
            gradeMap.put("七年级", 7);
            gradeMap.put("八年级", 8);
            gradeMap.put("九年级", 9);
            gradeMap.put("高一", 10);
            gradeMap.put("高二", 11);
            gradeMap.put("高三", 12);
            redisTemplate.opsForValue().set("grape_grade", gradeMap, livingTime, TimeUnit.SECONDS);
        }
        // 学段
        Object stage = redisTemplate.opsForValue().get("grape_stage");
        if (stage == null) {
            HashMap<String, Integer> stageMap = new HashMap<>();
            stageMap.put("高中", 4);
            stageMap.put("初中", 2);
            stageMap.put("小学", 1);
            redisTemplate.opsForValue().set("grape_stage", stageMap, livingTime, TimeUnit.SECONDS);
        }
        // 学科
        Object subject = redisTemplate.opsForValue().get("grape_subject");
        if (subject == null) {
            List<Subject> subjects = subjectMapper.selectList(new QueryWrapper<Subject>().eq("status", 1));
            HashMap<String, Integer> subjectMap = new HashMap<>();
            for (Subject su : subjects) {
                subjectMap.put(su.getSubjectName().trim(), su.getId());
            }
            redisTemplate.opsForValue().set("grape_subject", subjectMap, livingTime, TimeUnit.SECONDS);
        }
        // 题目类型
        Object type = redisTemplate.opsForValue().get("grape_type");
        if (type == null) {
            List<QuestionTypeMain> types = questionTypeMainMapper.selectList(new QueryWrapper<QuestionTypeMain>().eq("status", 1));
            HashMap<String, Integer> typeMap = new HashMap<>();
            for (QuestionTypeMain t : types) {
                typeMap.put((t.getGradeLevel() + "" + t.getSubjectId() + "" + t.getQuestionTypeName()).trim(), t.getId());
            }
            redisTemplate.opsForValue().set("grape_type", typeMap, livingTime, TimeUnit.SECONDS);
        }
        // 试卷类型
        Object paper_type = redisTemplate.opsForValue().get("grape_paper_type");
        if (paper_type == null) {
            HashMap<String, Integer> paperMap = new HashMap<>();
            List<PaperType> paperTypeList = paperTypeMapper.selectList(new QueryWrapper<PaperType>().eq("status", 1));
            for (PaperType p : paperTypeList) {
                paperMap.put((p.getGradeLevel() + p.getPaperTypeName()).trim(), p.getPaperTypeId());
            }
            redisTemplate.opsForValue().set("grape_paper_type", paperMap);
        }
        // 知识点
        Object knowledge = redisTemplate.opsForValue().get("grape_knowledge");
        if (knowledge == null) {
            List<KnowledgeMain> knowledgeMains = knowledgeMainMapper.selectList(new QueryWrapper<KnowledgeMain>().eq("status", 1));
            HashMap<String, Integer> knowledgeMap = new HashMap<>();
            for (KnowledgeMain k : knowledgeMains) {
                knowledgeMap.put((k.getGradeLevel() + "" + k.getSubjectId() + "" + k.getLevel() + "" + k.getKnowledgeName()).trim(), k.getId());
            }
            redisTemplate.opsForValue().set("grape_knowledge", knowledgeMap, livingTime, TimeUnit.SECONDS);
        }
        // smallQuesTypeMay
        Object sqType = redisTemplate.opsForValue().get("grape_sqType");
        if (sqType == null) {
            HashMap<String, Integer> sqTypeMap = new HashMap<>();
            sqTypeMap.put("单选题", 11);
            sqTypeMap.put("多选题", 12);
            sqTypeMap.put("判断题", 13);
            sqTypeMap.put("填空题", 21);
            sqTypeMap.put("解答题", 22);
            sqTypeMap.put("语文作文", 23);
            sqTypeMap.put("英语作文", 24);
            redisTemplate.opsForValue().set("grape_sqType", sqTypeMap, livingTime, TimeUnit.SECONDS);
        }
    }


    /**
     * 登录云校
     */
    @Test
    public void testLogin() {
        String[] split = str.split(",");
        System.out.println("开始缓存用户登录信息");
        int count = 0;
        for (int i = 0; i < split.length; i++) {
            String token = getToken(i);
            if (token.startsWith("tiku")) {
                count++;
                redisTemplate.opsForHash().put(tokenRedisKey, i + "", token);
            }
        }
        System.out.println("完成" + count + "用户登录信息缓存");
    }

    /**
     * 录题
     */
    @Test
    public void testTiku() {
        List<String> gradeNameList = new ArrayList<>();
//        gradeNameList.add("三年级上");
//        gradeNameList.add("三年级下");
//        gradeNameList.add("四年级上");
//        gradeNameList.add("四年级下");
//        gradeNameList.add("五年级上");
//        gradeNameList.add("五年级下");
//        gradeNameList.add("六年级上");
//        gradeNameList.add("六年级下");
//        gradeNameList.add("七年级上");
//        gradeNameList.add("七年级下");
//        gradeNameList.add("八年级上");
//        gradeNameList.add("八年级下");
//        gradeNameList.add("九年级上");
//        gradeNameList.add("九年级下");
        gradeNameList.add("全部");
        List<Integer> years = new ArrayList<>();
        years.add(2022);
        years.add(2023);
        List<String> commonExamTypeList = new ArrayList<>();

        List<String> primaryExamTypeList = new ArrayList<>();
        List<String> juniorExamTypeList = new ArrayList<>();
        List<String> highExamTypeList = new ArrayList<>();
        Map<String, List<String>> exxamTypeListMap = new HashMap<>();
        exxamTypeListMap.put("小学", primaryExamTypeList);
        exxamTypeListMap.put("初中", juniorExamTypeList);
        exxamTypeListMap.put("高中", highExamTypeList);
        commonExamTypeList.add("月考试卷");
        commonExamTypeList.add("期中试卷");
        commonExamTypeList.add("期末试卷");
        commonExamTypeList.add("单元测试");
        commonExamTypeList.add("同步练习");
//        小学
        primaryExamTypeList.add("小升初复习");
        primaryExamTypeList.add("小升初模拟");
        primaryExamTypeList.add("小升初真卷");
        primaryExamTypeList.addAll(commonExamTypeList);
//        初中
        juniorExamTypeList.add("中考复习");
        juniorExamTypeList.add("中考模拟");
        juniorExamTypeList.add("中考真卷");
        juniorExamTypeList.addAll(commonExamTypeList);
//        高中
        highExamTypeList.add("高考真卷");
        highExamTypeList.add("高考模拟");
        highExamTypeList.add("高考复习");
        highExamTypeList.addAll(commonExamTypeList);
        List<String> periods = new ArrayList<>();
        periods.add("小学");
        periods.add("初中");
        periods.add("高中");

        List<String> commonSubjectNames = new ArrayList<>();
        List<String> primarySubjectNames = new ArrayList<>();
        List<String> juniorSubjectNames = new ArrayList<>();
        List<String> highSubjectNames = new ArrayList<>();
        Map<String, List<String>> subjectNameMap = new HashMap<>();
        commonSubjectNames.add("语文");
        commonSubjectNames.add("数学");
        commonSubjectNames.add("英语");
        //小学
        primarySubjectNames.addAll(commonSubjectNames);
        subjectNameMap.put("小学", primarySubjectNames);
        //初中
        juniorSubjectNames.add("物理");
        juniorSubjectNames.add("化学");
        juniorSubjectNames.add("生物");
        juniorSubjectNames.add("政治");
        juniorSubjectNames.add("历史");
        juniorSubjectNames.add("地理");
        commonExamTypeList.addAll(commonSubjectNames);
        juniorSubjectNames.addAll(commonSubjectNames);
        subjectNameMap.put("初中", juniorSubjectNames);

        //高中
        highSubjectNames.add("物理");
        highSubjectNames.add("化学");
        highSubjectNames.add("生物");
        highSubjectNames.add("政治");
        highSubjectNames.add("历史");
        highSubjectNames.add("地理");
        highSubjectNames.addAll(commonSubjectNames);
        subjectNameMap.put("高中", highSubjectNames);

        String version = null;//"全部"; // 教材版本 : 人教版,部编版 ...
        System.out.println("  ");
        for (String period : periods) {
            List<String> subjecctNames = subjectNameMap.get(period);
            for (String subjectName : subjecctNames) {
                System.out.println(period + "  " + subjectName + " : ");
                List<String> examTypeList = exxamTypeListMap.get(period);
                for (String type : examTypeList) {
                    for (Integer year : years) {
                        System.out.println(type + "  " + year + " : ");
                        for (String gradeName : gradeNameList) {
                            if (gradeName.equals("全部")) gradeName = null;
                            //获取试卷xid
                            List<String> xids = getXids(gradeName, year, subjectName, period, version, type);
                            //获取试卷详情
                            getExamDetailInfo(xids);
                        }
                    }
                }
            }
        }
    }

    private void getExamDetailInfo(List<String> xids) {
        index = 0;
        int num = xids.size();
        for (String xid : xids) {
            xid = xid.replaceAll(" ", "");
            if (StringUtils.isBlank(xid)) continue;
            logger.info(xid);
            //避免重复保存
            Integer count = paperMainMapper.selectCount(new LambdaQueryWrapper<PaperMain>()
                    .eq(PaperMain::getXId, xid));
            if (count >= 1) {
                System.out.println("跳过" + xid);
                return;
            }
            System.out.println("下载" + xid);
            Boolean flag = getExamDetailInfo(xid);
            while (!flag) {// 爬取失败
                index++;
                flag = getExamDetailInfo(xid);
                System.out.println(xid);
                if (flag) {
                    System.out.println("换用户,再次执行成功: " + xid);
                    logger.info("换用户,再次执行成功: " + xid);
                } else {
                    if (!flag) System.out.println("换用户,再次执行失败: " + xid);
                    logger.info("换用户,再次执行失败: " + xid);
                }
            }
            int i = 300;
            System.out.println("剩余:" + num);
            logger.info("开始睡眠");
            try {
                Thread.sleep(i);
            } catch (Exception e) {
                e.printStackTrace();
            }
            logger.info("睡眠 : " + i + "ms");
            logger.info(" 下一卷 ");
        }
    }

    private Boolean getExamDetailInfo(String id) {
        String token = getTokenFromRedis(index);
        try {
            doGetExamDetailInfo(id, token);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Transactional
    void doGetExamDetailInfo(String id, String cookie) throws IOException {
        String b = " ";
        String a = " ";
        // 获取缓存数据
        Object stage = redisTemplate.opsForValue().get("grape_stage");
        Object subject = redisTemplate.opsForValue().get("grape_subject");
        Object type = redisTemplate.opsForValue().get("grape_type");
        Object paper_type = redisTemplate.opsForValue().get("grape_paper_type");
        Object knowledge = redisTemplate.opsForValue().get("grape_knowledge");
        Object grade = redisTemplate.opsForValue().get("grape_grade");
        Object grape_sqType = redisTemplate.opsForValue().get("grape_sqType");
        HashMap<String, Integer> stageMap = JSONObject.parseObject(JSON.toJSONString(stage), HashMap.class);

        HashMap<String, Integer> typeMap = JSONObject.parseObject(JSON.toJSONString(type), HashMap.class);
        HashMap<String, Integer> paperTypeMap = JSONObject.parseObject(JSON.toJSONString(paper_type), HashMap.class);
        HashMap<String, Integer> knowledgeMap = JSONObject.parseObject(JSON.toJSONString(knowledge), HashMap.class);
        HashMap<String, Integer> gradeMap = JSONObject.parseObject(JSON.toJSONString(grade), HashMap.class);
        HashMap<String, Integer> sqTypeMap = JSONObject.parseObject(JSON.toJSONString(grape_sqType), HashMap.class);
        // 题库token
        //        String token = "";
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient httpClient = httpClientBuilder.build();

        String url = "https://tiku.yunxiao.com/kb_api/v2/exampapers/" + id +
                "/?fields_type=common&serv_range=public";
        HttpGet httpGet = new HttpGet(url);

        httpGet.setHeader("Cookie", cookie);
        CloseableHttpResponse response = httpClient.execute(httpGet);
        String string = EntityUtils.toString(response.getEntity());

        JSONObject jsonObject = JSONObject.parseObject(string);

        Object data = jsonObject.get("data");
        GrapePaperVo grapePaperVo = JSONObject.parseObject(JSON.toJSONString(data), GrapePaperVo.class);
        String msg = jsonObject.get("msg").toString();
        String dataCode = jsonObject.get("code").toString();

        if (!dataCode.equals("6") && !msg.equalsIgnoreCase("OK")) {
            System.out.println(msg);
            throw new ApiException("访问超限");
        }
        if (dataCode.equals("6") && msg.contains("数量上限")) {
            System.out.println(msg);
            throw new ApiException("访问超限");
        }

        // 分解
        String paperVoName = grapePaperVo.getName();
        if (paperVoName.contains("云校")) {
            //            System.out.println("云校试卷,跳过");
            logger.info("云校试卷,跳过");
            return;
        }
        // 学段
        String periodName = grapePaperVo.getPeriod();
        Integer stageId = stageMap.get(periodName);
        if (stageId == null) stageId = 2;
        // 学科
        String subjectName = grapePaperVo.getSubject();
        //        if (subjectName.equals("政治")) subjectName = "道德与法治";
        Integer subjectId = CacheUtil.getSubjectId(StringUtils.trim(subjectName));
        if (subjectName.equals("政治") && stageId == 2) {
            subjectId = 4;
        }
        if (subjectId == null) subjectId = 0;
        // 年级
        String gradeName = grapePaperVo.getGrade();
        Integer gradeId = gradeMap.get(gradeName);
        if (gradeId == null) gradeId = 2;
        //
        Integer score = grapePaperVo.getScore();
        String paperName = grapePaperVo.getName();
        // 试卷类型
        String paperTypeName = grapePaperVo.getType();
        Integer paperTypeId = paperTypeMap.get(stageId + "" + paperTypeName);
        if (paperTypeId == null) paperTypeId = 0;

        String provinceName = grapePaperVo.getProvince();
        // regionId
        String cityName = grapePaperVo.getCity();
        Long regionId = getRegionId(cityName);
        if (regionId == null) {
            regionId = getRegionId(grapePaperVo.getProvince());
        }
        if (regionId == null) regionId = 20020L;
        // year
        Integer paperYear = 0;

        // 初步构建 paperMain
        PaperMain paperMain = new PaperMain();
        String paperId = idWorker.nextId() + "";
        paperMain.setEditStatus("1");
        //        paperMain.setAllotStatus("1");
        paperMain.setShowStatus("1");
        paperMain.setPaperBank("1");
        paperMain.setShareStatus("0");
        paperMain.setCheckStatus("0");
        paperMain.setGroupShareStatus(1);
        paperMain.setId(paperId);
        paperMain.setUserId("17D8C2875AD74E24B4377583D17D7B80");
        paperMain.setSchoolId("10010");
        paperMain.setPaperName(paperName);
        paperMain.setGradeLevel(stageId);
        paperMain.setGradeId(gradeId);
        paperMain.setUpdateTime(System.currentTimeMillis());
        paperMain.setPaperWay("0");
        paperMain.setRegionId(regionId);
        paperMain.setSubjectId(subjectId);
        paperMain.setPaperTypeId(paperTypeId);
        paperMain.setUploadFile("");
        paperMain.setBrowseCount(0);
        paperMain.setPaperLocation("");
        paperMain.setStatus("0");
        paperMain.setUploadType("0");
        paperMain.setYear(grapePaperVo.getTo_year());
        paperMain.setCreateTime(System.currentTimeMillis());
        paperMain.setXId(id);

        List<QuestionVo> questionVoArrayList = new ArrayList<>();
        // 拼接json
        PaperStructure structure = new PaperStructure();
        StructureVolume volume = new StructureVolume();
        structure.getVolumes().add(volume);
        List<StructureTopic> topics = volume.getTopics();

        List<GrapeBigQues> blocks = grapePaperVo.getBlocks();
        int code = 0;
        for (GrapeBigQues gbq : blocks) {
            StructureTopic topic = new StructureTopic();
            topics.add(topic);
            String title = gbq.getTitle();
            topic.setContent(title);
            String gbqType = gbq.getType();
            topic.setName(gbqType);
            Integer gbqScore = gbq.getScore();
            List<GrapeGeneral> questions_ext = gbq.getQuestions_ext();
            List<GrapeWebQuestionVo> questions = gbq.getQuestions();
            for (GrapeWebQuestionVo webVo : questions) {
                code++;
                StructureQuestion quesInfo = new StructureQuestion();
                QuestionVo questionVo = new QuestionVo();
                Integer webVoDifficulty = webVo.getDifficulty();

                String webVoType = webVo.getType();
                if (webVoType.equals("选择题") && stageId != 1) webVoType = "单选题";
                if (subjectId == 3) {
                    if ("单选题".equals(webVoType)) {
                        webVoType = "单项选择";
                    } else if ("填空题".equals(webVoType)) webVoType = "单项选择";
                }
                Integer quesTypeId = typeMap.get(stageId + "" + subjectId + webVoType);
                if (quesTypeId == null) {
                    quesTypeId = typeMap.get(stageId + "" + subjectId + "未知");
                }

                Integer webVoYear = webVo.getYear();
                Integer webVoScore = webVo.getScore();
                String description = webVo.getDescription();

                if (paperYear == 0) paperYear = webVoYear;

                // questionVo
                questionVo.setDifficulty(webVoDifficulty);
                questionVo.setSubjectId(subjectId);
                questionVo.setCode(code);// 题号
                if (description != null) {
                    String tk = description.replaceAll(b, a);
                    description = tk.replaceAll("&nbsp;", a);
                }
                //                    description = classFilter(description);
                questionVo.setTrunk(cutStr(description));
                String quesId = idWorker.nextId() + "";
                questionVo.setId(quesId);
                questionVo.setScore(webVoScore == null ? null : webVoScore.floatValue());

                questionVo.setType(quesTypeId);
                questionVo.setStage(stageId);
                questionVo.setYear(webVoYear);
                questionVo.setComment(webVo.getComment());
                List<SmallQuestionVo> quesBranches = questionVo.getBranches();
                // quesInfo
                quesInfo.setCode(code);
                quesInfo.setScore(webVoScore == null ? 0 : webVoScore.floatValue());
                quesInfo.setId(quesId);
                List<StructureBranch> infoBranches = quesInfo.getBranches();
                // 小题
                GrapeWebSmallQuesVo webVoBlocks = webVo.getBlocks();
                List<String> solutions = webVoBlocks.getSolutions();
                List<String> answers = webVoBlocks.getAnswers();
                List<String> types = webVoBlocks.getTypes();
                List<String> explanations = webVoBlocks.getExplanations();
                List<GrapeGeneral> stems = webVoBlocks.getStems();
                List<List<GrapeGeneral>> knowledges = webVoBlocks.getKnowledges();
                // 收集各个小题的解析
                StringBuffer buffer = new StringBuffer();
                for (int i = 0; i < stems.size(); i++) {
                    SmallQuestionVo smallQuestionVo = new SmallQuestionVo();
                    StructureBranch sqInfo = new StructureBranch();

                    String solution = solutions.get(i);
                    String answer = answers.get(i);

                    // 题型
                    String sqType = types.get(i);
                    if (sqType.equals("选择题")) sqType = "单选题";
                    Integer sqTypeId = sqTypeMap.get(sqType);
                    if (sqTypeId == null) sqTypeId = 11;

                    GrapeGeneral grapeGeneral = stems.get(i);
                    String stem = grapeGeneral.getStem();
                    HashMap<String, String> options = grapeGeneral.getOptions();
                    String sqId = idWorker.nextId() + "";
                    // 小题
                    // smallQues
                    String st = solution.replaceAll(b, a);
                    solution = st.replaceAll("&nbsp;", a);
                    //                        solution = classFilter(solution);

                    String sl = stem.replaceAll(b, a);
                    stem = sl.replaceAll("&nbsp;", a);
                    //                        stem = classFilter(stem);
                    smallQuestionVo.setStem(cutStr(stem));
                    smallQuestionVo.setId(sqId);
                    smallQuestionVo.setCode(i);
                    smallQuestionVo.setType(sqTypeId);

                    String explains = explanations.get(i);
                    // 选项
                    if (options != null && options.size() > 0) {
                        Set<String> keySet = options.keySet();
                        ArrayList<SmallQuestionOptionVo> opstions = new ArrayList<>();
                        for (String key : keySet) {
                            String value = options.get(key);
                            String content = GrapeUtil.exchangImg(value);
                            SmallQuestionOptionVo optionVo = new SmallQuestionOptionVo(key, content);
                            opstions.add(optionVo);
                        }
                        smallQuestionVo.setOptions(opstions);
                    }

                    List<GrapeGeneral> smallQuesKnowledge = new ArrayList<>();

                    if (knowledges.size() > i) {
                        smallQuesKnowledge = knowledges.get(i);// 知识点
                    }
                    ArrayList<SmallQuestionKnowledgeVo> knowledgeMains = new ArrayList<>();
                    StringBuilder sbb = new StringBuilder();
                    for (GrapeGeneral gg : smallQuesKnowledge) {
                        String ggName = gg.getName();// 知识点名称
                        if (StringUtils.isBlank(ggName)) continue;
                        sbb.append(ggName).append(",");
                        Integer kid = knowledgeMainMapper.selectIdByName(stageId, subjectId, ggName.trim());
//                        Integer knowledgeId = knowledgeMap.get((stageId + "" + subjectId + "" + 2 + ggName).trim());// 知识点id

                        if (kid != null) {
                            SmallQuestionKnowledgeVo knowledgeMain = new SmallQuestionKnowledgeVo(kid, ggName);
                            knowledgeMains.add(knowledgeMain);
                        } else continue;
                    }
                    smallQuestionVo.setKnowledges(knowledgeMains);// 知识点}
                    smallQuestionVo.setKnowledgeName(sbb.toString());

                    String sa = answer.replaceAll(b, a);
                    answer = sa.replaceAll("&nbsp;", a);
                    //                        answer = classFilter(answer);
                    smallQuestionVo.setSolution(cutStr(answer));
                    // 知识点
                    // 解析
                    smallQuestionVo.setExpla(cutStr(explains));
                    // 其他科目
                    if (i < stems.size() - 1) {
                        String s1 = cutStr(explains);
                        if (StringUtils.isNotBlank(s1))
                            smallQuestionVo.setExplanation(cutStr(explains) + "<br>" + cutStr(solution));// 解析
                        else
                            smallQuestionVo.setExplanation(cutStr(solution));// 解析
                    } else {
                        String s1 = cutStr(explains);
                        String s2 = cutStr(solution);
                        StringBuffer sb = new StringBuffer();
                        if (StringUtils.isNotBlank(s1)) sb.append(s1);
                        if (sb.toString().length() > 0) {
                            sb.append("<br>").append(s2);
                        } else {
                            sb.append(s2);
                        }
                        if (sb.toString().length() > 0) {
                            sb.append("<br>").append(cutStr(webVo.getComment()));
                        } else {
                            sb.append(cutStr(webVo.getComment()));
                        }
                        smallQuestionVo.setExplanation(sb.toString());// 解析
                    }
                    quesBranches.add(smallQuestionVo);
                    sqInfo.setId(sqId);
                    sqInfo.setCode(i);
                    infoBranches.add(sqInfo);
                }
                topic.getQuestions().add(quesInfo);
                questionVoArrayList.add(questionVo);
            }
        }
        // paperMain补充
        String jsonString = JSON.toJSONString(structure);
        paperMain.setPaperStyleJson(jsonString);
        paperMain.setYear(paperYear);
        // 构建basketPaperStyle
        BasketPaperStyle basketPaperStyle = new BasketPaperStyle();
        basketPaperStyle.setCreateTime(System.currentTimeMillis());
        basketPaperStyle.setPaperId(paperMain.getId());
        basketPaperStyle.setUserId(paperMain.getUserId());
        basketPaperStyle.setSchoolId(paperMain.getSchoolId());
        basketPaperStyle.setUpdateTime(System.currentTimeMillis());
        basketPaperStyle.setQuestionCategoryJson(paperMain.getPaperStyleJson());
        basketPaperStyle.setGrade_id(paperMain.getGradeId());
        basketPaperStyle.setTitle(paperMain.getPaperName());
        basketPaperStyle.setStudentInfo("学校:___________姓名：___________班级：___________考号：___________");
        basketPaperStyle.setAttentions("1．答题前填写好自己的姓名、班级、考号等信息↵2．将答案正确填写在答题卡上");
        basketPaperStyle.setDispTitle(1);
        basketPaperStyle.setDispSubtitle(0);
        basketPaperStyle.setDispGutter(0);
        basketPaperStyle.setDispSecretTag(0);
        basketPaperStyle.setDispPaperInfo(1);
        basketPaperStyle.setDispStudentInfo(1);
        basketPaperStyle.setDispScoreBox(0);
        basketPaperStyle.setDispAttentions(1);
        basketPaperStyle.setDispVolume(0);
        basketPaperStyle.setStatus(0);
        basketPaperStyle.setPaperInfo("试卷满分 " + score + " 分,考试时间120分钟");
        // 插入paperMain
        int insert = paperMainMapper.insert(paperMain);
        // 插入quesVo
        questionService.addPaperQuestions(questionVoArrayList, CheckQuestionOption.skipAll(), paperMain, paperMain.getPaperStyleJson(), CheckPaperStructureOption.allowScoreNull());
        // 插入basketPaperStyle
        int insert1 = basketPaperStyleMapper.insert(basketPaperStyle);
        // 插入 new_paper
        NewPaper build = NewPaper.builder()
                .paperId(paperMain.getId() + "")
                .paperName(paperMain.getPaperName())
                .gradeLevel(paperMain.getGradeLevel())
                .subject(paperMain.getSubjectId())
                .time(new Date())
                .xId(id)
                .json(JSON.toJSONString(data)).build();
        newPaperMapper.insert(build);
        // 发送批量保存题目请求
        //        System.out.println("success");
        logger.info("success");
    }

    /**
     * 查询redion_id
     */
    private Long getRegionId(String name) {
        QueryWrapper<Region> wrapper = new QueryWrapper<>();
        wrapper.eq("short_name", name);
        wrapper.eq("level", 3);
        List<Region> regions = regionMapper.selectList(wrapper);
        if (regions.size() == 0) return 1L; // 没有省份的试卷
        return regions.get(0).getId();
    }

    private String cutStr(String str) {
        String pattern1 = "^<span class=\"tikulistnr\">\\$\\{[(（]\\s*[09]+\\s*[）)]\\}\\$</span>";
        String pattern2 = "^<span class=\"tikulistnr\">[(（]\\s*[09]+\\s*[）)]</span>";
        String pattern3 = "^\\$[\\{\\{][(（]\\s*[09]+\\s*[）)]\\}\\$";
        String pattern4 = "^[(（]\\s*[09]+\\s*[）)]";
        String pattern5 = "^\\$[\\{\\{]\\u005Cleft\\s*[(（]\\s*[\\{\\{][09]+[\\}\\}]\\s*\\u005Cright\\s*[\\)\\）]\\}\\$";
        String s1 = str.replaceAll(pattern1, "");
        if (s1.length() < str.length()) return s1;
        String s2 = str.replaceAll(pattern2, "");
        if (s2.length() < str.length()) return s2;
        String s3 = str.replaceAll(pattern3, "");
        if (s3.length() < str.length()) return s3;
        String s4 = str.replaceAll(pattern4, "");
        if (s4.length() < str.length()) return s4;
        String s5 = str.replaceAll(pattern5, "");
        if (s5.length() < str.length()) return s5;
        return str;
    }

    //  获取试卷id列表
    private List<String> getXids(String gradeName, Integer year, String subjectName, String period, String version, String type) {
        Set<String> xidSet = new HashSet<>();
        for (int i = 0; i < 5; i++) {
            List<String> ids = doGetXids(version, gradeName, period, subjectName, 200 * i, type, year);
            xidSet.addAll(ids);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return new ArrayList<>(xidSet);
    }

    private List<String> doGetXids(String version, String grade, String period, String subject, int offset, String type, Integer year) {
        String token = getTokenFromRedis(index);
        // 试卷请求
        String url = "https://tiku.yunxiao.com/kb_api/v2/exampapers/by_search";
        // 试卷id 集合
        List<String> xidList = new ArrayList<>();
        // 发送请求
        PaperQuery paperQuery = PaperQuery.builder().grade(grade).limit(20).offset(offset).period(period).press_version(version).subject(subject).type(type).to_year(year).build();
        String body = HttpRequest.post(url)
                .header("Cookie", token)
                .header("UserAgent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36")
                .header("ContentType", "application/json")
                .body(JSONUtil.toJsonStr(paperQuery))
                .execute()
                .body();
        JSONObject jsonObject = JSONObject.parseObject(body);
        Object data = jsonObject.get("data");
        GrapePaperData grapePaperData = JSONObject.parseObject(JSON.toJSONString(data), GrapePaperData.class);
        String total_num = grapePaperData.getTotal_num();
        if (total_num == null) return Collections.emptyList();
        List<GrapeExamPaper> grapeExamPapers = grapePaperData.getExampapers();
        for (GrapeExamPaper gep : grapeExamPapers) {
            if (gep.getName() == null) continue;
            if (gep.getName().contains("云校")) continue;
            xidList.add(gep.getId());
        }
        return xidList;
    }

    // 获取token
    private String getToken(Integer index) {
        String[] split = str.split(",");
        String key = split[index];
        String account = "********" + key;
        String pwd = "880" + key;
        UserInfo userInfo = UserInfo.builder()
                .user(account)
                .passwd(pwd)
                .app("TIKU")
                .cert_app("HFS_TEACHER")
                .build();
        //用户登录，获取token
        HttpResponse login = HttpRequest.post("https://kb-apps.yunxiao.com/passport/v1/user/token/?from=")
                .header("ContentType", "application/json;charset=UTF8")
                .body(JSONUtil.toJsonStr(userInfo))
                .execute();
        List<HttpCookie> cookies = login.getCookies();

        UserInfoResult userInfoResult = JSONUtil.toBean(login.body(), UserInfoResult.class);
        System.out.println(userInfoResult);
        //通过token获取tikusessionid
        if (userInfoResult.getCode() == 0) {
            HttpResponse token = HttpRequest.post("https://tiku.yunxiao.com/user_api/v1/greetings?token=TOKEN&login_from=kbapp".replace("TOKEN", userInfoResult.getData().getToken()))
                    .execute();
            HttpCookie cookie = token.getCookie("tiku-session-id");
            return token.getCookie("tiku-session-id").toString();
        } else {
            return "";
        }
    }


    private String getTokenFromRedis(Integer index) {
        if (index >= str.length()) throw new ApiException("账号已遍历完毕");
        Object o = redisTemplate.opsForHash().get(tokenRedisKey, index + "");
        if (o == null) throw new ApiException("取不到token");
        return o.toString();
    }

    @Data
    @Builder
    static class UserInfo {
        @Tolerate
        protected UserInfo() {

        }

        private String user;
        private String passwd;
        private String app;
        private String cert_app;
    }

    @Data
    @Builder
    static class UserInfoResult {
        @Tolerate
        protected UserInfoResult() {
        }

        private Integer code;
        private String msg;
        private UserInfoDetailResult data;
    }

    @Data
    @Builder
    static class UserInfoDetailResult {
        @Tolerate
        protected UserInfoDetailResult() {
        }

        private String token;
    }

    @Data
    @Builder
    static class PaperQuery {

        @Tolerate
        protected PaperQuery() {
        }

        private String grade;
        private int limit;
        private int offset;
        private String period;
        private String press_version;
        private String subject;
        private Integer to_year;
        private String type;
    }

}
