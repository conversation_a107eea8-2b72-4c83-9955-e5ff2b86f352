import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.enums.BranchTypeEnum;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.PaperStatusEnum;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.mapper.SmallQuestionMapper;
import com.sure.question.service.QuestionBuildService;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.SmallQuestionVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 删除题干、答案、解析前面的小题号
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class RemoveBranchCodeTest {
    @Resource
    private PaperMainMapper paperMainMapper;
    @Resource
    private QuestionBuildService questionBuildService;
    @Resource
    private SmallQuestionMapper smallQuestionMapper;

    @Test
    public void start() {
        List<PaperMain> paperMains = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getUserId, "17D8C287-5AD7-4E24-B437-7583D17D7B80")
                .isNotNull(PaperMain::getXId)
                .gt(PaperMain::getCreateTime, DateUtil.parseDate("2023-08-23").getTime())
                .eq(PaperMain::getPaperBank, PaperBankEnum.Personal.getCode())
                .eq(PaperMain::getShareStatus, PaperStatusEnum.ShareStatus0.getCode())
                .orderByAsc(PaperMain::getCreateTime)
                .select(PaperMain::getId, PaperMain::getPaperName));
        System.out.println("共 " + paperMains.size() + " 份试卷待处理");
        int pIdx = 1;
        int replacedCount = 0;
        for (PaperMain p : paperMains) {
            if (pIdx % 100 == 1) {
                System.out.println("第 " + pIdx + " 份试卷");
            }
            pIdx++;
            List<QuestionVo> questions = questionBuildService.selectPaperQuestionVos(p.getId());
            for (QuestionVo q : questions) {
                replacedCount += handleQuestion(q);
            }
        }
        System.out.println("处理完毕");
        System.out.println("共替换 " + replacedCount + " 个小题题干");
    }

    private int handleQuestion(QuestionVo q) {
        int replaceCount = 0;
        List<SmallQuestionVo> branches = q.getBranches();
        boolean onlyFirstBranchHasExplanation = getIsOnlyFirstBranchHasExplanation(branches);
        for (int branchCode = 1; branchCode <= branches.size(); branchCode++) {
            SmallQuestionVo branch = branches.get(branchCode - 1);

            String newStem = replaceHtmlLeadingBranchCode(branch.getStem(), branchCode);
            String newExplanation = onlyFirstBranchHasExplanation ? null : replaceHtmlLeadingBranchCode(branch.getExplanation(), branchCode);
            String newAnswer = null;
            if (!BranchTypeEnum.FillBlank.getId().equals(branch.getQuestionTypeId())) {
                newAnswer = replaceHtmlLeadingBranchCode(branch.getSolution(), branchCode);
            }
            else {
                try {
                    List<String> blanks = JSON.parseArray(branch.getSolution(), String.class);
                    String firstBlank = replaceHtmlLeadingBranchCode(blanks.get(0), branchCode);
                    if (firstBlank != null) {
                        blanks.set(0, firstBlank);
                        newAnswer = JSON.toJSONString(blanks);
                    }
                }
                catch (Exception ex) {
                    System.out.println("解析填空题失败：" + branch.getId());
                }
            }

            if (newStem != null || newAnswer != null || newExplanation != null) {
                StringBuilder sb = new StringBuilder();
                sb.append("删除小题号，smallQuestionId = ").append(branch.getId()).append("\n");
                LambdaUpdateWrapper<SmallQuestion> updateWrapper = new LambdaUpdateWrapper<SmallQuestion>().eq(SmallQuestion::getId, branch.getId());
                if (newStem != null) {
                    sb.append("【旧题干】").append(branch.getStem()).append("\n").append("【新题干】").append(newStem).append("\n");
                    updateWrapper.set(SmallQuestion::getContentHtml, newStem);
                }
                if (newAnswer != null) {
                    sb.append("【旧答案】\n").append(branch.getSolution()).append("\n").append("【新答案】\n").append(newAnswer).append("\n");
                    updateWrapper.set(SmallQuestion::getAnswerHtml, newAnswer);
                }
                if (newExplanation != null) {
                    sb.append("【旧解析】\n").append(branch.getExplanation()).append("\n").append("【新解析】\n").append(newExplanation).append("\n");
                    updateWrapper.set(SmallQuestion::getExplanation, newExplanation);
                }
                smallQuestionMapper.update(null, updateWrapper);
                log.info(sb.toString());
                replaceCount++;
            }
        }
        return replaceCount;
    }

    private boolean getIsOnlyFirstBranchHasExplanation(List<SmallQuestionVo> branches) {
        if (branches.size() <= 1) {
            return false;
        }
        for (int idx = 0; idx < branches.size(); idx++) {
            SmallQuestionVo branch = branches.get(idx);
            boolean isEmpty = StringUtils.isEmpty(branch.getExplanation());
            if ((idx == 0 && isEmpty) || (idx > 0 && (!isEmpty))) {
                return false;
            }
        }
        return true;
    }

    private String replaceHtmlLeadingBranchCode(String html, int branchCode) {
        if (StringUtils.isEmpty(html)) {
            return null;
        }

        String replace = replaceTextLeadingBranchCode(html, branchCode);
        if (replace != null) {
            return replace;
        }
        else if (html.startsWith("<p")) {
            boolean find = false;
            Document doc = Jsoup.parseBodyFragment(html);
            if (!doc.body().children().isEmpty()) {
                Element p = doc.body().child(0);
                if (p.nodeName().equalsIgnoreCase("p") && p.childNodeSize() > 0) {
                    Node node = p.childNode(0);
                    if (node instanceof TextNode) {
                        String text = ((TextNode) node).text();
                        replace = replaceTextLeadingBranchCode(text, branchCode);
                        if (replace != null) {
                            TextNode textNode = new TextNode(replace);
                            node.replaceWith(textNode);
                            find = true;
                        }
                    }
                }
            }
            if (find) {
                return doc.body().html();
            }
        }
        return null;
    }

    private String replaceTextLeadingBranchCode(String text, int branchCode) {
        Pattern pattern = Pattern.compile("^\\s*[(（]\\s*" + branchCode + "\\s*[)）]\\s*");
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return text.substring(matcher.group(0).length());
        }
        return null;
    }

    /**
     * 修复填空题不能解析问题
     */
    @Test
    public void repairFillBlankAnswerImage() {
        List<String> branchIds = Arrays.asList("1694276749756293121",
                "1694280294832365568",
                "1694280475678171136",
                "1694280531307225089",
                "1694280726568853504",
                "1694282252867690497",
                "1694282273130373121",
                "1694282284245278721",
                "1694298005335531520",
                "1694298727754063873",
                "1694298753611948032",
                "1694298754312396800",
                "1694298754459197440",
                "1694298784121315329",
                "1694298809610100737",
                "1694298840006221825",
                "1694298875250958337",
                "1694298903587676161",
                "1694298904007106561",
                "1694298933199462400",
                "1694298933514035200",
                "1694298933715361792",
                "1694299013855928321",
                "1694299014250192897",
                "1694299057959034881",
                "1694299091047899137",
                "1694299091584770048",
                "1694299091622518784",
                "1694299091828039680",
                "1694299233746509826",
                "1694299233960419328",
                "1694299687427596289",
                "1694299809733500928",
                "1694299809989353473",
                "1694299873478533120",
                "1694299873889574912",
                "1694299900120752128",
                "1694299901144162305",
                "1694299927396311041",
                "1694636007119216641",
                "1694636020423548929");
        List<SmallQuestion> smallQuestions = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>().in(SmallQuestion::getId, branchIds));
        for (SmallQuestion sq: smallQuestions) {
            String answer = sq.getAnswerHtml();
            if (!BranchTypeEnum.FillBlank.getId().equals(sq.getQuestionTypeId())) {
                continue;
            }
            if (canParse(answer)) {
                continue;
            }
            List<String> blanks = splitBlanks(answer);
            if (blanks == null) {
                System.out.println(sq.getId());
                continue;
            }
            replaceBlanks(blanks, blank -> blank.replace("\n", "").replace("\\&quot;", ""));
            answer = mergeBlanks(blanks);
            if (!canParse(answer)) {
                replaceBlanks(blanks, blank -> blank.replace("\"", "\\\""));
                answer = mergeBlanks(blanks);
            }
            if (canParse(answer)) {
                smallQuestionMapper.update(null, new LambdaUpdateWrapper<SmallQuestion>()
                        .eq(SmallQuestion::getId, sq.getId())
                        .set(SmallQuestion::getAnswerHtml, answer));
            }
            else {
                System.out.println(sq.getId());
            }
        }
    }

    private boolean canParse(String answer) {
        try {
            JSON.parseArray(answer, String.class);
            return true;
        }
        catch (Exception ex) {
            return false;
        }
    }

    private List<String> splitBlanks(String answer) {
        if (!answer.startsWith("[") || !answer.endsWith("]")) {
            return null;
        }
        answer = answer.substring(1, answer.length() - 1);
        String[] split = StringUtils.split(answer, ",");
        List<String> blanks = new ArrayList<>();
        for (String s : split) {
            String blank = s.trim();
            if (!blank.startsWith("\"") || !blank.endsWith("\"")) {
                return null;
            }
            blanks.add(blank.substring(1, blank.length() - 1));
        }
        return blanks;
    }

    private void replaceBlanks(List<String> blanks, Function<String, String> method) {
        for (int i = 0; i < blanks.size(); i++) {
            blanks.set(i, method.apply(blanks.get(i)));
        }
    }

    private String mergeBlanks(List<String> blanks) {
        return "[" + blanks.stream().map(blank -> "\"" + blank + "\"").collect(Collectors.joining(",")) + "]";
    }
}
