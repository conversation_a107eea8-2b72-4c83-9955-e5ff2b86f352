import com.sure.question.QuestionApplication;
import com.sure.question.service.ChapterService;
import com.sure.question.service.KnowledgeMainService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class ImportKnowledgeTest {
    @Resource
    private KnowledgeMainService knowledgeMainService;
    @Resource
    private ChapterService chapterService;

    @Test
    public void run() throws IOException {
        String importExcelPath = "C:\\Users\\<USER>\\Desktop\\高中语文知识点.xlsx";
        int gradeLevelId = 4;
        int subjectId = 1;
        boolean save = false;

        InputStream inputStream = Files.newInputStream(Paths.get(importExcelPath));
        String outputExcelPath = importExcelPath.replace(".xlsx", "-变更.xlsx");
        OutputStream outputStream = Files.newOutputStream(Paths.get(outputExcelPath));
        String userId = "00000000-0000-0000-0000-000000000000";
        knowledgeMainService.importExcel(gradeLevelId, subjectId, inputStream, outputStream, save, userId);
        if (save) {
            knowledgeMainService.deleteCache(gradeLevelId, subjectId);
            chapterService.deleteCache(gradeLevelId, subjectId);
        }
    }
}
