package volcengine.ocr;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sure.question.QuestionApplication;
import com.sure.question.dto.coachBook.ingestion.PageImage;
import com.sure.question.entity.CoachBookIngestion;
import com.sure.question.mapper.CoachBookIngestionMapper;
import com.sure.question.service.OssManager;
import com.volcengine.service.visual.IVisualService;
import com.volcengine.service.visual.impl.VisualServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class DocumentParseTest {
    @Resource
    private CoachBookIngestionMapper coachBookIngestionMapper;
    @Resource
    private OssManager ossManager;

    @Test
    public void test() throws Exception {
        List<String> pageImageUrls = getPageImageUrls(5);
        String folder = "C:\\Users\\<USER>\\Desktop\\document-parse\\5\\";
        for (int i = 0; i < pageImageUrls.size(); i++) {
            int pageNumber = i + 1;
            String url = pageImageUrls.get(i);
            JSONObject parsedPage = parseOnePage(url, folder + pageNumber + ".json");
            Thread.sleep(1000);
            drawRects(url, parsedPage, pageNumber, folder + pageNumber + ".jpg");
        }
    }

    private List<String> getPageImageUrls(int ingestionId) {
        CoachBookIngestion ingestion = coachBookIngestionMapper.selectById(ingestionId);
        String pageImages = ingestion.getPageImages();
        if (StringUtils.isEmpty(pageImages)) {
            return new ArrayList<>();
        }
        List<PageImage> pageImageList = JSON.parseArray(pageImages, PageImage.class);
        return pageImageList.stream().map(PageImage::getUrl).map(ossManager::getPublicPathTK).collect(Collectors.toList());
    }

    private JSONObject parseOnePage(String url, String savePath) throws Exception {
        IVisualService visualService = VisualServiceImpl.getInstance();

        visualService.setAccessKey("AKLTMjIwY2ZmYTZjZDg4NDhkYWI3YTQ1YWI2YmU3MzRiNTE");
        visualService.setSecretKey("T1dabE1XVTRaRGc1T0dKak5HWmpNRGhpWm1VeVlXRTNZMk5rTVdZMk1UZw==");

        JSONObject request = new JSONObject();
        request.put("image_url", url);
        request.put("version", "v3");
        request.put("file_type", "image");
        String action = "OCRPdf";

        String resJson = visualService.ocrApi(action, request);
        Files.write(Paths.get(savePath), resJson.getBytes());

        JSONObject res = JSONObject.parseObject(resJson);
        if (!res.get("code").equals(10000)) {
            throw new RuntimeException("解析出错");
        }
        return res;
    }

    private static void drawRects(String url, JSONObject res, int pageNumber, String outputFilePath) throws IOException {
        BufferedImage image = ImageIO.read(new URL(url));
        JSONArray textblocks = res.getJSONObject("data").getJSONArray("detail").getJSONObject(0).getJSONArray("textblocks");
        int imageOrder = 1;
        for (int i = 0; i < textblocks.size(); i++) {
            JSONObject textblock = textblocks.getJSONObject(i);

            JSONObject norm_box = textblock.getJSONObject("norm_box");
            double x0 = norm_box.getDoubleValue("x0");
            double y0 = norm_box.getDoubleValue("y0");
            double x1 = norm_box.getDoubleValue("x1");
            double y1 = norm_box.getDoubleValue("y1");
            int x = (int) (x0 * image.getWidth());
            int y = (int) (y0 * image.getHeight());
            int w = (int) ((x1 - x0) * image.getWidth());
            int h = (int) ((y1 - y0) * image.getHeight());


            Graphics2D g = image.createGraphics();

            // 开启抗锯齿、文本抗锯齿
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 设置矩形线条粗细与颜色
            g.setStroke(new BasicStroke(2));
            g.setColor(Color.RED);
            g.drawRect(x, y, w, h);

            // 如果想要半透明填充（可选），取消注释下面两行：
             g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.05f));
             g.fillRect(x, y, w, h);
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 1f));

            if (!StringUtils.equals("image", textblock.getString("label"))) {
                continue;
            }
            String text = "IMG_" + pageNumber + "_" + String.valueOf(imageOrder++);
            FontRenderContext frc = g.getFontRenderContext();
            Font font = new Font("SansSerif", Font.BOLD, 24);
            g.setFont(font);
            Rectangle2D textBounds = font.getStringBounds(text, frc);
            int textWidth = (int) Math.ceil(textBounds.getWidth());
            int textHeight = (int) Math.ceil(textBounds.getHeight());
            int paddingX = 6; // 左右内边距
            int paddingY = 4; // 上下内边距

            // 文字背景矩形（红色）
            int bgW = textWidth + paddingX * 2;
            int bgH = textHeight + paddingY * 2;

            g.setColor(Color.RED);
            g.fillRect(x, y, bgW, bgH);

            // 绘制白色文字
            g.setColor(Color.WHITE);
            FontMetrics fm = g.getFontMetrics();
            int textBaseY = y + paddingY + fm.getAscent(); // 基线位置
            g.drawString(text, x + paddingX, textBaseY);

            // 释放资源并保存
            g.dispose();
        }
        ImageIO.write(image, "jpg", new File(outputFilePath));
    }
}
