package cleanTask.util;

import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Attribute;
import org.jsoup.nodes.Attributes;
import org.jsoup.nodes.Element;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ImageElementUtil {
    private static final Pattern StyleWidthPattern = Pattern.compile("(^|[\\s;])width\\s*:\\s*([^\\s;]+)\\s*($|;)");
    private static final Pattern StyleHeightPattern = Pattern.compile("(^|[\\s;])height\\s*:\\s*([^\\s;]+)\\s*($|;)");
    private static final Pattern CssZeroPattern = Pattern.compile("^[0.]+$");

    public static Size getImageSize(Element img) throws Exception {
        // 先尝试从样式中获取
        Double width = null;
        Double height = null;
        String styles = img.attr("style");
        if (StringUtils.isNotEmpty(styles)) {
            try {
                width = parseStyleSize(styles, StyleWidthPattern);
                height = parseStyleSize(styles, StyleHeightPattern);
            }
            catch (Exception ex) {
                throw new Exception("从样式中解析长宽出错, styles = " + styles);
            }
        }
        if (width != null && height != null) {
            return new Size(width, height);
        }

        // 再尝试从属性中获取
        if (width == null) {
            try {
                width = parseAttrSize(img.attr("width"));
            }
            catch (Exception ex) {
                throw new Exception("从属性中解析长宽出错, width = " + img.attr("width"));
            }
        }
        if (height == null) {
            try {
                height = parseAttrSize(img.attr("height"));
            }
            catch (Exception ex) {
                throw new Exception("从属性中解析长宽出错, height = " + img.attr("height"));
            }
        }
        if (width != null && height != null) {
            return new Size(width, height);
        }

        // 最后下载图片获取
        Size naturalSize;
        try {
            naturalSize = getImageNaturalSize(img.attr("src"));
        }
        catch (Exception ex) {
            throw new Exception("加载图片出错, src = " + img.attr("src"));
        }
        if (width != null) {
            return new Size(width, width / naturalSize.width * naturalSize.height);
        }
        else if (height != null) {
            return new Size(height / naturalSize.height * naturalSize.width, height);
        }
        else {
            return naturalSize;
        }
    }

    public static Double parseStyleSize(String styles, Pattern pattern) throws Exception {
        // 得到样式值
        Matcher m = pattern.matcher(styles);
        String match;
        if (m.find()) {
            match = m.group(2);
        }
        else {
            return null;
        }
        return parseStyleSizeValue(match);
    }

    public static Double parseStyleSizeValue(String value) throws Exception {
        // 若为0可无长度单位
        try {
            if (CssZeroPattern.matcher(value).matches()) {
                if (Double.parseDouble(value) == 0) {
                    return 0D;
                }
            }
        }
        catch (Exception ex) {
            // 如果无法解析成数字，则继续后续流程
        }
        // 判断长度单位
        CssUnitEnum unit = Arrays.stream(CssUnitEnum.values()).filter(unitEnum -> value.endsWith(unitEnum.identifier)).findFirst().orElse(null);
        if (unit == null) {
            throw new Exception("未匹配到长度单位");
        }
        // 转成像素
        String numberStr = value.substring(0, value.length() - unit.identifier.length());
        return Double.parseDouble(numberStr) * unit.scale;
    }

    public static Double parseAttrSize(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        value = value.replaceAll("/", "").replaceAll("\\\\", "");
        if (value.endsWith("px")) {
            return Double.parseDouble(value.substring(0, value.length() - 2));
        }
        return Double.parseDouble(value);
    }

    public static Size getImageNaturalSize(String src) throws IOException {
        if (StringUtils.isEmpty(src)) {
            return new Size(0, 0);
        }
        byte[] imageBytes = ImageUtil.getImageBytes(src);
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageBytes));
        return new Size(image.getWidth(), image.getHeight());
    }

    public static void cleanAttributes(Element img) throws Exception {
        // 可以保留的样式
        Double styleWidth = null;
        Double styleHeight = null;
        String styleFloat = null;
        String styleDisplay = null;

        Attributes attributes = img.attributes();
        for (Attribute attr : attributes.asList()) {
            String key = attr.getKey();
            String value = attr.getValue();
            // 保留src属性，删除其他属性，提取样式值
            if (key.equals("src")) {
                continue;
            }
            if (key.equals("width")) {
                styleWidth = parseAttrSize(value);
            }
            if (key.equals("height")) {
                styleHeight = parseAttrSize(value);
            }
            if (key.equals("style")) {
                Map<String, String> styles = CssStyleUtil.parseStyle(value);
                for (String styleKey : styles.keySet()) {
                    String styleValue = styles.get(styleKey);
                    if (styleKey.equals("width")) {
                        styleWidth = parseStyleSizeValue(styleValue);
                    }
                    if (styleKey.equals("height")) {
                        styleHeight = parseStyleSizeValue(styleValue);
                    }
                    if (styleKey.equals("float")) {
                        styleFloat = styleValue;
                    }
                    if (styleKey.equals("display")) {
                        styleDisplay = styleValue;
                    }
                }
            }

            attributes.remove(key);
        }

        // 存在样式则添加
        String style = "";
        if (styleWidth != null) {
            style += "width:" + styleWidth + "px;";
        }
        if (styleHeight != null) {
            style += "height:" + styleHeight + "px;";
        }
        if (styleFloat != null) {
            style += "float:" + styleFloat + ";";
        }
        if (styleDisplay != null) {
            style += "display:" + styleDisplay + ";";
        }
        if (StringUtils.isNotEmpty(style)) {
            attributes.put("style", style);
        }
    }
}
