package cleanTask.util;

import cn.hutool.http.HttpRequest;
import com.sure.question.common.IdWorker;
import org.apache.commons.lang3.StringUtils;

import java.net.URL;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ImageUtil {
    private static final Pattern DataUrlPrefixPattern = Pattern.compile("^data:[^;]+;base64,");

    private static final String[] validExtensions = {".png", ".jpg", ".gif", ".jpeg", ".svg"};
    private static final Map<String, String> validDataUrlExtensionMap = new HashMap<>();
    static {
        validDataUrlExtensionMap.put("data:image/png;base64,", ".png");
        validDataUrlExtensionMap.put("data:image/jpeg;base64,", ".jpg");
        validDataUrlExtensionMap.put("data:image/gif;base64,", ".gif");
        validDataUrlExtensionMap.put("data:image/svg+xml;base64,", ".svg");
    }

    private static final IdWorker idWorker = new IdWorker(11, 11);

    public static byte[] getImageBytes(String src) {
        byte[] bytes;
        Matcher matcher = DataUrlPrefixPattern.matcher(src);
        if (matcher.find()) {
            bytes = Base64.getDecoder().decode(src.substring(matcher.group(0).length()));
        }
        else {
            bytes = HttpRequest.get(src).execute().bodyBytes();
        }
        return bytes;
    }

    public static String getImageOSSName(String src, String questionId) throws Exception {
        return questionId + "_" + idWorker.nextId() + getImageUrlExtension(src);
    }

    public static String getImageUrlExtension(String src) throws Exception {
        String defaultExtension = validExtensions[0];
        if (StringUtils.isEmpty(src)) {
            return defaultExtension;
        }
        // 先尝试DataUrl
        for (String key : validDataUrlExtensionMap.keySet()) {
            if (src.startsWith(key)) {
                return validDataUrlExtensionMap.get(key);
            }
        }
        // 再尝试普通url
        String path = new URL(src).getPath();
        for (String extension : validExtensions) {
            if (path.endsWith(extension)) {
                return extension;
            }
        }
        return defaultExtension;
    }
}
