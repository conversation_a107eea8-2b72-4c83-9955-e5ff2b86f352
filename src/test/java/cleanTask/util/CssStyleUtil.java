package cleanTask.util;

import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

public class CssStyleUtil {
    public static Map<String, String> parseStyle(String style) throws Exception {
        Map<String, String> result = new LinkedHashMap<>();
        style = style.trim();
        if (StringUtils.isEmpty(style)) {
            return result;
        }
        String[] itemList = style.split(";");
        for (String item : itemList) {
            String[] propertyValue = item.split(":");
            if (propertyValue.length != 2) {
                throw new Exception("无法解析样式：" + style);
            }
            String property = propertyValue[0].trim().toLowerCase();
            String value = propertyValue[1].trim().toLowerCase();
            if (StringUtils.isAnyEmpty(property, value)) {
                throw new Exception("无法解析样式：" + style);
            }
            result.put(property, value);
        }
        return result;
    }
}
