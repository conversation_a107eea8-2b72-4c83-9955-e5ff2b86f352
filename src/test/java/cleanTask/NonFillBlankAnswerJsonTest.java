package cleanTask;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.entity.CleanTaskQuestion;
import com.sure.question.entity.CleanTaskSmallQuestion;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.enums.BranchTypeEnum;
import com.sure.question.enums.cleanTask.ConfirmStatusEnum;
import com.sure.question.mapper.CleanTaskQuestionMapper;
import com.sure.question.mapper.CleanTaskSmallQuestionMapper;
import com.sure.question.mapper.SmallQuestionMapper;
import com.sure.question.service.CleanTaskService;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 部分小题答案是JSON但小题类型不是填空题，找出并修正
 * SQL:
 * SELECT sq.id, sq.question_id, sq.answer_html
 * FROM small_question sq, paper_question pq, paper_main p
 * WHERE sq.question_id = pq.question_id
 * 	AND pq.paper_id = p.id
 * 	AND p.paper_bank = 3
 * 	AND p.status = 0
 * 	AND sq.question_type_id != 21
 * 	AND sq.answer_html IS NOT NULL
 * 	AND LEFT(sq.answer_html, 1) = '['
 * 	AND RIGHT(sq.answer_html, 1) = ']';
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class NonFillBlankAnswerJsonTest {
    @Resource
    private CleanTaskQuestionMapper cleanTaskQuestionMapper;
    @Resource
    private CleanTaskSmallQuestionMapper cleanTaskSmallQuestionMapper;
    @Resource
    private SmallQuestionMapper smallQuestionMapper;
    @Resource
    private CleanTaskService cleanTaskService;

    private static final int taskId = 153;

    @Test
    public void findObjectiveLike() {
        List<String> choices = Arrays.asList("A", "B", "C", "D", "E", "F", "G");
        List<Integer> choiceBranchTypes = Arrays.asList(BranchTypeEnum.SingleChoice.getId(), BranchTypeEnum.MultipleChoice.getId());

        // 查未确认的题目
        List<CleanTaskQuestion> unConfirmQuestions = cleanTaskQuestionMapper.selectList(new LambdaQueryWrapper<CleanTaskQuestion>().eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Init.getCode()));
        List<String> unConfirmQuestionIds = unConfirmQuestions.stream().map(CleanTaskQuestion::getQuestionId).collect(Collectors.toList());
        if (unConfirmQuestionIds.isEmpty()) {
            return;
        }

        // 查任务小题
        List<CleanTaskSmallQuestion> cleanTaskSmallQuestionList = cleanTaskSmallQuestionMapper.selectList(new LambdaQueryWrapper<CleanTaskSmallQuestion>()
                .eq(CleanTaskSmallQuestion::getTaskId, taskId)
                .in(CleanTaskSmallQuestion::getQuestionId, unConfirmQuestionIds));
        Map<String, List<CleanTaskSmallQuestion>> groupByQuestionId = cleanTaskSmallQuestionList.stream().collect(Collectors.groupingBy(CleanTaskSmallQuestion::getQuestionId));

        // 查小题
        List<String> smallQuestionIds = cleanTaskSmallQuestionList.stream().map(CleanTaskSmallQuestion::getSmallQuestionId).collect(Collectors.toList());
        List<SmallQuestion> smallQuestionList = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                .in(SmallQuestion::getId, smallQuestionIds)
                .select(SmallQuestion::getId, SmallQuestion::getOptions, SmallQuestion::getAnswerHtml, SmallQuestion::getQuestionTypeId));
        Map<String, SmallQuestion> smallQuestionMap = smallQuestionList.stream().collect(Collectors.toMap(SmallQuestion::getId, Function.identity()));


        for (CleanTaskQuestion q : unConfirmQuestions) {
            List<CleanTaskSmallQuestion> taskSmallQuestions = groupByQuestionId.get(q.getQuestionId());
            if (taskSmallQuestions.isEmpty()) {
                continue;
            }
            Map<String, String> smallQuestionIdAnswerMap = new HashMap<>();
            // 如果小题题型为选择题，且解析后的答案是有效的选择题答案，则更新答案
            for (CleanTaskSmallQuestion sq : taskSmallQuestions) {
                SmallQuestion smallQuestion = smallQuestionMap.get(sq.getSmallQuestionId());
                if (smallQuestion == null) {
                    continue;
                }
                if (StringUtils.isEmpty(smallQuestion.getOptions()) || !choiceBranchTypes.contains(smallQuestion.getQuestionTypeId())) {
                    continue;
                }
                List<String> answers;
                try {
                    answers = JSON.parseArray(sq.getOldAnswerHtml(), String.class);
                }
                catch (Exception ex) {
                    continue;
                }
                if (answers.isEmpty()) {
                    continue;
                }
                if (!choices.containsAll(answers)) {
                    continue;
                }
                boolean answerHasOrder = true;
                for (int i = 1; i < answers.size(); i++) {
                    if (answers.get(i).charAt(0) <= answers.get(i - 1).charAt(0)) {
                        answerHasOrder = false;
                        break;
                    }
                }
                if (!answerHasOrder) {
                    continue;
                }
                smallQuestionIdAnswerMap.put(sq.getSmallQuestionId(), StringUtils.join(answers, ""));
            }
            if (taskSmallQuestions.size() == smallQuestionIdAnswerMap.size()) {
                smallQuestionIdAnswerMap.forEach((smallQuestionId, answer) -> cleanTaskSmallQuestionMapper.update(null, new LambdaUpdateWrapper<CleanTaskSmallQuestion>()
                        .eq(CleanTaskSmallQuestion::getTaskId, taskId)
                        .eq(CleanTaskSmallQuestion::getSmallQuestionId, smallQuestionId)
                        .set(CleanTaskSmallQuestion::getAnswerHtml, answer)));
                cleanTaskQuestionMapper.update(null, new LambdaUpdateWrapper<CleanTaskQuestion>()
                        .eq(CleanTaskQuestion::getTaskId, taskId)
                        .eq(CleanTaskQuestion::getQuestionId, q.getQuestionId())
                        .set(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Confirmed.getCode()));
            }
        }
    }

    @Test
    public void updateAll() {
        cleanTaskService.updateAllConfirmedQuestions(taskId);
    }
}
