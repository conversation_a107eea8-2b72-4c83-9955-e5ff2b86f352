package cleanTask;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.entity.CleanTaskQuestion;
import com.sure.question.entity.CleanTaskSmallQuestion;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.enums.BranchTypeEnum;
import com.sure.question.mapper.CleanTaskQuestionMapper;
import com.sure.question.mapper.CleanTaskSmallQuestionMapper;
import com.sure.question.mapper.SmallQuestionMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 修复填空题答案JSON不能解析错误
 * clean_task_question已由删除小图片任务log手动插入
 * 此代码尝试做一些替换处理，处理后仍不能解析的，由人工处理
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class FillBlankAnswerJsonTest {
    @Resource
    private SmallQuestionMapper smallQuestionMapper;
    @Resource
    private CleanTaskQuestionMapper cleanTaskQuestionMapper;
    @Resource
    private CleanTaskSmallQuestionMapper cleanTaskSmallQuestionMapper;

    @Test
    public void insertTaskSmallQuestion() {
        int taskId = 129;
        List<CleanTaskQuestion> cleanTaskQuestionList = cleanTaskQuestionMapper.selectList(new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId));
        Map<String, CleanTaskQuestion> questionMap = cleanTaskQuestionList.stream()
                .collect(Collectors.toMap(CleanTaskQuestion::getQuestionId, Function.identity()));
        List<SmallQuestion> smallQuestionList = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                .eq(SmallQuestion::getQuestionTypeId, BranchTypeEnum.FillBlank.getId())
                .in(SmallQuestion::getQuestionId, questionMap.keySet())
                .select(SmallQuestion::getId, SmallQuestion::getQuestionId, SmallQuestion::getAnswerHtml));
        for (SmallQuestion sq : smallQuestionList) {
            String answer = sq.getAnswerHtml();
            boolean modified = false;
            try {
                JSON.parseArray(answer);
                continue;
            }
            catch (Exception ex) {
                // 解析失败不做处理
            }

            // 连续空白符替换为空格
            answer = answer.replaceAll("\\s+", " ");
            try {
                JSON.parseArray(answer);
                modified = true;
            }
            catch (Exception ex) {
                // 解析失败不做处理
            }

            // 删除引号转义
            if (!modified) {
                answer = answer.replaceAll("\"\\\\&quot;", "\\\\\"");
                answer = answer.replaceAll("\\\\&quot;\"", "\\\\\"");
                try {
                    JSON.parseArray(answer);
                    modified = true;
                }
                catch (Exception ex) {
                    // 解析失败不做处理
                }
            }

            // 样式后经常带有多余的引号，如display:"none
            if (!modified) {
                answer = answer.replaceAll(":\"", ":");
                try {
                    JSON.parseArray(answer);
                    modified = true;
                }
                catch (Exception ex) {
                    // 解析失败不做处理
                }
            }

            // 插入小题数据
            CleanTaskSmallQuestion entity = new CleanTaskSmallQuestion();
            entity.setTaskId(taskId);
            entity.setQuestionId(sq.getQuestionId());
            entity.setSmallQuestionId(sq.getId());
            entity.setOldAnswerHtml(sq.getAnswerHtml());
            if (modified) {
                entity.setAnswerHtml(answer);
            }
            entity.setCreateTime(new Date());
            cleanTaskSmallQuestionMapper.insert(entity);
        }
    }
}
