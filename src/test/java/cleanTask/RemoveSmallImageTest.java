package cleanTask;

import cleanTask.util.BankTaskNameUtil;
import cleanTask.util.ImageElementUtil;
import cleanTask.util.Size;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.entity.CleanTask;
import com.sure.question.mapper.CleanTaskMapper;
import com.sure.question.service.CleanTaskService;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;

/**
 * 移除题目内容中的小图片
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class RemoveSmallImageTest {
    @Resource
    private CleanTaskService cleanTaskService;
    @Resource
    private CleanTaskMapper cleanTaskMapper;

    private final Logger logger = LoggerFactory.getLogger("cleanTask");

    private final String taskName = "删除小图片";

    private final Map<String, BiConsumer<Integer, CleanTaskService.IHandleQuestionHtml>> taskNameRunnerMap = new HashMap<>();

    @PostConstruct
    void buildTaskNameRunnerMap() {
        taskNameRunnerMap.put(BankTaskNameUtil.getPublicBankTaskName(taskName), cleanTaskService::runPublicBankTask);
        taskNameRunnerMap.put(BankTaskNameUtil.getCoachBookTaskName(taskName), cleanTaskService::runCoachBookTask);
        taskNameRunnerMap.put(BankTaskNameUtil.getCoachBookCustomTaskName(taskName), cleanTaskService::runCoachBookCustomTask);
        taskNameRunnerMap.put(BankTaskNameUtil.getPushQuestionTaskName(taskName), cleanTaskService::runPushQuestionTask);
    }

    @Test
    public void startPublicBank() {
        start(BankTaskNameUtil.getPublicBankTaskName(taskName));
    }

    @Test
    public void updateAllPublicBank() {
        updateAll(BankTaskNameUtil.getPublicBankTaskName(taskName));
    }

    @Test
    public void startCoachBook() {
        start(BankTaskNameUtil.getCoachBookTaskName(taskName));
    }

    @Test
    public void updateAllCoachBook() {
        updateAll(BankTaskNameUtil.getCoachBookTaskName(taskName));
    }

    @Test
    public void startCoachBookCustom() {
        start(BankTaskNameUtil.getCoachBookCustomTaskName(taskName));
    }

    @Test
    public void updateAllCoachBookCustom() {
        updateAll(BankTaskNameUtil.getCoachBookCustomTaskName(taskName));
    }

    @Test
    public void startPushQuestion() {
        start(BankTaskNameUtil.getPushQuestionTaskName(taskName));
    }

    @Test
    public void updateAllPushQuestion() {
        updateAll(BankTaskNameUtil.getPushQuestionTaskName(taskName));
    }

    private void start(String taskName) {
        BiConsumer<Integer, CleanTaskService.IHandleQuestionHtml> runner = taskNameRunnerMap.get(taskName);
        if (runner == null) {
            return;
        }
        List<CleanTask> cleanTaskList = cleanTaskMapper.selectList(new LambdaQueryWrapper<CleanTask>().eq(CleanTask::getName, taskName));
        for (CleanTask task : cleanTaskList) {
            runner.accept(task.getId(), (html, paperId) -> this.removeHtmlSmallImages(html));
        }
    }

    private void updateAll(String taskName) {
        List<CleanTask> cleanTaskList = cleanTaskMapper.selectList(new LambdaQueryWrapper<CleanTask>().eq(CleanTask::getName, taskName));
        for (CleanTask task : cleanTaskList) {
            cleanTaskService.updateAllConfirmedQuestions(task.getId());
        }
    }

    /**
     * 剔除一段html文本中的小图片标签
     * @return 无变更返回null，否则返回变更后的html
     */
    private String removeHtmlSmallImages(String html) throws Exception {
        if (StringUtils.isBlank(html)) {
            return null;
        }
        if (!html.contains("<img")) {
            return null;
        }
        boolean flag = false;
        Document doc = Jsoup.parseBodyFragment(html);
        Elements imgElements = doc.getElementsByTag("img");
        for (Element img : imgElements) {
            Size size = ImageElementUtil.getImageSize(img);
            if (size.width <= 8 && size.height <=8) {
                logger.info("删除小图片, {}", img.outerHtml());
                img.remove();
                flag = true;
            }
        }
        if (flag) {
            return doc.body().html();
        }
        return null;
    }
}
