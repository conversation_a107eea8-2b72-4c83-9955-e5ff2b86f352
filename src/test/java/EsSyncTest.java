import com.sure.question.QuestionApplication;
import com.sure.question.jobHandler.XxlJobHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class EsSyncTest {
    @Resource
    private XxlJobHandler xxlJobHandler;

    @Test
    public void test() {
        System.out.println(xxlJobHandler.syncQuesV2(""));
    }
}
