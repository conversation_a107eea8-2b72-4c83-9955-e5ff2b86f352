import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sure.question.QuestionApplication;
import com.sure.question.entity.KnowledgeMain;
import com.sure.question.mapper.KnowledgeMainMapper;
import com.sure.question.service.KnowledgeDocService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class KnowledgeDocTest {
    @Resource
    private KnowledgeMainMapper knowledgeMainMapper;
    @Resource
    private KnowledgeDocService knowledgeDocService;

    @Test
    public void test() {
        List<KnowledgeMain> gradeLevelSubjects = knowledgeMainMapper.selectList(new QueryWrapper<KnowledgeMain>()
                .select("distinct grade_level, subject_id")
                .eq("status", 1));
        gradeLevelSubjects.forEach(x -> {
            System.out.println(x.getGradeLevel() + " " + x.getSubjectId());
            knowledgeDocService.sync(x.getGradeLevel(), x.getSubjectId());
        });
    }
}
