import com.sure.question.QuestionApplication;
import com.sure.question.service.BookService;
import com.sure.question.service.ChapterService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = QuestionApplication.class)
public class ImportChapterTest {
    @Resource
    private ChapterService chapterService;
    @Resource
    private BookService bookService;

    @Test
    public void importChapter() throws IOException {
        String importExcelPath = "C:\\Users\\<USER>\\Desktop\\英语章节新增.xlsx";
        int gradeLevelId = 2;
        int subjectId = 3;
        boolean save = false;

        InputStream inputStream = Files.newInputStream(Paths.get(importExcelPath));
        String outputExcelPath = importExcelPath.replace(".xlsx", "-变更.xlsx");
        OutputStream outputStream = Files.newOutputStream(Paths.get(outputExcelPath));
        String userId = "00000000-0000-0000-0000-000000000000";
        chapterService.importExcel(gradeLevelId, subjectId, inputStream, outputStream, save, userId);
        if (save) {
            chapterService.deleteCache(gradeLevelId, subjectId);
        }
    }

    @Test
    public void importCopyChapterQuestion() throws IOException {
        String importExcelPath = "C:\\Users\\<USER>\\Desktop\\英语章节题目复制.xlsx";
        int gradeLevelId = 2;
        int subjectId = 3;

        InputStream inputStream = Files.newInputStream(Paths.get(importExcelPath));
        String userId = "00000000-0000-0000-0000-000000000000";
        String message = chapterService.importCopyChapterQuestionExcel(gradeLevelId, subjectId, inputStream, false, userId);
        System.out.println(message);
    }
}
