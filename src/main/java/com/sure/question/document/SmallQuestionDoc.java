package com.sure.question.document;

import com.alibaba.fastjson.JSON;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.vo.question.SmallQuestionOptionVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmallQuestionDoc {
    private Long id;
    private String content_html;
    private String options;
    private String answer_html;
    private String explanation;
    private Short question_type_id;
    private List<Long> knowledge_ids = new ArrayList<>();

    private static final String OptionOrderedListClass = "igrade-options-array";
    private static final String AnswerOrderedListClass = "igrade-answer-array";

    public SmallQuestionDoc(SmallQuestion sq) {
        this.setId(Long.parseLong(sq.getId()));
        this.setContent_html(sq.getContentHtml());
        this.setOptions(convertOptionsJsonToHtml(sq.getOptions()));
        if (Objects.equals(sq.getQuestionTypeId(), 21)) {
            this.setAnswer_html(convertAnswerJsonToHtml(sq.getAnswerHtml()));
        } else {
            this.setAnswer_html(sq.getAnswerHtml());
        }
        this.setExplanation(sq.getExplanation());
        this.setQuestion_type_id(sq.getQuestionTypeId() == null ? null : sq.getQuestionTypeId().shortValue());
    }

    private static String convertOptionsJsonToHtml(String optionsJson) {
        if (StringUtils.isBlank(optionsJson)) {
            return null;
        }
        if ("[]".equals(optionsJson)) {
            return optionsJson;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("<ol class=\"" + OptionOrderedListClass + "\">");
        List<SmallQuestionOptionVo> options;
        try {
            options = JSON.parseArray(optionsJson, SmallQuestionOptionVo.class);
            for (SmallQuestionOptionVo option : options) {
                sb.append("<li data-label=\"").append(ObjectUtils.defaultIfNull(option.getLabel(), "")).append("\">")
                        .append(ObjectUtils.defaultIfNull(option.getContent(), ""))
                        .append("</li>");
            }
            sb.append("</ol>");
            return sb.toString();
        } catch (Exception ex) {
            // do nothing
        }
        List<String> options2;
        try {
            options2 = JSON.parseArray(optionsJson, String.class);
            options2.forEach(item -> sb.append("<li>").append(ObjectUtils.defaultIfNull(item, "")).append("</li>"));
            sb.append("</ol>");
            return sb.toString();
        } catch (Exception ex) {
            // do nothing
        }
        return optionsJson;
    }

    public static String convertOptionsHtmlToJson(String optionsHtml) {
        if (StringUtils.isBlank(optionsHtml)) {
            return optionsHtml;
        }
        if (!optionsHtml.startsWith("<ol") || !optionsHtml.endsWith("</ol>") || !optionsHtml.contains(OptionOrderedListClass)) {
            return optionsHtml;
        }
        List<SmallQuestionOptionVo> options = new ArrayList<>();
        // 解析 HTML 字符串
        Document document = Jsoup.parse(optionsHtml);
        Elements liElements = document.select("ol." + OptionOrderedListClass + " > li");
        // 遍历每个 li 元素
        for (Element li : liElements) {
            String label = li.attr("data-label");
            String content = li.html();
            options.add(new SmallQuestionOptionVo(label, content));
        }
        return JSON.toJSONString(options);
    }

    private static String convertAnswerJsonToHtml(String arrJson) {
        if (StringUtils.isBlank(arrJson)) {
            return null;
        }
        if ("[]".equals(arrJson)) {
            return arrJson;
        }
        try {
            List<String> arr = JSON.parseArray(arrJson, String.class);
            StringBuilder sb = new StringBuilder();
            sb.append("<ol class=\"" + AnswerOrderedListClass + "\">");
            arr.forEach(item -> sb.append("<li>").append(ObjectUtils.defaultIfNull(item, "")).append("</li>"));
            sb.append("</ol>");
            return sb.toString();
        } catch (Exception ex) {
            return arrJson;
        }
    }

    public static String convertAnswerHtmlToJson(String answerHtml) {
        if (StringUtils.isBlank(answerHtml)) {
            return answerHtml;
        }
        if (!answerHtml.startsWith("<ol") || !answerHtml.endsWith("</ol>") || !answerHtml.contains(AnswerOrderedListClass)) {
            return answerHtml;
        }
        List<String> arr = new ArrayList<>();
        // 解析 HTML 字符串
        Document document = Jsoup.parse(answerHtml);
        Elements liElements = document.select("ol." + AnswerOrderedListClass + " > li");
        // 遍历每个 li 元素
        for (Element li : liElements) {
            arr.add(li.html());
        }
        return JSON.toJSONString(arr);
    }
}
