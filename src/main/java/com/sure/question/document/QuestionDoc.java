package com.sure.question.document;

import com.sure.question.entity.Question;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuestionDoc {
    private Long id;
    private Byte grade_level;
    private Byte subject_id;
    private Integer question_type_id;
    private Byte difficulty;
    private Integer use_count;
    private Integer answer_count;
    private Float score_rate;
    private String content_html;
    private List<SmallQuestionDoc> small_questions = new ArrayList<>();
    private List<Long> knowledge_ids = new ArrayList<>();
    private List<Long> chapter_ids = new ArrayList<>();
    private List<PaperDoc> papers = new ArrayList<>();
    private Float combine_paper_weights;

    public QuestionDoc(Question q) {
        this.setId(Long.parseLong(q.getId()));
        this.setQuestion_type_id(q.getQuestionTypeId());
        this.setDifficulty(q.getDifficult() == null ? null : q.getDifficult().byteValue());
        this.setUse_count(q.getUseCount());
        this.setAnswer_count(q.getAnswerCount());
        this.setScore_rate(q.getScoreRate());
        this.setContent_html(q.getContentHtml());
    }

    public void setGradeLevelSubjectId() {
        if (papers == null || papers.isEmpty()) {
            return;
        }
        grade_level = papers.get(0).getGrade_level();
        subject_id = papers.get(0).getSubject_id();
    }

    public void calcCombinePaperWeights() {
        if (papers == null || papers.isEmpty()) {
            combine_paper_weights = 0f;
            return;
        }
        combine_paper_weights = (float) papers.stream().mapToDouble(PaperDoc::calcPaperWeight).max().orElse(0);
    }
}
