package com.sure.question.document;

import com.sure.question.entity.PaperMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaperDoc {
    private Long id;
    private String paper_name;
    private Byte grade_level;
    private Byte subject_id;
    private Byte grade_id;
    private Byte term;
    private Integer paper_type_id;
    private Short year;
    private Long region_id;

    public PaperDoc(PaperMain p) {
        this.setId(Long.parseLong(p.getId()));
        this.setPaper_name(p.getPaperName());
        this.setGrade_level(p.getGradeLevel() == null ? null : p.getGradeLevel().byteValue());
        this.setSubject_id(p.getSubjectId() == null ? null : p.getSubjectId().byteValue());
        this.setGrade_id(p.getGradeId() == null ? null : p.getGradeId().byteValue());
        this.setTerm(StringUtils.isEmpty(p.getTerm()) ? null : Byte.valueOf(p.getTerm()));
        this.setPaper_type_id(p.getPaperTypeId());
        this.setYear(p.getYear() == null ? null : p.getYear().shortValue());
        this.setRegion_id(p.getRegionId());
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class PaperTypeWeight {
        private int gradeLevel;
        private int paperTypeId;
        private float weight;
    }

    private static List<PaperTypeWeight> paperTypeWeights = Arrays.asList(
            new PaperTypeWeight(1, 21, 1.0F),
            new PaperTypeWeight(1, 22, 0.7F),
            new PaperTypeWeight(1, 23, 0.7F),
            new PaperTypeWeight(1, 3, 0.5F),
            new PaperTypeWeight(1, 2, 0.3F),
            new PaperTypeWeight(2, 41, 1.0F),
            new PaperTypeWeight(2, 42, 0.7F),
            new PaperTypeWeight(2, 43, 0.7F),
            new PaperTypeWeight(2, 3, 0.5F),
            new PaperTypeWeight(2, 2, 0.3F),
            new PaperTypeWeight(4, 61, 1.0F),
            new PaperTypeWeight(4, 62, 0.7F),
            new PaperTypeWeight(4, 63, 0.7F),
            new PaperTypeWeight(4, 64, 0.5F),
            new PaperTypeWeight(4, 3, 0.5F),
            new PaperTypeWeight(4, 2, 0.3F)
    );

    public float calcPaperWeight() {
        float weight = year;
        if (grade_level != null && paper_type_id != null) {
            PaperTypeWeight paperTypeWeight = paperTypeWeights.stream()
                    .filter(x -> x.gradeLevel == grade_level && x.paperTypeId == paper_type_id)
                    .findFirst().orElse(null);
            if (paperTypeWeight != null) {
                weight += paperTypeWeight.weight;
            }
        }
        return weight;
    }
}
