package com.sure.question.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KnowledgeDoc {
    private Integer id;
    private Integer parent_id;
    private Integer grade_level;
    private Integer subject_id;
    private String name;
    private String path;
    private Integer depth;
    private Integer children_count;
    private Boolean is_leaf;
    private List<Integer> ancestor_ids;
}
