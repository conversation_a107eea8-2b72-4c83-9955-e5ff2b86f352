package com.sure.question.controller;

import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.common.exception.ApiException;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.KnowledgeMainService;
import com.sure.question.service.PermissionService;
import com.sure.question.util.TokenUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("knowledge")
@Api(tags = "知识点查询")
@Validated
public class KnowledgeMainController {
    private final KnowledgeMainService knowledgeMainService;
    @Value("#{'${allowImportKnowledgeChapterUserIds:}'.split(',')}")
    private List<String> allowImportKnowledgeChapterUserIds;
    private final PermissionService permissionService;

    @GetMapping("byLidAndSid")
    @ApiOperation("查学段学科知识点")
    public Result findByLidAndSid(@NotNull Integer lid, @NotNull Integer sid) {
        return Result.ok(knowledgeMainService.getGradeLevelSubjectKnowledgeTreeWithCache(lid, sid));
    }

    @ApiOperation("清除指定学段学科知识点缓存")
    @DeleteMapping("deleteCache")
    @WithRoles({RoleEnum.SysManager})
    public Result deleteCache(@RequestParam @NotNull Integer gradeLevel, @RequestParam @NotNull Integer subjectId) {
        knowledgeMainService.deleteCache(gradeLevel, subjectId);
        return Result.ok();
    }

    @GetMapping("export")
    @ApiOperation("导出系统知识点")
    @AutoLog("导出系统知识点")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public void exportExcel(@NotNull Integer gradeLevel, @NotNull Integer subjectId,
                            HttpServletRequest req, HttpServletResponse res) throws IOException {
        permissionService.checkEditorPermission(TokenUserUtil.getUserId(req), gradeLevel, subjectId);
        knowledgeMainService.exportExcel(gradeLevel, subjectId, res.getOutputStream());
    }

    @PostMapping("importCheck")
    @ApiOperation("导入更新知识点检查")
    @AutoLog("导入更新知识点检查")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public void importExcelCheck(@NotNull Integer gradeLevel, @NotNull Integer subjectId, @RequestBody MultipartFile file,
                                 HttpServletRequest req, HttpServletResponse res) throws IOException {
        String userId = TokenUserUtil.getUserId(req);
        permissionService.checkEditorPermission(userId, gradeLevel, subjectId);
        knowledgeMainService.importExcel(gradeLevel, subjectId, file.getInputStream(), res.getOutputStream(), false, userId);
    }

    @PostMapping("import")
    @ApiOperation("导入更新知识点")
    @AutoLog("导入更新知识点")
    @WithRoles({RoleEnum.SysManager})
    public void importExcel(@NotNull Integer gradeLevel, @NotNull Integer subjectId, @RequestBody MultipartFile file,
                            HttpServletRequest req, HttpServletResponse res) throws IOException {
        String userId = TokenUserUtil.getUserId(req);
        if (!isImportAllowedUser(userId)) {
            throw new ApiException("无权限");
        }
        knowledgeMainService.importExcel(gradeLevel, subjectId, file.getInputStream(), res.getOutputStream(), true, userId);
    }

    @GetMapping("allowImport")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result allowImport(HttpServletRequest req) {
        String userId = TokenUserUtil.getUserId(req);
        return Result.ok(isImportAllowedUser(userId));
    }

    private boolean isImportAllowedUser(String userId) {
        return allowImportKnowledgeChapterUserIds != null && allowImportKnowledgeChapterUserIds.contains(userId);
    }
}
