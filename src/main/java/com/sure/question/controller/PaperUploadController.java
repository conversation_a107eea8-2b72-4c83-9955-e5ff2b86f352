package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.PaperUploadService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.paper.FindPaperPageParam;
import com.sure.question.vo.paper.upload.UploadPaperParam;
import com.sure.question.vo.question.PaperStructureQuestionIdsParam;
import com.sure.question.vo.question.PaperStructureQuestionVosParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@RestController
@RequestMapping("/paperUpload")
@Api(tags = "试卷上传与划题")
@Validated
@RequiredArgsConstructor
public class PaperUploadController {
    private final PaperUploadService paperUploadService;

    @ApiOperation("获取用户上传记录")
    @GetMapping("records")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stage", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subject", value = "学科id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flagCoachBookId", value = "教辅id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "paperGroupId", value = "试卷组id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页条数", dataType = "int", paramType = "query")})
    public Result selfUpload(@Valid FindPaperPageParam param, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        return Result.ok(paperUploadService.getSelfUpload(userId, param));
    }

    @ApiOperation("上传试卷Word")
    @PostMapping("/upload")
    public Result uploadWord(@RequestParam("file") MultipartFile file, @Valid UploadPaperParam param, HttpServletRequest request) {
        return Result.ok(paperUploadService.uploadPaper(file, param, false, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("上传试卷PDF（目前仅开放给系统编辑员）")
    @PostMapping("/uploadPdf")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result uploadPdf(@RequestParam("file") MultipartFile file, @Valid UploadPaperParam param, HttpServletRequest request) {
        return Result.ok(paperUploadService.uploadPaper(file, param, true, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("划题获取试卷内容")
    @GetMapping("dividePaperContent")
    public Result getDividePaperContent(@RequestParam @NotNull String paperId, @RequestParam @NotNull Boolean addRawHtml, HttpServletRequest request) {
        return Result.ok(paperUploadService.getDividePaperContent(paperId, addRawHtml, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("划题添加题目")
    @PostMapping("dividePaperQuestions")
    public Result addDividePaperQuestions(@RequestParam @NotNull String paperId, @RequestBody @Valid PaperStructureQuestionVosParam param,
                                          HttpServletRequest request) {
        return Result.ok(paperUploadService.addDividePaperQuestions(paperId, param.getPaperStructure(), param.getQuestions(), TokenUserUtil.getUserId(request)));
    }

    @PutMapping("dividePaperQuestions")
    @ApiOperation("划题修改题目")
    public Result changeDividePaperQuestions(@RequestParam @NotNull String paperId, @RequestBody @Valid PaperStructureQuestionVosParam param,
                                             HttpServletRequest request) {
        return Result.ok(paperUploadService.changeDividePaperQuestions(paperId, param.getPaperStructure(), param.getQuestions(), TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("划题删除题目")
    @DeleteMapping("dividePaperQuestions")
    public Result deleteDividePaperQuestions(@RequestParam @NotNull String paperId, @RequestBody @Valid PaperStructureQuestionIdsParam param,
                                             HttpServletRequest request) {
        paperUploadService.deleteDividePaperQuestions(paperId, param.getPaperStructure(), param.getQuestionIds(), TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @ApiOperation("划题修改试卷结构")
    @PutMapping("dividePaperStructure")
    public Result changeDividePaperStructure(@RequestParam @NotBlank String paperId, @RequestPart @NotBlank String paperStructure, HttpServletRequest request) {
        paperUploadService.changeDividePaperStructure(paperId, paperStructure, TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @ApiOperation("完成划题")
    @PutMapping("finishDivide")
    public Result finishDivide(@RequestParam @NotNull String paperId, Boolean allowNoScore, HttpServletRequest request) {
        paperUploadService.finishDivide(paperId, Boolean.TRUE.equals(allowNoScore), TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("重启划题")
    @PutMapping("restartDivide")
    public Result restartDivide(@RequestParam @NotNull String paperId, HttpServletRequest request) {
        paperUploadService.restartDivide(paperId, TokenUserUtil.getUserId(request));
        return Result.ok();
    }
}
