package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.annotation.CheckSign;
import com.sure.question.service.PaperMakeService;
import com.sure.question.service.PaperService;
import com.sure.question.service.PermissionService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paper.FindPaperPageParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@RestController
@RequestMapping("/paperMake")
@Api(tags = "组卷")
@Validated
@RequiredArgsConstructor
public class PaperMakeController {
    private final PaperMakeService paperMakeService;
    private final PaperService paperService;
    private final PermissionService permissionService;

    @ApiOperation("获取用户组卷记录")
    @GetMapping("records")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stage", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subject", value = "学科id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "bookType", value = "类型：all normal coach", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "flagCoachBookId", value = "教辅id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "paperGroupId", value = "试卷组id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页条数", dataType = "int", paramType = "query")})
    public Result selfMake(@Valid FindPaperPageParam param, HttpServletRequest request) {
        return Result.ok(paperMakeService.getSelfMake(param, TokenUserUtil.getUserId(request)));
    }

    // TODO 待删除
    @Deprecated
    @ApiOperation("获取组卷试卷内容")
    @GetMapping("/content")
    public Result getMakePaperContent(@NotBlank String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkViewPaperContentPermission(paperId, userVo);
        return Result.ok(paperService.getPaperContent(paperId));
    }

    // TODO 待删除
    @Deprecated
    @ApiOperation("获取组卷试卷内容（已渲染公式）")
    @GetMapping("/contentRendered")
    public Result getMakePaperContentRendered(@NotBlank String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkViewPaperContentPermission(paperId, userVo);
        return Result.ok(paperService.getPaperContentRendered(paperId));
    }

    @ApiOperation("再次编辑已组试卷")
    @PutMapping("/edit")
    public Result edit(@NotBlank String paperId, HttpServletRequest request) {
        paperMakeService.editMakePaper(paperId, TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @ApiOperation("获取用户组卷数量（内部调用）")
    @GetMapping("internal/paperNum")
    @CheckSign
    public Result getPaperNum(String userId) {
        return Result.ok(paperMakeService.getUserMakePaperCount(userId));
    }
}
