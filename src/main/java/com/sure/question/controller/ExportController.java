package com.sure.question.controller;

import com.sure.common.exception.ApiException;
import com.sure.question.util.WordConvertUtil;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.Arrays;

@RequiredArgsConstructor
@RestController
@RequestMapping("/export")
@Api(tags = "导出")
@Validated
public class ExportController {

    @PostMapping("/html2word")
    public void html2word(@NotEmpty String size, @NotEmpty String html,
                          HttpServletResponse response) throws IOException {
        if (!Arrays.asList("A4", "K8", "A3").contains(size)) {
            throw new ApiException("word文档纸张大小错误");
        }
        WordConvertUtil.html2word(html, size, response.getOutputStream());
    }
}
