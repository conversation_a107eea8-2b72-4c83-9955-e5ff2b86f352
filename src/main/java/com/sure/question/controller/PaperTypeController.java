package com.sure.question.controller;

import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.PaperTypeService;
import com.sure.question.util.TokenUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@RestController
@RequestMapping("/paperType")
@Api(tags = "试卷类型")
@Validated
@RequiredArgsConstructor
public class PaperTypeController {
    private final PaperTypeService paperTypeService;

    @GetMapping("list")
    @ApiOperation("查试卷类型")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result listPaperTypes(Integer gradeLevel) {
        return Result.ok(paperTypeService.listPaperTypes(gradeLevel));
    }

    @PostMapping("add")
    @ApiOperation("添加试卷类型")
    @AutoLog("添加试卷类型")
    @WithRoles({RoleEnum.SysManager})
    public Result addPaperType(@NotNull Integer gradeLevel, @NotEmpty String paperTypeName, HttpServletRequest request) {
        return Result.ok(paperTypeService.addPaperType(gradeLevel, paperTypeName, TokenUserUtil.getUserId(request)));
    }

    @PutMapping("update")
    @ApiOperation("更新试卷类型")
    @WithRoles({RoleEnum.SysManager})
    @AutoLog("修改试卷类型")
    public Result updatePaperType(@NotNull Integer gradeLevel, @NotNull Integer paperTypeId, @NotEmpty String paperTypeName,
                                  @NotNull Integer orderId, HttpServletRequest request) {
        paperTypeService.updatePaperType(gradeLevel, paperTypeId, paperTypeName, orderId, TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @DeleteMapping("delete")
    @ApiOperation("删除试卷类型")
    @AutoLog("删除试卷类型")
    @WithRoles({RoleEnum.SysManager})
    public Result deletePaperTypeById(@NotNull Integer gradeLevel, @NotNull Integer paperTypeId, HttpServletRequest request) {
        paperTypeService.deletePaperType(gradeLevel, paperTypeId, TokenUserUtil.getUserId(request));
        return Result.ok();
    }
}
