package com.sure.question.controller;

import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.common.exception.ApiException;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.ChapterService;
import com.sure.question.service.PermissionService;
import com.sure.question.util.TokenUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/chapter")
@Api(tags = "章节")
@Validated
public class ChapterController {
    private final ChapterService chapterService;
    private final PermissionService permissionService;

    @Value("#{'${allowImportKnowledgeChapterUserIds:}'.split(',')}")
    private List<String> allowImportKnowledgeChapterUserIds;

    @GetMapping("byBookId")
    @ApiOperation("按教材查询章节")
    public Result findChapterByBid(@NotNull Integer bid) {
        return Result.ok(chapterService.getBookChapterTreeWithCache(bid));
    }

    @DeleteMapping("deleteCache")
    @ApiOperation("清除章节缓存")
    @WithRoles({RoleEnum.SysManager})
    public Result deleteCache(Integer gradeLevel, Integer subjectId) {
        chapterService.deleteCache(gradeLevel, subjectId);
        return Result.ok();
    }

    @GetMapping("export")
    @ApiOperation("导出系统章节")
    @AutoLog("导出系统章节")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public void exportExcel(@NotNull Integer gradeLevel,
                            @NotNull Integer subjectId,
                            @RequestParam(required = false) Integer categoryId,
                            @RequestParam(required = false) Integer bookId,
                            HttpServletRequest req, HttpServletResponse res) throws IOException {
        permissionService.checkEditorPermission(TokenUserUtil.getUserId(req), gradeLevel, subjectId);
        chapterService.exportExcel(gradeLevel, subjectId, categoryId, bookId, res.getOutputStream());
    }

    @PostMapping("importCheck")
    @ApiOperation("导入更新章节检查")
    @AutoLog("导入更新章节检查")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public void importExcelCheck(@NotNull Integer gradeLevel, @NotNull Integer subjectId, @RequestBody MultipartFile file,
                                 HttpServletRequest req, HttpServletResponse res) throws IOException {
        String userId = TokenUserUtil.getUserId(req);
        permissionService.checkEditorPermission(userId, gradeLevel, subjectId);
        chapterService.importExcel(gradeLevel, subjectId, file.getInputStream(), res.getOutputStream(), false, userId);
    }

    @PostMapping("import")
    @ApiOperation("导入更新章节")
    @AutoLog("导入更新章节")
    @WithRoles({RoleEnum.SysManager})
    public void importExcel(@NotNull Integer gradeLevel, @NotNull Integer subjectId, @RequestBody MultipartFile file,
                            HttpServletRequest req, HttpServletResponse res) throws IOException {
        String userId = TokenUserUtil.getUserId(req);
        if (!isImportAllowedUser(userId)) {
            throw new ApiException("无权限");
        }
        chapterService.importExcel(gradeLevel, subjectId, file.getInputStream(), res.getOutputStream(), true, userId);
    }

    @PostMapping("importCopyChapterQuestionCheck")
    @ApiOperation("导入复制章节题目检查")
    @AutoLog("导入复制章节题目检查")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result importCopyChapterQuestionCheck(@NotNull Integer gradeLevel, @NotNull Integer subjectId, @RequestBody MultipartFile file,
                                                 HttpServletRequest req) throws IOException {
        String userId = TokenUserUtil.getUserId(req);
        permissionService.checkEditorPermission(userId, gradeLevel, subjectId);
        return Result.ok(chapterService.importCopyChapterQuestionExcel(gradeLevel, subjectId, file.getInputStream(),false, userId));
    }

    @PostMapping("importCopyChapterQuestion")
    @ApiOperation("导入复制章节题目")
    @AutoLog("导入复制章节题目")
    @WithRoles({RoleEnum.SysManager})
    public Result importCopyChapterQuestion(@NotNull Integer gradeLevel, @NotNull Integer subjectId, @RequestBody MultipartFile file,
                                            HttpServletRequest req) throws IOException {
        String userId = TokenUserUtil.getUserId(req);
        if (!isImportAllowedUser(userId)) {
            throw new ApiException("无权限");
        }
        return Result.ok(chapterService.importCopyChapterQuestionExcel(gradeLevel, subjectId, file.getInputStream(),true, userId));
    }

    @GetMapping("allowImport")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result allowImport(HttpServletRequest req) {
        String userId = TokenUserUtil.getUserId(req);
        return Result.ok(isImportAllowedUser(userId));
    }

    private boolean isImportAllowedUser(String userId) {
        return allowImportKnowledgeChapterUserIds != null && allowImportKnowledgeChapterUserIds.contains(userId);
    }
}
