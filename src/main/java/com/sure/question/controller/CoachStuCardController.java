package com.sure.question.controller;

import com.github.pagehelper.PageInfo;
import com.sure.common.entity.Result;
import com.sure.question.annotation.CheckSign;
import com.sure.question.annotation.WithRoles;
import com.sure.question.entity.CoachStuCard;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.CoachStuCardService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachstucard.*;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("coachStuCard")
@Api(tags = "教辅激活卡")
@Validated
@RequiredArgsConstructor
public class CoachStuCardController {
    private final CoachStuCardService coachStuCardService;

    @PostMapping("listCardByNos")
    @ApiOperation("获取教辅激活卡列表")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CoachStuCard.class, responseContainer = "List")})
    @CheckSign
    public Result listCardByNos(@RequestBody List<String> cardNos) {
        return Result.ok(coachStuCardService.listCardByNos(cardNos));
    }

    @PostMapping("activeCardBatch")
    @ApiOperation("家长为小孩激活教辅激活卡")
    @CheckSign
    public Result activeCardBatch(@RequestParam String parentId, @RequestParam String studentId, @RequestBody List<String> cardNos) {
        coachStuCardService.activeCardBatch(parentId, studentId, cardNos);
        return Result.ok();
    }

    @PostMapping("addCustomCardBatch")
    @ApiOperation("内定学校家长激活教辅")
    @CheckSign
    public Result addCustomCardBatch(@RequestBody List<CustomCardVo> customCardVos) {
        coachStuCardService.addCustomCardBatch(customCardVos);
        return Result.ok();
    }

    @GetMapping("listCoachBook")
    @ApiOperation("获取小孩已激活的教辅列表")
    @CheckSign
    public Result listCoachBook(@RequestParam String schoolId, @RequestParam String studentId,
                                @RequestParam(required = false) Integer semesterId, @RequestParam(required = false) Integer term) {
        return Result.ok(coachStuCardService.listCoachBook(schoolId, studentId, semesterId, term));
    }

    @GetMapping("cardCoachBookInfos")
    @ApiOperation("获取教辅激活卡对应的教辅信息")
    public Result cardCoachBookInfos(@RequestParam @NotBlank String cardNos, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        List<CardCoachBookInfoVo> voList = coachStuCardService.cardCoachBookInfos(cardNos, userId);
        return Result.ok(voList);
    }

    @ApiOperation("查询指定学生的激活状态")
    @GetMapping("getActiveStatus")
    public Result getActiveStatus(@RequestParam String studentId) {
        ActiveVo vo = coachStuCardService.getActiveStatus(studentId);
        return Result.ok(vo);
    }

    @CheckSign
    @ApiOperation("查询指定学年学期激活指定科目教辅的学生ID列表")
    @PostMapping("listActiveStudentIds")
    public Result listActiveStudentIds(String schoolId, Integer semesterId, Integer term,
                                       Integer subjectId, @RequestBody List<String> studentIds) {
        List<StudentSubjectVo> list = coachStuCardService.listActiveStudentIds(schoolId, semesterId, term, subjectId, studentIds);
        return Result.ok(list);
    }

    @CheckSign
    @ApiOperation("查询指定学年学期激活指定科目教辅的学生ID列表(只返回学生ID)")
    @PutMapping("activeStudentIdList")
    public Result activeStudentIdList(@RequestParam String schoolId, @RequestParam Integer semesterId, @RequestParam Integer term,
                                      @RequestParam Integer subjectId, @RequestBody List<String> studentIds) {
        List<StudentSubjectVo> list = coachStuCardService.listActiveStudentIds(schoolId, semesterId, term, subjectId, studentIds);
        Set<String> studentIdSet = list.stream().map(StudentSubjectVo::getStudentId).collect(Collectors.toSet());
        return Result.ok(studentIdSet);
    }

    @ApiOperation("超管查询激活卡列表")
    @GetMapping("listCard")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "coachBookId", value = "教辅ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "教辅名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevel", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "cardNo", value = "卡号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "studentName", value = "学生姓名", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "schoolId", value = "学校ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "regionId", value = "区域ID", dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态：0-初始，未激活；1-已激活；2-禁用", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", dataType = "int", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CardInfoVo.class, responseContainer = "List")})
    @WithRoles({RoleEnum.SuperManager})
    public Result listCard(Integer coachBookId, String coachBookName, Integer subjectId, Integer gradeLevel, Integer gradeId, Integer semesterId,
                           Integer term, String cardNo, String studentName, String schoolId, Long regionId, Integer status, String beginTime, String endTime,
                           Integer pageNum, Integer pageSize) {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        PageInfo<CardInfoVo> pageInfo = coachStuCardService.listCard(coachBookId, coachBookName, subjectId, gradeLevel, gradeId, semesterId,
                term, cardNo, studentName, schoolId, regionId, status, beginTime, endTime, pageNum, pageSize);
        return Result.ok(pageInfo);
    }

    @ApiOperation("机构查询激活卡列表")
    @GetMapping("listCardByOrg")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "coachBookId", value = "教辅ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "教辅名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevel", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "cardNo", value = "卡号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "studentName", value = "学生姓名", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "schoolId", value = "学校ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态：0-初始，未激活；1-已激活；2-禁用", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", dataType = "int", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CardInfoVo.class, responseContainer = "List")})
    public Result listCardByOrg(Integer coachBookId, String coachBookName, Integer subjectId, Integer gradeLevel, Integer gradeId, Integer semesterId,
                                Integer term, String cardNo, String studentName, String schoolId, Integer status, String beginTime, String endTime,
                                Integer pageNum, Integer pageSize, HttpServletRequest request) {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        PageInfo<CardInfoVo> pageInfo = coachStuCardService.listCardByOrg(coachBookId, coachBookName, subjectId, gradeLevel, gradeId, semesterId,
                term, cardNo, studentName, schoolId, status, beginTime, endTime, pageNum, pageSize, userVo);
        return Result.ok(pageInfo);
    }

    @ApiOperation("学校查询激活卡列表")
    @GetMapping("listCardBySchool")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "coachBookId", value = "教辅ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "教辅名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevel", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "cardNo", value = "卡号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "studentName", value = "学生姓名", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "status", value = "状态：0-初始，未激活；1-已激活；2-禁用", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", dataType = "int", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CardInfoVo.class, responseContainer = "List")})
    public Result listCardBySchool(Integer coachBookId, String coachBookName, Integer subjectId, Integer gradeLevel, Integer gradeId, Integer semesterId,
                                   Integer term, String cardNo, String studentName, Integer status, String beginTime, String endTime,
                                   Integer pageNum, Integer pageSize, HttpServletRequest request) {
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        PageInfo<CardInfoVo> pageInfo = coachStuCardService.listCard(coachBookId, coachBookName, subjectId, gradeLevel, gradeId, semesterId,
                term, cardNo, studentName, userVo.getSchoolId(), null, status, beginTime, endTime, pageNum, pageSize);
        return Result.ok(pageInfo);
    }


    @ApiOperation("超管查询教辅激活统计（查全部或一所学校）")
    @GetMapping("activeStat")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "schoolId", value = "学校ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevel", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "搜索关键词：教辅名称或征订代码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "regionId", value = "区域ID", dataType = "Long", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CardInfoVo.class, responseContainer = "List")})
    @WithRoles({RoleEnum.SuperManager})
    public Result activeStat(Integer semesterId, Integer term, Integer subjectId, Integer gradeLevel, Integer gradeId, String coachBookName, String schoolId, Long regionId) {
        List<ActiveStatVo> voList = coachStuCardService.activeStat(semesterId, term, subjectId, gradeLevel, gradeId, coachBookName, schoolId, regionId);
        return Result.ok(voList);
    }

    @ApiOperation("机构查询教辅激活统计（按教辅，查机构下属全部或一所学校）")
    @GetMapping("activeStatByOrg")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "schoolId", value = "学校ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevel", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "搜索关键词：教辅名称或征订代码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CardInfoVo.class, responseContainer = "List")})
    public Result activeStatByOrg(Integer semesterId, Integer term, Integer subjectId, Integer gradeLevel, Integer gradeId,
                                  String coachBookName, String schoolId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        List<ActiveStatVo> voList = coachStuCardService.activeStatByOrg(semesterId, term, subjectId, gradeLevel, gradeId, coachBookName, schoolId, userVo);
        return Result.ok(voList);
    }

    @ApiOperation("学校查询教辅激活统计（按教辅，查本校）")
    @GetMapping("activeStatBySchool")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevel", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "搜索关键词：教辅名称或征订代码", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CardInfoVo.class, responseContainer = "List")})
    public Result activeStatBySchool(Integer semesterId, Integer term, Integer subjectId, Integer gradeLevel, Integer gradeId, String coachBookName, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        List<ActiveStatVo> voList = coachStuCardService.activeStat(semesterId, term, subjectId, gradeLevel, gradeId, coachBookName, userVo.getSchoolId(), null);
        return Result.ok(voList);
    }


    @ApiOperation("超管查询教辅学校激活统计（按学校，查全部或一所学校）")
    @GetMapping("schoolStat")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "教辅名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "schoolId", value = "学校ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "regionId", value = "区域ID", dataType = "Long", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CoachSchoolStatVo.class, responseContainer = "List")})
    @WithRoles({RoleEnum.SuperManager})
    public Result schoolStat(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, String schoolId, Long regionId) {
        List<CoachSchoolStatVo> voList = coachStuCardService.schoolStat(semesterId, term, gradeId, subjectId, coachBookName, schoolId, regionId);
        return Result.ok(voList);
    }

    @ApiOperation("机构查询教辅学校激活统计（查机构下属全部或一所学校）")
    @GetMapping("schoolStatByOrg")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "教辅名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "schoolId", value = "学校ID", dataType = "String", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CoachSchoolStatVo.class, responseContainer = "List")})
    @WithRoles({RoleEnum.SchoolManager})
    public Result schoolStatByOrg(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, String schoolId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        List<CoachSchoolStatVo> voList = coachStuCardService.schoolStatByOrg(semesterId, term, gradeId, subjectId, coachBookName, schoolId, userVo);
        return Result.ok(voList);
    }

    @ApiOperation("学校查询教辅学校激活统计（查本校）")
    @GetMapping("schoolStatBySchool")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "教辅名称", dataType = "String", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CoachSchoolStatVo.class, responseContainer = "List")})
    @WithRoles({RoleEnum.SchoolManager})
    public Result schoolStatBySchool(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        List<CoachSchoolStatVo> voList = coachStuCardService.schoolStat(semesterId, term, gradeId, subjectId, coachBookName, userVo.getSchoolId(), null);
        return Result.ok(voList);
    }


    @ApiOperation("超管查询学情分析资源包激活数量统计")
    @GetMapping("countStat")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "教辅名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "schoolId", value = "学校ID", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "regionId", value = "区域ID", dataType = "Long", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CountStatVo.class)})
    @WithRoles({RoleEnum.SuperManager})
    public Result countStat(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, String schoolId, Long regionId) {
        return Result.ok(coachStuCardService.countStat(semesterId, term, gradeId, subjectId, coachBookName, schoolId, regionId));
    }

    @ApiOperation("机构查询学情分析资源包激活数量统计（查机构下属全部或一所学校）")
    @GetMapping("countStatByOrg")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "教辅名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "schoolId", value = "学校ID", dataType = "String", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CountStatVo.class)})
    @WithRoles({RoleEnum.SchoolManager})
    public Result countStatByOrg(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, String schoolId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(coachStuCardService.countStatByOrg(semesterId, term, gradeId, subjectId, coachBookName, schoolId, userVo));
    }

    @ApiOperation("机构查询学情分析资源包激活数量统计（查本校）")
    @GetMapping("countStatBySchool")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "semesterId", value = "学年ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期：1-第一学期；2-第二学期", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "coachBookName", value = "教辅名称", dataType = "String", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CountStatVo.class)})
    @WithRoles({RoleEnum.SchoolManager})
    public Result countStatBySchool(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(coachStuCardService.countStat(semesterId, term, gradeId, subjectId, coachBookName, userVo.getSchoolId(), null));
    }
}
