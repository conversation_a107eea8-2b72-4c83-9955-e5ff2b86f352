package com.sure.question.controller;

import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.question.service.FrontPageService;
import com.sure.question.service.PermissionService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.frontPage.FrontPageImageVo;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("frontPage")
@Api(tags = "首页")
@Validated
@RequiredArgsConstructor
public class FrontPageController {
    private final FrontPageService frontPageService;
    private final PermissionService permissionService;

    @PostMapping("uploadImage")
    Result uploadImage(@RequestParam @NotNull Integer gradeLevel, @RequestParam @NotNull Integer subjectId,
                       @RequestBody @NotNull MultipartFile file, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkEditorPermission(userInfo, gradeLevel, subjectId);
        return Result.ok(frontPageService.uploadFrontPageImage(gradeLevel, subjectId, file));
    }

    @PutMapping("saveImages")
    @AutoLog("设置题库首页图片")
    Result addImage(@RequestParam @NotNull Integer gradeLevel, @RequestParam @NotNull Integer subjectId,
                    @RequestBody(required = false) List<FrontPageImageVo> images, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkEditorPermission(userInfo, gradeLevel, subjectId);
        frontPageService.saveFrontPageImage(gradeLevel, subjectId, images, userInfo.getUserId());
        return Result.ok();
    }

    @GetMapping("imageList")
    Result getImages(@RequestParam @NotNull Integer gradeLevel, @RequestParam @NotNull Integer subjectId) {
        return Result.ok(frontPageService.getFrontPageImagesWithCache(gradeLevel, subjectId));
    }

    @PutMapping("savePapers")
    @AutoLog("设置题库首页试卷")
    Result savePapers(@RequestParam @NotNull Integer gradeLevel, @RequestParam @NotNull Integer subjectId,
                      @RequestParam @NotEmpty String collectionName, @RequestParam @NotNull Integer collectionSortCode,
                      @RequestBody @NotEmpty List<String> paperIds, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkEditorPermission(userInfo, gradeLevel, subjectId);
        frontPageService.saveFrontPagePapers(gradeLevel, subjectId, collectionName, collectionSortCode, paperIds, userInfo.getUserId());
        return Result.ok();
    }

    @DeleteMapping("deletePaperCollection")
    @AutoLog("删除题库首页试卷合集")
    Result deletePaperCollection(@RequestParam @NotNull Integer gradeLevel, @RequestParam @NotNull Integer subjectId,
                                 @RequestParam @NotNull String collectionName, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkEditorPermission(userInfo, gradeLevel, subjectId);
        frontPageService.deleteFrontPagePaperCollection(gradeLevel, subjectId, collectionName);
        return Result.ok();
    }

    @PutMapping("changePaperCollectionOrder")
    @AutoLog("调整题库首页试卷合集顺序")
    Result changePaperCollectionOrder(@RequestParam @NotNull Integer gradeLevel, @RequestParam @NotNull Integer subjectId,
                                      @RequestBody @NotEmpty List<String> collectionNames, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkEditorPermission(userInfo, gradeLevel, subjectId);
        frontPageService.changePaperCollectionOrder(gradeLevel, subjectId, collectionNames);
        return Result.ok();
    }

    @GetMapping("paperList")
    Result getPapers(@RequestParam @NotNull Integer gradeLevel, @RequestParam @NotNull Integer subjectId, @RequestParam(required = false) String collectionName) {
        return Result.ok(frontPageService.getFrontPagePapersWithCache(gradeLevel, subjectId, collectionName));
    }
}
