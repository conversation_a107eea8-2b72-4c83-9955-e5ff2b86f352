package com.sure.question.controller;

import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.question.annotation.WithRoles;
import com.sure.question.entity.SchoolCoachBook;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.CoachBookService;
import com.sure.question.service.SchoolCoachService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.BookSchoolVo;
import com.sure.question.vo.coachBook.PaperVo;
import com.sure.question.vo.paperVo.SchoolCoachPaperVo;
import com.sure.question.vo.question.UserQuestionVo;
import com.sure.question.vo.schoolCoachBook.*;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Set;

/**
 * 学校定制教辅
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("schoolCoach")
@Api(tags = "学校教辅")
@Validated
@RequiredArgsConstructor
public class SchoolCoachController {
    private final SchoolCoachService schoolCoachBookService;
    private final CoachBookService coachBookService;

    @ApiOperation("分页查询当前学校的定制教辅")
    @GetMapping("getBookPage")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = BookInfo.class, responseContainer = "List")})
    public Result getBookPage(@Validated QueryBookVo queryVo,
                              @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer page,
                              @RequestParam(required = false, defaultValue = "10") @ApiParam("页大小") Integer size,
                              HttpServletRequest request) {
        queryVo.setSchoolId(TokenUserUtil.getSchoolId(request));
        return Result.ok(schoolCoachBookService.getBookPage(queryVo, page, size));
    }

    @ApiOperation("分页查询当前学校的定制试卷")
    @GetMapping("getPaperPage")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = SchoolCoachPaperVo.class, responseContainer = "List")})
    public Result getPaperPage(QueryPaperVo queryVo,
                               @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer page,
                               @RequestParam(required = false, defaultValue = "10") @ApiParam("页大小") Integer size,
                               HttpServletRequest request) {
        return Result.ok(schoolCoachBookService.getPapersPage(queryVo, page, size, TokenUserUtil.getSchoolId(request), TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("分页查询定制试题（按知识点多条件）")
    @GetMapping("getQuesPageByKnows")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = UserQuestionVo.class, responseContainer = "List")})
    public Result getQuesPageByKnows(@Valid QueryQuestionVo queryVo, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(schoolCoachBookService.findQuestionByChapterOrKnowledge(userVo, queryVo));
    }

    @ApiOperation("分页查询定制试题（按章节多条件）")
    @GetMapping("getQuesPageByChapter")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = UserQuestionVo.class, responseContainer = "List")})
    public Result getQuesPageByChapter(@Valid QueryQuestionVo queryVo, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(schoolCoachBookService.findQuestionByChapterOrKnowledge(userVo, queryVo));
    }

    @ApiOperation("查询学校定制的特定试卷信息（内部调用）")
    @GetMapping("getSimplePaper")
    public SchoolCoachPaperVo getSimplePaper(@RequestParam String schoolId, @RequestParam Integer semesterId, @RequestParam String paperId) {
        return schoolCoachBookService.getSimplePaper(schoolId, semesterId, paperId);
    }

    @ApiOperation("分页查询指定教辅定制的学校")
    @GetMapping("getSchoolPage")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = BookSchoolVo.class, responseContainer = "List")})
    public Result getSchoolPage(@RequestParam @ApiParam("教辅 id") Integer coachBookId,
                                @RequestParam(required = false) @ApiParam("学校名称模糊搜索") String keyword,
                                @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer page,
                                @RequestParam(required = false, defaultValue = "10") @ApiParam("页大小") Integer size) {
        return Result.ok(schoolCoachBookService.getSchoolPage(coachBookId, keyword, page, size));
    }

    @ApiOperation("添加一本教辅到指定的学校学年（学期、年级与教辅相同）")
    @PostMapping("addBookToSchools")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    @AutoLog("添加一本教辅到指定的学校学年")
    public Result addBookToSchools(@RequestParam @ApiParam("教辅 id") Integer coachBookId,
                                   @RequestParam @ApiParam("学年 id") Integer semesterId,
                                   @RequestBody @ApiParam("要定制学校 ids") @NotNull @Size(min = 1) Set<String> schoolIds,
                                   HttpServletRequest request) {
        String msg = schoolCoachBookService.addBookToSchools(coachBookId, semesterId, schoolIds, TokenUserUtil.getUserId(request));
        return Result.ok(null, msg);
    }

    @ApiOperation("批量删除定制教辅的学校（按教辅）")
    @DeleteMapping("delSchoolByBook")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    @AutoLog("批量删除定制教辅的学校")
    public Result delSchoolCoach(@RequestParam @ApiParam("教辅 id") Integer coachBookId,
                                 @RequestBody @ApiParam("记录ids") List<Integer> ids) {
        schoolCoachBookService.delSchoolByBook(coachBookId, ids);
        return Result.ok();
    }

    @ApiOperation("修改学校一本教辅信息")
    @PutMapping("editSchoolBook")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    @AutoLog("修改学校一本教辅信息")
    public Result editSchoolBook(@RequestBody SchoolCoachBook vo, HttpServletRequest request) {
        schoolCoachBookService.editSchoolBook(vo, TokenUserUtil.getUserId(request));
        return Result.ok();
    }


    @ApiOperation("添加试卷到校本教辅")
    @PostMapping("savePaperToSchoolCoach")
    public Result savePaperToSchoolCoach(
            @RequestParam @ApiParam("试卷 id") String paperId,
            @RequestParam @ApiParam("年级 id") Integer gradeId,
            @RequestParam @ApiParam("学年 id") Integer semesterId,
            @RequestParam @ApiParam("学期 id") Integer term,
            @RequestParam @ApiParam("学校 id") String schoolId,
            @RequestParam @ApiParam("操作者 id") String userId) {
        schoolCoachBookService.savePaperToSchoolCoach(paperId, gradeId, semesterId, term, schoolId, userId);
        return Result.ok();
    }

    @GetMapping("listBookPapers")
    @ApiOperation("查询学校下指定教辅的试卷列表")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = PaperVo.class, responseContainer = "List")})
    public Result getSchoolBookPapers(@RequestParam Integer bookId, @RequestParam String schoolId, String studentId, Boolean isAsc, HttpServletRequest request) {
        if (isAsc == null) {
            isAsc = true;
        }
        return Result.ok(coachBookService.getSchoolBookPapers(bookId, schoolId, studentId, isAsc, TokenUserUtil.getUserId(request)));
    }

    @GetMapping("listMySchoolBookPapers")
    @ApiOperation("查询本校定制教辅包含试卷")
    public Result getMySchoolBookPapers(@NotNull Integer coachBookId, HttpServletRequest request) {
        String schoolId = TokenUserUtil.getSchoolId(request);
        String userId = TokenUserUtil.getUserId(request);
        return Result.ok(coachBookService.getSchoolBookPapers(coachBookId, schoolId, null, true, userId));
    }

    @ApiOperation("查询当前学校开通的全部教辅")
    @GetMapping("listSchoolBooks")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = SchoolCoachVo.class, responseContainer = "List")})
    public Result listSchoolBooks(HttpServletRequest request) {
        String schoolId = TokenUserUtil.getSchoolId(request);
        List<SchoolCoachVo> voList = schoolCoachBookService.listSchoolBooks(schoolId);
        return Result.ok(voList);
    }

    @ApiOperation("学校管理员设置教辅错题标记方式")
    @PostMapping("setWrongQuesMark")
    @AutoLog("学校管理员设置教辅错题标记方式")
    public Result setWrongQuesMark(@RequestBody @Valid List<WrongQuesMarkVo> vos, HttpServletRequest request) {
        schoolCoachBookService.setWrongQuesMark(vos, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("查询上一份试卷ID")
    @GetMapping("getLastPaperId")
    public Result getLastPaperId(String schoolId, Integer semesterId, Integer term, String paperId) {
        String lastPaperId = schoolCoachBookService.getLastPaperId(schoolId, semesterId, term, paperId);
        return Result.ok(lastPaperId);
    }
}
