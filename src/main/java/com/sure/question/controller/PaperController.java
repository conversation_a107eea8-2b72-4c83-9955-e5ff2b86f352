package com.sure.question.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.common.entity.UserInfo;
import com.sure.common.exception.ApiException;
import com.sure.question.annotation.CheckSign;
import com.sure.question.annotation.WithRoles;
import com.sure.question.entity.PaperMain;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.PaperMakeService;
import com.sure.question.service.PaperService;
import com.sure.question.service.PermissionService;
import com.sure.question.service.QuesLogService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.Basket;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.CoachBookPaperMainVo;
import com.sure.question.vo.paper.FindPaperPageParam;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;

@RestController
@RequestMapping(value = "/paper")
@Api(tags = "试卷接口")
@Validated
@RequiredArgsConstructor
public class PaperController {
    private final PaperService paperService;
    private final QuesLogService quesLogService;
    private final PaperMakeService paperMakeService;
    private final PermissionService permissionService;

    // TODO 待删除
    @Deprecated
    @ApiOperation("获取组卷内容")
    @GetMapping("/make/{id}")
    @CheckSign
    public Result getMakeById(@PathVariable("id") String id) {
        return Result.ok(paperService.getPaperContent(id));
    }

    // TODO 待删除
    @Deprecated
    @GetMapping("/internal/make/{id}")
    @ApiOperation("内部获取组卷内容")
    @CheckSign
    public Result internalGetMakeById(@PathVariable("id") String id) {
        return Result.ok(paperService.getPaperContent(id));
    }

    // TODO 待删除
    @Deprecated
    @GetMapping("/make/rendered")
    @ApiOperation("获取组卷内容（已渲染公式）")
    @CheckSign
    public Result getRenderedPaperById(@RequestParam String paperId) {
        return Result.ok(paperService.getPaperContentRendered(paperId));
    }

    // TODO 待删除
    @Deprecated
    @ApiOperation("获取用户组卷数量")
    @GetMapping("paperNum")
    @CheckSign
    public Result getPaperNum(String userId) {
        return Result.ok(paperMakeService.getUserMakePaperCount(userId));
    }

    // TODO 待删除
    @Deprecated
    @ApiOperation("查试卷名称")
    @GetMapping("getPaperNameByPaperId")
    @CheckSign
    public Result getPaperName(@RequestParam String paperId) {
        return Result.ok(paperService.getPaperNameByPaperId(paperId));
    }

    // TODO 待删除
    @Deprecated
    @ApiOperation("查试卷信息")
    @GetMapping("info/{id}")
    public Result getPaperInfoOld(@PathVariable("id") String id, HttpServletRequest request) {
        permissionService.checkViewPaperInfoPermission(id, TokenUserUtil.getUserInfo(request));
        return Result.ok(paperService.getPaperInfo(id));
    }

    // TODO 待删除
    @Deprecated
    @ApiOperation("增加答题卡或反馈卡下载次数")
    @PutMapping("addSheetDownloadCount")
    @CheckSign
    public Result addSheetDownloadCountOld(String paperId, String type) {
        paperService.addSheetDownloadCount(paperId, type);
        return Result.ok();
    }

    // TODO 待删除
    @Deprecated
    @ApiOperation("获取答题卡或反馈卡下载次数")
    @GetMapping("sheetDownloadCount")
    public Result getSheetDownloadCountOld(String paperId, String type) {
        return Result.ok(paperService.getSheetDownloadCount(paperId, type));
    }

    // TODO 待删除
    @Deprecated
    @ApiOperation("删除试卷（标记删除）")
    @DeleteMapping("/{id}")
    public Result delPaperByIdOld(@PathVariable("id") String id, HttpServletRequest request) {
        paperService.delPaperById(id, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    // TODO 待删除
    @GetMapping("getSchoolCoachPaper")
    @ApiOperation("查询学校定制教辅下指定试卷的信息")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = CoachBookPaperMainVo.class)})
    public Result getSchoolCoachPaper(@RequestParam String id, @RequestParam String schoolId, String studentId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        // TODO 检查家长端请求
        if (StringUtils.isNotEmpty(userVo.getSchoolId())) {
            if (!StringUtils.equals(schoolId, userVo.getSchoolId())) {
                throw new ApiException("无权限");
            }
            permissionService.checkViewPaperContentPermission(id, userVo);
        }
        return Result.ok(paperService.getSchoolCoachPaper(id, schoolId, studentId, userVo.getUserId()));
    }

    @ApiOperation("多条件查询公共库试卷")
    @GetMapping("/byCondition")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stage", value = "学段", dataType = "int"),
            @ApiImplicitParam(name = "subject", value = "学科", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "试卷类型", dataType = "int"),
            @ApiImplicitParam(name = "grade", value = "年级", dataType = "int"),
            @ApiImplicitParam(name = "term", value = "学期", dataType = "int"),
            @ApiImplicitParam(name = "year", value = "年份", dataType = "int"),
            @ApiImplicitParam(name = "region", value = "地区", dataType = "string"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "string"),
            @ApiImplicitParam(name = "orderBy", value = "排序规则", dataType = "string", defaultValue = "year : 按年份排序;browseCount: 按浏览次数排序"),
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "页面大小", dataType = "int")})
    public Result byCondition(@Valid FindPaperPageParam param, HttpServletRequest request) {
        return Result.ok(paperService.findInPublicBank(param, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("多条件分页查询收藏试卷")
    @GetMapping("fav")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "页面大小", dataType = "int"),
            @ApiImplicitParam(name = "subject", value = "学科id", dataType = "int"),
            @ApiImplicitParam(name = "stage", value = "学段", dataType = "int"),
            @ApiImplicitParam(name = "grade", value = "年级id", dataType = "int"),
            @ApiImplicitParam(name = "term", value = "学期", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "试卷类型", dataType = "int")})
    public Result getFavorite(@Valid FindPaperPageParam param, HttpServletRequest request) {
        return Result.ok(paperService.getFavorite(param, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("改变试卷收藏状态")
    @PutMapping("changeFav/{id}")
    public Result changeFav(@PathVariable("id") Long paperId, HttpServletRequest request) {
        TokenUserVo tokenUserVo = TokenUserUtil.getTokenUserVo(request);
        paperService.changeFav(paperId, tokenUserVo.getUserId(), tokenUserVo.getSchoolId());
        return Result.ok();
    }

    @ApiOperation("获取试卷内容")
    @GetMapping("/content")
    public Result getPaperContent(@RequestParam String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkViewPaperContentPermission(paperId, userVo);
        Basket content = paperService.getPaperContent(paperId);
        quesLogService.saveQuesLog(userVo, paperId, request, "view");
        return Result.ok(content);
    }

    @ApiOperation("获取试卷内容（已渲染公式）")
    @GetMapping("/contentRendered")
    public Result getPaperContentRendered(@RequestParam String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkViewPaperContentPermission(paperId, userVo);
        Basket content = paperService.getPaperContentRendered(paperId);
        quesLogService.saveQuesLog(userVo, paperId, request, "view");
        return Result.ok(content);
    }

    @ApiOperation("获取试卷内容（内部调用）")
    @GetMapping("/internal/content")
    @CheckSign
    public Result getPaperContent(@RequestParam String paperId) {
        return Result.ok(paperService.getPaperContent(paperId));
    }

    @ApiOperation("获取试卷内容（已渲染公式）（内部调用）")
    @GetMapping("/internal/contentRendered")
    @CheckSign
    public Result getPaperContentRendered(@RequestParam String paperId) {
        return Result.ok(paperService.getPaperContentRendered(paperId));
    }

    @ApiOperation("查试卷名称（内部调用）")
    @GetMapping("internal/getPaperNameByPaperId")
    @CheckSign
    public Result getPaperNameByPaperId(@RequestParam String paperId) {
        return Result.ok(paperService.getPaperNameByPaperId(paperId));
    }

    @ApiOperation("查试卷信息")
    @GetMapping("info")
    public Result getPaperInfo(@RequestParam @NotBlank String paperId, HttpServletRequest request) {
        permissionService.checkViewPaperInfoPermission(paperId, TokenUserUtil.getUserInfo(request));
        return Result.ok(paperService.getPaperInfo(paperId));
    }

    @ApiOperation("获取教辅试卷内容")
    @GetMapping("/coachBookPaperContent")
    public Result getCoachBookPaperContent(@RequestParam String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkViewPaperContentPermission(paperId, userVo);
        Basket content = paperService.getCoachBookPaperContent(paperId, userVo.getSchoolId());
        quesLogService.saveQuesLog(userVo, paperId, request, "view");
        return Result.ok(content);
    }

    @ApiOperation("获取教辅试卷内容（已渲染公式）")
    @GetMapping("/coachBookPaperContentRendered")
    public Result getCoachBookPaperContentRendered(@RequestParam String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkViewPaperContentPermission(paperId, userVo);
        Basket content = paperService.getCoachBookPaperContentRendered(paperId, userVo.getSchoolId());
        quesLogService.saveQuesLog(userVo, paperId, request, "view");
        return Result.ok(content);
    }

    @ApiOperation("家长获取教辅试卷内容")
    @GetMapping("/coachBookPaperContentParent")
    public Result getCoachBookPaperContentParent(@RequestParam String paperId,
                                                 @RequestParam String studentId,
                                                 @RequestParam String schoolId,
                                                 HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        Basket content = paperService.getCoachBookPaperContentParent(paperId, studentId, schoolId, userVo);
        quesLogService.saveQuesLog(userVo, paperId, request, "view");
        return Result.ok(content);
    }

    @ApiOperation("查教辅试卷信息")
    @GetMapping("coachBookPaperInfo")
    public Result getCoachBookPaperInfo(@RequestParam @NotBlank String paperId, HttpServletRequest request) {
        UserInfo userInfo = TokenUserUtil.getUserInfo(request);
        permissionService.checkViewPaperInfoPermission(paperId, userInfo);
        return Result.ok(paperService.getCoachBookPaperInfo(paperId, userInfo.getSchoolId()));
    }

    @ApiOperation("修改试卷信息")
    @PutMapping("infoChange")
    public Result changeInfo(@RequestBody PaperMain paperMain, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        paperService.changeInfo(paperMain, userVo);
        return Result.ok();
    }

    @ApiOperation("修改试卷结构")
    @PutMapping("structure")
    @WithRoles({RoleEnum.SysEditor, RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result changePaperStructure(@NotBlank String paperId, @RequestPart @NotBlank String paperStructure, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        paperService.changePaperStructure(paperId, paperStructure, userVo);
        return Result.ok();
    }

    @ApiOperation("修改试卷题目别名")
    @PutMapping("updateQuestionAlias")
    public Result updateQuestionAlias(@NotBlank String paperId, @RequestPart @NotBlank String paperStructure, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        paperService.updateQuestionAlias(paperId, paperStructure, userVo);
        return Result.ok();
    }

    @ApiOperation("增加答题卡或反馈卡下载次数")
    @PutMapping("internal/addSheetDownloadCount")
    @CheckSign
    public Result addSheetDownloadCount(String paperId, String type) {
        paperService.addSheetDownloadCount(paperId, type);
        return Result.ok();
    }

    @ApiOperation("获取答题卡或反馈卡下载次数")
    @GetMapping("internal/sheetDownloadCount")
    @CheckSign
    public Result getSheetDownloadCount(String paperId, String type) {
        return Result.ok(paperService.getSheetDownloadCount(paperId, type));
    }

    @ApiOperation("删除试卷（标记删除）")
    @DeleteMapping("delete")
    public Result delPaperById(@RequestParam @NotBlank String paperId, HttpServletRequest request) {
        paperService.delPaperById(paperId, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @PostMapping("/downloadWord")
    @ApiOperation("下载试卷word")
    @AutoLog("下载试卷word")
    public void downloadWord(@RequestParam @NotEmpty String paperId,
                             @RequestParam @NotEmpty String size,
                             @RequestParam(required = false) Integer gradeId,
                             @RequestParam(required = false) Integer paperTypeId,
                             @RequestPart String html,
                             HttpServletRequest request,
                             HttpServletResponse response) throws IOException {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        paperService.downloadPaperWord(paperId, html, size, gradeId, paperTypeId, userVo, response.getOutputStream());
        // 记录下载操作
        quesLogService.saveQuesLog(userVo, paperId, request, "download");
    }

    @GetMapping("availableCoachBookPapers")
    @ApiOperation("获取用户组卷中可添加入教辅的试卷列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stage", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "学科id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "coachBookId", value = "要添加同步卷的教辅id", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "flagCoachBookId", value = "标记的教辅id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "paperGroupId", value = "试卷组id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页条数", dataType = "int", paramType = "query")})
    public Result getAvailableCoachBookPapers(Integer stage, Integer subjectId, String keyword, @RequestParam @NotNull Integer coachBookId,
                                              Integer flagCoachBookId, Integer paperGroupId, @NotNull @Min(1) Integer page, @NotNull @Min(1) Integer size, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        Page<PaperMain> pageInfo = paperService.getAvailableCoachBookPapers(userId, page, size, keyword, stage, subjectId, coachBookId, flagCoachBookId, paperGroupId);
        return Result.ok(pageInfo);
    }

    @GetMapping("availableCoachBookPapersUpload")
    @ApiOperation("获取用户上传卷中可加入教辅的试卷列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stage", value = "学段", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "学科id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "coachBookId", value = "要添加同步卷的教辅id", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "flagCoachBookId", value = "教辅id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "paperGroupId", value = "试卷组id", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "page", value = "页码", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "size", value = "每页条数", dataType = "int", paramType = "query")})
    public Result getAvailableCoachBookPapersUpload(Integer stage, Integer subjectId, String keyword, @RequestParam @NotNull Integer coachBookId, Integer flagCoachBookId, Integer paperGroupId,
                                                    @NotNull @Min(1) Integer page, @NotNull @Min(1) Integer size, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        Page<PaperMain> pageInfo = paperService.getAvailableCoachBookPapersUpload(userId, page, size, keyword, stage, subjectId, coachBookId, flagCoachBookId, paperGroupId);
        return Result.ok(pageInfo);
    }
}
