package com.sure.question.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.common.entity.Result;
import com.sure.question.annotation.WithRoles;
import com.sure.question.entity.QuestionCorrect;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.PermissionService;
import com.sure.question.service.QuestionCorrectService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.questioncorrect.QuestionCorrectVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/3/24 11:01
 */
@RestController
@RequestMapping("/questionCorrect")
@RequiredArgsConstructor
@Api(tags = "题目纠错")
@Validated
public class QuestionCorrectController {
    private final PermissionService permissionService;
    private final QuestionCorrectService questionCorrectService;

    @ApiOperation("标记题目")
    @PutMapping("/mark")
    public Result mark(@RequestBody QuestionCorrect params, HttpServletRequest request) {
        params.setUserId(TokenUserUtil.getUserId(request));
        questionCorrectService.mark(params);
        return Result.ok();
    }

    @ApiOperation("取消标记")
    @PutMapping("/unMark")
    public Result mark(@RequestParam @NotBlank String questionId, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        questionCorrectService.unMark(questionId, userId);
        return Result.ok();
    }

    @ApiOperation("报错记录")
    @GetMapping("item")
    public Result getItem(@RequestParam @NotBlank String questionId, HttpServletRequest request) {
        return Result.ok(questionCorrectService.getQuestionCorrectItem(questionId, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("查询纠错题目列表")
    @GetMapping("/list")
    @ApiImplicitParams({
            @io.swagger.annotations.ApiImplicitParam(name = "gradeLevel", value = "学段", dataType = "Integer", paramType = "query"),
            @io.swagger.annotations.ApiImplicitParam(name = "subjectId", value = "学科id", dataType = "Integer", paramType = "query"),
            @io.swagger.annotations.ApiImplicitParam(name = "status", value = "状态：0-未处理；1-已处理", dataType = "Integer", paramType = "query"),
            @io.swagger.annotations.ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "String", paramType = "query"),
            @io.swagger.annotations.ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "String", paramType = "query"),
            @io.swagger.annotations.ApiImplicitParam(name = "currentPage", value = "当前页", dataType = "Integer", paramType = "query"),
            @io.swagger.annotations.ApiImplicitParam(name = "pageSize", value = "每页条数", dataType = "Integer", paramType = "query")})
    @ApiResponses({@io.swagger.annotations.ApiResponse(code = 0, message = "成功", response = QuestionCorrectVo.class)})
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result list(Integer gradeLevel, Integer subjectId, @NotNull Integer status, String beginTime, String endTime,
                       Integer currentPage, Integer pageSize, HttpServletRequest request) {
        // 如果不是题库管理员，是编辑，需要判断是否有权限
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        permissionService.checkEditorPermission(userInfo, gradeLevel, subjectId);

        if (currentPage == null) {
            currentPage = 1;
        }
        if (pageSize == null) {
            pageSize = 10;
        }
        Page<QuestionCorrectVo> page = questionCorrectService.listQuestion(gradeLevel, subjectId, status, beginTime, endTime, currentPage, pageSize);
        return Result.ok(page);
    }

    @ApiOperation("更新状态为已处理")
    @PutMapping("/updateStatusToHandled")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result updateStatus(@RequestParam @NotBlank String questionId, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        questionCorrectService.updateStatusToHandled(questionId, userInfo);
        return Result.ok();
    }

    @ApiOperation("纠正题目")
    @PutMapping("correct")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result correct(@RequestBody @NotNull QuestionVo questionVo, HttpServletRequest request) {
        return Result.ok(questionCorrectService.correct(questionVo, TokenUserUtil.getUserId(request)));
    }

}
