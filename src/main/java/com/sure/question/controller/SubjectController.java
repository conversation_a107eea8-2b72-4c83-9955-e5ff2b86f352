package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.service.SubjectService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.subject.StudentSubjectVo;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/subject")
@ResponseBody
@Api("学科")
public class SubjectController {
    private final SubjectService subjectService;

    @GetMapping("studentSubject")
    @ApiOperation("查询学生学科")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "studentId", value = "学生id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "semesterId", value = "学年id", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期", dataType = "Integer", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = StudentSubjectVo.class, responseContainer = "List")})
    public Result studentSubject(@RequestParam("studentId") String studentId, @RequestParam @NotNull Integer semesterId,
                                 @RequestParam @NotNull Integer term, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        List<StudentSubjectVo> subjects = subjectService.studentSubject(studentId, userId, semesterId, term);
        return Result.ok(subjects);
    }
}
