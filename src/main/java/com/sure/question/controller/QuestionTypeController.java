package com.sure.question.controller;

import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.common.entity.StatusCode;
import com.sure.question.annotation.WithRoles;
import com.sure.question.entity.QuestionTypeMain;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.QuestionTypeMainService;
import com.sure.question.util.TokenUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;

@RestController
@ResponseBody
@RequestMapping("/questionType")
@Api(tags = "试题类型")
@Validated
@RequiredArgsConstructor
public class QuestionTypeController {
    private final QuestionTypeMainService questionTypeMainService;

    @GetMapping("bySidAndGid")
    @ApiOperation("根据学科ID和学段ID查询试题类型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "subject", value = "学科ID", dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "stage", value = "学段ID", dataType = "int", paramType = "query")})
    public Result findQuestionTypeBySidAndGl(Integer subject, Integer stage) {
        if (subject == null || stage == null) {
            return new Result(StatusCode.REQUEST_PARAMETER_ERROR, "请求参数有误");
        }
        return Result.ok(questionTypeMainService.findByStageAndSubject(stage, subject));
    }

    @PostMapping("add")
    @ApiOperation("添加试题类型")
    @WithRoles({RoleEnum.SysManager})
    @AutoLog("添加题型")
    public Result addQuestionType(@RequestBody QuestionTypeMain questionTypeMain, HttpServletRequest request) {
        questionTypeMainService.add(questionTypeMain, TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @PutMapping("update")
    @ApiOperation("更新试题类型")
    @WithRoles({RoleEnum.SysManager})
    @AutoLog("修改题型")
    public Result updateQuestionType(@RequestBody QuestionTypeMain questionTypeMain, HttpServletRequest request) {
        questionTypeMainService.update(questionTypeMain, TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @DeleteMapping("delete")
    @ApiOperation("删除试题类型")
    @AutoLog("删除题型")
    @WithRoles({RoleEnum.SysManager})
    public Result deleteQuestionTypeById(@NotNull Integer gradeLevel, @NotNull Integer subjectId, @NotNull Integer id,
                                         HttpServletRequest request) {
        questionTypeMainService.del(gradeLevel, subjectId, id, TokenUserUtil.getUserId(request));
        return Result.ok();
    }
}
