package com.sure.question.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.common.entity.Result;
import com.sure.question.annotation.CheckSign;
import com.sure.question.entity.SchoolCoachBook;
import com.sure.question.mapper.SchoolCoachBookMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("feign")
@Api(tags = "内部服务")
@Validated
@RequiredArgsConstructor
public class FeignServerController {
    private final SchoolCoachBookMapper schoolCoachBookMapper;

    @PostMapping("listSchoolCoachBook")
    @ApiOperation("查询学校开通的教辅")
    @CheckSign
    public Result listSchoolCoachBook(@RequestParam String schoolId, @RequestBody List<Integer> bookIds) {
        List<SchoolCoachBook> schoolCoachBooks = schoolCoachBookMapper.selectList(new LambdaQueryWrapper<SchoolCoachBook>()
                .eq(SchoolCoachBook::getSchoolId, schoolId)
                .in(SchoolCoachBook::getCoachBookId, bookIds)
                .select(SchoolCoachBook::getCoachBookId,
                        SchoolCoachBook::getSemesterId,
                        SchoolCoachBook::getTerm,
                        SchoolCoachBook::getGradeLevel,
                        SchoolCoachBook::getGradeId,
                        SchoolCoachBook::getSubjectId));
        return Result.ok(schoolCoachBooks);
    }

    @PostMapping("existsSchoolCoachBook")
    @ApiOperation("查询学校是否开通定制教辅")
    @CheckSign
    public Result existsSchoolCoachBook(@RequestParam String schoolId, @RequestParam Integer semesterId, @RequestParam Integer term) {
        boolean exists = schoolCoachBookMapper.selectOne(new LambdaQueryWrapper<SchoolCoachBook>()
                .eq(SchoolCoachBook::getSchoolId, schoolId)
                .eq(SchoolCoachBook::getSemesterId, semesterId)
                .eq(SchoolCoachBook::getTerm, term)
                .select(SchoolCoachBook::getId)
                .last("limit 1")
        ) != null;

        return Result.ok(exists);
    }
}
