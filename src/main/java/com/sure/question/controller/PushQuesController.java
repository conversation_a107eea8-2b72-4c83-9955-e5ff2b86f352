package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.annotation.CheckSign;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.ExamSubjectPushQuesService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.pushQuesVo.PaperPushInfoVo;
import com.sure.question.vo.pushQuesVo.PaperPushRequestVo;
import com.sure.question.vo.pushQuesVo.PushQuesCountVo;
import com.sure.question.vo.pushQuesVo.SimpleExamSubjectVo;
import com.sure.question.vo.question.UserQuestionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("pushQues")
@Api(tags = "试卷变式题")
@RequiredArgsConstructor
public class PushQuesController {
    private final ExamSubjectPushQuesService examsubjectPushQuesService;

    @ApiOperation("处理推题任务")
    @PutMapping("handlePushQuesTask")
    @CheckSign
    public Result submitPushQuesTask(String examSubjectId, String paperId, String userId, String timestampMillis) {
        examsubjectPushQuesService.handlePushQuesTask(examSubjectId, paperId, userId, timestampMillis);
        return Result.ok();
    }

    @ApiOperation("为试卷生成变式题并保存")
    @PostMapping("generateAndSavePaperPushQues")
    public Result generateAndSavePaperPushQues(@RequestBody @Validated PaperPushRequestVo vo, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        examsubjectPushQuesService.generateAndSavePaperPushQues(vo, userInfo);
        return Result.ok();
    }

    @ApiOperation("查看试卷中一道题目的变式题列表（根据试卷id、题目id和考试科目id）")
    @GetMapping("listPushQuesByPaperIdAndQuestionId")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = UserQuestionVo.class, responseContainer = "List")})
    public Result byPidAndQid(@RequestParam String paperId, @RequestParam String examSubjectId, @RequestParam String questionId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(examsubjectPushQuesService.listPushQuesByPaperIdAndQuestionId(paperId, examSubjectId, questionId, userVo));
    }

    @ApiOperation("获取试卷变式相关的考试科目列表")
    @GetMapping("listExamSubjectByPaperId")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = SimpleExamSubjectVo.class, responseContainer = "List")})
    public Result listExamSubjectByPaperId(@RequestParam String paperId) {
        return Result.ok(examsubjectPushQuesService.listExamSubjectByPaperId(paperId));
    }

    @ApiOperation("查询本卷或考卷是否已有变式题")
    @GetMapping("existsPushQues")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = Boolean.class)})
    public Result existsPushQues(@RequestParam String paperId, @RequestParam String examSubjectId) {
        return Result.ok(examsubjectPushQuesService.existsPushQues(paperId, examSubjectId));
    }

    @ApiOperation("获取指定考试的变式题列表")
    @GetMapping("getExamSubjectPushQuestionList")
    public Result getExamSubjectPushQuestionList(@RequestParam String examSubjectId) {
        return Result.ok(examsubjectPushQuesService.getExamSubjectPushQuestionList(examSubjectId));
    }

    @ApiOperation("获取指定考试的变式题列表")
    @GetMapping("listPushQuestionListByExamSubjectIdOrPaperId")
    public Result listPushQuestionListByExamSubjectIdOrPaperId(@RequestParam String examSubjectId, @RequestParam String paperId) {
        return Result.ok(examsubjectPushQuesService.listPushQuestionListByExamSubjectIdOrPaperId(examSubjectId, paperId));
    }

    @ApiOperation("获取指定试卷的变式题列表")
    @PostMapping("listPushQuestionListByPaperIds")
    public Result listPushQuestionListByPaperIds(@RequestBody List<String> paperIds) {
        return Result.ok(examsubjectPushQuesService.listPushQuestionListByPaperIds(paperIds));
    }

    @ApiOperation("添加单题的若干变式题到原卷")
    @PostMapping("addPushQuesToPaper")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result addPushQuesToPaper(@RequestParam @NotBlank String paperId, @RequestParam @NotBlank String quesId,
                                     @RequestBody @NotEmpty List<String> pushQuestIds, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        examsubjectPushQuesService.addPushQuesToPaper(paperId, quesId, pushQuestIds, userInfo);
        return Result.ok();
    }

    @ApiOperation("删除单道题的变式题")
    @DeleteMapping("deletePushQues")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result deletePushQues(@RequestParam @NotBlank String paperId, @RequestParam @NotBlank String quesId,
                                 @RequestBody @NotEmpty List<String> pushQuestIds, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        examsubjectPushQuesService.deletePushQues(paperId, quesId, pushQuestIds, userInfo);
        return Result.ok();
    }

    @ApiOperation("删除试卷的变式题")
    @DeleteMapping("deletePaperPushQues")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result deletePaperPushQues(@RequestParam @NotBlank String paperId, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        examsubjectPushQuesService.deletePaperPushQues(paperId, userInfo);
        return Result.ok();
    }

    @ApiOperation("同步原卷变式题到指定考试科目")
    @PostMapping("syncPaperPushQuesToExamSubject")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result syncPaperPushQuesToExamSubject(@RequestParam @NotBlank String paperId, @RequestParam @NotBlank String examSubjectId) {
        examsubjectPushQuesService.syncPaperPushQuesToExamSubject(paperId, examSubjectId);
        return Result.ok();
    }

    @ApiOperation("查询试卷推题信息")
    @GetMapping("getPaperPushInfo")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = PaperPushInfoVo.class)})
    public Result paperPushInfo(@RequestParam @NotBlank String paperId) {
        PaperPushInfoVo vo = examsubjectPushQuesService.paperPushInfo(paperId);
        return Result.ok(vo);
    }

    @ApiOperation("查询试卷各题变式题数量")
    @GetMapping("getQuesionIdAndPushCountList")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = PushQuesCountVo.class)})
    public Result getQuesionIdAndPushCountList(@RequestParam @NotBlank String paperId) {
        return Result.ok(examsubjectPushQuesService.getQuesionIdAndPushCountList(paperId));
    }

    @ApiOperation("更新是否标准推题标记")
    @PostMapping("updateIsStandardPushQues")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result updateIsStandardPushQues(@RequestParam @NotBlank String paperId, @RequestParam @NotNull Boolean isStandardPushQues, HttpServletRequest request) {
        TokenUserVo userInfo = TokenUserUtil.getTokenUserVo(request);
        examsubjectPushQuesService.updateIsStandardPushQues(paperId, isStandardPushQues, userInfo);
        return Result.ok();
    }

    @ApiOperation("查询有标准推题的试卷ID列表")
    @PostMapping("listPaperIdWithStandardPushQues")
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = String.class, responseContainer = "List")})
    public Result listPaperIdWithStandardPushQues(@RequestBody @NotEmpty List<String> paperIds) {
        List<String> paperIdList = examsubjectPushQuesService.listPaperIdWithStandardPushQues(paperIds);
        return Result.ok(paperIdList);
    }

}
