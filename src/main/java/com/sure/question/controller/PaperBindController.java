package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.annotation.CheckSign;
import com.sure.question.service.PaperBindService;
import com.sure.question.util.TokenUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;

@RestController
@RequestMapping("/paperBind")
@Api(tags = "绑定试卷")
@Validated
@RequiredArgsConstructor
public class PaperBindController {
    private final PaperBindService paperBindService;

    @ApiOperation("阅卷服务获取试卷题目数据")
    @GetMapping("internal/markPaperData")
    @CheckSign
    public Result getMarkPaperQuestionData(@NotBlank String paperId, Boolean addQuestionType, Boolean addKnowledge) {
        return Result.ok(paperBindService.extractMarkPaperStructure(paperId, addQuestionType, addKnowledge));
    }

    @ApiOperation("检查绑定试卷权限")
    @GetMapping("internal/checkBindPaperPermission")
    @CheckSign
    public Result checkBindPaperPermission(@NotBlank String paperId, @NotBlank String schoolId, @NotBlank String userId) {
        return Result.ok(paperBindService.checkBindPaperPermission(paperId, schoolId, userId));
    }

    @ApiOperation("获取可绑定试卷")
    @GetMapping("availablePapers")
    public Result getAvailablePapers(Integer subjectId, Integer stage, Integer gradeId, Integer term, Integer type, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        return Result.ok(paperBindService.getAvailablePapers(subjectId, stage, gradeId, term, userId, type));
    }
}
