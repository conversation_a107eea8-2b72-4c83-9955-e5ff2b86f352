package com.sure.question.controller;

import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.BookService;
import com.sure.question.util.TokenUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@RestController
@RequestMapping("/book")
@Api(tags = "教材")
@RequiredArgsConstructor
@Validated
public class BookController {
    private final BookService bookService;

    @GetMapping("list")
    @ApiOperation("查询教材")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result listBooks(Integer gradeLevel, Integer subjectId) {
        return Result.ok(bookService.listBooks(gradeLevel, subjectId));
    }

    @GetMapping("listBookCategories")
    @ApiOperation("查询教材版本")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SysEditor})
    public Result listBookCategory(Integer gradeLevel, Integer subjectId) {
        return Result.ok(bookService.listBookCategories(gradeLevel, subjectId));
    }

    @PostMapping("add")
    @ApiOperation("添加教材")
    @AutoLog("添加教材")
    @WithRoles({RoleEnum.SysManager})
    public Result addBook(@NotNull Integer gradeLevel, @NotNull Integer subjectId, Integer gradeId, Integer term,
                          @NotEmpty String bookName, @NotNull Integer categoryId, Boolean enabled,
                          HttpServletRequest request) {
        return Result.ok(bookService.addBook(gradeLevel, subjectId, gradeId, term, bookName, categoryId, Boolean.TRUE.equals(enabled), TokenUserUtil.getUserId(request)));
    }

    @PutMapping("update")
    @ApiOperation("修改教材")
    @AutoLog("修改教材")
    @WithRoles({RoleEnum.SysManager})
    public Result updateBook(@NotNull Integer id, @NotEmpty String bookName, Integer gradeId, Integer term, @NotNull Integer orderId, @NotNull Boolean enabled,
                             HttpServletRequest request) {
        bookService.updateBook(id, bookName, gradeId, term, orderId, enabled, TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @PutMapping("updateBookCategoryOrder")
    @ApiOperation("修改教材版本顺序")
    @AutoLog("修改教材版本顺序")
    @WithRoles({RoleEnum.SysManager})
    public Result updateBookCategoryOrder(@NotNull Integer gradeLevel, @NotNull Integer subjectId, @NotNull Integer categoryId,
                                          @NotNull Integer categoryOrderId, HttpServletRequest request) {
        bookService.updateBookCategoryOrder(gradeLevel, subjectId, categoryId, categoryOrderId, TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @DeleteMapping("delete")
    @ApiOperation("删除教材")
    @AutoLog("删除教材")
    @WithRoles({RoleEnum.SysManager})
    public Result deleteBook(@NotNull Integer id, HttpServletRequest request) {
        bookService.deleteBook(id, TokenUserUtil.getUserId(request));
        return Result.ok();
    }
}
