//package com.sure.question.controller;
//
//import com.sure.common.entity.Result;
//import com.sure.question.service.CleanTaskService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.validation.constraints.NotEmpty;
//import javax.validation.constraints.NotNull;
//
//@RestController
//@RequestMapping("cleanTask")
//@Api(tags = "题库清理")
//@Validated
//public class CleanTaskController {
//    @Resource
//    private CleanTaskService cleanTaskService;
//
//    @PostMapping("create")
//    @ApiOperation("创建清理任务")
//    public Result createTask(@NotEmpty String name, String description, @NotNull Integer gradeLevel, @NotNull Integer subjectId) {
//        cleanTaskService.createCleanTask(name, description, gradeLevel, subjectId);
//        return Result.ok();
//    }
//
//    @PostMapping("createForAllGradeLevelSubject")
//    @ApiOperation("为所有学段学科创建清理任务")
//    public Result createTaskForAllGradeLevelSubject(@NotEmpty String name, String description) {
//        cleanTaskService.createCleanTaskForAllGradeLevelSubject(name, description);
//        return Result.ok();
//    }
//
//    @GetMapping("list")
//    @ApiOperation("获取清理任务列表")
//    public Result getTaskList(Integer gradeLevel, Integer subjectId, String name, @NotNull Boolean addStats) {
//        return Result.ok(cleanTaskService.getCleanTaskList(gradeLevel, subjectId, name, addStats));
//    }
//
//    @GetMapping("info")
//    @ApiOperation("获取清理任务信息")
//    public Result getTaskInfo(@NotNull Integer taskId) {
//        return Result.ok(cleanTaskService.getCleanTaskInfo(taskId));
//    }
//
//    @GetMapping("unConfirmQuestionPage")
//    @ApiOperation("获取一页未确认题目")
//    public Result getUnConfirmQuestionPage(@NotNull Integer taskId, Integer page, Integer size) {
//        return Result.ok(cleanTaskService.getUnConfirmQuestionPage(taskId, page, size));
//    }
//
//    @GetMapping("ignoredQuestionPage")
//    @ApiOperation("获取一页已忽略题目")
//    public Result getIgnoredQuestionPage(@NotNull Integer taskId, Integer page, Integer size) {
//        return Result.ok(cleanTaskService.getIgnoredQuestionPage(taskId, page, size));
//    }
//
//    @GetMapping("confirmedQuestionPage")
//    @ApiOperation("获取一页已确认题目")
//    public Result getConfirmedQuestionPage(@NotNull Integer taskId, Integer page, Integer size) {
//        return Result.ok(cleanTaskService.getConfirmedQuestionPage(taskId, page, size));
//    }
//
//    @GetMapping("updatedQuestionPage")
//    @ApiOperation("获取一页已更新题目")
//    public Result getUpdatedQuestionPage(@NotNull Integer taskId, Integer page, Integer size) {
//        return Result.ok(cleanTaskService.getUpdatedQuestionPage(taskId, page, size));
//    }
//
//    @PutMapping("confirmQuestion")
//    @ApiOperation("确认题目")
//    public Result confirmQuestion(@NotNull Integer taskId, @NotEmpty String questionId) {
//        cleanTaskService.confirmTaskQuestion(taskId, questionId);
//        return Result.ok();
//    }
//
//    @PutMapping("ignoreQuestion")
//    @ApiOperation("忽略题目")
//    public Result ignoreQuestion(@NotNull Integer taskId, @NotEmpty String questionId) {
//        cleanTaskService.ignoreTaskQuestion(taskId, questionId);
//        return Result.ok();
//    }
//
//    @PutMapping("updateQuestion")
//    @ApiOperation("更新题目")
//    public Result updateQuestion(@NotNull Integer taskId, @NotEmpty String questionId) {
//        cleanTaskService.updateOneQuestion(taskId, questionId);
//        return Result.ok();
//    }
//
//    @PutMapping("confirmAndUpdateQuestion")
//    @ApiOperation("确认并更新题目")
//    public Result confirmAndUpdateQuestion(@NotNull Integer taskId, @NotEmpty String questionId) {
//        cleanTaskService.confirmAndUpdateQuestion(taskId, questionId);
//        return Result.ok();
//    }
//
//    @PutMapping("updateAllConfirmedQuestion")
//    @ApiOperation("更新所有已确认题目")
//    public Result updateAllConfirmedQuestions(@NotNull Integer taskId) {
//        cleanTaskService.updateAllConfirmedQuestions(taskId);
//        return Result.ok();
//    }
//}
