package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.common.exception.ApiException;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.BasketService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.Basket;
import com.sure.question.vo.question.PaperStructureQuestionVosParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@RequiredArgsConstructor
@RestController
@RequestMapping("/basket")
@Api("试题篮")
public class BasketController {
    private final BasketService basketService;

    @GetMapping("new")
    @ApiOperation("获取试题篮")
    public Result getBasket(HttpServletRequest request) {
        return Result.ok(basketService.getBasketAddContent(TokenUserUtil.getUserId(request)));
    }

    @PutMapping("clearNew")
    @ApiOperation("清空试题篮")
    public Result clear(HttpServletRequest request) {
        basketService.clearBasket(TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @PutMapping()
    @ApiOperation("修改试题篮结构")
    public Result updateBasket(@RequestBody Basket basket, HttpServletRequest request) {
        basketService.updateBasket(basket, TokenUserUtil.getUserId(request));
        return Result.ok();
    }

    @PostMapping("addQuestion")
    @ApiOperation("添加题目")
    public Result addQuestion(@RequestBody @Valid PaperStructureQuestionVosParam param, HttpServletRequest request) {
        return Result.ok(basketService.addQuestions(param.getQuestions(), param.getPaperStructure(), TokenUserUtil.getUserId(request)));
    }

    @PutMapping("ques")
    @ApiOperation("修改组卷题目")
    public Result updateQuesVo(@RequestParam @NotBlank String questionId, @RequestBody @Valid PaperStructureQuestionVosParam param, HttpServletRequest request) {
        if (param.getQuestions().size() > 1) {
            throw new ApiException("请逐个修改");
        }
        return Result.ok(basketService.updateQuesVo(questionId, param.getQuestions().get(0), param.getPaperStructure(), TokenUserUtil.getUserId(request)));
    }

    @PutMapping("saveAsPaper")
    @ApiOperation("保存试卷")
    public Result saveAsPaper(Integer gradeLevel, Integer subjectId, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        String schoolId = TokenUserUtil.getSchoolId(request);
        return Result.ok(basketService.saveAsPaper(userId, schoolId, gradeLevel, subjectId));
    }

    @GetMapping("userRawCache")
    @ApiOperation("管理员查看用户试题篮原始内容")
    @WithRoles({RoleEnum.SysManager})
    public Result getUserRawCache(String userId) {
        return Result.ok(basketService.getUserBasketRawCache(userId));
    }
}
