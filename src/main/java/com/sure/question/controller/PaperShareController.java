package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.FeignService;
import com.sure.question.service.PaperShareService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.UserMsgVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/paperShare")
@Api(tags = "试卷分享库")
public class PaperShareController {
    @Resource
    PaperShareService paperShareService;
    @Resource
    FeignService feignService;

    @ApiOperation("多条件查看我分享出去的试卷")
    @GetMapping("myShares")
    public Result getMyShares(Integer type, Integer grade, Integer year, String region, Integer subject, Integer stage,
                              String bookType, Integer flagCoachBookId, Integer page, Integer size, HttpServletRequest request) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(paperShareService.getMyShares(userVo, type, grade, year, region, subject, stage, bookType, flagCoachBookId, page, size));
    }

    @ApiOperation("查看我分享某试卷的记录")
    @GetMapping("myShareSpecifiedPaperId")
    public Result getMySharePaperHistory(String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(paperShareService.getMyShareSpecifiedPaperId(paperId, userVo));
    }

    @ApiOperation("多条件查看分享给我的试卷")
    @GetMapping("shareToMe")
    public Result getShareToMe(Integer type, Integer grade, Integer year, String region, Integer subject, Integer stage, Integer bank,
                               String keyword, String bookType, Integer flagCoachBookId, Integer paperGroupId, Integer page, Integer size, HttpServletRequest request) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(paperShareService.getShareToMe(userVo, type, grade, year, region, subject, stage, page, size, bank, keyword, bookType, flagCoachBookId, paperGroupId));
    }

    @ApiOperation("多条件查看分享给我的试卷")
    @GetMapping("availableCoachBookPapersShareToMe")
    public Result getAvailableCoachBookPapersShareToMe(Integer type, Integer grade, Integer year, String region, Integer subject, Integer stage, Integer bank,
                                                       String keyword, Integer coachBookId, Integer flagCoachBookId, Integer paperGroupId, Integer page, Integer size, HttpServletRequest request) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(paperShareService.getAvailableCoachBookPapersShareToMe(userVo, type, grade, year, region, subject,
                stage, page, size, bank, keyword, coachBookId, flagCoachBookId, paperGroupId));
    }

    @ApiOperation("根据用户名,真实姓名,手机号码搜索我能分享的人")
    @GetMapping("targets")
    public Result getTargets(String key, String schoolId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        // 非系统管理员只能分享给本校老师
        if (!userVo.getRoles().contains(RoleEnum.SuperManager.getCode())) {
            schoolId = userVo.getSchoolId();
        }
        List<UserMsgVo> list = feignService.lstTeacherInfoBySchoolIdAndKey(schoolId, key);
        return Result.ok(list);
    }

    @ApiOperation("分享试卷给目标对象")
    @PutMapping("share")
    public Result shareTo(String paperId, String targetId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        paperShareService.shareTo(userVo, targetId, paperId);
        return Result.ok();
    }

    @ApiOperation("分享撤回")
    @DeleteMapping("recall")
    public Result recall(Integer id, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        paperShareService.recall(userVo, id);
        return Result.ok();
    }

    @ApiOperation("被分享者删除试卷")
    @DeleteMapping("hide")
    public Result hidePaper(Integer id, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        paperShareService.hidePaper(userVo, id);
        return Result.ok();
    }
}
