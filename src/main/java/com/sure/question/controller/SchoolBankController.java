package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.common.exception.ApiException;
import com.sure.question.service.SchoolBankService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.question.FindQuestionPageParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/schoolBank")
@Api(tags = "校本库")
@Validated
public class SchoolBankController {

    private final SchoolBankService schoolBankService;

    @ApiOperation("多条件查试卷")
    @GetMapping("papers")
    public Result getPapers(@NotNull @Min(1) Integer page, @NotNull @Min(1) Integer size, @NotNull Integer stage, @NotNull Integer subject,
                            Integer grade, Integer term, Integer type, Integer year, String region, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(schoolBankService.getPapers(userVo, stage, grade, term, subject, type, year, region, page, size));
    }

    @ApiOperation("按章节多条件查题目")
    @GetMapping("quesByChapter")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "请求页码", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "页面数据量", dataType = "int"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "string"),
            @ApiImplicitParam(name = "stage", value = "学段ID", dataType = "int"),
            @ApiImplicitParam(name = "subject", value = "学科ID", dataType = "int"),
            @ApiImplicitParam(name = "year", value = "试卷年份", dataType = "int"),
            @ApiImplicitParam(name = "bookId", value = "教材Id", dataType = "int"),
            @ApiImplicitParam(name = "chapter", value = "章节Id", dataType = "int"),
            @ApiImplicitParam(name = "difficulty", value = "难度ID", dataType = "int"),
            @ApiImplicitParam(name = "ques_type", value = "试题类型ID", dataType = "int"),
            @ApiImplicitParam(name = "source", value = "试题来源(试卷类型)", dataType = "int"),
            @ApiImplicitParam(name = "region", value = "地区", dataType = "long"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int")})
    public Result getQuesByChapter(@Valid FindQuestionPageParam param, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(schoolBankService.findQuestionByChapterOrKnowledge(userVo, param));
    }

    @ApiOperation("按知识点多条件查题目")
    @GetMapping("quesByKnows")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "请求页码", dataType = "int"),
            @ApiImplicitParam(name = "size", value = "页面数据量", dataType = "int"),
            @ApiImplicitParam(name = "keyword", value = "关键字", dataType = "string"),
            @ApiImplicitParam(name = "stage", value = "学段ID", dataType = "int"),
            @ApiImplicitParam(name = "subject", value = "学科ID", dataType = "int"),
            @ApiImplicitParam(name = "year", value = "试卷年份", dataType = "int"),
            @ApiImplicitParam(name = "knowledge", value = "知识点ID", dataType = "int"),
            @ApiImplicitParam(name = "difficulty", value = "难度ID", dataType = "int"),
            @ApiImplicitParam(name = "ques_type", value = "试题类型ID", dataType = "int"),
            @ApiImplicitParam(name = "source", value = "试题来源(试卷类型)", dataType = "int"),
            @ApiImplicitParam(name = "region", value = "地区", dataType = "long"),
            @ApiImplicitParam(name = "gradeId", value = "年级ID", dataType = "int")})
    public Result quesByKnows(@Valid FindQuestionPageParam param, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(schoolBankService.findQuestionByChapterOrKnowledge(userVo, param));
    }

    @ApiOperation("学校教师分享到校本库")
    @PutMapping("teacherSharePaper")
    public Result teacherSharePaper(@NotNull String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        schoolBankService.sharePaper(paperId, userVo.getSchoolId(), userVo.getUserId());
        return Result.ok();
    }

    @ApiOperation("题库管理员分享到校本库")
    @PutMapping("administratorSharePaper")
//    @WithRoles({RoleEnum.SysManager})
    public Result administratorSharePaper(@NotNull String paperId, @NotNull String schoolId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        schoolBankService.sharePaper(paperId, schoolId, userVo.getUserId());
        return Result.ok();
    }

    @ApiOperation("学校教师撤回分享到校本库的试卷")
    @PutMapping("teacherWithdrawPaper")
    public Result teacherWithdrawPaper(@NotNull String paperId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        schoolBankService.withdrawPaper(paperId, userVo.getSchoolId(), userVo.getUserId());
        return Result.ok();
    }

    @ApiOperation("题库管理员撤回分享到校本库的试卷")
    @PutMapping("administratorWithdrawPaper")
//    @WithRoles({RoleEnum.SysManager})
    public Result administratorWithdrawPaper(@NotNull String paperId, @NotNull String schoolId, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        schoolBankService.withdrawPaper(paperId, schoolId, userVo.getUserId());
        return Result.ok();
    }

    @ApiOperation("学校教师获取分享到学校卷库记录")
    @GetMapping("teacherShares")
    public Result getTeacherShares(Integer stage, Integer grade, Integer term, Integer subject, Integer type, Integer year,
                                   String region, String bookType, Integer flagCoachBookId, Integer paperGroupId, Integer page, Integer size, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(schoolBankService.getShareRecords(userVo.getUserId(), userVo.getSchoolId(), stage, grade, term, subject,
                type, year, region, bookType, flagCoachBookId, paperGroupId, page, size));
    }

    @ApiOperation("题库管理员获取分享到学校卷库记录")
    @GetMapping("administratorShares")
//    @WithRoles({RoleEnum.SysManager})
    public Result getAdministratorShares(Integer stage, Integer grade, Integer term, Integer subject, Integer type, Integer year,
                                         String region, String bookType, Integer flagCoachBookId, Integer paperGroupId, Integer page, Integer size, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        return Result.ok(schoolBankService.getShareRecords(userVo.getUserId(), null, stage, grade, term, subject, type, year,
                region, bookType, flagCoachBookId, paperGroupId, page, size));
    }

    @ApiOperation("删除校本库试卷")
    @DeleteMapping("deletePaperBatch")
//    @WithRoles({RoleEnum.SchoolManager})
    public Result deletePapers(@RequestBody List<String> paperIds, HttpServletRequest request) {
        checkPaperIdsNotEmptyThrowEx(paperIds);
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        schoolBankService.deletePapers(paperIds, userVo.getSchoolId(), userVo.getUserId());
        return Result.ok();
    }

    @ApiOperation("恢复校本库试卷")
    @PutMapping("recoverPaperBatch")
//    @WithRoles({RoleEnum.SchoolManager})
    public Result recoverPapers(@RequestBody List<String> paperIds, HttpServletRequest request) {
        checkPaperIdsNotEmptyThrowEx(paperIds);
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        schoolBankService.recoverPapers(paperIds, userVo.getSchoolId());
        return Result.ok();
    }

    @ApiOperation("公开试卷")
    @PutMapping("makePapersPublic")
//    @WithRoles({RoleEnum.SchoolManager})
    public Result makePapersPublic(@RequestBody List<String> paperIds, HttpServletRequest request) {
        checkPaperIdsNotEmptyThrowEx(paperIds);
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        schoolBankService.changePapersPublic(paperIds, userVo.getSchoolId(), true);
        return Result.ok();
    }

    @ApiOperation("隐藏试卷")
    @PutMapping("makePapersUnPublic")
//    @WithRoles({RoleEnum.SchoolManager})
    public Result makePapersUnPublic(@RequestBody List<String> paperIds, HttpServletRequest request) {
        checkPaperIdsNotEmptyThrowEx(paperIds);
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        schoolBankService.changePapersPublic(paperIds, userVo.getSchoolId(), false);
        return Result.ok();
    }

    private void checkPaperIdsNotEmptyThrowEx(List<String> paperIds) {
        if (paperIds == null || paperIds.isEmpty()) {
            throw new ApiException("参数错误");
        }
    }
}
