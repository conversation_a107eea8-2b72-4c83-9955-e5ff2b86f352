package com.sure.question.controller;

import com.github.pagehelper.PageInfo;
import com.sure.common.entity.Result;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.QuesLogService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.queslog.PaperStatVo;
import com.sure.question.vo.queslog.QuesLogDetailVo;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/5/18 9:53
 */
@RestController
@RequestMapping("/quesLog")
@RequiredArgsConstructor
@Api(tags = "试卷日志")
public class QuesLogController {

    private final QuesLogService quesLogService;

    @ApiOperation("试卷日志列表")
    @GetMapping("/listQuesLog")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "schoolId", value = "学校id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "日志类型：1-浏览试卷；2-下载试卷", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "paperId", value = "试卷id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "paperName", value = "试卷名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", dataType = "Integer", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "成功", response = QuesLogDetailVo.class, responseContainer = "List")})
    @WithRoles({RoleEnum.SysManager})
    public Result listQuesLog(String schoolId, Integer type, String beginTime, String endTime, String paperId, String paperName, Integer pageNum, Integer pageSize, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        PageInfo<QuesLogDetailVo> pageInfo = quesLogService.listQuesLog(schoolId, type, beginTime, endTime, paperId, paperName, pageNum, pageSize, userVo);
        return Result.ok(pageInfo);
    }

    @ApiOperation("试卷统计")
    @GetMapping("/statistic")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "beginTime", value = "开始时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "endTime", value = "结束时间", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "gradeLevel", value = "年级", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "paperName", value = "试卷名称", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "每页条数", dataType = "Integer", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "成功", response = PaperStatVo.class, responseContainer = "List")})
    @WithRoles({RoleEnum.SysManager})
    public Result statistic(String beginTime, String endTime, Integer gradeLevel, Integer subjectId, String paperName, Integer pageNum, Integer pageSize, HttpServletRequest request) {
        TokenUserVo userVo = TokenUserUtil.getTokenUserVo(request);
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        PageInfo<PaperStatVo> pageInfo = quesLogService.statistic(beginTime, endTime, gradeLevel, subjectId, paperName, pageNum, pageSize, userVo);
        return Result.ok(pageInfo);
    }

}
