package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.service.CustomPaperService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.coachBook.PaperVo;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/16 16:20
 */
@RestController
@RequestMapping("customPaper")
@RequiredArgsConstructor
public class CustomPaperController {

    private final CustomPaperService customPaperService;

    @ApiOperation("查询当前学生的定制试卷")
    @GetMapping("list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "studentId", value = "学生id", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "subjectId", value = "科目id", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "semesterId", value = "学年id", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "term", value = "学期", dataType = "Integer", paramType = "query")})
    @ApiResponses({@ApiResponse(code = 0, message = "OK", response = PaperVo.class, responseContainer = "List")})
    public Result list(@RequestParam @NotBlank String studentId, @RequestParam @NotNull Integer subjectId,
                       @RequestParam @NotNull Integer semesterId, @RequestParam @NotNull Integer term, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        List<PaperVo> voList = customPaperService.lstStuPaper(studentId, subjectId, semesterId, term, userId);
        return Result.ok(voList);
    }
}
