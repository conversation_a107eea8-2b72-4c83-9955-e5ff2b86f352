package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.BaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/base")
@Api("基础信息")
@RequiredArgsConstructor
public class BaseController {
    private final BaseService baseService;

    @GetMapping("region")
    @ApiOperation("查询地区")
    public Result getRegion() {
        return Result.ok(baseService.getRegionWithCache());
    }

    @DeleteMapping("deleteRegionCache")
    @ApiOperation("清空redis地区缓存")
    @WithRoles({RoleEnum.SysManager})
    public Result deleteRegionCache() {
        baseService.deleteRegionCache();
        return Result.ok();
    }

    @GetMapping("message")
    @ApiOperation("查询题库基础数据")
    public Result getBaseMassage() {
        return Result.ok(baseService.getMessageWithCache());
    }

    @DeleteMapping("deleteMessageCache")
    @ApiOperation("清空基础数据缓存")
    @WithRoles({RoleEnum.SysManager})
    public Result deleteMessageCache() {
        baseService.deleteMessageCache();
        return Result.ok();
    }
}
