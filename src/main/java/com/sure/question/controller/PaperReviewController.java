package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.annotation.WithRoles;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.PaperReviewService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.paper.FindPaperPageParam;
import com.sure.question.vo.question.PaperStructureQuestionIdsParam;
import com.sure.question.vo.question.PaperStructureQuestionVosParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/paperReview")
@Api(tags = "试卷审核")
@Validated
@RequiredArgsConstructor
public class PaperReviewController {
    private final PaperReviewService paperReviewService;

    @ApiOperation("查待分配试卷")
    @GetMapping("unassigned")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result getUnAssigned(@Valid FindPaperPageParam param) {
        return Result.ok(paperReviewService.getUnassigned(param));
    }

    @ApiOperation("查已分配待提交试卷")
    @GetMapping("assignedUnsubmitted")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result getAssignedAndUnSubmitted(@Valid FindPaperPageParam param) {
        return Result.ok(paperReviewService.getAssignedAndUnSubmitted(param));
    }

    @ApiOperation("查已提交待发布试卷")
    @GetMapping("submittedUnpublished")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result getSubmittedAndUnpublished(@Valid FindPaperPageParam param) {
        return Result.ok(paperReviewService.getSubmittedAndUnpublished(param));
    }

    @ApiOperation("查个人未提交试卷")
    @GetMapping("selfUnsubmitted")
    @WithRoles({RoleEnum.SysEditor, RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result getSelfUnsubmitted(@Valid FindPaperPageParam param, HttpServletRequest request) {
        return Result.ok(paperReviewService.getSelfUnsubmitted(param, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("查个人已提交试卷")
    @GetMapping("selfSubmitted")
    @WithRoles({RoleEnum.SysEditor, RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result getSelfSubmitted(@Valid FindPaperPageParam param, HttpServletRequest request) {
        return Result.ok(paperReviewService.getSelfSubmitted(param, TokenUserUtil.getUserId(request)));
    }

    @ApiOperation("分配审核任务")
    @PutMapping("assign")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result assign(@NotBlank String editorId, @RequestBody @NotEmpty List<String> paperIds) {
        paperReviewService.assign(editorId, paperIds);
        return Result.ok();
    }

    @ApiOperation("取消分配任务")
    @PutMapping("removeAssignment")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result removeAssignment(@RequestBody @NotEmpty List<String> paperIds) {
        paperReviewService.removeAssignment(paperIds);
        return Result.ok();
    }

    @ApiOperation("提交审核试卷")
    @PutMapping("submit")
    @WithRoles({RoleEnum.SysEditor, RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result submit(@NotBlank String paperId, Boolean allowNoScore, HttpServletRequest request) {
        paperReviewService.submit(paperId, Boolean.TRUE.equals(allowNoScore), TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("驳回提交")
    @PutMapping("rejectSubmission")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result rejectSubmission(@RequestBody @NotEmpty List<String> paperIds) {
        paperReviewService.rejectSubmission(paperIds);
        return Result.ok();
    }

    @ApiOperation("发布至公共库")
    @PutMapping("publish")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result publish(@NotBlank String paperId, Boolean allowNoScore) {
        paperReviewService.publish(paperId, Boolean.TRUE.equals(allowNoScore));
        return Result.ok();
    }

    @ApiOperation("撤回发布至公共库")
    @PutMapping("unpublish")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result unpublish(@NotBlank String paperId) {
        paperReviewService.unpublish(paperId);
        return Result.ok();
    }


    @ApiOperation("审卷获取试卷内容")
    @GetMapping("reviewPaperContent")
    public Result getReviewPaperContent(@RequestParam @NotNull String paperId, HttpServletRequest request) {
        return Result.ok(paperReviewService.getReviewPaperContent(paperId, TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("审卷添加题目")
    @PostMapping("reviewPaperQuestions")
    public Result addReviewPaperQuestions(@RequestParam @NotNull String paperId, @RequestBody @Valid PaperStructureQuestionVosParam param,
                                          HttpServletRequest request) {
        return Result.ok(paperReviewService.addReviewPaperQuestions(paperId, param.getPaperStructure(), param.getQuestions(), TokenUserUtil.getTokenUserVo(request)));
    }

    @PutMapping("reviewPaperQuestions")
    @ApiOperation("审卷修改题目")
    public Result changeReviewPaperQuestions(@RequestParam @NotNull String paperId, @RequestBody @Valid PaperStructureQuestionVosParam param,
                                             HttpServletRequest request) {
        return Result.ok(paperReviewService.changeReviewPaperQuestions(paperId, param.getPaperStructure(), param.getQuestions(), TokenUserUtil.getTokenUserVo(request)));
    }

    @ApiOperation("审卷删除题目")
    @DeleteMapping("reviewPaperQuestions")
    public Result deleteReviewPaperQuestions(@RequestParam @NotNull String paperId, @RequestBody @Valid PaperStructureQuestionIdsParam param,
                                             HttpServletRequest request) {
        paperReviewService.deleteReviewPaperQuestions(paperId, param.getPaperStructure(), param.getQuestionIds(), TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }

    @ApiOperation("审卷修改试卷结构")
    @PutMapping("reviewPaperStructure")
    public Result changeReviewPaperStructure(@RequestParam @NotBlank String paperId, @RequestPart @NotBlank String paperStructure, HttpServletRequest request) {
        paperReviewService.changeReviewPaperStructure(paperId, paperStructure, TokenUserUtil.getTokenUserVo(request));
        return Result.ok();
    }


    @ApiOperation("题库管理员查看编辑审核试卷统计")
    @GetMapping("editorStats/review")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result getEditorsReviewPaperStats(@NotNull Integer gradeLevel, @NotNull Integer subjectId) {
        return Result.ok(paperReviewService.getEditorsReviewPaperStats(gradeLevel, subjectId));
    }

    @ApiOperation("编辑员查看个人看题统计")
    @GetMapping("editorStats/review/self")
    public Result getEditorSelfReviewPaperStats(@NotNull Integer gradeLevel, @NotNull Integer subjectId, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        return Result.ok(paperReviewService.getEditorSelfReviewPaperStats(gradeLevel, subjectId, userId));
    }

    @ApiOperation("题库管理员查看编辑组卷统计")
    @GetMapping("editorStats/make")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result getEditorsMakePaperStats(@NotNull Integer gradeLevel, @NotNull Integer subjectId, String bookType) {
        return Result.ok(paperReviewService.getEditorsMakePaperStats(gradeLevel, subjectId, bookType));
    }

    @ApiOperation("编辑员查看个人组卷统计")
    @GetMapping("editorStats/make/self")
    public Result getEditorSelfMakePaperStats(@NotNull Integer gradeLevel, @NotNull Integer subjectId, String bookType, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        return Result.ok(paperReviewService.getEditorSelfMakePaperStats(gradeLevel, subjectId, bookType, userId));
    }

    @ApiOperation("题库管理员查看编辑上传统计")
    @GetMapping("editorStats/upload")
    @WithRoles({RoleEnum.SysManager, RoleEnum.SuperManager})
    public Result getEditorsUploadPaperStats(@NotNull Integer gradeLevel, @NotNull Integer subjectId, String bookType) {
        return Result.ok(paperReviewService.getEditorsUploadPaperStats(gradeLevel, subjectId, bookType));
    }

    @ApiOperation("编辑员查看个人上传统计")
    @GetMapping("editorStats/upload/self")
    public Result getEditorSelfUploadPaperStats(@NotNull Integer gradeLevel, @NotNull Integer subjectId, String bookType, HttpServletRequest request) {
        String userId = TokenUserUtil.getUserId(request);
        return Result.ok(paperReviewService.getEditorSelfUploadPaperStats(gradeLevel, subjectId, bookType, userId));
    }
}
