package com.sure.question.controller;

import com.sure.base.log.annotation.AutoLog;
import com.sure.common.entity.Result;
import com.sure.question.annotation.WithRoles;
import com.sure.question.entity.PaperGroup;
import com.sure.question.enums.RoleEnum;
import com.sure.question.service.PaperGroupService;
import com.sure.question.util.TokenUserUtil;
import com.sure.question.vo.papergroup.SortCodeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/19 11:04
 */
@RestController
@RequestMapping("/paperGroup")
@RequiredArgsConstructor
@Api(tags = "试卷分组")
public class PaperGroupController {

    private final PaperGroupService paperGroupService;

    @PostMapping("/add")
    @ApiOperation("添加试卷分组")
    @AutoLog("添加试卷分组")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stage", value = "学段", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "subjectId", value = "科目", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "groupName", value = "分组名称", required = true, dataType = "String"),
            @ApiImplicitParam(name = "sortCode", value = "排序", dataType = "Integer")})
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result addPaperGroup(Integer stage, Integer subjectId, String groupName, Integer sortCode, HttpServletRequest request) {
        if (sortCode == null) {
            sortCode = 0;
        }
        String userId = TokenUserUtil.getUserId(request);
        paperGroupService.addPaperGroup(stage, subjectId, groupName, sortCode, userId);
        return Result.ok();
    }

    @ApiOperation("查询试卷分组")
    @GetMapping("/list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "stage", value = "学段", required = true, dataType = "Integer"),
            @ApiImplicitParam(name = "subjectId", value = "科目", required = true, dataType = "Integer")})
    public Result listPaperGroup(Integer stage, Integer subjectId) {
        List<PaperGroup> paperGroups = paperGroupService.listPaperGroup(stage, subjectId);
        return Result.ok(paperGroups);
    }

    @ApiOperation("根据ID删除试卷分组")
    @DeleteMapping("/delete")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "分组ID", required = true, dataType = "Integer")})
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    @AutoLog("删除试卷分组")
    public Result deletePaperGroup(Integer id) {
        paperGroupService.deletePaperGroup(id);
        return Result.ok();
    }

    @ApiOperation("修改试卷分组排序")
    @PostMapping("/updateSortCode")
    @AutoLog("修改试卷分组排序")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    public Result updateSortCode(@RequestBody @NotEmpty @Valid List<SortCodeVo> vos) {
        paperGroupService.updateSortCode(vos);
        return Result.ok();
    }

    @ApiOperation("根据分组ID清除试卷分组")
    @PostMapping("/clearByGroupId")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "分组ID", required = true, dataType = "Integer")})
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    @AutoLog("根据分组ID清除试卷分组")
    public Result clearByGroupId(Integer id) {
        paperGroupService.clearByGroupId(id);
        return Result.ok();
    }

    @ApiOperation("清除指定试卷的试卷分组")
    @PostMapping("/clearByPaperIds")
    @WithRoles({RoleEnum.SuperManager, RoleEnum.SysManager})
    @AutoLog("清除指定试卷的试卷分组")
    public Result clearByPaperIds(@RequestBody @NotEmpty List<String> ids) {
        paperGroupService.clearByPaperIds(ids);
        return Result.ok();
    }

}
