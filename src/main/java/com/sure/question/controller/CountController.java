package com.sure.question.controller;

import com.sure.common.entity.Result;
import com.sure.question.service.PaperService;
import com.sure.question.service.SchoolBankService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/count")
@Api("统计")
public class CountController {
    private final PaperService paperService;
    private final SchoolBankService schoolBankService;

    @ApiOperation("公共库浏览次数自增1")
    @PutMapping("public/view")
    public Result pubBankPaperViewIncr(String paperId) {
        paperService.paperViewIncr(paperId);
        return Result.ok();
    }

    @ApiOperation("校本库浏览次数自增1")
    @PutMapping("school/view")
    public Result schoolBankPaperViewIncr(Integer sharePaperId) {
        schoolBankService.paperViewIncr(sharePaperId);
        return Result.ok();
    }

    @ApiOperation("公共库下载次数自增1")
    @PutMapping("public/down")
    public Result pubBankPaperDownIncr(String paperId) {
        paperService.paperDownIncr(paperId);
        return Result.ok();
    }

    @ApiOperation("校本库下载次数自增1")
    @PutMapping("school/down")
    public Result schoolBankPaperDownIncr(Integer sharePaperId) {
        schoolBankService.paperDownIncr(sharePaperId);
        return Result.ok();
    }

}
