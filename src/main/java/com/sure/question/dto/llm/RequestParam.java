package com.sure.question.dto.llm;

import com.volcengine.ark.runtime.model.completion.chat.ChatCompletionRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RequestParam {
    private ChatCompletionRequest request;
    private String modelName;
    private String userId;
    private String schoolId;
    private String businessType;
    private String businessId;

    public void check() {
        if (request == null) {
            throw new IllegalArgumentException("请求不能为空");
        }
        if (StringUtils.isBlank(modelName)) {
            throw new IllegalArgumentException("模型不能为空");
        }
        if (StringUtils.isBlank(userId)) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (StringUtils.isBlank(schoolId)) {
            throw new IllegalArgumentException("学校ID不能为空");
        }
        if (StringUtils.isBlank(businessType)) {
            throw new IllegalArgumentException("业务类型不能为空");
        }
        if (StringUtils.isBlank(businessId)) {
            throw new IllegalArgumentException("业务ID不能为空");
        }
    }
}
