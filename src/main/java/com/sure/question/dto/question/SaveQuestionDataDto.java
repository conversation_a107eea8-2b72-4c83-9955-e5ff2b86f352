package com.sure.question.dto.question;

import com.sure.question.entity.Question;
import com.sure.question.entity.QuestionBook;
import com.sure.question.entity.QuestionKnowledge;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.vo.question.QuestionVo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 保存题目数据
 */
@Data
@NoArgsConstructor
public class SaveQuestionDataDto {
    private List<QuestionVo> questionVos = new ArrayList<>();
    private List<Question> questionList = new ArrayList<>();
    private List<SmallQuestion> smallQuestionList = new ArrayList<>();
    private List<QuestionKnowledge> questionKnowledgeList = new ArrayList<>();
    private List<QuestionBook> questionBookList = new ArrayList<>();

    public SaveQuestionDataDto(Collection<QuestionVo> questionVos) {
        this.questionVos.addAll(questionVos);
        for (QuestionVo questionVo : questionVos) {
            questionList.add(questionVo.toEntity(smallQuestionList, questionKnowledgeList, questionBookList));
        }
    }
}
