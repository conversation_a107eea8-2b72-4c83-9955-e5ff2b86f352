package com.sure.question.dto.question;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SelectQuestionVoOption {
    private boolean addKnowledge = true;
    private boolean addChapter = true;
    private boolean addListening = true;
    private boolean addPublicPaper = true;
    private boolean addDivideContent = false;

    /**
     * 知识点、章节、听力、试卷、划题内容都不添加
     */
    public static SelectQuestionVoOption addNothing() {
        SelectQuestionVoOption option = new SelectQuestionVoOption();
        option.setAddKnowledge(false);
        option.setAddChapter(false);
        option.setAddListening(false);
        option.setAddPublicPaper(false);
        option.setAddDivideContent(false);
        return option;
    }

    /**
     * 知识点、章节、听力、试卷、划题内容都添加
     */
    public static SelectQuestionVoOption addAll() {
        SelectQuestionVoOption option = new SelectQuestionVoOption();
        option.setAddKnowledge(true);
        option.setAddChapter(true);
        option.setAddListening(true);
        option.setAddPublicPaper(true);
        option.setAddDivideContent(true);
        return option;
    }
}
