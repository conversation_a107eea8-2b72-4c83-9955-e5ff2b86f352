package com.sure.question.dto.question;

import lombok.Data;

@Data
public class CheckQuestionOption {
    /**
     * 允许题型为空
     */
    private boolean allowQuestionTypeNull = false;
    /**
     * 允许难度为空
     */
    private boolean allowDifficultyNull = false;
    /**
     * 允许答案为空
     */
    private boolean allowAnswerNull = false;
    /**
     * 允许分数为空
     */
    private boolean allowScoreNull = false;
    /**
     * 允许知识点为空
     */
    private boolean allowKnowledgeEmpty = true;

    public static CheckQuestionOption checkAll() {
        CheckQuestionOption option = new CheckQuestionOption();
        option.setAllowQuestionTypeNull(false);
        option.setAllowDifficultyNull(false);
        option.setAllowAnswerNull(false);
        option.setAllowScoreNull(false);
        option.setAllowKnowledgeEmpty(false);
        return option;
    }

    public static CheckQuestionOption checkAllExceptScore() {
        CheckQuestionOption option = new CheckQuestionOption();
        option.setAllowScoreNull(true);
        return option;
    }

    public static CheckQuestionOption skipAll() {
        CheckQuestionOption option = new CheckQuestionOption();
        option.setAllowQuestionTypeNull(true);
        option.setAllowDifficultyNull(true);
        option.setAllowAnswerNull(true);
        option.setAllowScoreNull(true);
        option.setAllowKnowledgeEmpty(true);
        return option;
    }
}
