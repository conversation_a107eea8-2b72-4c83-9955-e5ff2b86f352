package com.sure.question.dto.paper.structure;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckPaperStructureOption {
    /**
     * 允许分数为空
     */
    private boolean allowScoreNull = false;

    public static CheckPaperStructureOption allowScoreNull() {
        return new CheckPaperStructureOption(true);
    }

    public static CheckPaperStructureOption checkAll() {
        return new CheckPaperStructureOption(false);
    }
}
