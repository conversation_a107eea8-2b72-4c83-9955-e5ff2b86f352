package com.sure.question.dto.paper.structure;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 大题
 */
@Data
@NoArgsConstructor
public class StructureTopic {
    /**
     * 大题号
     */
    private Integer code;
    /**
     * 中文大题号
     */
    private String codeInChinese;
    /**
     * 大题名称
     */
    private String name;
    /**
     * 大题说明
     */
    private String description;
    /**
     * 旧版大题说明，含名称及说明
     */
    private String content;
    /**
     * 显示题目分数
     */
    private Boolean showQuestionScore;
    /**
     * 题目
     */
    private List<StructureQuestion> questions = new ArrayList<>();
}
