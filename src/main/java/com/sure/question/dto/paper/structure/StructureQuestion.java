package com.sure.question.dto.paper.structure;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 题目
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StructureQuestion {
    /**
     * 题目Id
     */
    private String id;
    /**
     * 题号，为0表示小题展开，展开时各小题号对应试卷上题号。如某英语试卷，31-40是完形填空，此时题号为0，小题号分别为31-40
     */
    private Integer code;
    /**
     * 别名
     */
    private String alias;
    /**
     * 分数
     */
    private Float score;
    /**
     * 小题
     */
    private List<StructureBranch> branches = new ArrayList<>();
    /**
     * 是否客观题
     */
    private Boolean isObjective;

    /**
     * 小题是否展开
     */
    @JsonIgnore
    @JSONField(serialize = false)
    public boolean isBranchesExpanded() {
        return code != null && code == 0;
    }
}
