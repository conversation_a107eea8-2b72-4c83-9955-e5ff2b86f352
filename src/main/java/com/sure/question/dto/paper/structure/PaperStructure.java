package com.sure.question.dto.paper.structure;

import cn.hutool.core.collection.CollUtil;
import com.sure.common.exception.ApiException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 试卷结构
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaperStructure {
    /**
     * 分卷列表，如：第一卷，第二卷；选择题卷、非选择题卷
     */
    private List<StructureVolume> volumes = new ArrayList<>();

    /**
     * 检查试卷结构
     */
    public void check(CheckPaperStructureOption option) {
        if (CollUtil.isEmpty(volumes)) {
            return;
        }

        Set<Integer> topicCodeSet = new HashSet<>();
        Set<Integer> questionCodeSet = new HashSet<>();
        Set<String> questionIdSet = new HashSet<>();
        Set<String> branchIdSet = new HashSet<>();

        for (StructureVolume volume : volumes) {
            if (volume == null) {
                throw new ApiException("分卷不能为空");
            }
            if (CollUtil.isEmpty(volume.getTopics())) {
                throw new ApiException("分卷中无大题");
            }
            for (StructureTopic topic : volume.getTopics()) {
                if (topic == null) {
                    throw new ApiException("大题不能为空");
                }
                if (topic.getCode() == null) {
                    throw new ApiException("大题号不能为空");
                } else if (topic.getCode() <= 0) {
                    throw new ApiException("大题号必须大于0");
                }
                if (StringUtils.isBlank(topic.getName())) {
                    throw new ApiException("大题名不能为空");
                }
                if (topicCodeSet.contains(topic.getCode())) {
                    throw new ApiException("大题号不能重复");
                }
                topicCodeSet.add(topic.getCode());
                if (CollUtil.isEmpty(topic.getQuestions())) {
                    throw new ApiException("大题中无题目");
                }
                for (StructureQuestion question : topic.getQuestions()) {
                    if (question == null) {
                        throw new ApiException("题目不能为空");
                    }

                    if (StringUtils.isBlank(question.getId())) {
                        throw new ApiException("题目Id不能为空");
                    }
                    if (questionIdSet.contains(question.getId())) {
                        throw new ApiException("题目Id不能重复");
                    }
                    questionIdSet.add(question.getId());

                    if (question.getCode() == null) {
                        throw new ApiException("题号不能为空");
                    } else if (question.getCode() < 0) {
                        throw new ApiException("题号不能小于0");
                    }
                    if (!question.isBranchesExpanded()) {
                        if (questionCodeSet.contains(question.getCode()))  {
                            throw new ApiException("题号不能重复");
                        }
                        questionCodeSet.add(question.getCode());
                    }

                    if (CollUtil.isEmpty(question.getBranches())) {
                        throw new ApiException("题目中无小题");
                    }
                    float branchScoreSum = 0;
                    Set<Integer> branchCodeSet = new HashSet<>();
                    for (StructureBranch branch : question.getBranches()) {
                        if (branch == null) {
                            throw new ApiException("小题不能为空");
                        }

                        if (StringUtils.isBlank(branch.getId())) {
                            throw new ApiException("小题Id不能为空");
                        }
                        if (branchIdSet.contains(branch.getId())) {
                            throw new ApiException("小题Id不能重复");
                        }
                        branchIdSet.add(branch.getId());

                        if (branch.getCode() == null) {
                            throw new ApiException("小题号不能为空");
                        } else if (branch.getCode() <= 0) {
                            throw new ApiException("小题号必须大于0");
                        }
                        if (question.isBranchesExpanded()) {
                            if (questionCodeSet.contains(branch.getCode()))  {
                                throw new ApiException("题号不能重复");
                            }
                            questionCodeSet.add(branch.getCode());
                        } else {
                            if (branchCodeSet.contains(branch.getCode())) {
                                throw new ApiException("小题号不能重复");
                            }
                            branchCodeSet.add(branch.getCode());
                        }

                        if (branch.getScore() == null || branch.getScore() == 0) {
                            if (!option.isAllowScoreNull()) {
                                throw new ApiException("小题分数不能为空");
                            }
                        } else if (branch.getScore() < 0) {
                            throw new ApiException("小题分数不能小于0");
                        } else {
                            branchScoreSum += branch.getScore();
                        }
                    }

                    if (question.getScore() == null || question.getScore() == 0) {
                        if (!option.isAllowScoreNull()) {
                            throw new ApiException("题目分数不能为空");
                        }
                    } else if (question.getScore() < 0) {
                        throw new ApiException("题目分数不能小于0");
                    } else if (branchScoreSum != question.getScore()) {
                        if (!option.isAllowScoreNull()) {
                            throw new ApiException("小题分数之和与题目分数不相等");
                        }
                    }
                }
            }
        }
    }

    /**
     * 提取题目Id与小题Id映射
     */
    public Map<String, Set<String>> extractQuestionIdBranchIdsMap() {
        Map<String, Set<String>> questionIdBranchIdsMap = new LinkedHashMap<>();
        for (StructureVolume volume : volumes) {
            for (StructureTopic topic : volume.getTopics()) {
                for (StructureQuestion question : topic.getQuestions()) {
                    Set<String> branchIdSet = new LinkedHashSet<>();
                    questionIdBranchIdsMap.put(question.getId(), branchIdSet);
                    for (StructureBranch branch : question.getBranches()) {
                        branchIdSet.add(branch.getId());
                    }
                }
            }
        }
        return questionIdBranchIdsMap;
    }

    /**
     * 提取题目Id与名称映射
     */
    public Map<String, String> extractQuestionIdNameMap() {
        Map<String, String> questionIdNameMap = new LinkedHashMap<>();
        for (StructureVolume volume : volumes) {
            for (StructureTopic topic : volume.getTopics()) {
                for (StructureQuestion question : topic.getQuestions()) {
                    String questionName;
                    if (question.getCode() == null) {
                        questionName = "无题号";
                    } else if (question.getCode() == 0) {
                        StructureBranch firstBranch = question.getBranches().get(0);
                        StructureBranch lastBranch = question.getBranches().get(question.getBranches().size() - 1);
                        String firstBranchName = firstBranch.getCode() == null ? "" : firstBranch.getCode().toString();
                        String lastBranchName = lastBranch.getCode() == null ? "" : lastBranch.getCode().toString();
                        questionName = firstBranchName + "-" + lastBranchName + "题";
                    } else {
                        questionName = question.getCode() + "题";
                    }
                    questionIdNameMap.put(question.getId(), questionName);
                }
            }
        }
        return questionIdNameMap;
    }

    /**
     * 比较结构题目与试卷题目
     */
    public boolean compareStructureQuestionsWithPaperQuestions(Map<String, Set<String>> paperQuestionIdBranchIdsMap) {
        Map<String, Set<String>> structureQuestionIdBranchIdsMap = extractQuestionIdBranchIdsMap();
        if (structureQuestionIdBranchIdsMap.size() != paperQuestionIdBranchIdsMap.size()) {
            return false;
        }
        for (Map.Entry<String, Set<String>> entry : structureQuestionIdBranchIdsMap.entrySet()) {
            String questionId = entry.getKey();
            Set<String> paperBranchIds = paperQuestionIdBranchIdsMap.get(questionId);
            if (paperBranchIds == null) {
                return false;
            }
            Set<String> branchIds = entry.getValue();
            if (!branchIds.containsAll(paperBranchIds)) {
                return false;
            }
        }
        return true;
    }

}
