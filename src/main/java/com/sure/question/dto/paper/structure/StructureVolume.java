package com.sure.question.dto.paper.structure;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 分卷
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StructureVolume {
    /**
     * 说明内容
     */
    private String content;
    /**
     * 大题列表
     */
    private List<StructureTopic> topics = new ArrayList<>();
}
