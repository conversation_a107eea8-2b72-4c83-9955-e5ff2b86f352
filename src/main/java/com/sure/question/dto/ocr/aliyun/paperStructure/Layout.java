package com.sure.question.dto.ocr.aliyun.paperStructure;

import com.sure.question.dto.ocr.aliyun.Point;
import lombok.Data;

import java.util.List;

@Data
public class Layout {
    /**
     * 类型
     * text：普通文字，special_text：特殊文字，table：表格，head：页眉，foot：页脚，side_column：侧栏，bold：黑体，complex：特殊体
     */
    private String layout_type;
    /**
     * 外矩形四点（左上、右上、右下、左下）
     */
    private List<Point> pos;
}
