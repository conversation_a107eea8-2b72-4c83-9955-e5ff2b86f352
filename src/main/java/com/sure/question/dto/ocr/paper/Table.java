package com.sure.question.dto.ocr.paper;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Table implements IElement{
    private int left;
    private int right;
    private int top;
    private int bottom;

    private List<WordInfo> words = new ArrayList<>();
    private List<List<List<WordInfo>>> rows = new ArrayList<>();

    public int getElementType() {
        return 3;
    }
}
