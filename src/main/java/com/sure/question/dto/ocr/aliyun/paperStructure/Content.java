package com.sure.question.dto.ocr.aliyun.paperStructure;

import com.sure.question.dto.ocr.aliyun.Point;
import lombok.Data;

import java.util.List;

@Data
public class Content {
    /**
     * 内容类型
     * 0：图片；1：文本；2：公式
     */
    private int type;
    /**
     * 整题文本信息，可能包含 latex 公式
     */
    private String string;
    /**
     * 置信度
     */
    private float prob;
    private String option;
    /**
     * 外矩形四点
     */
    private List<Point> pos;

    public boolean isFormula() {
        return type == 2;
    }
}
