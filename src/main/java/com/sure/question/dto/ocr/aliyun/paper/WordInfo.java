package com.sure.question.dto.ocr.aliyun.paper;

import com.sure.question.dto.ocr.aliyun.Point;
import lombok.Data;

import java.util.List;

@Data
public class WordInfo implements IElement {
    private int x;
    private int y;
    private int width;
    private int height;
    private float angle;
    private int direction;
    private List<Point> pos;
    private float prob;
    /**
     * 文字属性分类
     * 0：中文印刷，1：拉丁语种，2：手写体，3：韩语，4：泰文，5：公式
     */
    private int recClassify;
    /**
     * 文字块内容
     */
    private String word;
    /**
     * 单字信息
     */
    private List<CharInfo> charInfo;

    /**
     * 位置（自定义属性，接口返回数据中不存在）
     */
    private int left;
    private int right;
    private int top;
    private int bottom;

    /**
     * 是否公式（自定义属性，接口返回数据中不存在）
     */
    private boolean isFormula;

    public boolean isRecClassifyFormula() {
        return recClassify == 5 || recClassify == 51;
    }

    public int getElementType() {
        return 1;
    }
}
