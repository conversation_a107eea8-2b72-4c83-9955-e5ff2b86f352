package com.sure.question.dto.ocr.paperStructure;

import com.sure.question.dto.ocr.Point;
import lombok.Data;

import java.util.List;

@Data
public class Element {
    /**
     * 题目元素类型
     * 0：题干；1：选项；2：解析；3：答案
     */
    private int type;
    /**
     * 整题文本信息，可能包含 latex 公式
     */
    private String text;
    /**
     * 各区域外矩形四点
     */
    private List<List<Point>> pos_list;
    /**
     * 内容
     */
    private List<Content> content_list;
}
