package com.sure.question.dto.ocr;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class Figure {
    /**
     * 配图类型
     * subject_sline：分栏线，竖线；subject_bline：空格线/下划线，竖线；subject_bracket：括号；subject_quad：
     * 田字格和四线三格；subject_table：表格；subject_pattern：图案；subject_big_bracket：大括号；subject_match_question：
     * 连线题；subject_question：非材料题；subject_material：材料题
     */
    private String type;
    /**
     * 左上角横坐标
     */
    private int x;
    /**
     * 左上角纵坐标
     */
    private int y;
    /**
     * 宽度
     */
    private int w;
    /**
     * 高度
     */
    private int h;
    /**
     * 图案左上、右上、右下、左下四点坐标
     */
    private List<Point> points;
    /**
     * 图案坐标信息
     */
    private Box box;

    public boolean isImage() {
        return StringUtils.equals(type, "subject_pattern");
    }

    public boolean isTable() {
        return StringUtils.equals(type, "subject_table");
    }
}
