package com.sure.question.dto.ocr.paperStructure;

import com.sure.question.dto.ocr.Figure;
import lombok.Data;

import java.util.List;

@Data
public class Body {
    private String algo_version;
    private String prism_version;
    private String prism_wnum;
    /**
     * 页码
     */
    private int page_id;
    /**
     * 页标题
     */
    private String page_title;
    /**
     * 矫正后图片宽度
     */
    private int width;
    /**
     * 矫正后图片高度
     */
    private int height;
    /**
     * 原图宽度
     */
    private int orgWidth;
    /**
     * 原图高度
     */
    private int orgHeight;
    /**
     * 版面信息
     */
    private List<Layout> doc_layout;
    /**
     * 特殊文字信息
     */
    private List<Layout> doc_sptext;
    /**
     * 子区域
     */
    private List<Layout> doc_subfield;
    /**
     * 图案信息
     */
    private List<Figure> figure;
    /**
     * 题型大类信息
     */
    private List<Part> part_info;
}
