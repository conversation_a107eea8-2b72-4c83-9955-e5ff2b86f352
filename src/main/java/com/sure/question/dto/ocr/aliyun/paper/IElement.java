package com.sure.question.dto.ocr.aliyun.paper;

public interface IElement {
    int getLeft();
    void setLeft(int left);
    int getRight();
    void setRight(int right);
    int getTop();
    void setTop(int top);
    int getBottom();
    void setBottom(int bottom);

    int getElementType();
    default boolean isWord() {
        return getElementType() == 1;
    }
    default boolean isImage() {
        return getElementType() == 2;
    }
    default boolean isTable() {
        return getElementType() == 3;
    }
}
