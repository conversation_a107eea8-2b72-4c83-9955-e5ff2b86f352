package com.sure.question.dto.ocr.paper;

import com.sure.question.dto.ocr.Figure;
import lombok.Data;

import java.util.List;

@Data
public class PaperBody {
    private String algo_version;
    private String prism_version;
    private String prism_wnum;
    /**
     * 矫正后图片宽度
     */
    private int width;
    /**
     * 矫正后图片高度
     */
    private int height;
    /**
     * 原图宽度
     */
    private int orgWidth;
    /**
     * 原图高度
     */
    private int orgHeight;
    /**
     * 角度
     */
    private float angle;
    /**
     * 内容汇总
     */
    private String content;
    /**
     * 图案
     */
    private List<Figure> figure;
    /**
     * 文字块
     */
    private List<WordInfo> prism_wordsInfo;
}
