package com.sure.question.dto.ocr.paperStructure;

import com.sure.question.dto.ocr.Point;
import lombok.Data;

import java.util.List;

@Data
public class Subject {
    /**
     * 在题型大类中的序号
     */
    private int index;
    /**
     * 题目类型
     * 0：选择题；1：填空题；2：阅读理解（阅读+问答选择）；3：完型填空（阅读+选择）；4：阅读填空（阅读+填空）；5：问答题；6：选择题，多选多；7：填空、选择题混合；8：应用题；9：判断题；10：作图题；11：材料题；12：计算题；13：连线题；14：作文题；15：解答题；16：其他；17：图；18：表格
     */
    private int type;
    /**
     * 整题文本信息，可能包含 latex 公式
     */
    private String text;
    /**
     * 置信度
     */
    private float prob;
    /**
     * 选项个数
     */
    private int num_choices;
    /**
     * 题目各区域外矩形四点
     */
    private List<List<Point>> pos_list;
    /**
     * 题目元素
     */
    private List<Element> element_list;
    /**
     * 答案
     */
    private List<List<Point>> answer_list;
    /**
     * 图案
     */
    private List<List<Point>> figure_list;
    /**
     * 表格
     */
    private List<List<Point>> table_list;
}
