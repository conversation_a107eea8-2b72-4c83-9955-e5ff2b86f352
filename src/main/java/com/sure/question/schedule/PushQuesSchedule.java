package com.sure.question.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sure.question.entity.ExamsubjectPushQues;
import com.sure.question.entity.PushQuesTask;
import com.sure.question.enums.PushQuesTaskStatusEnum;
import com.sure.question.mapper.ExamSubjectPushQuesMapper;
import com.sure.question.mapper.PushQuesTaskMapper;
import com.sure.question.service.ExamSubjectPushQuesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/2/17 11:39
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PushQuesSchedule {
    private final PushQuesTaskMapper pushQuesTaskMapper;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ExamSubjectPushQuesService examsubjectPushQuesService;
    private final ExamSubjectPushQuesMapper examSubjectPushQuesMapper;

    // 每两分钟执行一次
    @Scheduled(cron = "0 0/2 * * * ?")
    public void pushQuesTask() {
        // 更新状态为处理中，且更新时间在30分钟之前的任务的状态为待处理
        pushQuesTaskMapper.update(null, new LambdaUpdateWrapper<PushQuesTask>()
                .eq(PushQuesTask::getStatus, PushQuesTaskStatusEnum.HANDLING.getCode())
                .lt(PushQuesTask::getUpdateTime, new Date(System.currentTimeMillis() - 30 * 60 * 1000))
                .set(PushQuesTask::getStatus, PushQuesTaskStatusEnum.PENDING.getCode())
                .set(PushQuesTask::getUpdateTime, new Date()));

        List<PushQuesTask> taskList = pushQuesTaskMapper.selectList(new LambdaQueryWrapper<PushQuesTask>()
                .eq(PushQuesTask::getStatus, PushQuesTaskStatusEnum.PENDING.getCode())
                .ge(PushQuesTask::getCreateTime, DateUtil.offsetDay(new Date(), -7)));

        if (taskList.isEmpty()) {
            return;
        }
        for (PushQuesTask task : taskList) {
            handleTask(task);
        }
    }

    private void handleTask(PushQuesTask task) {
        // 更新状态为处理中，防止重复处理
        int updateCount = pushQuesTaskMapper.update(null, new LambdaUpdateWrapper<PushQuesTask>()
                .eq(PushQuesTask::getId, task.getId())
                .eq(PushQuesTask::getStatus, PushQuesTaskStatusEnum.PENDING.getCode())
                .set(PushQuesTask::getStatus, PushQuesTaskStatusEnum.HANDLING.getCode())
                .set(PushQuesTask::getUpdateTime, new Date()));
        if (updateCount == 0) {
            return;
        }
        List<ExamsubjectPushQues> examsubjectPushQues;
        try {
            examsubjectPushQues = examsubjectPushQuesService.list(new LambdaQueryWrapper<ExamsubjectPushQues>()
                    .eq(ExamsubjectPushQues::getPaperId, task.getPaperId())
                    .eq(ExamsubjectPushQues::getExamSubjectId, StringUtils.EMPTY));
            if (!examsubjectPushQues.isEmpty()) {
                for (ExamsubjectPushQues item : examsubjectPushQues) {
                    item.setExamSubjectId(task.getExamSubjectId());
                }
            } else {
                examsubjectPushQues = examsubjectPushQuesService.generatePaperPushQues(task.getPaperId(), task.getExamSubjectId(), 10, null);
            }
            // 生成推题数据
        } catch (Exception e) {
            // 更新状态为处理失败
            pushQuesTaskMapper.update(null, new LambdaUpdateWrapper<PushQuesTask>()
                    .eq(PushQuesTask::getId, task.getId())
                    .eq(PushQuesTask::getStatus, PushQuesTaskStatusEnum.HANDLING.getCode())
                    .set(PushQuesTask::getStatus, PushQuesTaskStatusEnum.FAIL.getCode())
                    .set(PushQuesTask::getUpdateTime, new Date()));
            log.error("推题任务处理失败，task: " + JSONUtil.toJsonStr(task), e);
            return;
        }

        // 查看同examSubjectId 2两秒内有没有在处理的的任务，如果有，则等待2秒后再处理
        String redisKey = "Question:pushQuesTask:" + task.getExamSubjectId();
        Boolean absent = redisTemplate.opsForValue().setIfAbsent(redisKey, 0);
        if (Boolean.FALSE.equals(absent)) {
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
            }
        } else {
            redisTemplate.expire(redisKey, 2, TimeUnit.SECONDS);
        }

        // 生成推题数据成功，检查一下任务的最新状态，如果是处理中，则插入数据，如果不是，则直接返回
        PushQuesTask newTask = pushQuesTaskMapper.selectById(task.getId());
        if (newTask.getStatus() != PushQuesTaskStatusEnum.HANDLING.getCode()) {
            return;
        }
        // 插入推题数据
        examSubjectPushQuesMapper.deleteByPaperIdAndExamSubjectId(task.getPaperId(), task.getExamSubjectId());
        if (CollUtil.isNotEmpty(examsubjectPushQues)) {
            examsubjectPushQuesService.saveBatch(examsubjectPushQues);
        }
        // 更新状态为处理成功
        pushQuesTaskMapper.update(null, new LambdaUpdateWrapper<PushQuesTask>()
                .eq(PushQuesTask::getId, task.getId())
                .eq(PushQuesTask::getStatus, PushQuesTaskStatusEnum.HANDLING.getCode())
                .set(PushQuesTask::getStatus, PushQuesTaskStatusEnum.SUCCESS.getCode())
                .set(PushQuesTask::getUpdateTime, new Date()));
    }
}
