package com.sure.question.schedule;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sure.question.entity.CoachBookCustomPaper;
import com.sure.question.mapper.CoachBookCustomPaperMapper;
import com.sure.question.service.CoachBookCustomPaperService;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/24 15:52
 */
@Component
@RequiredArgsConstructor
public class PdfSchedule {

    private final CoachBookCustomPaperMapper coachBookCustomPaperMapper;
    private final CoachBookCustomPaperService coachBookCustomPaperService;

    /**
     * 生成自定义试卷pdf，每5秒执行一次
     */
    @Scheduled(cron = "0/5 * * * * ?")
    private void generateCustomPaperPdf() {
        // 更新时间为30分钟前的状态为生成中的任务状态改为待生成
        coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                .eq(CoachBookCustomPaper::getPdfStatus, 1)
                .lt(CoachBookCustomPaper::getUpdateTime, DateUtil.offsetMinute(new Date(), -30))
                .set(CoachBookCustomPaper::getPdfStatus, 0));

        // 查出状态为0的数据，根据ID排序，每次取5条
        List<CoachBookCustomPaper> coachBookCustomPaperList = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>()
                .eq(CoachBookCustomPaper::getPdfStatus, 0)
                .orderByAsc(CoachBookCustomPaper::getId)
                .last("limit 5"));
        for (CoachBookCustomPaper coachBookCustomPaper : coachBookCustomPaperList) {
            coachBookCustomPaperService.generateCustomPaperPdf(coachBookCustomPaper);
        }
    }

}
