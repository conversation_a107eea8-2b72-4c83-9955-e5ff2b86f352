package com.sure.question.service;

import com.sure.common.entity.UserInfo;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.PaperMain;
import com.sure.question.vo.TokenUserVo;

public interface PermissionService {
    /**
     * 获取试卷
     */
    PaperMain getPaperForCheckPermission(String paperId);

    /**
     * 是否具有查看试卷信息权限
     */
    boolean hasViewPaperInfoPermission(String paperId, UserInfo userInfo);

    /**
     * 检查具有查看试卷信息权限
     */
    default void checkViewPaperInfoPermission(String paperId, UserInfo userInfo) {
        if (!hasViewPaperInfoPermission(paperId, userInfo)) {
            throw new ApiException("无查看试卷信息权限");
        }
    }

    /**
     * 是否具有查看试卷内容权限
     */
    boolean hasViewPaperContentPermission(String paperId, int paperGradeLevel, int paperSubjectId, String paperBank, String paperUserId, TokenUserVo userVo);

    /**
     * 是否具有查看试卷内容权限
     */
    default boolean hasViewPaperContentPermission(PaperMain paperMain, TokenUserVo userVo) {
        return hasViewPaperContentPermission(paperMain.getId(), paperMain.getGradeLevel(), paperMain.getSubjectId(), paperMain.getPaperBank(), paperMain.getUserId(), userVo);
    }

    /**
     * 是否具有查看试卷内容权限
     */
    default boolean hasViewPaperContentPermission(String paperId, TokenUserVo userVo) {
        PaperMain paper = getPaperForCheckPermission(paperId);
        return paper != null && hasViewPaperContentPermission(paper, userVo);
    }

    /**
     * 检查具有查看试卷内容权限
     */
    default void checkViewPaperContentPermission(PaperMain paperMain, TokenUserVo userVo) {
        if (paperMain == null) {
            throw new ApiException("试卷不存在");
        }
        if (!hasViewPaperContentPermission(paperMain, userVo)) {
            throw new ApiException("无查看试卷权限");
        }
    }

    /**
     * 检查具有查看试卷内容权限
     */
    default void checkViewPaperContentPermission(String paperId, TokenUserVo userVo) {
        if (!hasViewPaperContentPermission(paperId, userVo)) {
            throw new ApiException("无查看试卷权限");
        }
    }


    /**
     * 是否具有修改试卷权限
     */
    boolean hasUpdatePaperPermission(int paperGradeLevel, int paperSubjectId, String paperBank, String paperUserId, TokenUserVo userVo);

    /**
     * 是否具有修改试卷权限
     */
    default boolean hasUpdatePaperPermission(PaperMain paperMain, TokenUserVo userVo) {
        return hasUpdatePaperPermission(paperMain.getGradeLevel(), paperMain.getSubjectId(), paperMain.getPaperBank(), paperMain.getUserId(), userVo);
    }

    /**
     * 是否具有修改试卷权限
     */
    default boolean hasUpdatePaperPermission(String paperId, TokenUserVo userVo) {
        PaperMain paper = getPaperForCheckPermission(paperId);
        return paper != null && hasUpdatePaperPermission(paper, userVo);
    }

    /**
     * 检查具有修改试卷权限
     */
    default void checkUpdatePaperPermission(PaperMain paperMain, TokenUserVo userVo) {
        if (paperMain == null) {
            throw new ApiException("试卷不存在");
        }
        if (!hasUpdatePaperPermission(paperMain, userVo)) {
            throw new ApiException("无修改试卷权限");
        }
    }

    /**
     * 检查具有修改试卷权限
     */
    default void checkUpdatePaperPermission(String paperId, TokenUserVo userVo) {
        if (!hasUpdatePaperPermission(paperId, userVo)) {
            throw new ApiException("无查看试卷权限");
        }
    }

    /**
     * 是否具有学段学科编辑权限
     */
    boolean hasEditorPermission(String userId, int gradeLevel, int subjectId);

    /**
     * 是否具有学段学科编辑权限
     */
    boolean hasEditorPermission(TokenUserVo userVo, int gradeLevel, int subjectId);

    /**
     * 是否具有学段学科编辑权限
     */
    default boolean hasEditorPermission(TokenUserVo userVo, String paperId) {
        PaperMain paper = getPaperForCheckPermission(paperId);
        return paper != null && hasEditorPermission(userVo, paper.getGradeLevel(), paper.getSubjectId());
    }
    
    /**
     * 检查学段学科编辑权限
     */
    default void checkEditorPermission(TokenUserVo userVo, int gradeLevel, int subjectId) {
        if (!hasEditorPermission(userVo, gradeLevel, subjectId)) {
            throw new ApiException("无权限");
        }
    }
    
    /**
     * 检查学段学科编辑权限
     */
    default void checkEditorPermission(String userId, int gradeLevel, int subjectId) {
        if (!hasEditorPermission(userId, gradeLevel, subjectId)) {
            throw new ApiException("无权限");
        }
    }

    /**
     * 检查学段学科编辑权限
     */
    default void checkEditorPermission(TokenUserVo userVo, String paperId) {
        if (!hasEditorPermission(userVo, paperId)) {
            throw new ApiException("无权限");
        }
    }
}
