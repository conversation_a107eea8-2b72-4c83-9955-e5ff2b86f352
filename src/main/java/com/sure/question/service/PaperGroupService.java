package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.PaperGroup;
import com.sure.question.vo.papergroup.SortCodeVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
public interface PaperGroupService extends IService<PaperGroup> {

    /**
     * 添加试卷分组
     *
     * @param stage     学段
     * @param subjectId 科目ID
     * @param groupName 分组名称
     * @param sortCode  排序
     * @param userId    用户id
     */
    void addPaperGroup(Integer stage, Integer subjectId, String groupName, Integer sortCode, String userId);

    /**
     * 查询试卷分组
     *
     * @param stage     学段
     * @param subjectId 科目ID
     * @return 试卷分组列表
     */
    List<PaperGroup> listPaperGroup(Integer stage, Integer subjectId);

    /**
     * 删除试卷分组
     *
     * @param id 主键
     */
    void deletePaperGroup(Integer id);

    /**
     * 更新排序
     *
     * @param vos 排序列表
     */
    void updateSortCode(List<SortCodeVo> vos);

    /**
     * 清空分组下的试卷
     *
     * @param groupId 分组id
     */
    void clearByGroupId(Integer groupId);

    /**
     * 清空分组下的试卷
     *
     * @param ids 试卷id列表
     */
    void clearByPaperIds(List<String> ids);
}
