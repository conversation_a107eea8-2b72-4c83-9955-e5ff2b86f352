package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.sure.question.entity.CoachBook;
import com.sure.question.entity.CoachStuCard;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachstucard.*;

import java.util.List;

public interface CoachStuCardService extends IService<CoachStuCard> {
    void generateCardBatch(Integer coachBookId, Integer total, String userId);

    List<CoachStuCard> listCardByNos(List<String> cardNos);

    void activeCardBatch(String parentId, String studentId, List<String> cardNos);

    /**
     * 内定学校家长激活教辅
     */
    void addCustomCardBatch(List<CustomCardVo> customCardVos);

    List<CoachBook> listCoachBook(String schoolId, String studentId, Integer semesterId, Integer term);

    List<CardCoachBookInfoVo> cardCoachBookInfos(String cardNos, String parentId);

    ActiveVo getActiveStatus(String studentId);

    /**
     * 查询指定学年学期激活指定科目教辅的学生ID列表
     *
     * @param schoolId   学校ID
     * @param semesterId 学年学期ID
     * @param term       学期
     * @param subjectId  科目ID
     * @param studentIds 学生ID列表
     * @return 学生ID列表
     */
    List<StudentSubjectVo> listActiveStudentIds(String schoolId, Integer semesterId, Integer term, Integer subjectId, List<String> studentIds);

    /**
     * 查询教辅激活码列表
     */
    PageInfo<CardInfoVo> listCard(Integer coachBookId, String coachBookName, Integer subjectId, Integer gradeLevel, Integer gradeId, Integer semesterId,
                                  Integer term, String cardNo, String studentName, String schoolId, Long regionId, Integer status, String beginTime, String endTime,
                                  Integer pageNum, Integer pageSize);

    PageInfo<CardInfoVo> listCardByOrg(Integer coachBookId, String coachBookName, Integer subjectId, Integer gradeLevel,
                                       Integer gradeId, Integer semesterId, Integer term, String cardNo, String studentName, String schoolId,
                                       Integer status, String beginTime, String endTime, Integer pageNum, Integer pageSize, TokenUserVo userVo);

    /**
     * 超管查询教辅激活统计（按教辅，查全部或一所学校）
     */
    List<ActiveStatVo> activeStat(Integer semesterId, Integer term, Integer subjectId, Integer gradeLevel, Integer gradeId, String coachBookName, String schoolId, Long regionId);

    /**
     * 机构查询教辅激活统计（按教辅，查机构下属全部或一所学校）
     */
    List<ActiveStatVo> activeStatByOrg(Integer semesterId, Integer term, Integer subjectId, Integer gradeLevel, Integer gradeId,
                                       String coachBookName, String schoolId, TokenUserVo userVo);

    /**
     * 超管查询教辅学校激活统计（按学校，查全部或一所学校）
     */
    List<CoachSchoolStatVo> schoolStat(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, String schoolId, Long regionId);

    /**
     * 机构查询教辅学校激活统计（按学校，查机构下属全部或一所学校）
     */
    List<CoachSchoolStatVo> schoolStatByOrg(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, String schoolId, TokenUserVo userVo);

    /**
     * 超管查询学情分析资源包激活数量统计
     */
    CountStatVo countStat(Integer semesterId, Integer term, Integer subjectId, Integer gradeId, String coachBookName, String schoolId, Long regionId);

    /**
     * 机构查询学情分析资源包激活数量统计（查机构下属全部或一所学校）
     */
    CountStatVo countStatByOrg(Integer semesterId, Integer term, Integer subjectId, Integer gradeId, String coachBookName, String schoolId, TokenUserVo userVo);

    boolean checkoutSchoolIdsByRegionIdAndSchoolId(Long regionId, String schoolId, List<String> outSchoolIds);
}
