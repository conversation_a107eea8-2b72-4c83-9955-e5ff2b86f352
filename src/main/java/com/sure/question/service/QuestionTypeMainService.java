package com.sure.question.service;

import com.sure.question.entity.QuestionTypeMain;

import java.util.List;

public interface QuestionTypeMainService {
    List<QuestionTypeMain> findByStageAndSubject(int gradeLevelId, int subjectId);

    void add(QuestionTypeMain questionTypeMain, String userId);

    void update(QuestionTypeMain questionTypeMain, String userId);

    void del(int gradeLevelId, int subjectId, int id, String userId);
}
