package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.dto.paper.structure.CheckPaperStructureOption;
import com.sure.question.dto.question.CheckQuestionOption;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.PaperQuestion;
import com.sure.question.entity.Question;
import com.sure.question.vo.ques.UpdateQues;
import com.sure.question.vo.question.ChangeChapterParam;
import com.sure.question.vo.question.ChangeKnowledgeParam;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.UserQuestionVo;

import java.util.Collection;
import java.util.List;

public interface QuestionService extends IService<Question> {

    /**
     * 改变题目收藏状态
     */
    Integer changeFav(Long quesId, String userId, String schoolId);

    /**
     * 多条件分页查询收藏题目
     */
    IPage<UserQuestionVo> getFavorite(String userId, int page, int size, int stage, int subjectId, Integer type, Integer difficulty);



    /**
     * 生成一批雪花Id
     */
    List<String> generateIds(int size);

    /**
     * 保存试卷所属题目，不添加试卷题目关联
     */
    void saveQuestionsOfPaper(List<QuestionVo> questionVos, PaperMain paper);

    /**
     * 创建题目试卷关联
     */
    List<PaperQuestion> createPaperQuestionRelations(String paperId, Collection<String> questionIds);

    /**
     * 保存试卷题目，并添加试卷题目关联、更新试卷结构
     */
    void addPaperQuestions(List<QuestionVo> questionVos, CheckQuestionOption checkQuestionOption,
                           PaperMain paper, String paperStructure, CheckPaperStructureOption checkStructureOption);

    /**
     * 修改试卷题目
     */
    void changePaperQuestions(Collection<QuestionVo> questionVos, CheckQuestionOption checkQuestionOption, boolean updateDivideContent,
                                     String paperId, String paperStructure, CheckPaperStructureOption checkStructureOption);

    /**
     * 删除试卷题目
     */
    void deletePaperQuestions(Collection<String> questionIds, String paperId, String paperStructure, CheckPaperStructureOption checkStructureOption);

    /**
     * 编辑修改题目
     */
    QuestionVo editorChangeQuestion(QuestionVo questionVo, String editorId);

    /**
     * 编辑修改题目知识点
     */
    void editorChangeQuestionsKnowledge(List<ChangeKnowledgeParam> changeKnowledgeParamList, String editorId);

    /**
     * 编辑修改题目章节
     */
    void editorChangeQuestionsChapter(List<ChangeChapterParam> changeChapterParamList, String editorId);



    /**
     * 修改题目的作答次数和得分率
     */
    void updateQues(List<UpdateQues> updateQuesList);
}
