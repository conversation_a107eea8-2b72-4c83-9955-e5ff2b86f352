package com.sure.question.service;

import com.sure.question.dto.paper.structure.CheckPaperStructureOption;
import com.sure.question.vo.question.QuestionVo;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

public interface PaperQuestionService {
    /**
     * 查出试卷中题目Id与小题Id映射
     */
    Map<String, Set<String>> getPaperQuestionIdBranchIdsMap(String paperId);

    /**
     * 检查试卷结构
     */
    void checkPaperStructure(String paperId, String paperStructure, CheckPaperStructureOption option,
                             Collection<QuestionVo> addQuestions, Collection<QuestionVo> changeQuestions, Collection<String> deleteQuestionIds);
}
