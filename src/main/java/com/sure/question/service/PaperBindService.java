package com.sure.question.service;

import com.sure.question.entity.PaperMain;
import com.sure.question.vo.paper.bind.BindPaperStructure;

import java.util.List;

public interface PaperBindService {

    /**
     * 阅卷服务获取试卷结构数据
     * @param paperId 试卷Id
     * @param addQuestionType 是否添加题型
     * @param addKnowledge 是否添加知识点
     */
    BindPaperStructure extractMarkPaperStructure(String paperId, Boolean addQuestionType, Boolean addKnowledge);

    /**
     * 检查绑定试卷权限
     */
    boolean checkBindPaperPermission(String paperId, String schoolId, String userId);

    /**
     * 查可绑定试卷
     */
    List<PaperMain> getAvailablePapers(Integer subjectId, Integer stage, Integer gradeId, Integer term, String userId, Integer type);
}
