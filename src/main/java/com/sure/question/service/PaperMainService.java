package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.PaperMain;

import java.io.IOException;

public interface PaperMainService extends IService<PaperMain> {

    void paperRemoveAndAddWaterMark(String paperId);

    void rollback(String paperId);

    void fileRemoveAddAddWaterMark(String filePath) throws IOException;

    String removeAndAddHtmlImageWaterMark(String html);
}
