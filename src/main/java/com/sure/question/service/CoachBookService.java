package com.sure.question.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.question.entity.CoachBook;
import com.sure.question.vo.SimpleVo;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.*;
import com.sure.question.vo.dataVo.StrStrVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Set;

public interface CoachBookService {
    /**
     * 查询
     */
    Page<InfoVo> listPage(QueryVo queryVo, Page<CoachBook> page);

    /**
     * 查询单个教辅信息
     */
    InfoVo getInfo(Integer coachBookId);

    /**
     * 新建
     */
    Integer addBook(InfoVo infoVo, MultipartFile coverFile);

    /**
     * 更新
     */
    void updateBook(InfoVo infoVo, MultipartFile coverFile);

    /**
     * 删除
     */
    void deleteBook(int coachBookId);

    /**
     * 查询教辅内试卷
     */
    List<PaperVo> getPapersOfBook(int coachBookId, Integer paperGroupId, Boolean onlyOpen, TokenUserVo userInfo);


    void downloadPaperFeedbackSheets(Integer coachBookId, List<String> paperIds, TokenUserVo userInfo, ServletOutputStream outputStream);

    void downloadCoachBookFeedbackSheets(Integer coachBookId, List<String> paperIds, TokenUserVo userInfo, ServletOutputStream outputStream);

    /**
     * 下载教辅试卷资料
     */
    void downloadCoachBookPaperAssetZip(int coachBookId, List<String> paperIds, OutputStream outputStream, String type, TokenUserVo userInfo);

    /**
     * 查询学校下指定教辅的试卷列表
     */
    List<PaperVo> getSchoolBookPapers(Integer coachBookId, String schoolId, String studentId, Boolean isAsc, String userId);

    /**
     * 查询书内试卷
     */
    List<PaperVo> getCustomPapersOfBook(int coachBookId, Integer paperGroupId, TokenUserVo userInfo);

    /**
     * 添加试卷
     */
    void addPapersToBook(int coachBookId, List<String> paperIds, TokenUserVo userInfo);

    /**
     * 添加试卷
     */
    void addCustomPapersToBook(int coachBookId, List<String> paperIds, TokenUserVo userInfo);

    /**
     * 更新试卷排序码
     */
    void updateBookPaperSortCode(List<PaperVo> paperVos, TokenUserVo userInfo);

    /**
     * 更新试卷排序码
     */
    void updateBookCustomPaperSortCode(List<PaperVo> paperVos, TokenUserVo userInfo);

    /**
     * 移除试卷
     */
    void removePapersFromBook(int coachBookId, List<String> paperIds, TokenUserVo userInfo);

    /**
     * 移除试卷
     */
    void removeCustomPapersFromBook(int coachBookId, List<String> paperIds, TokenUserVo userInfo);

    /**
     * 设置试卷别名
     */
    void updateBookPaperAlias(int coachBookId, List<StrStrVo> paperIdAliasList, TokenUserVo userInfo);

    /**
     * 设置是否启用分值分析
     */
    void updateBookPaperScoreAnalysis(int coachBookId, List<StrStrVo> paperIdScoreAnalysisList, TokenUserVo userInfo);

    List<String> getOrderPaperIds(List<String> paperIds);

    void setPaperOpenTime(List<OpenTimeVo> vos);

    void setCustomPaperOpenTime(List<OpenTimeVo> vos);

    /**
     * 设置是否锁定试卷
     */
    void setPaperLocked(Integer coachBookId, String paperId, Boolean locked);

    /**
     * 上传教辅试卷资料
     * @param type 资料类型：content-正文pdf, answer-答案pdf, listening-听力语音
     */
    String uploadPaperAsset(int coachBookId, String paperId, MultipartFile file, String type);

    /**
     * 删除教辅试卷资料
     * @param type 资料类型：content-正文pdf, answer-答案pdf, listening-听力语音
     */
    void deletePaperAsset(int coachBookId, String paperId, String type);

    /**
     * 获取教辅下试卷的PDF
     */
    PdfVo getCustomPaperPdf(Integer coachBookId, String paperId, String studentId, String userId);

    /**
     * 查询教辅内反馈卡
     */
    List<SheetVo> getSheetsOfBook(int coachBookId, TokenUserVo userInfo);

    /**
     * 添加反馈卡
     */
    void addSheetsToBook(int coachBookId, List<String> sheetIds, String userId);

    /**
     * 更新反馈卡排序码
     */
    void updateBookSheetSortCode(List<SheetVo> sheetVos);

    /**
     * 移除反馈卡
     */
    void removeSheetsFromBook(int coachBookId, List<String> sheetIds);

    /**
     * 关联反馈卡到试卷
     */
    void linkSheetToPaper(int coachBookId, String sheetId, String paperId);

    Integer getUnreadPaperCount(String studentId, TokenUserVo userInfo);

    /**
     * 查询同步卷对应的教辅名称
     *
     * @param paperIds 同步卷id
     * @return 教辅名称
     */
    List<PaperCoachVo> getBookNameBySyncPaperIds(List<String> paperIds);

    List<CoachBook> listCoachBook(Integer semesterId, Integer term);

    List<SimpleVo> listSchools(Integer semesterId, Integer term);

    List<SimpleVo> listSchoolsByOrg(Integer semesterId, Integer term, TokenUserVo userInfo);

    void setWrongQuesMark(List<WrongQuesMarkVo> vos);

    List<CoachBook> teachingGradeSubjectCoachBooks(Integer semesterId, Integer term, TokenUserVo userInfo);

    List<PaperExamInfoVo> paperExams(Integer semesterId, Integer term, Integer coachBookId, String classId, Boolean isAsc, TokenUserVo userInfo);

    List<BookPaperVo> paperCoachBookInfos(Set<String> paperIds);

    CoachBook getSchoolCoachBookBySheetId(String schoolId, String userId, String sheetId);

    CoachBook getSchoolCoachBookByPaperId(String schoolId, String userId, String paperId);

    List<Long> listCoachBookPaperIds(Integer coachBookId);

    Boolean getScoreAnalysisStatus(Integer coachBookId, Long paperId);

    List<SimpleVo> getCoachBookNames(List<Integer> coachBookIds);

    List<CoachBookPlanVo> getSchoolCoachBookPlans(String schoolId, Integer gradeId, Integer semesterId, Integer term);

    FeignCoachBookPaperInfoVo getCoachBookPaperInfoByPaperId(String paperId);

    boolean isCoachBookPaperAndLocked(String paperId);
}
