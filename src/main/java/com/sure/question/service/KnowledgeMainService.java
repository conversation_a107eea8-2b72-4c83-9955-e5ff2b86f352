package com.sure.question.service;

import com.sure.question.entity.KnowledgeMain;
import com.sure.question.vo.KnowledgeMainVo;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

public interface KnowledgeMainService {

    /**
     * 从缓存查学段学科知识点树
     */
    List<KnowledgeMainVo> getGradeLevelSubjectKnowledgeTreeWithCache(int gradeLevel, int subjectId);

    /**
     * 清除缓存
     */
    void deleteCache(int gradeLevel, int subjectId);

    /**
     * 查学段学科知识点树
     */
    List<KnowledgeMainVo> getGradeLevelSubjectKnowledgeTree(int gradeLevel, int subjectId);

    /**
     * 查学段学科知识点列表
     */
    List<KnowledgeMain> getGradeLevelSubjectKnowledgeList(int gradeLevel, int subjectId);

    /**
     * 导出知识点
     */
    void exportExcel(int gradeLevel, int subjectId, OutputStream outputStream) throws IOException;

    /**
     * 导入知识点
     * @param gradeLevel 学段Id
     * @param subjectId 学科Id
     * @param inputStream 导入Excel文件流
     * @param outputStream 变更Excel保存到的流
     * @param save 是否保存更改
     * @param userId 用户Id
     */
    void importExcel(int gradeLevel, int subjectId, InputStream inputStream, OutputStream outputStream, boolean save, String userId);
}
