package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.sure.question.entity.QuesLog;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.queslog.PaperStatVo;
import com.sure.question.vo.queslog.QuesLogDetailVo;

import javax.servlet.http.HttpServletRequest;

public interface QuesLogService extends IService<QuesLog> {

    void saveQuesLog(TokenUserVo userVo, String paperId, HttpServletRequest request, String logType);

    PageInfo<QuesLogDetailVo> listQuesLog(String schoolId, Integer type, String beginTime, String endTime, String paperId, String paperName, Integer pageNum, Integer pageSize, TokenUserVo userVo);

    PageInfo<PaperStatVo> statistic(String beginTime, String endTime, Integer gradeLevel, Integer subjectId, String paperName, Integer pageNum, Integer pageSize, TokenUserVo userVo);
}
