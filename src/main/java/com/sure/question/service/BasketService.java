package com.sure.question.service;

import com.sure.question.vo.Basket;
import com.sure.question.vo.basket.SavePaperResultVo;
import com.sure.question.vo.question.QuestionVo;

import java.util.List;

public interface BasketService {
    /**
     * 获取试题篮，补充题目等内容
     */
    Basket getBasketAddContent(String userId);

    /**
     * 获取用户试题篮缓存原始数据
     */
    Basket getUserBasketRawCache(String userId);

    /**
     * 更新试题篮
     */
    void updateBasket(Basket basket, String userId);

    /**
     * 清空试题篮
     */
    void clearBasket(String userId);

    /**
     * 替换试题篮
     */
    void replaceBasket(Basket basket, String userId);

    /**
     * 添加题目
     */
    List<QuestionVo> addQuestions(List<QuestionVo> questionVos, String paperStructure, String userId);

    /**
     * 更改题目
     * @param oldQuestionId 原题Id
     * @param questionVo 修改后的新题
     */
    QuestionVo updateQuesVo(String oldQuestionId, QuestionVo questionVo, String paperStructure, String userId);

    /**
     * 存为试卷
     */
    SavePaperResultVo saveAsPaper(String userId, String schoolId, Integer gradeLevel, Integer subjectId);

}
