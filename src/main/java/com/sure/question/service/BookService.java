package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.Book;
import com.sure.question.entity.BookCategory;
import com.sure.question.vo.book.BookVo;

import java.util.List;

public interface BookService extends IService<Book> {
    /**
     * 查询教材
     */
    List<BookVo> listBooks(Integer gradeLevel, Integer subjectId);

    /**
     * 查询教材版本
     */
    List<BookCategory> listBookCategories(Integer gradeLevel, Integer subjectId);

    /**
     * 添加教材
     */
    Integer addBook(int gradeLevelId, int subjectId, Integer gradeId, Integer term, String bookName, int categoryId, boolean enabled, String userId);

    /**
     * 修改教材
     */
    void updateBook(int id, String bookName, Integer gradeId, Integer term, int orderId, boolean enabled, String userId);

    /**
     * 修改教材版本顺序
     */
    void updateBookCategoryOrder(int gradeLevel, int subjectId, int categoryId, int categoryOrderId, String userId);

    /**
     * 删除教材
     */
    void deleteBook(int id, String userId);
}
