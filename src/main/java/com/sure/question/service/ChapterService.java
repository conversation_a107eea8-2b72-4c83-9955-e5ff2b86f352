package com.sure.question.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.Chapter;
import com.sure.question.vo.chapterVo.ChapterVo;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collection;
import java.util.List;

public interface ChapterService extends IService<Chapter> {

    /**
     * 查一批章节及其所有后代章节关联的知识点及其所有后代知识点Id
     */
    List<Integer> selectChaptersSelfAndDescendantKnowledgeIds(Collection<Integer> chapterIds);

    /**
     * 从缓存查教材章节树
     */
    List<ChapterVo> getBookChapterTreeWithCache(int bookId);

    /**
     * 清除缓存
     */
    void deleteCache(Integer gradeLevel, Integer subjectId);

    /**
     * 导出教材章节
     */
    void exportExcel(int gradeLevel, int subjectId, Integer categoryId, Integer bookId, OutputStream outputStream) throws IOException;

    /**
     * 导入教材章节
     * @param gradeLevel 学段Id
     * @param subjectId 学科Id
     * @param inputStream 导入Excel文件流
     * @param outputStream 变更Excel保存到的流
     * @param save 是否保存
     * @param userId 用户Id
     */
    void importExcel(int gradeLevel, int subjectId, InputStream inputStream, OutputStream outputStream, boolean save, String userId);

    /**
     * 导入复制章节题目
     * @param gradeLevel 学段Id
     * @param subjectId 学科Id
     * @param inputStream 导入Excel文件流
     * @param save 是否保存
     * @param userId 用户Id
     * @return 变更统计数据汇总
     */
    String importCopyChapterQuestionExcel(int gradeLevel, int subjectId, InputStream inputStream, boolean save, String userId);
}
