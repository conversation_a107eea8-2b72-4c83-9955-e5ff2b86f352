package com.sure.question.service;

import com.sure.question.vo.frontPage.FrontPageImageVo;
import com.sure.question.vo.frontPage.FrontPagePaperVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface FrontPageService {
    String uploadFrontPageImage(int gradeLevel, int subjectId, MultipartFile file);

    void saveFrontPageImage(int gradeLevel, int subjectId, List<FrontPageImageVo> images, String userId);

    List<FrontPageImageVo> getFrontPageImagesWithCache(int gradeLevel, int subjectId);

    void saveFrontPagePapers(int gradeLevel, int subjectId, String collectionName, int collectionSortCode, List<String> paperIds, String userId);

    void deleteFrontPagePaperCollection(int gradeLevel, int subjectId, String collectionName);

    void changePaperCollectionOrder(int gradeLevel, int subjectId, List<String> collectionNames);

    List<FrontPagePaperVo> getFrontPagePapersWithCache(int gradeLevel, int subjectId, String collectionName);

}
