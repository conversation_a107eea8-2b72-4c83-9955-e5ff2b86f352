package com.sure.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.ExamsubjectPushQues;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.pushQuesVo.PaperPushInfoVo;
import com.sure.question.vo.pushQuesVo.PaperPushRequestVo;
import com.sure.question.vo.pushQuesVo.PushQuesCountVo;
import com.sure.question.vo.pushQuesVo.SimpleExamSubjectVo;
import com.sure.question.vo.question.UserQuestionVo;

import java.util.List;

public interface ExamSubjectPushQuesService extends IService<ExamsubjectPushQues> {
    /**
     * 记录推题任务
     */
    void handlePushQuesTask(String examSubjectId, String paperId, String userId, String timestampMillis);

    /**
     * 为试卷生成变式题并保存
     */
    void generateAndSavePaperPushQues(PaperPushRequestVo vo, TokenUserVo userVo);

    /**
     * 为试卷生成变式题
     * @param paperId 试卷Id
     * @param examSubjectId 考试科目Id
     * @param pushNum 推题数量
     * @param onlyQuestionId 如果存在，则只生成该题的变式题
     */
    List<ExamsubjectPushQues> generatePaperPushQues(String paperId, String examSubjectId, int pushNum, String onlyQuestionId);

    /**
     * 查看试卷中一道题目的变式题列表
     */
    List<UserQuestionVo> listPushQuesByPaperIdAndQuestionId(String paperId, String examSubjectId, String questionId, TokenUserVo userVo);

    /**
     * 获取试卷变式题相关的考试科目列表
     */
    List<SimpleExamSubjectVo> listExamSubjectByPaperId(String paperId);

    /**
     * 查询本卷或考卷是否已有变式题
     */
    boolean existsPushQues(String paperId, String examSubjectId);

    /**
     * 获取指定考试的变式题列表
     */
    List<ExamsubjectPushQues> getExamSubjectPushQuestionList(String examSubjectId);

    /**
     * 获取指定考试的变式题列表，无则返回本卷变式题
     */
    List<ExamsubjectPushQues> listPushQuestionListByExamSubjectIdOrPaperId(String examSubjectId, String paperId);

    /**
     * 获取多份试卷的所有变式题，含考试变式题及本卷变式题
     */
    List<ExamsubjectPushQues> listPushQuestionListByPaperIds(List<String> paperIds);

    /**
     * 查询试卷推题信息
     */
    PaperPushInfoVo paperPushInfo(String paperId);

    /**
     * 查询试卷各题变式题数量
     */
    List<PushQuesCountVo> getQuesionIdAndPushCountList(String paperId);

    /**
     * 添加本卷变式题
     */
    void addPushQuesToPaper(String paperId, String quesId, List<String> pushQuestIds, TokenUserVo userInfo);

    /**
     * 删除本卷单道题目的变式题
     */
    void deletePushQues(String paperId, String quesId, List<String> pushQuestIds, TokenUserVo userInfo);

    /**
     * 删除本卷全部变式题
     */
    void deletePaperPushQues(String paperId, TokenUserVo userInfo);

    /**
     * 复制本卷变式题到考试科目
     */
    void syncPaperPushQuesToExamSubject(String paperId, String examSubjectId);

    /**
     * 查询有标准推题的试卷ID列表
     */
    List<String> listPaperIdWithStandardPushQues(List<String> paperIds);

    /**
     * 更新是否标准推题标记
     */
    void updateIsStandardPushQues(String paperId, Boolean isStandardPushQues, TokenUserVo userInfo);
}
