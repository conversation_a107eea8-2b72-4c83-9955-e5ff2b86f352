package com.sure.question.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.sure.common.entity.Result;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.SysGrade;
import com.sure.question.feign.MarkService;
import com.sure.question.feign.UserService;
import com.sure.question.service.FeignService;
import com.sure.question.util.FeignResultUtil;
import com.sure.question.vo.QuesUserVo;
import com.sure.question.vo.SimpleVo;
import com.sure.question.vo.UserMsgVo;
import com.sure.question.vo.coachBook.AnswerSheetVo;
import com.sure.question.vo.coachBook.SheetVo;
import com.sure.question.vo.pushQuesVo.SimpleExamSubjectVo;
import com.sure.question.vo.sheet.SimpleSheetVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class FeignServiceImpl implements FeignService {
    @Resource
    private UserService userService;
    @Resource
    private MarkService markService;

    @Override
    public List<UserMsgVo> lstTeacherInfoBySchoolIdAndKey(String schoolId, String key) {
        List<UserMsgVo> result = userService.lstTeacherInfoBySchoolIdAndKey(schoolId, key);
        desensitizedMobile(result);
        return result;
    }

    @Override
    public List<UserMsgVo> lstTeacherInfoByIds(List<String> teacherIds) {
        List<UserMsgVo> result = userService.lstTeacherInfoByIds(teacherIds);
        desensitizedMobile(result);
        return result;
    }

    private void desensitizedMobile(List<UserMsgVo> vos) {
        for (UserMsgVo vo : vos) {
            if (StringUtils.isNotEmpty(vo.getMobile())) {
                vo.setMobile(StrUtil.desensitized(vo.getMobile(), DesensitizedUtil.DesensitizedType.MOBILE_PHONE));
            }
        }
    }

    @Override
    public Map<String, String> getUserIdNameMobileMap(List<String> userIds) {
        List<UserMsgVo> list = lstTeacherInfoByIds(userIds);
        return list.stream().collect(Collectors.toMap(UserMsgVo::getUserId, vo -> {
            if (StringUtils.isNotBlank(vo.getMobile())) {
                return vo.getRealName().concat("(").concat(vo.getMobile()).concat(")");
            } else {
                return vo.getRealName();
            }
        }));
    }

    @Override
    public Map<String, String> getUserIdNameMap(List<String> userIds) {
        List<UserMsgVo> list = lstTeacherInfoByIds(userIds);
        if (list.isEmpty()) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(UserMsgVo::getUserId, UserMsgVo::getRealName));
    }

    @Override
    public Map<String, String> getEditorNameMap(List<String> editorIds) {
        return FeignResultUtil.getListThrowMessage(userService.getTeacherInfos(getUniqueIds(editorIds)), QuesUserVo.class)
                .stream().collect(Collectors.toMap(QuesUserVo::getId, QuesUserVo::getRealName));
    }

    @Override
    public List<QuesUserVo> getEditorList(Integer gradeLevel, Integer subjectId) {
        return FeignResultUtil.getListThrowMessage(userService.getEditorList(gradeLevel, subjectId), QuesUserVo.class);
    }

    @Override
    public Map<String, String> getSchoolIdNameMap(List<String> schoolIds) {
        return FeignResultUtil.getListThrowMessage(userService.getSchoolInfos(getUniqueIds(schoolIds)), SimpleVo.class)
                .stream().collect(Collectors.toMap(SimpleVo::getId, SimpleVo::getName));
    }

    private List<String> getUniqueIds(List<String> ids) {
        return ids.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public Map<String, SimpleSheetVo> getPaperIdFeedbackSheetMap(List<String> paperIds) {
        return getPaperIdSheetMap(paperIds, markService::getPaperIdFeedbackSheetMap);
    }

    @Override
    public Map<String, SimpleSheetVo> getPaperIdAnswerSheetMap(List<String> paperIds) {
        return getPaperIdSheetMap(paperIds, markService::getPaperIdAnswerSheetMap);
    }

    private Map<String, SimpleSheetVo> getPaperIdSheetMap(List<String> paperIds, Function<String, Result> api) {
        String paperIdsStr = StringUtils.join(paperIds, ",");
        Result result = api.apply(paperIdsStr);
        String dataStr = FeignResultUtil.getDataStringThrowMessage(result);
        if (StringUtils.isEmpty(dataStr)) {
            return new HashMap<>();
        }
        Map<String, SimpleSheetVo> map = JSON.parseObject(dataStr, new TypeReference<Map<String, SimpleSheetVo>>() {
        });
        return map == null ? new HashMap<>() : map;
    }

    @Override
    public List<SheetVo> getFeedbackSheetsInfo(List<String> sheetIds) {
        String sheetIdsStr = StringUtils.join(sheetIds, ",");
        return FeignResultUtil.getListThrowMessage(markService.getFeedbackSheetsInfo(sheetIdsStr), SheetVo.class);
    }

    @Override
    public List<AnswerSheetVo> getAnswerSheetsInfo(List<String> sheetIds) {
        String sheetIdsStr = StringUtils.join(sheetIds, ",");
        return FeignResultUtil.getListThrowMessage(markService.getAnswerSheetsInfo(sheetIdsStr), AnswerSheetVo.class);
    }

    @Override
    public SysGrade getSysGrade(int gradeId) {
        return userService.getSysGradeInfo(gradeId);
    }

    @Override
    public List<SimpleExamSubjectVo> listExamSubjectByIds(List<String> examSubjectIds) {
        return FeignResultUtil.getListThrowMessage(markService.listExamSubjectByIds(examSubjectIds), SimpleExamSubjectVo.class);
    }

    @Override
    public List<String> checkoutSchoolIdsByRegionId(Long regionId, Integer schoolType, Boolean isEnabled) {
        return FeignResultUtil.getListThrowMessage(userService.getSchoolIdsByRegionId(regionId, schoolType, isEnabled), String.class);
    }

    @Override
    public boolean isDisableFlagStuWrong(String schoolId) {
        Result result = userService.isSchoolDisableFlagStuWrong(schoolId);
        if (result.getCode() != 0) {
            throw new ApiException(result.getMessage());
        }

        return (boolean)result.getData();
    }
}
