package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.CoachBook;
import com.sure.question.entity.CoachBookCustomPaper;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.PaperStuAccess;
import com.sure.question.feign.UserService;
import com.sure.question.mapper.*;
import com.sure.question.service.CustomPaperService;
import com.sure.question.service.FeignService;
import com.sure.question.vo.coachBook.PaperVo;
import com.sure.question.vo.dataVo.StrIntVo;
import com.sure.question.vo.sheet.SimpleSheetVo;
import com.sure.question.vo.student.StudentInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/16 16:21
 */
@Service("customPaperService")
@RequiredArgsConstructor
@Slf4j
public class CustomPaperServiceImpl implements CustomPaperService {
    private final UserService userService;
    private final SchoolCoachBookMapper schoolCoachBookMapper;
    private final CoachBookCustomPaperMapper coachBookCustomPaperMapper;
    private final PaperMainMapper paperMainMapper;
    private final CoachBookMapper coachBookMapper;
    private final FeignService feignService;
    private final ExamSubjectPushQuesMapper examSubjectPushQuesMapper;
    private final PaperStuAccessMapper paperStuAccessMapper;

    @Override
    public List<PaperVo> lstStuPaper(String studentId, Integer subjectId, Integer semesterId, Integer term, String userId) {
        // 鉴权，校验当前学生是不是当前用户的家长
        StudentInfoVo studentInfo = userService.getStudentInfo(studentId);
        if (studentInfo.getParentIds() == null || !studentInfo.getParentIds().contains(userId)) {
            throw new ApiException("无权限！");
        }

        // 查已激活教辅
        List<Integer> coachBookIds = schoolCoachBookMapper.queryStudentCoachBookIds(studentInfo.getSchoolId(), semesterId, term, subjectId, studentId);
        if (coachBookIds.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Integer, List<String>> bookIdPaperIdsMap = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>()
                        .in(CoachBookCustomPaper::getCoachBookId, coachBookIds)
                        .le(CoachBookCustomPaper::getOpenTime, new Date())
                        .orderByDesc(CoachBookCustomPaper::getSortCode)
                        .select(CoachBookCustomPaper::getPaperId, CoachBookCustomPaper::getCoachBookId))
                .stream().collect(Collectors.groupingBy(CoachBookCustomPaper::getCoachBookId, Collectors.mapping(CoachBookCustomPaper::getPaperId, Collectors.toList())));
        if (bookIdPaperIdsMap.isEmpty()) {
            return Collections.emptyList();
        }

        List<PaperVo> result = new ArrayList<>();
        bookIdPaperIdsMap.forEach((bookId, paperIds) -> {
            CoachBook book = coachBookMapper.selectById(bookId);
            if (book == null) {
                throw new ApiException("参数错误，找不到该定制资源");
            }

            Map<String, PaperMain> paperMainMap = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                            .in(PaperMain::getId, paperIds)
                            .select(PaperMain::getId,
                                    PaperMain::getPaperName,
                                    PaperMain::getGradeLevel,
                                    PaperMain::getSubjectId,
                                    PaperMain::getGradeId,
                                    PaperMain::getYear,
                                    PaperMain::getRegionId,
                                    PaperMain::getPaperTypeId,
                                    PaperMain::getTerm))
                    .stream().collect(Collectors.toMap(PaperMain::getId, Function.identity()));

            // 构建
            for (String paperId : paperIds) {
                PaperMain paperMain = paperMainMap.get(paperId);
                if (paperMain == null) {
                    log.error("找不到教辅包含的练习卷详情！coachBookId={}, paperId={}. studentId={}, userId={}", bookId, paperId, studentId, userId);
                    continue;
                }

                result.add(PaperVo.builder()
                        .id(paperMain.getId())
                        .paperName(paperMain.getPaperName())
                        .gradeLevel(paperMain.getGradeLevel())
                        .subjectId(paperMain.getSubjectId())
                        .gradeId(paperMain.getGradeId())
                        .term(StringUtils.isNotEmpty(paperMain.getTerm()) ? Integer.valueOf(paperMain.getTerm()) : null)
                        .year(paperMain.getYear())
                        .regionId(paperMain.getRegionId())
                        .paperTypeId(paperMain.getPaperTypeId())
                        .coachBookId(bookId)
                        .coachBookName(book.getBookName())
                        .build());
            }
        });

        // 试卷是否查看过
        Set<Long> accessPaperIds = paperStuAccessMapper.selectObjs(new LambdaQueryWrapper<PaperStuAccess>()
                        .eq(PaperStuAccess::getStudentId, studentId)
                        .in(PaperStuAccess::getPaperId, result.stream().map(PaperVo::getId).collect(Collectors.toList()))
                        .select(PaperStuAccess::getPaperId))
                .stream().map(x -> Long.parseLong(x.toString())).collect(Collectors.toSet());

        result.forEach(vo -> vo.setIsAccessed(accessPaperIds.contains(Long.parseLong(vo.getId()))));

        return result;
    }


    public List<PaperVo> queryList_Obsolete(String studentId, Integer subjectId, Integer semesterId, Integer term, String userId) {
        // 鉴权，校验当前学生是不是当前用户的家长
        StudentInfoVo studentInfo = userService.getStudentInfo(studentId);
        if (studentInfo.getParentIds() == null || !studentInfo.getParentIds().contains(userId)) {
            throw new ApiException("无权限！");
        }

        // 查已激活教辅
        List<Integer> coachBookIds = schoolCoachBookMapper.queryStudentCoachBookIds(studentInfo.getSchoolId(), semesterId, term, subjectId, studentId);
        if (coachBookIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> paperIds = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>()
                        .in(CoachBookCustomPaper::getCoachBookId, coachBookIds)
                        .le(CoachBookCustomPaper::getOpenTime, new Date())
                        .select(CoachBookCustomPaper::getPaperId)
                        .orderByAsc(CoachBookCustomPaper::getSortCode))
                .stream().map(CoachBookCustomPaper::getPaperId).collect(Collectors.toList());
        if (paperIds.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Integer, List<CoachBookCustomPaper>> coachBookIdPaperListMap = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>()
                        .in(CoachBookCustomPaper::getCoachBookId, coachBookIds)
                        .in(CoachBookCustomPaper::getPaperId, paperIds))
                .stream().collect(Collectors.groupingBy(CoachBookCustomPaper::getCoachBookId));

        List<PaperVo> voList = new ArrayList<>();
        for (Integer coachBookId : coachBookIdPaperListMap.keySet()) {
            CoachBook book = coachBookMapper.selectById(coachBookId);
            if (book == null) {
                throw new ApiException("参数错误，找不到该定制资源");
            }
            // 查包含试卷
            List<CoachBookCustomPaper> coachBookCustomPapers = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>()
                    .eq(CoachBookCustomPaper::getCoachBookId, coachBookId)
                    .isNotNull(CoachBookCustomPaper::getOpenTime)
                    .le(CoachBookCustomPaper::getOpenTime, new Date())
                    .orderByAsc(CoachBookCustomPaper::getSortCode));
            if (coachBookCustomPapers.isEmpty()) {
                return Collections.emptyList();
            }

            // 查试卷信息
            List<String> paperIdList = coachBookIdPaperListMap.get(coachBookId).stream().map(CoachBookCustomPaper::getPaperId).collect(Collectors.toList());
            List<PaperMain> paperMains = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                    .in(PaperMain::getId, paperIdList)
                    .select(PaperMain::getId,
                            PaperMain::getPaperName,
                            PaperMain::getGradeLevel,
                            PaperMain::getSubjectId,
                            PaperMain::getGradeId,
                            PaperMain::getYear,
                            PaperMain::getRegionId,
                            PaperMain::getPaperTypeId,
                            PaperMain::getTerm));
            Map<String, PaperMain> paperMainMap = paperMains.stream().collect(Collectors.toMap(PaperMain::getId, Function.identity()));

            // 查错题反馈卡信息
            Map<String, SimpleSheetVo> paperIdSheetMap = feignService.getPaperIdFeedbackSheetMap(paperIdList);

            // 查试卷已有标准变式题数量
            Map<String, Integer> paperIdAndStandardCountMap = examSubjectPushQuesMapper.getPaperIdAndStandardCount(paperIdList).stream().collect(Collectors.toMap(StrIntVo::getKey, StrIntVo::getValue));

            // 构建
            List<PaperVo> vos = coachBookCustomPapers.stream().map(paper -> {
                PaperMain paperMain = paperMainMap.get(paper.getPaperId());
                if (paperMain == null) {
                    throw new ApiException("该教辅书中包含了不存在的试卷");
                }
                SimpleSheetVo sheet = paperIdSheetMap.get(paperMain.getId());
                return PaperVo.builder()
                        .id(paperMain.getId())
                        .paperName(paperMain.getPaperName())
                        .gradeLevel(paperMain.getGradeLevel())
                        .subjectId(paperMain.getSubjectId())
                        .gradeId(paperMain.getGradeId())
                        .term(StringUtils.isNotEmpty(paperMain.getTerm()) ? Integer.valueOf(paperMain.getTerm()) : null)
                        .year(paperMain.getYear())
                        .regionId(paperMain.getRegionId())
                        .paperTypeId(paperMain.getPaperTypeId())
                        .coachBookId(paper.getCoachBookId())
                        .coachBookName(book.getBookName())
                        .standardQuestionCount(paperIdAndStandardCountMap.getOrDefault(paper.getPaperId(), 0))
                        .sortCode(paper.getSortCode())
                        .feedbackSheetId(sheet == null ? null : sheet.getId())
                        .feedbackSheetName(sheet == null ? null : sheet.getName())
                        .createTime(paper.getCreateTime())
                        .build();
            }).sorted(Comparator.comparingInt(o -> paperIds.indexOf(o.getId()))).collect(Collectors.toList());
            voList.addAll(vos);
        }

        // 试卷是否查看过
        Set<Long> accessPaperIds = paperStuAccessMapper.selectObjs(new LambdaQueryWrapper<PaperStuAccess>()
                        .eq(PaperStuAccess::getStudentId, studentId)
                        .in(PaperStuAccess::getPaperId, voList.stream().map(PaperVo::getId).collect(Collectors.toList()))
                        .select(PaperStuAccess::getPaperId))
                .stream().map(x -> Long.parseLong(x.toString())).collect(Collectors.toSet());
        voList.forEach(vo -> vo.setIsAccessed(accessPaperIds.contains(Long.parseLong(vo.getId()))));

        return voList;
    }
}
