package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.BasketPaperStyle;
import com.sure.question.entity.CoachBook;
import com.sure.question.entity.PaperMain;
import com.sure.question.enums.PaperStatusEnum;
import com.sure.question.enums.PaperUploadTypeEnum;
import com.sure.question.mapper.BasketPaperStyleMapper;
import com.sure.question.mapper.CoachBookMapper;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.service.BasketService;
import com.sure.question.service.FeignService;
import com.sure.question.service.PaperMakeService;
import com.sure.question.vo.Basket;
import com.sure.question.vo.paper.FindPaperPageParam;
import com.sure.question.vo.sheet.SimpleSheetVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaperMakeServiceImpl implements PaperMakeService {
    private final PaperMainMapper paperMainMapper;
    private final FeignService feignService;
    private final CoachBookMapper coachBookMapper;
    private final BasketPaperStyleMapper basketPaperStyleMapper;
    private final BasketService basketService;

    /**
     * 获取用户组卷记录
     */
    @Override
    public IPage<PaperMain> getSelfMake(FindPaperPageParam param, String userId) {
        // 构建查询条件
        LambdaQueryWrapper<PaperMain> queryWrapper = param.buildPaperQueryWrapper()
                .eq(PaperMain::getUserId, userId)
                .eq(PaperMain::getUploadType, PaperUploadTypeEnum.MAKE.getId())
                .orderByDesc(PaperMain::getCreateTime);

        IPage<PaperMain> paperPage = paperMainMapper.selectPage(new Page<>(param.getPage(), param.getSize()), queryWrapper);

        if (!paperPage.getRecords().isEmpty()) {
            // 补充反馈卡信息
            List<String> paperIds = paperPage.getRecords().stream().map(x -> String.valueOf(x.getId())).collect(Collectors.toList());
            Map<String, SimpleSheetVo> paperIdSheetMap = feignService.getPaperIdFeedbackSheetMap(paperIds);
            for (PaperMain record : paperPage.getRecords()) {
                SimpleSheetVo sheet = paperIdSheetMap.get(String.valueOf(record.getId()));
                if (sheet != null) {
                    record.setFeedbackSheetId(sheet.getId());
                }
            }
            List<Integer> ids = paperPage.getRecords().stream().map(PaperMain::getFlagCoachBookId).filter(Objects::nonNull).collect(Collectors.toList());
            if (!ids.isEmpty()) {
                Map<Integer, String> coachBookIdCoachBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                                .in(CoachBook::getId, ids)
                                .select(CoachBook::getId, CoachBook::getBookName))
                        .stream().collect(Collectors.toMap(CoachBook::getId, CoachBook::getBookName));
                paperPage.getRecords().forEach(x -> {
                    if (x.getFlagCoachBookId() != null) {
                        x.setFlagCoachBookName(coachBookIdCoachBookNameMap.get(x.getFlagCoachBookId()));
                    }
                });
            }
        }
        return paperPage;
    }

    /**
     * 再次编辑已组试卷
     */
    @Override
    public void editMakePaper(String paperId, String userId) {
        int count = paperMainMapper.selectCount(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .eq(PaperMain::getUserId, userId)
                .eq(PaperMain::getUploadType, PaperUploadTypeEnum.MAKE.getId()));
        if (count == 0) {
            throw new ApiException("无权限");
        }

        Basket basket = new Basket();
        PaperMain paperMain = addBasketPaperInfo(paperId, basket);
        addBasketPaperStyle(basket, paperMain);
        basket.setPaperStructure(paperMain.getPaperStyleJson());

        basketService.replaceBasket(basket, userId);
    }

    private PaperMain addBasketPaperInfo(String paperId, Basket basket) {
        PaperMain paperMain = paperMainMapper.selectById(paperId);
        Map<String, Object> map = new HashMap<>();
        map.put("id", paperId);
        map.put("paperName", paperMain.getPaperName());
        map.put("gradeLevel", paperMain.getGradeLevel());
        map.put("subjectId", paperMain.getSubjectId());
        map.put("answerSheetDownloadCount", paperMain.getAnswerSheetDownloadCount());
        map.put("feedbackSheetDownloadCount", paperMain.getFeedbackSheetDownloadCount());
        basket.setPaperInfo(map);
        return paperMain;
    }

    private void addBasketPaperStyle(Basket basket, PaperMain paperMain) {
        BasketPaperStyle basketPaperStyle = basketPaperStyleMapper.selectById(paperMain.getId());
        if (basketPaperStyle == null) {
            basketPaperStyle = new BasketPaperStyle();
            basketPaperStyle.setPaperId(paperMain.getId());
            basketPaperStyle.setQuestionCategoryJson(paperMain.getPaperStyleJson());
            basketPaperStyle.setPaperInfo(JSON.toJSONString(basket.getPaperInfo()));
            basketPaperStyle.setTitle(paperMain.getPaperName());
        }
        basket.setBasketPaperStyle(basketPaperStyle);
    }

    /**
     * 获取用户组卷数量
     */
    @Override
    public int getUserMakePaperCount(String userId) {
        return paperMainMapper.selectCount(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getUserId, userId)
                .eq(PaperMain::getUploadType, PaperUploadTypeEnum.MAKE.getId())
                .eq(PaperMain::getStatus, PaperStatusEnum.Status0.getCode()));
    }
}
