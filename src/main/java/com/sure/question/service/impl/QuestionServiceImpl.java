package com.sure.question.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.paper.structure.*;
import com.sure.question.dto.question.CheckQuestionOption;
import com.sure.question.dto.question.SaveQuestionDataDto;
import com.sure.question.dto.question.SelectQuestionVoOption;
import com.sure.question.entity.*;
import com.sure.question.enums.BranchTypeEnum;
import com.sure.question.mapper.*;
import com.sure.question.service.*;
import com.sure.question.util.IdUtil;
import com.sure.question.vo.ques.UpdateQues;
import com.sure.question.vo.question.*;
import com.sure.question.vo.sheet.SimpleSheetVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
@RequiredArgsConstructor
public class QuestionServiceImpl extends ServiceImpl<QuestionMapper, Question> implements QuestionService {
    private final QuestionMapper questionMapper;
    private final PaperQuestionMapper paperQuestionMapper;
    private final SmallQuestionMapper smallQuestionMapper;
    private final SmallQuestionService smallQuestionService;
    private final PaperMainMapper paperMainMapper;
    private final QuestionBookMapper questionBookMapper;
    private final QuestionKnowledgeMapper questionKnowledgeMapper;
    private final KnowledgeMainMapper knowledgeMainMapper;
    private final ChapterMapper chapterMapper;
    private final FavoriteQuesMapper favoriteQuesMapper;
    private final ListeningService listeningService;
    private final QuestionCorrectMapper questionCorrectMapper;
    private final CoachBookPaperMapper coachBookPaperMapper;
    private final RenderedQuestionService renderedQuestionService;
    private final FeignService feignService;
    private final BasketPaperStyleMapper basketPaperStyleMapper;
    private final QuestionEsSyncMapper questionEsSyncMapper;
    private final QuestionBuildService questionBuildService;
    private final QuestionSearchService questionSearchService;
    private final PaperQuestionService paperQuestionService;
    private final PermissionService permissionService;
    private final TransactionTemplate transactionTemplate;


    /**
     * 改变题目收藏状态
     */
    @Override
    public Integer changeFav(Long quesId, String userId, String schoolId) {
        LambdaQueryWrapper<FavoriteQues> queryWrapper = new LambdaQueryWrapper<FavoriteQues>()
                .eq(FavoriteQues::getQuestionId, quesId)
                .eq(FavoriteQues::getUserId, userId);
        // 查出记录
        List<FavoriteQues> favoriteQues = favoriteQuesMapper.selectList(queryWrapper);
        // 存在收藏则删除收藏
        if (!favoriteQues.isEmpty()) {
            favoriteQuesMapper.delete(queryWrapper);
            return 0;
        }
        // 尚未收藏则添加收藏
        else {
            FavoriteQues fq = new FavoriteQues();
            fq.setQuestionId(quesId);
            fq.setUserId(userId);
            fq.setSchoolId(schoolId);
            fq.setCreateTime(System.currentTimeMillis());
            favoriteQuesMapper.insert(fq);
            return 1;
        }
    }

    /**
     * 多条件分页查询收藏题目
     */
    @Override
    public IPage<UserQuestionVo> getFavorite(String userId, int page, int size, int stage, int subjectId, Integer type, Integer difficulty) {
        List<String> questionIds = questionMapper.selectFavoriteQuestionIds(userId, stage, subjectId, difficulty, type);
        return questionSearchService.buildQuesSearchVoPage(questionIds, page, size, userId);
    }


    /**
     * 生成一批雪花Id
     */
    @Override
    public List<String> generateIds(int size) {
        List<String> ids = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            ids.add(IdUtil.StringId());
        }
        return ids;
    }

    /**
     * 检查一批题目
     */
    private void checkQuestions(Collection<QuestionVo> questionVos, CheckQuestionOption option) {
        questionVos.forEach(questionVo -> questionVo.check(option));
    }

    /**
     * 检查题目是否仅存在于本试卷中
     */
    private void checkQuestionsExistOnlyInThePaper(Collection<String> questionIds, String paperId) {
        if (new HashSet<>(questionIds).size() != questionIds.size()) {
            throw new ApiException("参数错误：题目Id重复");
        }
        List<PaperQuestion> paperQuestions = paperQuestionMapper.selectList(new LambdaQueryWrapper<PaperQuestion>()
                .in(PaperQuestion::getQuestionId, questionIds));
        if (paperQuestions.stream().anyMatch(pq -> !StringUtils.equals(pq.getPaperId(), paperId))) {
            throw new ApiException("题目存在于其他试卷，不可修改或删除");
        }
        if (paperQuestions.size() != questionIds.size()) {
            throw new ApiException("参数错误：找不到题目");
        }
    }

    /**
     * 保存题目
     * @param dto 保存题目数据
     */
    private void saveQuestions(SaveQuestionDataDto dto) {
        transactionTemplate.execute(status -> {
            // 听力
            if (CollUtil.isNotEmpty(dto.getQuestionVos())) {
                dto.getQuestionVos().forEach(listeningService::saveQuestionListening);
            }
            // 题目
            if (CollUtil.isNotEmpty(dto.getQuestionList())) {
                this.saveBatch(dto.getQuestionList());
            }
            // 小题
            if (CollUtil.isNotEmpty(dto.getSmallQuestionList())) {
                smallQuestionService.saveBatch(dto.getSmallQuestionList());
            }
            // 知识点
            if (CollUtil.isNotEmpty(dto.getQuestionKnowledgeList())) {
                questionKnowledgeMapper.insertList(dto.getQuestionKnowledgeList());
            }
            // 章节
            if (CollUtil.isNotEmpty(dto.getQuestionBookList())) {
                questionBookMapper.insertList(dto.getQuestionBookList());
            }
            return null;
        });
    }

    /**
     * 保存试卷所属题目，不添加试卷题目关联
     */
    @Override
    public void saveQuestionsOfPaper(List<QuestionVo> questionVos, PaperMain paper) {
        // 构建保存数据
        SaveQuestionDataDto saveQuestionDataDto = new SaveQuestionDataDto(questionVos);

        // 更改题目学段学科年级与试卷相同
        saveQuestionDataDto.getQuestionList().forEach(q -> {
            q.setPaperId(paper.getId());
            q.setGradeLevel(paper.getGradeLevel());
            q.setSubjectId(paper.getSubjectId());
            q.setGradeId(paper.getGradeId());
        });

        // 保存
        saveQuestions(saveQuestionDataDto);
    }

    /**
     * 创建题目试卷关联
     */
    @Override
    public List<PaperQuestion> createPaperQuestionRelations(String paperId, Collection<String> questionIds) {
        long createTime = System.currentTimeMillis();
        return questionIds.stream().map(qId -> {
            PaperQuestion paperQuestion = new PaperQuestion();
            paperQuestion.setId(IdUtil.longId());
            paperQuestion.setQuestionId(qId);
            paperQuestion.setPaperId(paperId);
            paperQuestion.setStatus(0);
            paperQuestion.setCreateTime(createTime);
            return paperQuestion;
        }).collect(Collectors.toList());
    }

    /**
     * 保存试卷题目，并添加试卷题目关联、更新试卷结构
     */
    @Override
    public void addPaperQuestions(List<QuestionVo> questionVos, CheckQuestionOption checkQuestionOption,
                                  PaperMain paper, String paperStructure, CheckPaperStructureOption checkStructureOption) {
        // 检查题目和试卷结构
        checkQuestions(questionVos, checkQuestionOption);
        paperQuestionService.checkPaperStructure(paper.getId(), paperStructure, checkStructureOption, questionVos, null, null);

        // 构建试卷题目关联
        List<String> questionIds = questionVos.stream().map(QuestionVo::getId).collect(Collectors.toList());
        List<PaperQuestion> paperQuestionList = createPaperQuestionRelations(paper.getId(), questionIds);

        // 保存
        transactionTemplate.execute(status -> {
            saveQuestionsOfPaper(questionVos, paper);
            paperQuestionMapper.insertList(paperQuestionList);
            paperMainMapper.updatePaperStructure(paper.getId(), paperStructure);
            return null;
        });
    }

    /**
     * 批量修改题目
     * @param updateDivideContent 是否更新划题内容
     */
    private void changeQuestions(Collection<QuestionVo> questionVos, boolean updateDivideContent) {
        // 查出原题目
        List<String> questionIds = questionVos.stream().map(QuestionVo::getId).collect(Collectors.toList());
        List<Question> questions = questionMapper.selectList(new LambdaQueryWrapper<Question>().in(Question::getId, questionIds));
        if (questions.size() != questionIds.size()) {
            throw new ApiException("找不到题目");
        }

        // 构建新题目
        SaveQuestionDataDto saveQuestionDataDto = new SaveQuestionDataDto(questionVos);
        Map<String, Question> newQuestionMap = saveQuestionDataDto.getQuestionList().stream().collect(Collectors.toMap(Question::getId, Function.identity()));

        // 更改题目字段
        questions.forEach(q -> {
            Question newQuestion = newQuestionMap.get(q.getId());
            q.setQuestionTypeId(newQuestion.getQuestionTypeId());
            if (updateDivideContent) {
                q.setDivideHtml(newQuestion.getDivideHtml());
            }
            q.setContentHtml(newQuestion.getContentHtml());
            q.setComment(newQuestion.getComment());
            q.setDifficult(newQuestion.getDifficult());
            q.setScore(newQuestion.getScore());
            q.setUpdateTime(newQuestion.getUpdateTime());
        });
        saveQuestionDataDto.setQuestionList(questions);

        transactionTemplate.execute(status -> {
            // 删除原题目，保留听力及题目报错
            deleteQuestions(questionIds, false, false);
            // 保存更新后的题目
            saveQuestions(saveQuestionDataDto);
            return null;
        });
    }

    /**
     * 修改试卷题目
     */
    @Override
    public void changePaperQuestions(Collection<QuestionVo> questionVos, CheckQuestionOption checkQuestionOption, boolean updateDivideContent,
                                     String paperId, String paperStructure, CheckPaperStructureOption checkStructureOption) {
        // 检查题目和试卷结构
        checkQuestions(questionVos, checkQuestionOption);
        paperQuestionService.checkPaperStructure(paperId, paperStructure, checkStructureOption, null, questionVos, null);

        // 检查题目是否仅存在于本试卷中
        List<String> questionIds = questionVos.stream().map(QuestionVo::getId).collect(Collectors.toList());
        checkQuestionsExistOnlyInThePaper(questionIds, paperId);

        transactionTemplate.execute(status -> {
            changeQuestions(questionVos, updateDivideContent);
            paperMainMapper.updatePaperStructure(paperId, paperStructure);
            return null;
        });
    }

    /**
     * 批量删除题目
     * @param questionIds 题目Ids
     * @param deleteListening 是否删除听力
     * @param deleteQuestionCorrect 是否删除题目报错
     */
    private void deleteQuestions(Collection<String> questionIds, boolean deleteListening, boolean deleteQuestionCorrect) {
        // 查出小题Id
        List<String> smallQuestionIds = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                        .in(SmallQuestion::getQuestionId, questionIds)
                        .select(SmallQuestion::getId))
                .stream().map(SmallQuestion::getId).collect(Collectors.toList());
        transactionTemplate.execute(status -> {
            if (!smallQuestionIds.isEmpty()) {
                // 删小题知识点
                questionKnowledgeMapper.deleteBySmallQuestionIds(smallQuestionIds);
                // 删小题
                smallQuestionMapper.delete(new LambdaQueryWrapper<SmallQuestion>()
                        .in(SmallQuestion::getId, smallQuestionIds)
                        .in(SmallQuestion::getQuestionId, questionIds));
            }
            // 删题目章节
            questionBookMapper.delete(new LambdaQueryWrapper<QuestionBook>()
                    .in(QuestionBook::getQuestionId, questionIds));
            // 删题目
            questionMapper.delete(new LambdaQueryWrapper<Question>()
                    .in(Question::getId, questionIds));
            // 删已渲染公式
            renderedQuestionService.deleteRenderedQuestions(questionIds);
            // 删听力
            if (deleteListening) {
                listeningService.deleteQuestionsListening(questionIds);
            }
            // 删题目报错
            if (deleteQuestionCorrect) {
                questionCorrectMapper.delete(new LambdaQueryWrapper<QuestionCorrect>()
                        .in(QuestionCorrect::getQuestionId, questionIds));
            }
            return null;
        });
    }

    /**
     * 删除试卷题目
     */
    @Override
    public void deletePaperQuestions(Collection<String> questionIds, String paperId, String paperStructure, CheckPaperStructureOption checkStructureOption) {
        // 检查试卷结构
        paperQuestionService.checkPaperStructure(paperId, paperStructure, checkStructureOption, null, null, questionIds);

        // 检查题目是否仅存在于本试卷中
        checkQuestionsExistOnlyInThePaper(questionIds, paperId);

        transactionTemplate.execute(status -> {
            // 删题目
            deleteQuestions(questionIds, true, true);
            // 删题目试卷关联
            paperQuestionMapper.delete(new LambdaQueryWrapper<PaperQuestion>()
                    .eq(PaperQuestion::getPaperId, paperId)
                    .in(PaperQuestion::getQuestionId, questionIds));
            // 更新试卷结构
            paperMainMapper.updatePaperStructure(paperId, paperStructure);
            return null;
        });
    }


    /**
     * 编辑修改题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public QuestionVo editorChangeQuestion(QuestionVo questionVo, String editorId) {
        permissionService.checkEditorPermission(editorId, questionVo.getStage(), questionVo.getSubjectId());

        // 检查题目
        questionVo.check(CheckQuestionOption.checkAllExceptScore());

        // 查出原题
        Question question = questionMapper.selectById(questionVo.getId());
        if (question == null) {
            throw new ApiException("找不到题目");
        }

        // 若小题Id及题型改变，需更新试卷结构
        Map<String, String> paperIdStructureMap = new HashMap<>();
        List<SmallQuestion> newSmallQuestionList = questionVo.getBranches().stream()
                .sorted(Comparator.comparing(SmallQuestionVo::getCode))
                .map(branch -> SmallQuestion.builder()
                        .id(branch.getId())
                        .questionTypeId(branch.getType())
                        .score(branch.getScore()).build()
                ).collect(Collectors.toList());
        if (isSmallQuestionIdTypeChanged(questionVo.getId(), newSmallQuestionList)) {
            paperIdStructureMap = getNeedUpdatePaperStructureThrowEx(question.getId(), newSmallQuestionList);
        }

        // 保存题目
        changeQuestions(Collections.singletonList(questionVo), false);

        // 更新试卷结构
        paperIdStructureMap.forEach((paperId, structure) -> {
            paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                    .eq(PaperMain::getId, paperId)
                    .set(PaperMain::getPaperStyleJson, structure));
            basketPaperStyleMapper.update(null, new LambdaUpdateWrapper<BasketPaperStyle>()
                    .eq(BasketPaperStyle::getPaperId, paperId)
                    .set(BasketPaperStyle::getQuestionCategoryJson, structure));
            log.info("修改题目引起试卷结构更新, questionId = {}, paperId = {}", question.getId(), paperId);
        });

        // 同步ES
        questionEsSyncMapper.insertPendingQuestionsBatch(Collections.singletonList(question.getId()));

        return questionBuildService.selectJustSavedQuestionVos(Collections.singletonList(questionVo), SelectQuestionVoOption.addAll()).get(0);
    }

    /**
     * 判断小题Id、题型是否改变（与原题不能一一对应）
     */
    private boolean isSmallQuestionIdTypeChanged(String questionId, List<SmallQuestion> newSmallQuestionList) {
        List<SmallQuestion> oldSmallQuestionList = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                .eq(SmallQuestion::getQuestionId, questionId)
                .orderByAsc(SmallQuestion::getQuestionNo)
                .select(SmallQuestion::getId, SmallQuestion::getQuestionTypeId));
        if (oldSmallQuestionList.size() != newSmallQuestionList.size()) {
            return true;
        }
        for (int idx = 0; idx < oldSmallQuestionList.size(); idx++) {
            SmallQuestion oldSmallQuestion = oldSmallQuestionList.get(idx);
            SmallQuestion newSmallQuestion = newSmallQuestionList.get(idx);
            if (!oldSmallQuestion.getId().equals(newSmallQuestion.getId()) ||
                    !oldSmallQuestion.getQuestionTypeId().equals(newSmallQuestion.getQuestionTypeId())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 找出关联试卷，获取更新后的题目结构
     * @param questionId           题目Id
     * @param newSmallQuestionList 新小题
     */
    private Map<String, String> getNeedUpdatePaperStructureThrowEx(String questionId, List<SmallQuestion> newSmallQuestionList) {
        Map<String, String> result = new HashMap<>();

        // 查关联试卷Id
        List<String> paperIds = paperQuestionMapper.selectList(new LambdaQueryWrapper<PaperQuestion>().eq(PaperQuestion::getQuestionId, questionId))
                .stream().map(PaperQuestion::getPaperId).collect(Collectors.toList());
        if (paperIds.isEmpty()) {
            return result;
        }

        // 若关联试卷是教辅试卷且已生成错题反馈卡，禁止更改
        List<String> coachBookPaperIds = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                        .in(CoachBookPaper::getPaperId, paperIds).select(CoachBookPaper::getPaperId))
                .stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());
        if (!coachBookPaperIds.isEmpty()) {
            Map<String, SimpleSheetVo> paperIdSheetMap = feignService.getPaperIdFeedbackSheetMap(coachBookPaperIds);
            if (!paperIdSheetMap.isEmpty()) {
                throw new ApiException("本题所在教辅试卷已生成错题反馈卡，请勿增删小题！");
            }
            paperIdSheetMap = feignService.getPaperIdAnswerSheetMap(coachBookPaperIds);
            if (!paperIdSheetMap.isEmpty()) {
                throw new ApiException("本题所在教辅试卷已生成答题卡，请勿增删小题！");
            }
        }

        // 新结构题目和小题
        List<Integer> objectiveBranchTypes = Arrays.asList(BranchTypeEnum.SingleChoice.getId(),
                BranchTypeEnum.MultipleChoice.getId(),
                BranchTypeEnum.TrueOrFalse.getId());
        List<StructureBranch> newBranches = new ArrayList<>();
        int branchCode = 1;
        for (SmallQuestion sq : newSmallQuestionList) {
            StructureBranch newBranch = new StructureBranch();
            newBranch.setId(sq.getId());
            newBranch.setCode(branchCode++);
            newBranch.setScore(sq.getScore() == null ? null : sq.getScore());
            newBranch.setIsObjective(objectiveBranchTypes.contains(sq.getQuestionTypeId()));
            newBranches.add(newBranch);
        }
        float questionScore = newSmallQuestionList.stream().map(SmallQuestion::getScore).reduce(0F, Float::sum);
        StructureQuestion newQuestion = new StructureQuestion();
        newQuestion.setId(questionId);
        newQuestion.setCode(1);
        newQuestion.setScore(questionScore);
        newQuestion.setIsObjective(newBranches.size() == 1 && newBranches.get(0).getIsObjective());
        newQuestion.setBranches(newBranches);

        // 试卷结构中替换原题
        List<PaperMain> paperMainList = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds).select(PaperMain::getId, PaperMain::getPaperStyleJson));
        for (PaperMain paperMain : paperMainList) {
            String newStructure = replacePaperStructureQuestion(questionId, paperMain.getPaperStyleJson(), newQuestion);
            result.put(paperMain.getId(), newStructure);
        }
        return result;
    }

    /**
     * 替换结构中题目
     * @param questionId 题目Id
     * @param oldStructure 旧试卷题目结构
     * @param newQuestion 新结构题目
     */
    private String replacePaperStructureQuestion(String questionId, String oldStructure, StructureQuestion newQuestion) {
        // 深复制
        newQuestion = JSON.parseObject(JSON.toJSONString(newQuestion), StructureQuestion.class, Feature.OrderedField);

        // 试卷题目（要替换的题除外）
        List<StructureQuestion> paperQuestionList = new ArrayList<>();
        // 对应旧题题号
        int oldCode = 0;
        // 题号改变，
        int codeDiff = 0;

        PaperStructure structure = JSON.parseObject(oldStructure, PaperStructure.class, Feature.OrderedField);
        for (StructureVolume volume : structure.getVolumes()) {
            for (StructureTopic topic : volume.getTopics()) {
                List<StructureQuestion> topicQuestions = topic.getQuestions();
                for (int qIdx = 0; qIdx < topicQuestions.size(); qIdx++) {
                    StructureQuestion oldQuestion = topicQuestions.get(qIdx);
                    // 替换旧题
                    if (oldQuestion.getId().equals(questionId)) {
                        // 相同小题分数保持不变
                        List<StructureBranch> oldBranches = oldQuestion.getBranches();
                        Map<String, Float> oldBranchScoreMap = oldBranches.stream().collect(Collectors.toMap(StructureBranch::getId, StructureBranch::getScore));
                        List<StructureBranch> newBranches = newQuestion.getBranches();
                        newBranches.forEach(newBranch -> {
                            Float oldBranchScore = oldBranchScoreMap.get(newBranch.getId());
                            if (oldBranchScore != null) {
                                newBranch.setScore(oldBranchScore);
                            }
                        });
                        float questionScore = newBranches.stream().map(StructureBranch::getScore)
                                .filter(Objects::nonNull).reduce(0F, Float::sum);
                        newQuestion.setScore(questionScore);

                        topicQuestions.set(qIdx, newQuestion);
                        // 题号保持不变
                        newQuestion.setCode(oldQuestion.getCode());

                        // 题号为0表示小题展开，小题展开情形要更新各小题题号及后续题目题号
                        if (oldQuestion.getCode() == 0) {
                            int code = oldBranches.get(0).getCode();
                            for (StructureBranch newBranch : newBranches) {
                                newBranch.setCode(code++);
                            }
                            if (newBranches.size() != oldBranches.size()) {
                                oldCode = oldBranches.get(0).getCode();
                                codeDiff = newBranches.size() - oldBranches.size();
                            }
                        }
                    } else {
                        paperQuestionList.add(oldQuestion);
                    }
                }
            }
        }

        // 若新旧题目小题数不同，且旧题目在原卷中小题展开，则后续题目需要更新题号
        if (oldCode > 0) {
            for (StructureQuestion q : paperQuestionList) {
                if (StringUtils.equals(q.getId(), newQuestion.getId())) {
                    continue;
                }
                if (q.getCode() > oldCode) {
                    q.setCode(q.getCode() + codeDiff);
                } else if (q.getCode() == 0) {
                    for (StructureBranch b : q.getBranches()) {
                        if (b.getCode() > oldCode) {
                            b.setCode(b.getCode() + codeDiff);
                        }
                    }
                }
            }
        }

        return JSON.toJSONString(structure);
    }

    /**
     * 编辑修改题目知识点
     */
    @Override
    public void editorChangeQuestionsKnowledge(List<ChangeKnowledgeParam> changeKnowledgeParamList, String editorId) {
        // 查题目
        Set<String> questionIds =  changeKnowledgeParamList.stream().map(ChangeKnowledgeParam::getQuestionId).collect(Collectors.toSet());
        if (questionIds.size() != changeKnowledgeParamList.size()) {
            throw new ApiException("题目重复");
        }

        // 检查权限
        checkQuestionsExistAndHasPermission(editorId, questionIds);

        // 查小题
        List<SmallQuestion> smallQuestionList = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                .in(SmallQuestion::getQuestionId, questionIds)
                .select(SmallQuestion::getId, SmallQuestion::getQuestionId));
        Map<String, Set<String>> questionIdBranchIdsMap = smallQuestionList.stream().collect(Collectors.groupingBy(SmallQuestion::getQuestionId,
                Collectors.mapping(SmallQuestion::getId, Collectors.toSet())));

        // 构建新小题知识点
        List<QuestionKnowledge> newQuestionKnowledgeList = new ArrayList<>();
        changeKnowledgeParamList.forEach(changeKnowledgeParam -> {
            Set<String> branchIds = questionIdBranchIdsMap.get(changeKnowledgeParam.getQuestionId());
            if (branchIds == null) {
                throw new ApiException("小题不存在");
            }
            changeKnowledgeParam.getBranches().forEach(changeKnowledgeBranchParam -> {
                if (!branchIds.contains(changeKnowledgeBranchParam.getBranchId())) {
                    throw new ApiException("小题不存在");
                }
                branchIds.remove(changeKnowledgeBranchParam.getBranchId());
                changeKnowledgeBranchParam.getKnowledgeIds().stream().sorted().forEach(knowledgeId -> {
                    QuestionKnowledge item = new QuestionKnowledge();
                    item.setId(IdUtil.longId());
                    item.setQuestionId(changeKnowledgeBranchParam.getBranchId());
                    item.setKnowledgeId(knowledgeId);
                    newQuestionKnowledgeList.add(item);
                });
            });
            if (!branchIds.isEmpty()) {
                throw new ApiException("小题不完整");
            }
        });

        // 检查知识点
        Set<Integer> knowledgeIds = newQuestionKnowledgeList.stream().map(QuestionKnowledge::getKnowledgeId).collect(Collectors.toSet());
        int knowledgeCount = knowledgeMainMapper.selectCount(new LambdaQueryWrapper<KnowledgeMain>().in(KnowledgeMain::getId, knowledgeIds));
        if (knowledgeCount != knowledgeIds.size()) {
            throw new ApiException("知识点不存在");
        }

        Set<String> branchIds = smallQuestionList.stream().map(SmallQuestion::getId).collect(Collectors.toSet());
        transactionTemplate.execute(status -> {
            // 删除旧小题知识点
            questionKnowledgeMapper.deleteBySmallQuestionIds(branchIds);
            // 插入新小题知识点
            questionKnowledgeMapper.insertList(newQuestionKnowledgeList);
            // 同步es
            questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);
            return null;
        });
    }

    /**
     * 检查题目存在，且编辑具有题目所在学段学科权限
     */
    private void checkQuestionsExistAndHasPermission(String editorId, Collection<String> questionIds) {
        List<Question> questionList = questionMapper.selectList(new LambdaQueryWrapper<Question>()
                .in(Question::getId, questionIds)
                .select(Question::getId, Question::getGradeLevel, Question::getSubjectId));
        if (questionList.size() != questionIds.size()) {
            throw new ApiException("题目不存在");
        }

        // 检查权限
        Map<Integer, Set<Integer>> gradeSubjectIdSetMap = questionList.stream().collect(Collectors.groupingBy(Question::getGradeLevel,
                Collectors.mapping(Question::getSubjectId, Collectors.toSet())));
        gradeSubjectIdSetMap.forEach((gradeLevel, subjectIds) -> subjectIds.forEach(subjectId -> permissionService.checkEditorPermission(editorId, gradeLevel, subjectId)));
    }

    /**
     * 编辑修改题目章节
     */
    @Override
    public void editorChangeQuestionsChapter(List<ChangeChapterParam> changeChapterParamList, String editorId) {
        // 查题目
        Set<String> questionIds =  changeChapterParamList.stream().map(ChangeChapterParam::getQuestionId).collect(Collectors.toSet());
        if (questionIds.size() != changeChapterParamList.size()) {
            throw new ApiException("题目重复");
        }

        // 检查权限
        checkQuestionsExistAndHasPermission(editorId, questionIds);

        // 查章节
        Set<Integer> chapterIds = changeChapterParamList.stream().flatMap(param -> param.getChapterIds().stream()).collect(Collectors.toSet());
        int chapterCount = chapterMapper.selectCount(new LambdaQueryWrapper<Chapter>().in(Chapter::getId, chapterIds));
        if (chapterCount != chapterIds.size()) {
            throw new ApiException("章节不存在");
        }

        // 构建新题目章节
        List<QuestionBook> newQuestionBookList = new ArrayList<>();
        changeChapterParamList.forEach(param -> param.getChapterIds().forEach(chapterId -> {
            QuestionBook qb = new QuestionBook();
            qb.setId(IdUtil.longId());
            qb.setChapterId(chapterId);
            qb.setQuestionId(param.getQuestionId());
            newQuestionBookList.add(qb);
        }));

        transactionTemplate.execute(status -> {
            // 删除旧题目章节
            questionBookMapper.delete(new LambdaQueryWrapper<QuestionBook>()
                    .in(QuestionBook::getQuestionId, questionIds));
            // 插入新题目章节
            questionBookMapper.insertList(newQuestionBookList);
            // 同步es
            questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);
            return null;
        });
    }


    /**
     * 修改题目的作答次数和得分率
     */
    @Override
    public void updateQues(List<UpdateQues> updateQuesList) {
        List<Question> questions = updateQuesList.stream().map(updateQues ->
                        Question.builder().id(updateQues.getQuesId()).answerCount(updateQues.getAnswerCount()).scoreRate(updateQues.getScoreRate()).esSync(0).build())
                .collect(Collectors.toList());
        this.updateBatchById(questions);
        List<String> questionIds = questions.stream().map(Question::getId).collect(Collectors.toList());
        questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);
    }
}
