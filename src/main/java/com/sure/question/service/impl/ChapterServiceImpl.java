package com.sure.question.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.cache.Cache;
import com.sure.question.common.IdWorker;
import com.sure.question.entity.*;
import com.sure.question.enums.BaseEnum;
import com.sure.question.enums.GradeLevelEnum;
import com.sure.question.mapper.*;
import com.sure.question.service.ChapterService;
import com.sure.question.service.KnowledgeMainService;
import com.sure.question.util.CacheUtil;
import com.sure.question.util.RedissonLockUtil;
import com.sure.question.util.excelExport.Column;
import com.sure.question.util.excelExport.Sheet;
import com.sure.question.util.excelExport.Style;
import com.sure.question.util.excelExport.Workbook;
import com.sure.question.vo.KnowledgeMainVo;
import com.sure.question.vo.chapterVo.*;
import com.sure.question.vo.dataVo.IntIntVo;
import com.sure.question.vo.dataVo.StrStrVo;
import com.sure.question.vo.excelImport.IImportOperation;
import com.sure.question.vo.excelImport.IImportRow;
import com.sure.question.vo.excelImport.IOperationItem;
import com.sure.question.vo.excelImport.TreeNode;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RequiredArgsConstructor
@Service
public class ChapterServiceImpl extends ServiceImpl<ChapterMapper, Chapter> implements ChapterService {
    private final Cache cache;
    private final ChapterMapper chapterMapper;
    private final ChapterKnowledgeMapper chapterKnowledgeMapper;
    private final QuestionBookMapper questionBookMapper;
    private final BookMapper bookMapper;
    private final BookCategoryMapper bookCategoryMapper;
    private final KnowledgeMainMapper knowledgeMainMapper;
    private final KnowledgeMainService knowledgeMainService;
    private final IdWorker idWorker;
    private final QuestionEsSyncMapper questionEsSyncMapper;
    private final TransactionTemplate transactionTemplate;

    /**
     * 查一批章节及其所有后代章节关联的知识点及其所有后代知识点Id
     */
    @Override
    public List<Integer> selectChaptersSelfAndDescendantKnowledgeIds(Collection<Integer> chapterIds) {
        if (chapterIds == null || chapterIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 查章节及后代
        chapterIds = chapterMapper.selectSelfAndDescendantIds(chapterIds);
        if (chapterIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 查关联知识点
        List<Integer> knowledgeIds = chapterKnowledgeMapper.selectChapterKnowledgeIds(chapterIds);
        if (knowledgeIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 查知识点及后代
        return knowledgeMainMapper.selectSelfAndDescendantIds(knowledgeIds);
    }

    /**
     * 从缓存查教材章节树
     */
    public List<ChapterVo> getBookChapterTreeWithCache(int bookId) {
        String key = cache.getBookChapterTreeKey(bookId);
        String treeJson = cache.redisTemplateGet(key);
        if (StringUtils.isNotEmpty(treeJson)) {
            return JSON.parseArray(treeJson, ChapterVo.class);
        }
        List<ChapterVo> chapterTree = getBookChapterTree(bookId);
        if (!chapterTree.isEmpty()) {
            cache.redisTemplateSet(key, JSON.toJSONString(chapterTree));
        }
        return chapterTree;
    }

    private List<ChapterVo> getBookChapterTree(int bookId) {
        Book book = bookMapper.selectById(bookId);
        if (book == null) {
            throw new ApiException("找不到教材");
        }
        Map<Book, List<ChapterKnowledgeTreeNode>> bookChapterTreeMap = getBookChapterTreeMap(Collections.singletonList(book));
        List<ChapterKnowledgeTreeNode> tree = bookChapterTreeMap.get(book);
        if (tree == null || tree.isEmpty()) {
            return Collections.emptyList();
        }
        return convertChapterTree(tree);
    }

    private Map<Book, List<ChapterKnowledgeTreeNode>> getBookChapterTreeMap(List<Book> books) {
        int gradeLevelId = books.get(0).getGradeLevel();
        int subjectId = books.get(0).getSubjectId();
        if (!books.stream().allMatch(book -> book.getGradeLevel() == gradeLevelId && book.getSubjectId() == subjectId)) {
            throw new ApiException("各教材不属于同一学段学科");
        }

        // 查教材所有章节
        Map<Book, List<Chapter>> bookChapterMap = new LinkedHashMap<>();
        for (Book book : books) {
            List<Chapter> chapters = chapterMapper.selectList(new LambdaQueryWrapper<Chapter>().eq(Chapter::getBookId, book.getId()));
            bookChapterMap.put(book, chapters);
        }

        // 查章节关联知识点
        List<Integer> chapterIds = bookChapterMap.values().stream().flatMap(Collection::stream).map(Chapter::getId).collect(Collectors.toList());
        Map<Integer, List<KnowledgeMain>> chapterIdKnowledgeListMap = selectChapterIdKnowledgeListMap(gradeLevelId, subjectId, chapterIds);

        // 分教材构建章节树
        Map<Book, List<ChapterKnowledgeTreeNode>> result = new LinkedHashMap<>();
        bookChapterMap.forEach((book, chapters) -> {
            // 按父级Id分组
            Map<Integer, List<Chapter>> parentIdChaptersMap = chapters.stream().filter(x -> x.getPid() != null).collect(Collectors.groupingBy(Chapter::getPid));
            List<ChapterKnowledgeTreeNode> tree = buildChapterTree(0, parentIdChaptersMap, chapterIdKnowledgeListMap);
            result.put(book, tree);
        });
        return result;
    }

    private Map<Integer, List<KnowledgeMain>> selectChapterIdKnowledgeListMap(int gradeLevelId, int subjectId, Collection<Integer> chapterIds) {
        // 分批查章节知识点关联
        List<ChapterKnowledge> chapterKnowledgeList = new ArrayList<>();
        CollUtil.split(chapterIds, 1000).forEach(batch -> {
            List<ChapterKnowledge> partial = chapterKnowledgeMapper.selectList(new LambdaQueryWrapper<ChapterKnowledge>()
                    .in(ChapterKnowledge::getChapterId, batch));
            chapterKnowledgeList.addAll(partial);
        });
        if (chapterKnowledgeList.isEmpty()) {
            return Collections.emptyMap();
        }

        // 查学段学科知识点
        List<KnowledgeMain> knowledgeList = knowledgeMainService.getGradeLevelSubjectKnowledgeList(gradeLevelId, subjectId);
        Map<Integer, KnowledgeMain> knowledgeMap = knowledgeList.stream().collect(Collectors.toMap(KnowledgeMain::getId, Function.identity()));
        List<Integer> knowledgeIdList = knowledgeList.stream().map(KnowledgeMain::getId).collect(Collectors.toList());

        // 组装
        Map<Integer, List<KnowledgeMain>> result = new HashMap<>();
        Map<Integer, List<ChapterKnowledge>> chapterKnowledgeMap = chapterKnowledgeList.stream().collect(Collectors.groupingBy(ChapterKnowledge::getChapterId));
        chapterKnowledgeMap.forEach((chapterId, list) -> {
            List<KnowledgeMain> knowledgeMains = list.stream().map(ChapterKnowledge::getKnowledgeId)
                    // 章节下知识点排序
                    .sorted(Comparator.comparing(knowledgeIdList::indexOf))
                    .map(knowledgeMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            if (!knowledgeMains.isEmpty()) {
                result.put(chapterId, knowledgeMains);
            }
        });
        return result;
    }

    private List<ChapterKnowledgeTreeNode> buildChapterTree(int parentId, Map<Integer, List<Chapter>> parentIdChaptersMap,
                                                            Map<Integer, List<KnowledgeMain>> chapterIdKnowledgeListMap) {
        List<Chapter> chapters = parentIdChaptersMap.get(parentId);
        if (chapters == null || chapters.isEmpty()) {
            return Collections.emptyList();
        }
        List<ChapterKnowledgeTreeNode> nodes = new ArrayList<>();
        chapters.sort(Comparator.comparing(Chapter::getOrderId));
        for (Chapter chapter : chapters) {
            ChapterKnowledgeTreeNode node = new ChapterKnowledgeTreeNode();
            nodes.add(node);
            node.setChapter(chapter);
            node.setKnowledgeList(chapterIdKnowledgeListMap.getOrDefault(chapter.getId(), new ArrayList<>()));
            node.setChildren(buildChapterTree(chapter.getId(), parentIdChaptersMap, chapterIdKnowledgeListMap));
        }
        return nodes;
    }

    private List<ChapterVo> convertChapterTree(List<ChapterKnowledgeTreeNode> nodes) {
        List<ChapterVo> chapterVos = new ArrayList<>();
        for (ChapterKnowledgeTreeNode node : nodes) {
            ChapterVo chapterVo = new ChapterVo();
            chapterVos.add(chapterVo);
            chapterVo.setId(node.getChapter().getId());
            chapterVo.setChapterName(node.getChapter().getChapterName());
            chapterVo.setBookId(node.getChapter().getBookId());
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                chapterVo.setChildren(convertChapterTree(node.getChildren()));
            } else if (node.getKnowledgeList() != null && !node.getKnowledgeList().isEmpty()) {
                List<ChapterKnowledgeVo> knowledgeVos = node.getKnowledgeList().stream()
                        .map(k -> new ChapterKnowledgeVo(k.getId(), k.getKnowledgeName())).collect(Collectors.toList());
                chapterVo.setChildren(knowledgeVos);
            }
        }
        return chapterVos;
    }

    /**
     * 清除缓存
     */
    @Override
    public void deleteCache(Integer gradeLevel, Integer subjectId) {
        List<Book> books = bookMapper.selectList(new LambdaQueryWrapper<Book>()
                .eq(gradeLevel != null, Book::getGradeLevel, gradeLevel)
                .eq(subjectId != null, Book::getSubjectId, subjectId));
        for (Book book : books) {
            String key = cache.getBookChapterTreeKey(book.getId());
            cache.redisTemplateDelete(key);
        }
    }

    /**
     * 导出教材章节
     */
    @Override
    public void exportExcel(int gradeLevel, int subjectId, Integer categoryId, Integer bookId, OutputStream outputStream) throws IOException {
        // 查版本教材
        List<Book> books = bookMapper.selectList(new LambdaQueryWrapper<Book>()
                .eq(Book::getGradeLevel, gradeLevel)
                .eq(Book::getSubjectId, subjectId)
                .eq(categoryId != null, Book::getCategoryId, categoryId)
                .eq(bookId != null, Book::getId, bookId)
                .orderByAsc(Book::getCategoryId)
                .orderByAsc(Book::getOrderId));
        if (books.isEmpty()) {
            throw new ApiException("找不到教材");
        }

        // 查版本
        Map<Integer, String> bookCategoryMap = getBookCategoryMap(gradeLevel, subjectId);

        // 查章节树
        Map<Book, List<ChapterKnowledgeTreeNode>> bookChapterTreeMap = getBookChapterTreeMap(books);

        // 整理出行
        List<ExportExcelItem> items = new ArrayList<>();
        bookChapterTreeMap.forEach((book, chapterTree) -> {
            List<ExportExcelItem> bookItems = extractExportExcelItem(chapterTree, 0);
            bookItems.forEach(item -> {
                item.setCategoryName(bookCategoryMap.get(book.getCategoryId()));
                item.setBookName(book.getBookName());
            });
            items.addAll(bookItems);
        });

        // 创建excel
        List<Column<ExportExcelItem>> columns = new ArrayList<>();
        String gradeLevelName = BaseEnum.getNameById(GradeLevelEnum.class, gradeLevel);
        columns.add(new Column<>("学段", x -> gradeLevelName));
        String subjectName = CacheUtil.getSubjectName(subjectId);
        columns.add(new Column<>("学科", x -> subjectName));
        columns.add(new Column<>("版本", ExportExcelItem::getCategoryName));
        columns.add(new Column<>("教材", ExportExcelItem::getBookName));
        columns.add(new Column<>("章", ExportExcelItem::getChapter1Name));
        columns.add(new Column<>("节", ExportExcelItem::getChapter2Name));
        columns.add(new Column<>("小节", ExportExcelItem::getChapter3Name));
        columns.add(new Column<>("知识点", ExportExcelItem::getKnowledgeName));
        columns.forEach(Column::setColumnAutoWidth);

        String sheetName = gradeLevelName + subjectName;
        if (categoryId != null || bookId != null) {
            sheetName += bookCategoryMap.get(books.get(0).getCategoryId());
        }
        if (bookId != null) {
            sheetName += books.get(0).getBookName();
        }

        Sheet<ExportExcelItem> sheet = new Sheet<>();
        sheet.setSheetName(sheetName);
        sheet.setColumns(columns);
        sheet.setFreezeHeader(true);

        new Workbook().addSheet(sheet, items).write(outputStream);
    }

    /**
     * 获取版本Id名称映射
     */
    private Map<Integer, String> getBookCategoryMap(int gradeLevel, int subjectId) {
        return bookCategoryMapper.selectList(new LambdaQueryWrapper<BookCategory>()
                        .eq(BookCategory::getGradeLevel, gradeLevel)
                        .eq(BookCategory::getSubjectId, subjectId))
                .stream().collect(Collectors.toMap(BookCategory::getCategoryId, BookCategory::getCategoryName));
    }

    private List<ExportExcelItem> extractExportExcelItem(List<ChapterKnowledgeTreeNode> nodes, int level) {
        List<ExportExcelItem> items = new ArrayList<>();
        for (ChapterKnowledgeTreeNode node : nodes) {
            List<ExportExcelItem> thisNodeItems = new ArrayList<>();

            // 有知识点，则每个知识点加一行
            // 没有知识点没有下级章节，仅加章节本身一行
            // 没有知识点有下级章节，章节本身不加入
            boolean hasChildren = node.getChildren() != null && !node.getChildren().isEmpty();
            boolean hasKnowledge = node.getKnowledgeList() != null && !node.getKnowledgeList().isEmpty();
            if (!hasKnowledge && !hasChildren) {
                thisNodeItems.add(new ExportExcelItem());
            }
            if (hasKnowledge) {
                for (KnowledgeMain knowledgeMain : node.getKnowledgeList()) {
                    ExportExcelItem item = new ExportExcelItem();
                    item.setKnowledgeName(knowledgeMain.getKnowledgeName());
                    thisNodeItems.add(item);
                }
            }
            if (hasChildren) {
                thisNodeItems.addAll(extractExportExcelItem(node.getChildren(), level + 1));
            }

            // 根据层级设置章节名称
            String chapterName = node.getChapter().getChapterName();
            if (level == 0) {
                thisNodeItems.forEach(item -> item.setChapter1Name(chapterName));
            } else if (level == 1) {
                thisNodeItems.forEach(item -> item.setChapter2Name(chapterName));
            } else if (level == 2) {
                thisNodeItems.forEach(item -> item.setChapter3Name(chapterName));
            }

            items.addAll(thisNodeItems);
        }
        return items;
    }

    @Data
    private static class ExportExcelItem {
        private String categoryName;
        private String bookName;
        private String chapter1Name;
        private String chapter2Name;
        private String chapter3Name;
        private String knowledgeName;
    }

    /**
     * 导入教材章节
     */
    @Override
    public void importExcel(int gradeLevel, int subjectId, InputStream inputStream, OutputStream outputStream, boolean save, String userId) {
        RedissonLockUtil.executeAfterLocked("Ques:Lock:ChangeBookChapterKnowledge",
                () -> doImportExcel(gradeLevel, subjectId, inputStream, outputStream, save, userId));
    }

    private void doImportExcel(int gradeLevelId, int subjectId, InputStream inputStream, OutputStream outputStream, boolean save, String userId) {
        DoImportContext context = new DoImportContext();
        context.gradeLevelId = gradeLevelId;
        context.subjectId = subjectId;
        context.bookCategoryMap = getBookCategoryMap(gradeLevelId, subjectId);
        
        // 读取excel
        context.records = extractExcelRecords(inputStream);
        // 检查每行
        context.books = checkRows(context.records, gradeLevelId, subjectId, context.bookCategoryMap);

        // 构建操作树
        context.operationTree = IImportOperation.buildOperationItemTree(context.records, (a, b) -> {
            a.getRows().addAll(b.getRows());
            a.getKnowledgeOperations().addAll(b.getKnowledgeOperations());
        });
        context.operationItems = TreeNode.getFlatElements(context.operationTree);
        // 检查树节点各行
        checkTreeNodeRows(context.operationItems);

        // 清空导入原始数据
        context.records = null;
        context.operationItems.forEach(item -> item.setRows(null));

        // 构建系统教材章节树
        context.dbChapterTree = buildDBChapterTree(context.books, context.bookCategoryMap);
        context.dbChapterMap = TreeNode.getFlatElements(context.dbChapterTree).stream().collect(Collectors.toMap(Chapter::getId, Function.identity()));
        // 与系统章节对比，添加章节Id
        IOperationItem.addOperationItemTreeIdThrowEx(context.operationTree, context.dbChapterTree, Chapter::getId, Chapter::getChapterName, "章节");
        // 检查章节操作
        checkOperation(context.operationTree);

        // 构建系统知识点树
        context.dbKnowledgeTree = knowledgeMainService.getGradeLevelSubjectKnowledgeTree(gradeLevelId, subjectId);
        // 检查知识点
        checkKnowledge(context.operationItems, context.dbKnowledgeTree);
        // 查章节关联知识点
        context.dbChapterIdKnowledgeListMap = selectDBChapterKnowledgeListMap(context.operationItems, context.gradeLevelId, context.subjectId);
        // 检查知识点操作
        checkKnowledgeOperation(context.operationItems, context.dbChapterIdKnowledgeListMap);

        // 提取变更
        Changes changes = extractChanges(context);
        // 整理变更后的新版章节
        buildNewChapterList(changes, context.dbChapterMap);
        // 检查章节名称重复
        checkChapterNameDuplicate(changes.newList, context.books, context.bookCategoryMap);

        // 查关联删除
        selectRelativeDeletes(changes);

        // 保存
        if (save) {
            saveDB(changes, userId);
            deleteCache(gradeLevelId, subjectId);
        }

        // 导出变更
        exportChanges(context, changes, outputStream);
    }

    /**
     * 上下文数据
     */
    private static class DoImportContext {
        private int gradeLevelId;
        private int subjectId;
        private Map<Integer, String> bookCategoryMap;
        public List<ChapterImportVo> records;
        public List<Book> books;
        public List<TreeNode<ChapterOperationItem>> operationTree;
        public List<ChapterOperationItem> operationItems;
        public List<TreeNode<Chapter>> dbChapterTree;
        public Map<Integer, Chapter> dbChapterMap;
        public List<KnowledgeMainVo> dbKnowledgeTree;
        public Map<Integer, List<KnowledgeMain>> dbChapterIdKnowledgeListMap;
        public Supplier<Integer> chapterIdGenerator;
    }

    /**
     * 从Excel中提取数据
     */
    private List<ChapterImportVo> extractExcelRecords(InputStream inputStream) {
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        reader.addHeaderAlias("学段", "gradeLevelName");
        reader.addHeaderAlias("学科", "subjectName");
        reader.addHeaderAlias("版本", "categoryName");
        reader.addHeaderAlias("教材", "bookName");
        reader.addHeaderAlias("章", "chapter1Name");
        reader.addHeaderAlias("节", "chapter2Name");
        reader.addHeaderAlias("小节", "chapter3Name");
        reader.addHeaderAlias("章节操作", "operation");
        reader.addHeaderAlias("知识点", "knowledgeName");
        reader.addHeaderAlias("知识点操作", "knowledgeOperation");
        List<ChapterImportVo> records = reader.setIgnoreEmptyRow(false).readAll(ChapterImportVo.class);
        if (records.isEmpty()) {
            throw new ApiException("excel中无数据");
        }
        IImportRow.setSequentialRowNo(records, 2);
        return records;
    }

    /**
     * 检查每行
     */
    private List<Book> checkRows(List<ChapterImportVo> vos, int gradeLevelId, int subjectId, Map<Integer, String> bookCategoryMap) {
        vos.forEach(ChapterImportVo::clean);

        IImportOperation.checkGradeLevel(vos, gradeLevelId);

        IImportOperation.checkSubject(vos, subjectId);

        IImportOperation.checkRuleThrowEx("版本不能为空", vos, vo -> StringUtils.isEmpty(vo.getCategoryName()));
        List<Book> books = bookMapper.selectList(new LambdaQueryWrapper<Book>()
                .eq(Book::getGradeLevel, gradeLevelId)
                .eq(Book::getSubjectId, subjectId));
        Map<String, Integer> categoryMap = books.stream().collect(Collectors.toMap(book -> bookCategoryMap.get(book.getCategoryId()), Book::getCategoryId, (a, b) -> a));
        vos.forEach(vo -> vo.setCategoryId(categoryMap.get(vo.getCategoryName())));
        IImportOperation.checkRuleThrowEx("版本不存在", vos, vo -> vo.getCategoryId() == null);

        IImportOperation.checkRuleThrowEx("教材不能为空", vos, vo -> StringUtils.isEmpty(vo.getBookName()));
        Map<String, Integer> bookNameIdMap = books.stream().collect(Collectors.toMap(book -> bookCategoryMap.get(book.getCategoryId()) + book.getBookName(), Book::getId));
        vos.forEach(vo -> vo.setBookId(bookNameIdMap.get(vo.getCategoryName() + vo.getBookName())));
        IImportOperation.checkRuleThrowEx("教材不存在", vos, vo -> vo.getBookId() == null);

        IImportOperation.checkRuleThrowEx("章不能为空", vos, vo -> StringUtils.isEmpty(vo.getChapter1Name()));
        IImportOperation.checkRuleThrowEx("节为空时不能有小节", vos, vo -> StringUtils.isEmpty(vo.getChapter2Name()) && StringUtils.isNotEmpty(vo.getChapter3Name()));

        IImportOperation.checkOperation(vos, Arrays.asList("新增", "删除", "改名称", "移入", "移出"), Arrays.asList("新增", "删除"));

        IImportOperation.checkRuleThrowEx("未知知识点操作", vos, vo -> StringUtils.isNotBlank(vo.getKnowledgeOperation()) && !StringUtils.equalsAny(vo.getKnowledgeOperation(), "新增", "删除"));

        Map<Integer, Book> bookIdMap = books.stream().collect(Collectors.toMap(Book::getId, Function.identity()));
        return vos.stream().map(ChapterImportVo::getBookId).distinct().map(bookIdMap::get).collect(Collectors.toList());
    }

    /**
     * 检查树节点各行
     */
    private void checkTreeNodeRows(List<ChapterOperationItem> operationItems) {
        Function<ChapterOperationItem, String> nameGetter = item -> {
            String rows = item.getRows().stream().map(ChapterImportVo::getRowNo).map(String::valueOf)
                    .collect(Collectors.joining(", "));
            return "第 " + rows + " 行";
        };

        IOperationItem.throwLongEx("同一章节各行不连续", operationItems, nameGetter, 10, item -> {
            if (item.getRows() == null || item.getRows().isEmpty()) {
                return false;
            }
            int minRowNo = item.getRows().stream().mapToInt(ChapterImportVo::getRowNo).min().orElse(0);
            int maxRowNo = item.getRows().stream().mapToInt(ChapterImportVo::getRowNo).max().orElse(0);
            return maxRowNo - minRowNo + 1 != item.getRows().size();
        });

        IOperationItem.throwLongEx("同一章节各行操作不一致", operationItems, nameGetter, 10, item -> {
            if (item.getRows() == null || item.getRows().isEmpty()) {
                return false;
            }
            String operation = item.getRows().get(0).getOperation();
            List<ChapterImportVo> notFirstRows = item.getRows().subList(1, item.getRows().size());
            boolean allEmpty = notFirstRows.stream().allMatch(row -> StringUtils.isEmpty(row.getOperation()));
            boolean allSame = notFirstRows.stream().allMatch(row -> StringUtils.equals(row.getOperation(), operation));
            return !allEmpty && !allSame;
        });

        IOperationItem.throwLongEx("同一章节各行知识点重复", operationItems, nameGetter, 10, item -> {
            if (item.getRows() == null || item.getRows().isEmpty()) {
                return false;
            }
            Set<String> knowledgeNames = new HashSet<>();
            for (ChapterImportVo row : item.getRows()) {
                if (knowledgeNames.contains(row.getKnowledgeName())) {
                    return true;
                }
                knowledgeNames.add(row.getKnowledgeName());
            }
            return false;
        });
    }

    /**
     * 构建系统章节树
     */
    private List<TreeNode<Chapter>> buildDBChapterTree(List<Book> books, Map<Integer, String> bookCategoryMap) {
        Set<Integer> bookIds = books.stream().map(Book::getId).collect(Collectors.toSet());
        List<Chapter> chapters = chapterMapper.selectList(new LambdaQueryWrapper<Chapter>().in(Chapter::getBookId, bookIds));
        Map<Integer, List<Chapter>> chaptersGroupByBook = chapters.stream().collect(Collectors.groupingBy(Chapter::getBookId));
        List<TreeNode<Chapter>> result = new ArrayList<>();
        // 保持导入excel中教材顺序
        int bookIndex = 0;
        for (Book book : books) {
            List<Chapter> thisBookChapters = chaptersGroupByBook.get(book.getId());
            List<TreeNode<Chapter>> chapterNodes = thisBookChapters == null ? Collections.emptyList() :
                    TreeNode.buildTree(thisBookChapters, Chapter::getId, Chapter::getPid);
            // 将教材当作顶级章节，层级为-1，id取负值避免与章节Id重复
            TreeNode<Chapter> bookNode = new TreeNode<>();
            Chapter bookItem = new Chapter();
            bookItem.setId(-book.getId());
            bookItem.setChapterName(bookCategoryMap.get(book.getCategoryId()) + book.getBookName());
            bookItem.setPid(0);
            bookItem.setLevel(-1);
            bookItem.setOrderId(bookIndex++);
            bookNode.setItem(bookItem);
            bookNode.setChildren(chapterNodes);
            chapterNodes.forEach(node -> node.setParent(bookNode));
            result.add(bookNode);
        }
        return result;
    }

    /**
     * 检查章节操作
     */
    private void checkOperation(List<TreeNode<ChapterOperationItem>> operationTree) {
        IOperationItem.checkOperation(operationTree, "章节");
        IOperationItem.checkNodesThrowEx(operationTree, node -> {
            if (!StringUtils.equalsAny(node.getItem().getOperationName(), "移入", "移出")) {
                return false;
            }
            TreeNode<ChapterOperationItem> another = IOperationItem.findNodeByFullName(node.getItem().getOperationValue(), operationTree);
            return another == null || !Objects.equals(another.getItem().getCategoryId(), node.getItem().getCategoryId());
        }, "移入移出必须在同一个版本内");
    }

    /**
     * 检查知识点
     */
    private void checkKnowledge(List<ChapterOperationItem> operationItems, List<KnowledgeMainVo> dbKnowledgeTree) {
        Map<String, Integer> knowledgeNameIdMap = new HashMap<>();
        buildKnowledgeNameIdMap(dbKnowledgeTree, knowledgeNameIdMap);
        List<ChapterOperationItem.KnowledgeOperation> knowledgeOperations = operationItems.stream().flatMap(item -> item.getKnowledgeOperations().stream()).collect(Collectors.toList());
        knowledgeOperations.forEach(k -> k.setKnowledgeId(knowledgeNameIdMap.get(k.getKnowledgeName())));
        IOperationItem.throwLongEx("系统不存在知识点", knowledgeOperations, ChapterOperationItem.KnowledgeOperation::getKnowledgeName, 100, k -> k.getKnowledgeId() == null);
    }
    /**
     * 构建知识点名称Id映射
     */
    private void buildKnowledgeNameIdMap(List<KnowledgeMainVo> vos, Map<String, Integer> result) {
        for (KnowledgeMainVo vo : vos) {
            result.put(vo.getKnowledgeName(), vo.getId());
            if (vo.getKnowledgeVoList() != null) {
                buildKnowledgeNameIdMap(vo.getKnowledgeVoList(), result);
            }
        }
    }

    /**
     * 查章节知识点关联
     */
    private Map<Integer, List<KnowledgeMain>> selectDBChapterKnowledgeListMap(List<ChapterOperationItem> items, int gradeLevelId, int subjectId) {
        List<Integer> chapterIds = items.stream().map(ChapterOperationItem::getId).filter(id -> id != null && id > 0).collect(Collectors.toList());
        if (chapterIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return selectChapterIdKnowledgeListMap(gradeLevelId, subjectId, chapterIds);
    }

    /**
     * 检查知识点操作
     */
    private void checkKnowledgeOperation(List<ChapterOperationItem> items, Map<Integer, List<KnowledgeMain>> chapterIdKnowledgeListMap) {
        List<String> notInExcel = new ArrayList<>();
        List<String> mustAdd = new ArrayList<>();
        List<String> shouldNotAdd = new ArrayList<>();

        for (ChapterOperationItem item : items) {
            Map<Integer, String> dbKnowledgeMap = chapterIdKnowledgeListMap.getOrDefault(item.getId(), new ArrayList<>()).stream().collect(Collectors.toMap(KnowledgeMain::getId, KnowledgeMain::getKnowledgeName));
            StringBuilder sbMustAdd = new StringBuilder();
            StringBuilder sbShouldNotAdd = new StringBuilder();
            for (ChapterOperationItem.KnowledgeOperation k : item.getKnowledgeOperations()) {
                if (dbKnowledgeMap.containsKey(k.getKnowledgeId())) {
                    if ("新增".equals(k.getKnowledgeOperation())) {
                        sbShouldNotAdd.append("、").append(k.getKnowledgeName());
                    }
                    dbKnowledgeMap.remove(k.getKnowledgeId());
                } else if (!"新增".equals(k.getKnowledgeOperation())) {
                    sbMustAdd.append("、").append(k.getKnowledgeOperation());
                }
            }
            if (sbMustAdd.length() > 0) {
                mustAdd.add(item.getFullName() + "：" + sbMustAdd.substring(1));
            }
            if (sbShouldNotAdd.length() > 0) {
                shouldNotAdd.add(item.getFullName() + "：" + sbShouldNotAdd.substring(1));
            }
            if (!dbKnowledgeMap.isEmpty()) {
                notInExcel.add(item.getFullName() + "：" + String.join("、", dbKnowledgeMap.values()));
            }
        }

        if (!notInExcel.isEmpty()) {
            IOperationItem.throwLongEx("缺少系统章节关联知识点", notInExcel, Function.identity(), 100);
        }
        if (!mustAdd.isEmpty()) {
            IOperationItem.throwLongEx("未关联的知识点其操作只能是新增", mustAdd, Function.identity(), 100);
        }
        if (!shouldNotAdd.isEmpty()) {
            IOperationItem.throwLongEx("已关联的知识点其操作不能是新增", shouldNotAdd, Function.identity(), 100);
        }
    }

    /**
     * 提取变更
     */
    private Changes extractChanges(DoImportContext context) {
        context.chapterIdGenerator = createIdGenerator();

        Changes changes = new Changes();
        int order = 0;
        Map<Integer, Book> bookMap = context.books.stream().collect(Collectors.toMap(Book::getId, Function.identity()));
        for (TreeNode<ChapterOperationItem> node : context.operationTree) {
            Book book = bookMap.get(-node.getItem().getId());
            if (getOneChange(node, -1, order, book, context, changes)) {
                order++;
            }
        }
        changes.updateOldList = changes.updateList.stream().map(Chapter::getId).map(context.dbChapterMap::get).collect(Collectors.toList());
        return changes;
    }

    /**
     * 变更
     */
    private static class Changes {
        /**
         * 新增章节
         */
        public List<Chapter> addList = new ArrayList<>();
        /**
         * 更新章节
         */
        public List<Chapter> updateList = new ArrayList<>();
        /**
         * 更新前的旧章节
         */
        public List<Chapter> updateOldList = new ArrayList<>();
        /**
         * 删除章节
         */
        public List<Chapter> deleteList = new ArrayList<>();
        /**
         * 新版章节
         */
        public List<Chapter> newList = new ArrayList<>();
        /**
         * 所有章节（新版章节与删除章节）
         */
        public List<Chapter> fullList = new ArrayList<>();
        /**
         * 删除章节题目
         */
        public List<QuestionBook> questionBookDeleteList = new ArrayList<>();
        /**
         * 新增章节知识点
         */
        public List<ChapterKnowledge> chapterKnowledgeAddList = new ArrayList<>();
        /**
         * 删除章节知识点
         */
        public List<ChapterKnowledge> chapterKnowledgeDeleteList = new ArrayList<>();
    }

    /**
     * Id生成器
     */
    private Supplier<Integer> createIdGenerator() {
        Chapter lastChapter = chapterMapper.selectOne(new LambdaQueryWrapper<Chapter>().orderByDesc(Chapter::getId).last("limit 1"));
        ChapterKnowledge lastChapterKnowledge = chapterKnowledgeMapper.selectOne(new LambdaQueryWrapper<ChapterKnowledge>()
                .orderByDesc(ChapterKnowledge::getChapterId).last("limit 1"));
        QuestionBook lastQuestionBook = questionBookMapper.selectOne(new LambdaQueryWrapper<QuestionBook>()
                .orderByDesc(QuestionBook::getChapterId).last("limit 1"));
        int maxId = Stream.of(lastChapter.getId(), lastChapterKnowledge.getChapterId(), lastQuestionBook.getChapterId())
                .sorted().collect(Collectors.toList()).get(2);

        IntIntVo vo = new IntIntVo();
        vo.setValue(maxId);
        return () -> {
            Integer id = vo.getValue() + 1;
            vo.setValue(id);
            return id;
        };
    }

    /**
     * 比较一个节点的变更
     */
    private boolean getOneChange(TreeNode<ChapterOperationItem> node, Integer level, Integer order, Book book, DoImportContext context, Changes changes) {
        boolean deleted = false;

        ChapterOperationItem item = node.getItem();
        String operationName = item.getOperationName();
        String operationValue = item.getOperationValue();
        Chapter dbChapter = item.getId() == null ? null : context.dbChapterMap.get(item.getId());

        if ("新增".equals(operationName)) {
            item.setId(context.chapterIdGenerator.get());
            Chapter newChapter = Chapter.builder()
                    .id(item.getId())
                    .bookId(book.getId())
                    .gradeLevel(book.getGradeLevel())
                    .subjectId(book.getSubjectId())
                    .gradeId(book.getGradeId())
                    .chapterName(item.getName())
                    .pid(level > 0 ? node.getParent().getItem().getId() : 0)
                    .level(level)
                    .orderId(order)
                    .status("1")
                    .build();
            changes.addList.add(newChapter);
        } else if ("删除".equals(operationName)) {
            deleted = true;
            assert dbChapter != null;
            changes.deleteList.add(dbChapter);
        } else if ("改名称".equals(operationName)) {
            assert dbChapter != null;
            Chapter updateChapter = copyChapter(dbChapter);
            updateChapter.setChapterName(item.getOperationValue());
            updateChapter.setLevel(level);
            updateChapter.setOrderId(order);
            changes.updateList.add(updateChapter);
        } else if ("移入".equals(operationName)) {
            TreeNode<ChapterOperationItem> srcNode = IOperationItem.findNodeByFullName(operationValue, context.operationTree);
            assert srcNode != null;
            item.setId(srcNode.getItem().getId());
            Chapter moveChapter = copyChapter(context.dbChapterMap.get(srcNode.getItem().getId()));
            moveChapter.setBookId(book.getId());
            moveChapter.setGradeId(book.getGradeId());
            moveChapter.setChapterName(item.getName());
            moveChapter.setPid(level > 0 ? node.getParent().getItem().getId() : 0);
            moveChapter.setLevel(level);
            moveChapter.setOrderId(order);
            changes.updateList.add(moveChapter);
        } else if ("".equals(operationName) && level >= 0) {
            assert dbChapter != null;
            if (!level.equals(dbChapter.getLevel()) || !order.equals(dbChapter.getOrderId())) {
                Chapter copy = copyChapter(dbChapter);
                copy.setLevel(level);
                copy.setOrderId(order);
                changes.updateList.add(copy);
            }
        }

        assert item.getId() != null;
        for (ChapterOperationItem.KnowledgeOperation k : item.getKnowledgeOperations()) {
            if ("新增".equals(k.getKnowledgeOperation())) {
                changes.chapterKnowledgeAddList.add(createChapterKnowledge(item.getId(), k.getKnowledgeId()));
            } else if ("删除".equals(k.getKnowledgeOperation())) {
                changes.chapterKnowledgeDeleteList.add(createChapterKnowledge(item.getId(), k.getKnowledgeId()));
            }
        }

        int childOrder = 0;
        for (TreeNode<ChapterOperationItem> child : node.getChildren()) {
            if (getOneChange(child, level + 1, childOrder, book, context, changes)) {
                childOrder++;
            }
        }

        return !deleted;
    }

    /**
     * 复制章节
     */
    private Chapter copyChapter(Chapter src) {
        return Chapter.builder()
                .id(src.getId())
                .bookId(src.getBookId())
                .gradeLevel(src.getGradeLevel())
                .subjectId(src.getSubjectId())
                .gradeId(src.getGradeId())
                .chapterName(src.getChapterName())
                .pid(src.getPid())
                .level(src.getLevel())
                .orderId(src.getOrderId())
                .status(src.getStatus())
                .build();
    }

    /**
     * 创建章节知识点关联
     */
    private ChapterKnowledge createChapterKnowledge(int chapterId, int knowledgeId) {
        return ChapterKnowledge.builder().chapterId(chapterId).knowledgeId(knowledgeId).build();
    }

    /**
     * 整理变更后的新版章节
     */
    private void buildNewChapterList(Changes changes, Map<Integer, Chapter> dbChapterMap) {
        // 将章的父节点Id改为教材Id以构建树，然后再改回0
        List<Chapter> dbChapterList = new ArrayList<>(dbChapterMap.values());
        List<Chapter> chapter1List = Stream.of(changes.addList, changes.updateList, changes.deleteList, dbChapterList)
                .flatMap(Collection::stream).filter(chapter -> chapter.getLevel() == 0).collect(Collectors.toList());
        chapter1List.forEach(chapter -> chapter.setPid(-chapter.getBookId()));
        TreeNode.buildNewList(changes.addList, changes.updateList, changes.deleteList, changes.fullList, changes.newList,
                dbChapterList, Chapter::getId, Chapter::getPid, Comparator.comparing(Chapter::getOrderId));
        chapter1List.forEach(chapter -> chapter.setPid(0));
    }

    /**
     * 检查章节名称重复
     */
    private void checkChapterNameDuplicate(List<Chapter> list, List<Book> books, Map<Integer, String> bookCategoryMap) {
        Map<Integer, String> bookNameIdMap = books.stream().collect(Collectors.toMap(Book::getId, book -> bookCategoryMap.get(book.getCategoryId()) + book.getBookName()));
        Map<Integer, List<Chapter>> groupByBook = list.stream().filter(x -> x.getId() > 0).collect(Collectors.groupingBy(Chapter::getBookId));
        groupByBook.forEach((bookId, chapters) -> {
            List<TreeNode<Chapter>> nodes = TreeNode.buildTree(chapters, Chapter::getId, Chapter::getPid);
            checkNodesNameDuplicate(nodes, bookNameIdMap.get(bookId));
        });
    }
    private void checkNodesNameDuplicate(List<TreeNode<Chapter>> nodes, String bookName) {
        Set<String> names = new HashSet<>();
        nodes.forEach(node -> {
            if (names.contains(node.getItem().getChapterName())) {
                throw new ApiException("章节名称重复：" + bookName + "\\" + node.getFullName(Chapter::getChapterName));
            } else {
                names.add(node.getItem().getChapterName());
            }
        });
        for (TreeNode<Chapter> node : nodes) {
            checkNodesNameDuplicate(node.getChildren(), bookName);
        }
    }

    /**
     * 查删除章节关联删除的章节题目、章节知识点
     */
    private void selectRelativeDeletes(Changes changes) {
        if (changes.deleteList.isEmpty()) {
            return;
        }
        List<Integer> deleteIds = changes.deleteList.stream().map(Chapter::getId).collect(Collectors.toList());
        changes.questionBookDeleteList = questionBookMapper.selectList(new LambdaQueryWrapper<QuestionBook>()
                .in(QuestionBook::getChapterId, deleteIds));
        // 删除章节知识点不重复
        Set<String> deleteChapterKnowledgeSet = changes.chapterKnowledgeDeleteList.stream()
                .map(x -> x.getChapterId() + "-" + x.getKnowledgeId()).collect(Collectors.toSet());
        chapterKnowledgeMapper.selectList(new LambdaQueryWrapper<ChapterKnowledge>()
                .in(ChapterKnowledge::getChapterId, deleteIds)).forEach(x -> {
            String key = x.getChapterId() + "-" + x.getKnowledgeId();
            if (!deleteChapterKnowledgeSet.contains(key)) {
                changes.chapterKnowledgeDeleteList.add(x);
                deleteChapterKnowledgeSet.add(key);
            }
        });

    }

    /**
     * 导出变更
     */
    private void exportChanges(DoImportContext context, Changes changes, OutputStream outputStream) {
        if (outputStream == null) {
            return;
        }

        List<Column<ExportChangeRow>> columns = new ArrayList<>();
        columns.add(new Column<>("章节Id", ExportChangeRow::getChapterId, false, null));
        columns.add(new Column<>("学段", ExportChangeRow::getGradeLevelName));
        columns.add(new Column<>("学科", ExportChangeRow::getSubjectName));
        columns.add(new Column<>("版本", ExportChangeRow::getCategoryName, false, null));
        columns.add(new Column<>("教材", ExportChangeRow::getBookName, false, null));
        columns.add(new Column<>("章", ExportChangeRow::getChapter1Name, false, null));
        columns.add(new Column<>("节", ExportChangeRow::getChapter2Name, false, null));
        columns.add(new Column<>("小节", ExportChangeRow::getChapter3Name, false, null));
        columns.add(new Column<>("层级", ExportChangeRow::getLevel));
        columns.add(new Column<>("顺序", ExportChangeRow::getOrder));
        columns.add(new Column<>("更改类型", ExportChangeRow::getChangeType));
        columns.add(new Column<>("更改详情", ExportChangeRow::getChangeDetail, false, null));
        columns.add(new Column<>("知识点Id", ExportChangeRow::getKnowledgeId, false, null));
        columns.add(new Column<>("知识点名称", ExportChangeRow::getKnowledgeName, false, null));
        columns.add(new Column<>("知识点操作", ExportChangeRow::getKnowledgeOperation));

        Function<ExportChangeRow, Style> conditionalRowStyle = x -> {
            if ("修改".equals(x.getChangeType())) {
                Style style = new Style();
                style.setBackgroundColor("FFE7BA");
                style.setFontColor("873800");
                return style;
            } else if ("新增".equals(x.getChangeType())) {
                Style style = new Style();
                style.setBackgroundColor("D9F7BE");
                style.setFontColor("135200");
                return style;
            } else if ("删除".equals(x.getChangeType())) {
                Style style = new Style();
                style.setBackgroundColor("FFCCC7");
                style.setFontColor("F5222d");
                return style;
            } else {
                return null;
            }
        };

        String stats = getChangesStats(changes);

        Sheet<ExportChangeRow> sheet = new Sheet<>();
        sheet.setSheetName("更改列表");
        sheet.setColumns(columns);
        sheet.setFreezeHeader(true);
        sheet.setConditionalRowStyleGetter(conditionalRowStyle);
        sheet.setFooter(stats);

        Workbook workbook = new Workbook();
        List<ExportChangeRow> rows = extractExportRows(context, changes);
        workbook.addSheet(sheet, rows);
        try {
            workbook.write(outputStream);
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
    }
    
    @Data
    private static class ExportChangeRow {
        private Integer chapterId;
        private String gradeLevelName;
        private String subjectName;
        private String categoryName;
        private String bookName;
        private String chapter1Name;
        private String chapter2Name;
        private String chapter3Name;
        private Integer level;
        private Integer order;
        private String changeType;
        private String changeDetail;
        private Integer knowledgeId;
        private String knowledgeName;
        private String knowledgeOperation;
    }

    /**
     * 变更excel行
     */
    private List<ExportChangeRow> extractExportRows(DoImportContext context, Changes changes) {
        Map<Integer, Book> bookMap = context.books.stream().collect(Collectors.toMap(Book::getId, Function.identity()));
        Map<Integer, Chapter> fullMap = changes.fullList.stream().collect(Collectors.toMap(Chapter::getId, Function.identity()));

        Set<Integer> addIds = changes.addList.stream().map(Chapter::getId).collect(Collectors.toSet());
        Set<Integer> updateIds = changes.updateList.stream().map(Chapter::getId).collect(Collectors.toSet());
        Set<Object> deleteIds = changes.deleteList.stream().map(Chapter::getId).collect(Collectors.toSet());

        Map<Integer, List<ChapterKnowledge>> addChapterKnowledgeMap = changes.chapterKnowledgeAddList.stream().collect(Collectors.groupingBy(ChapterKnowledge::getChapterId));
        Map<Integer, List<ChapterKnowledge>> deleteChapterKnowledgeMap = changes.chapterKnowledgeDeleteList.stream().collect(Collectors.groupingBy(ChapterKnowledge::getChapterId));
        Map<Integer, String> knowledgeIdNameMap = new HashMap<>();
        buildKnowledgeIdNameMap(context.dbKnowledgeTree, knowledgeIdNameMap);

        List<ExportChangeRow> rows = new ArrayList<>();
        String gradeLevelName = BaseEnum.getNameById(GradeLevelEnum.class, context.gradeLevelId);
        String subjectName = CacheUtil.getSubjectName(context.subjectId);

        for (Chapter chapter : changes.fullList) {
            Integer chapterId = chapter.getId();
            if (chapterId <= 0) {
                continue;
            }
            Book book = bookMap.get(chapter.getBookId());
            String chapter1Name = getChapterParentName(chapter, 0, fullMap);
            String chapter2Name = getChapterParentName(chapter, 1, fullMap);
            String chapter3Name = getChapterParentName(chapter, 2, fullMap);
            String changeType = null;
            if (addIds.contains(chapterId)) {
                changeType = "新增";
            } else if (updateIds.contains(chapterId)) {
                changeType = "修改";
            } else if (deleteIds.contains(chapterId)) {
                changeType = "删除";
            }
            String changeDetail = null;
            if (updateIds.contains(chapterId)) {
                Chapter old = context.dbChapterMap.get(chapterId);
                Map<String, StrStrVo> changeMap = new LinkedHashMap<>();
                if (!old.getBookId().equals(chapter.getBookId())) {
                    changeMap.put("教材", new StrStrVo(old.getBookId().toString(), chapter.getBookId().toString()));
                }
                if (!old.getPid().equals(chapter.getPid())) {
                    changeMap.put("上级", new StrStrVo(old.getPid().toString(), chapter.getPid().toString()));
                }
                if (!old.getChapterName().equals(chapter.getChapterName())) {
                    changeMap.put("名称", new StrStrVo(old.getChapterName(), chapter.getChapterName()));
                }
                if (!old.getLevel().equals(chapter.getLevel())) {
                    changeMap.put("层级", new StrStrVo(old.getLevel().toString(), chapter.getLevel().toString()));
                }
                if (!old.getOrderId().equals(chapter.getOrderId())) {
                    changeMap.put("顺序", new StrStrVo(old.getOrderId().toString(), chapter.getOrderId().toString()));
                }
                changeDetail = changeMap.entrySet().stream()
                        .map(entry -> entry.getKey() + "(" + entry.getValue().getKey() + "->" + entry.getValue().getValue() + ")")
                        .collect(Collectors.joining("；"));
            }

            List<KnowledgeMain> oldKnowledgeList = context.dbChapterIdKnowledgeListMap.getOrDefault(chapter.getId(), new ArrayList<>());
            List<ChapterKnowledge> addKnowledgeList = addChapterKnowledgeMap.getOrDefault(chapter.getId(), new ArrayList<>());
            Set<Integer> deleteKnowledgeIds = deleteChapterKnowledgeMap.getOrDefault(chapter.getId(), new ArrayList<>()).stream().map(ChapterKnowledge::getKnowledgeId).collect(Collectors.toSet());

            List<ChapterOperationItem.KnowledgeOperation> knowledgeOperations = new ArrayList<>();
            for (KnowledgeMain k : oldKnowledgeList) {
                String operation = deleteKnowledgeIds.contains(k.getId()) ? "删除" : "";
                ChapterOperationItem.KnowledgeOperation op = new ChapterOperationItem.KnowledgeOperation(k.getId(), k.getKnowledgeName(), operation);
                knowledgeOperations.add(op);
            }
            addKnowledgeList.forEach(k -> {
                ChapterOperationItem.KnowledgeOperation op = new ChapterOperationItem.KnowledgeOperation(k.getKnowledgeId(), knowledgeIdNameMap.get(k.getKnowledgeId()), "新增");
                knowledgeOperations.add(op);
            });
            if (knowledgeOperations.isEmpty()) {
                knowledgeOperations.add(new ChapterOperationItem.KnowledgeOperation(null, null, null));
            }

            for (ChapterOperationItem.KnowledgeOperation k : knowledgeOperations) {
                ExportChangeRow row = new ExportChangeRow();
                row.setChapterId(chapterId);
                row.setGradeLevelName(gradeLevelName);
                row.setSubjectName(subjectName);
                row.setCategoryName(context.bookCategoryMap.get(book.getCategoryId()));
                row.setBookName(book.getBookName());
                row.setChapter1Name(chapter1Name);
                row.setChapter2Name(chapter2Name);
                row.setChapter3Name(chapter3Name);
                row.setLevel(chapter.getLevel());
                row.setOrder(chapter.getOrderId());
                row.setChangeType(changeType);
                row.setChangeDetail(changeDetail);
                row.setKnowledgeId(k.getKnowledgeId());
                row.setKnowledgeName(k.getKnowledgeName());
                row.setKnowledgeOperation(k.getKnowledgeOperation());
                rows.add(row);
            }
        }

        return rows;
    }
    
    private void buildKnowledgeIdNameMap(List<KnowledgeMainVo> nodes, Map<Integer, String> result) {
        for (KnowledgeMainVo node : nodes) {
            result.put(node.getId(), node.getKnowledgeName());
            if (node.getKnowledgeVoList() != null) {
                buildKnowledgeIdNameMap(node.getKnowledgeVoList(), result);
            }
        }
    }

    /**
     * 上级章节名称
     */
    private String getChapterParentName(Chapter x, int level, Map<Integer, Chapter> map) {
        if (x.getLevel() < level) {
            return null;
        }
        Chapter p = x;
        while (p.getLevel() > level) {
            p = map.get(p.getPid());
        }
        return p.getChapterName();
    }

    /**
     * 更改统计
     */
    private String getChangesStats(Changes changes) {
        List<Chapter> addList = changes.addList;
        List<Chapter> updateList = changes.updateList;
        List<Chapter> deleteList = changes.deleteList;
        List<QuestionBook> qBDeleteList = changes.questionBookDeleteList;
        List<ChapterKnowledge> cKAddList = changes.chapterKnowledgeAddList;
        List<ChapterKnowledge> cKDeleteList = changes.chapterKnowledgeDeleteList;

        StringBuilder sb = new StringBuilder();
        if (!addList.isEmpty()) {
            sb.append("；新增 ").append(addList.size()).append(" 个章节");
        }
        if (!updateList.isEmpty()) {
            sb.append("；更新 ").append(updateList.size()).append(" 个章节");
        }
        if (!deleteList.isEmpty()) {
            sb.append("；删除 ").append(deleteList.size()).append(" 个章节");
        }
        if (!qBDeleteList.isEmpty()) {
            sb.append("；删除 ").append(qBDeleteList.size()).append(" 个题目章节关联");
        }
        if (!cKAddList.isEmpty()) {
            sb.append("；新增 ").append(cKAddList.size()).append(" 个章节知识点关联");
        }
        if (!cKDeleteList.isEmpty()) {
            sb.append("；删除 ").append(cKDeleteList.size()).append(" 个章节章节关联");
        }
        if (sb.length() > 0) {
            return sb.substring(1);
        } else {
            return "无更改";
        }
    }

    /**
     * 保存
     */
    private void saveDB(Changes changes, String userId) {
        List<Chapter> addList = changes.addList;
        List<Chapter> updateList = changes.updateList;
        List<Chapter> updateOldList = changes.updateOldList;
        List<Chapter> deleteList = changes.deleteList;
        List<QuestionBook> qBDeleteList = changes.questionBookDeleteList;
        List<ChapterKnowledge> cKAddList = changes.chapterKnowledgeAddList;
        List<ChapterKnowledge> cKDeleteList = changes.chapterKnowledgeDeleteList;

        long operationId = idWorker.nextId();
        Date time = new Date();

        transactionTemplate.execute(status -> {
            if (!addList.isEmpty()) {
                chapterMapper.insertChapterOperationLog(operationId, time, userId, "ADD", addList);
                chapterMapper.insertList(addList);
            }
            if (!updateList.isEmpty()) {
                chapterMapper.insertChapterOperationLog(operationId, time, userId, "UPDATE", updateOldList);
                updateList.forEach(chapterMapper::updateById);
            }
            if (!deleteList.isEmpty()) {
                chapterMapper.insertChapterOperationLog(operationId, time, userId, "DELETE", deleteList);
                List<Integer> deleteIds = deleteList.stream().map(Chapter::getId).collect(Collectors.toList());
                chapterMapper.delete(new LambdaQueryWrapper<Chapter>().in(Chapter::getId, deleteIds));
            }
            if (!qBDeleteList.isEmpty()) {
                questionBookMapper.insertQuestionBookOperationLog(operationId, time, userId, "DELETE", qBDeleteList);
                qBDeleteList.forEach(qB -> questionBookMapper.deleteByQuestionIdChapterId(qB.getQuestionId(), qB.getChapterId()));
                Set<String> questionIds = qBDeleteList.stream().map(QuestionBook::getQuestionId).collect(Collectors.toSet());
                CollUtil.split(questionIds, 1000).forEach(questionEsSyncMapper::insertPendingQuestionsBatch);
            }
            if (!cKAddList.isEmpty()) {
                chapterKnowledgeMapper.insertChapterKnowledgeOperationLog(operationId, time, userId, "ADD", cKAddList);
                chapterKnowledgeMapper.insertList(cKAddList);
            }
            if (!cKDeleteList.isEmpty()) {
                chapterKnowledgeMapper.insertChapterKnowledgeOperationLog(operationId, time, userId, "DELETE", cKDeleteList);
                cKDeleteList.forEach(ck -> chapterKnowledgeMapper.deleteByChapterIdAndKnowledgeId(ck.getChapterId(), ck.getKnowledgeId()));
            }
            return null;
        });
    }
    
    @Override
    public String importCopyChapterQuestionExcel(int gradeLevel, int subjectId, InputStream inputStream, boolean save, String userId) {
        return RedissonLockUtil.executeAfterLocked("Ques:Lock:ChangeBookChapterKnowledge",
                () -> doImportCopyChapterQuestionExcel(gradeLevel, subjectId, inputStream, save, userId));
    }
    
    private String doImportCopyChapterQuestionExcel(int gradeLevel, int subjectId, InputStream inputStream, boolean save, String userId) {
        List<CopyChapterQuestionImportVo> records = extractCopyChapterQuestionExcelRecords(inputStream);
        checkCopyChapterQuestionRecords(records, gradeLevel, subjectId);
        List<QuestionBook> inserts = buildCopyChapterQuestionInserts(records);
        if (inserts.isEmpty()) {
            return "无新增关联";
        } else {
            if (save) {
                saveCopyChapterQuestionDB(inserts, userId);
            }
            return "新增 " + inserts.size() + " 条题目章节关联";
        }
    }

    /**
     * 从Excel中提取数据
     */
    private List<CopyChapterQuestionImportVo> extractCopyChapterQuestionExcelRecords(InputStream inputStream) {
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        reader.addHeaderAlias("学段", "gradeLevelName");
        reader.addHeaderAlias("学科", "subjectName");
        reader.addHeaderAlias("原版本", "srcCategoryName");
        reader.addHeaderAlias("原教材", "srcBookName");
        reader.addHeaderAlias("原章", "srcChapter1Name");
        reader.addHeaderAlias("原节", "srcChapter2Name");
        reader.addHeaderAlias("原小节", "srcChapter3Name");
        reader.addHeaderAlias("新版本", "dstCategoryName");
        reader.addHeaderAlias("新教材", "dstBookName");
        reader.addHeaderAlias("新章", "dstChapter1Name");
        reader.addHeaderAlias("新节", "dstChapter2Name");
        reader.addHeaderAlias("新小节", "dstChapter3Name");
        List<CopyChapterQuestionImportVo> records = reader.setIgnoreEmptyRow(false).readAll(CopyChapterQuestionImportVo.class);
        if (records.isEmpty()) {
            throw new ApiException("excel中无数据");
        }
        IImportRow.setSequentialRowNo(records, 2);
        return records;
    }

    /**
     * 检查数据
     */
    private void checkCopyChapterQuestionRecords(List<CopyChapterQuestionImportVo> vos, int gradeLevelId, int subjectId) {
        // 整理导入字段
        vos.forEach(CopyChapterQuestionImportVo::clean);

        // 检查学段学科
        IImportOperation.checkGradeLevel(vos, gradeLevelId);
        IImportOperation.checkSubject(vos, subjectId);

        // 检查版本、教材、章节不为空
        IImportOperation.checkRuleThrowEx("版本不能为空", vos, vo -> StringUtils.isAnyBlank(vo.getSrcCategoryName(), vo.getDstCategoryName()));
        IImportOperation.checkRuleThrowEx("教材不能为空", vos, vo -> StringUtils.isAnyBlank(vo.getSrcBookName(), vo.getDstBookName()));
        IImportOperation.checkRuleThrowEx("章不能为空", vos, vo -> StringUtils.isAnyBlank(vo.getSrcChapter1Name(), vo.getDstChapter1Name()));
        IImportOperation.checkRuleThrowEx("节为空时小节必须为空", vos, vo ->
                (StringUtils.isBlank(vo.getSrcChapter2Name()) && StringUtils.isNotBlank(vo.getSrcChapter3Name()))
                        || (StringUtils.isBlank(vo.getDstChapter2Name()) && StringUtils.isNotBlank(vo.getDstChapter3Name())));

        // 检查教材
        checkBook(vos, gradeLevelId, subjectId);

        // 检查章节
        checkChapter(vos);
    }

    /**
     * 检查教材
     */
    private void checkBook(List<CopyChapterQuestionImportVo> vos, int gradeLevelId, int subjectId) {
        List<Book> books = bookMapper.selectList(new LambdaQueryWrapper<Book>()
                .eq(Book::getGradeLevel, gradeLevelId)
                .eq(Book::getSubjectId, subjectId));
        Map<Integer, String> bookCategoryMap = getBookCategoryMap(gradeLevelId, subjectId);
        Map<String, Integer> categoryNameIdMap = books.stream().collect(Collectors.toMap(book -> bookCategoryMap.get(book.getCategoryId()), Book::getCategoryId, (a, b) -> a));
        IImportOperation.checkRuleThrowEx("原版本不存在", vos, vo -> {
            vo.setSrcCategoryId(categoryNameIdMap.get(vo.getSrcCategoryName()));
            return vo.getSrcCategoryId() == null;
        });
        IImportOperation.checkRuleThrowEx("新版本不存在", vos, vo -> {
            vo.setDstCategoryId(categoryNameIdMap.get(vo.getDstCategoryName()));
            return vo.getDstCategoryId() == null;
        });
        Map<String, Integer> bookNameIdMap = books.stream().collect(Collectors.toMap(book -> bookCategoryMap.get(book.getCategoryId()) + book.getBookName(), Book::getId));
        IImportOperation.checkRuleThrowEx("原教材不存在", vos, vo -> {
            vo.setSrcBookId(bookNameIdMap.get(vo.getSrcCategoryName() + vo.getSrcBookName()));
            return vo.getSrcBookId() == null;
        });
        IImportOperation.checkRuleThrowEx("新教材不存在", vos, vo -> {
            vo.setDstBookId(bookNameIdMap.get(vo.getDstCategoryName() + vo.getDstBookName()));
            return vo.getDstBookId() == null;
        });
    }

    /**
     * 检查章节
     */
    private void checkChapter(List<CopyChapterQuestionImportVo> vos) {
        Set<Integer> bookIds = vos.stream().flatMap(vo -> Stream.of(vo.getSrcBookId(), vo.getDstBookId())).collect(Collectors.toSet());
        List<Chapter> chapterList = chapterMapper.selectList(new LambdaQueryWrapper<Chapter>().in(Chapter::getBookId, bookIds));
        Map<Integer, List<Chapter>> groupByBook = chapterList.stream().collect(Collectors.groupingBy(Chapter::getBookId));

        Map<Integer, Map<String, Integer>> bookIdChapterFullNameIdMap = new HashMap<>();
        groupByBook.forEach((bookId, chapters) -> {
            List<TreeNode<Chapter>> tree = TreeNode.buildTree(chapters, Chapter::getId, Chapter::getPid);
            List<TreeNode<Chapter>> flatNodes = tree.stream().flatMap(node -> node.getFlatNodes().stream()).collect(Collectors.toList());
            Map<String, Integer> fullNameIdMap = flatNodes.stream().collect(Collectors.toMap(node -> node.getFullName(Chapter::getChapterName), node -> node.getItem().getId()));
            bookIdChapterFullNameIdMap.put(bookId, fullNameIdMap);
        });

        for (CopyChapterQuestionImportVo vo : vos) {
            Map<String, Integer> srcFullNameIdMap = bookIdChapterFullNameIdMap.getOrDefault(vo.getSrcBookId(), new HashMap<>());
            vo.setSrcChapterId(srcFullNameIdMap.get(vo.getSrcFullChapterName()));
            Map<String, Integer> dstFullNameIdMap = bookIdChapterFullNameIdMap.getOrDefault(vo.getDstBookId(), new HashMap<>());
            vo.setDstChapterId(dstFullNameIdMap.get(vo.getDstFullChapterName()));
        }

        IImportOperation.checkRuleThrowEx("原章节不存在", vos, vo -> vo.getSrcChapterId() == null);
        IImportOperation.checkRuleThrowEx("新章节不存在", vos, vo -> vo.getDstChapterId() == null);
        IImportOperation.checkRuleThrowEx("原章节与新章节不能相同", vos, vo -> Objects.equals(vo.getSrcChapterId(), vo.getDstChapterId()));

        Map<String, Long> srcIdDstIdCountMap = vos.stream().collect(Collectors.groupingBy(vo -> vo.getSrcChapterId() + "-" + vo.getDstChapterId(), Collectors.counting()));
        Set<String> duplicates = srcIdDstIdCountMap.entrySet().stream().filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toSet());
        IImportOperation.checkRuleThrowEx("重复行", vos, vo -> duplicates.contains(vo.getSrcChapterId() + "-" + vo.getDstChapterId()));
    }

    /**
     * 创建新章节题目关联
     */
    private List<QuestionBook> buildCopyChapterQuestionInserts(List<CopyChapterQuestionImportVo> vos) {
        // 查章节对应题目
        Set<Integer> chapterIds = vos.stream().flatMap(vo -> Stream.of(vo.getSrcChapterId(), vo.getDstChapterId()))
                .collect(Collectors.toSet());
        Map<Integer, List<QuestionBook>> chapterIdQuestionsMap = new HashMap<>();
        CollUtil.split(chapterIds, 100).forEach(subList -> {
            List<QuestionBook> questionBookList = questionBookMapper.selectList(new LambdaQueryWrapper<QuestionBook>()
                    .in(QuestionBook::getChapterId, subList));
            for (QuestionBook questionBook : questionBookList) {
                List<QuestionBook> list = chapterIdQuestionsMap.computeIfAbsent(questionBook.getChapterId(), k -> new ArrayList<>());
                list.add(questionBook);
            }
        });

        // 按目的章节分组
        Map<Integer, List<Integer>> groupByDstChapterId = vos.stream().collect(
                Collectors.groupingBy(CopyChapterQuestionImportVo::getDstChapterId,
                        Collectors.mapping(CopyChapterQuestionImportVo::getSrcChapterId, Collectors.toList())));

        // 创建QuestionBook，不能重复
        List<QuestionBook> inserts = new ArrayList<>();
        long updateTime = new Date().getTime();
        groupByDstChapterId.forEach((dstChapterId, srcChapterIds) -> {
            Set<String> dstQuestionIds = chapterIdQuestionsMap.getOrDefault(dstChapterId, new ArrayList<>())
                    .stream().map(QuestionBook::getQuestionId).collect(Collectors.toSet());
            for (Integer srcChapterId : srcChapterIds) {
                List<QuestionBook> srcQuestionBooks = chapterIdQuestionsMap.getOrDefault(srcChapterId, new ArrayList<>());
                for (QuestionBook srcQuestionBook : srcQuestionBooks) {
                    if (!dstQuestionIds.contains(srcQuestionBook.getQuestionId())) {
                        QuestionBook newQuestion = new QuestionBook();
                        newQuestion.setId(idWorker.nextId());
                        newQuestion.setQuestionId(srcQuestionBook.getQuestionId());
                        newQuestion.setQuestionNo(srcQuestionBook.getQuestionNo());
                        newQuestion.setChapterId(dstChapterId);
                        newQuestion.setUpdateTime(updateTime);
                        dstQuestionIds.add(newQuestion.getQuestionId());
                        inserts.add(newQuestion);
                    }
                }
            }
        });

        return inserts;
    }

    /**
     * 保存
     */
    private void saveCopyChapterQuestionDB(List<QuestionBook> inserts, String userId) {
        long operationId = idWorker.nextId();
        Date time = new Date();
        transactionTemplate.execute(status -> {
            CollUtil.split(inserts, 1000).forEach(batch -> {
                questionBookMapper.insertQuestionBookOperationLog(operationId, time, userId, "ADD", batch);
                questionBookMapper.insertList(batch);
                Set<String> questionIds = batch.stream().map(QuestionBook::getQuestionId).collect(Collectors.toSet());
                questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);
            });
            return null;
        });
    }
}
