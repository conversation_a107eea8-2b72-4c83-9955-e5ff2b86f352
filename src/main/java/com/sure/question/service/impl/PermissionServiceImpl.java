package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.common.entity.UserInfo;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.PaperShare;
import com.sure.question.entity.SchoolPaper;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.RoleEnum;
import com.sure.question.feign.UserService;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.mapper.PaperShareMapper;
import com.sure.question.mapper.SchoolCoachBookMapper;
import com.sure.question.mapper.SchoolPaperMapper;
import com.sure.question.service.PermissionService;
import com.sure.question.vo.TokenUserVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {
    private final PaperMainMapper paperMainMapper;
    private final PaperShareMapper paperShareMapper;
    private final SchoolPaperMapper schoolPaperMapper;
    private final SchoolCoachBookMapper schoolCoachBookMapper;
    private final UserService userService;

    /**
     * 获取试卷
     */
    @Override
    public PaperMain getPaperForCheckPermission(String paperId) {
        return paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .select(PaperMain::getId, PaperMain::getGradeLevel, PaperMain::getSubjectId, PaperMain::getPaperBank, PaperMain::getUserId));
    }

    /**
     * 是否具有查看试卷信息权限
     */
    @Override
    public boolean hasViewPaperInfoPermission(String paperId, UserInfo userInfo) {
        // 系统用户
        if (Boolean.TRUE.equals(userInfo.getIsSys())) {
            return true;
        }
        // 超级管理员、题库管理员、题库编辑
        if (Stream.of(RoleEnum.SuperManager, RoleEnum.SysManager, RoleEnum.SysEditor).anyMatch(role -> userInfo.getRoles().contains(role.getCode()))) {
            return true;
        }
        TokenUserVo userVo = new TokenUserVo(userInfo.getUserId(), userInfo.getSchoolId(), userInfo.getRoles());
        return hasViewPaperContentPermission(paperId, userVo);
    }

    /**
     * 是否具有查看试卷内容权限
     */
    @Override
    public boolean hasViewPaperContentPermission(String paperId, int paperGradeLevel, int paperSubjectId, String paperBank, String paperUserId, TokenUserVo userVo) {
        // 公共试卷
        if (PaperBankEnum.Public.getCode().toString().equals(paperBank)) {
            return true;
        }
        // 个人试卷
        if (StringUtils.equals(paperUserId, userVo.getUserId())) {
            return true;
        }
        // 题库管理员、系统管理员
        if (userVo.isSysManager() || userVo.isSuperManager()) {
            return true;
        }
        // 题库学科编辑
        if (userVo.isSysEditor() && userService.editorHasPermission(userVo.getUserId(), paperGradeLevel, paperSubjectId)) {
            return true;
        }
        // 分享试卷
        int shareCount = paperShareMapper.selectCount(new LambdaQueryWrapper<PaperShare>()
                .eq(PaperShare::getPaperId, paperId)
                .eq(PaperShare::getShareId, userVo.getUserId())
                .eq(PaperShare::getShareDelete, false));
        if (shareCount > 0) {
            return true;
        }
        // 学校卷库
        int schoolPaperCount = schoolPaperMapper.selectCount(new LambdaQueryWrapper<SchoolPaper>()
                .eq(SchoolPaper::getSchoolId, userVo.getSchoolId())
                .eq(SchoolPaper::getPaperId, paperId)
                .eq(SchoolPaper::getIsDelete,  false));
        if (schoolPaperCount > 0) {
            return true;
        }
        // 定制教辅
        boolean isSchoolCoachBookPaper = schoolCoachBookMapper.selectExistsSchoolCoachBookPaper(userVo.getSchoolId(), paperId);
        if (isSchoolCoachBookPaper) {
            return true;
        }
        // 定制教辅练习卷
        return schoolCoachBookMapper.selectExistsSchoolCoachBookCustomPaper(userVo.getSchoolId(), paperId);
    }

    /**
     * 是否具有修改试卷权限
     */
    @Override
    public boolean hasUpdatePaperPermission(int paperGradeLevel, int paperSubjectId, String paperBank, String paperUserId, TokenUserVo userVo) {
        // 个人库个人试卷
        if (PaperBankEnum.Personal.getCode().toString().equals(paperBank) && StringUtils.equals(userVo.getUserId(), paperUserId)) {
            return true;
        }
        // 题库管理员、系统管理员
        if (userVo.isSysManager() || userVo.isSuperManager()) {
            return true;
        }
        // 题库学科编辑
        return userVo.isSysEditor() && userService.editorHasPermission(userVo.getUserId(), paperGradeLevel, paperSubjectId);
    }

    /**
     * 是否具有学段学科编辑权限
     */
    @Override
    public boolean hasEditorPermission(TokenUserVo userVo, int gradeLevel, int subjectId) {
        if (userVo.isSuperManager() || userVo.isSysManager()) {
            return true;
        }
        if (userVo.isSysEditor()) {
            return userService.editorHasPermission(userVo.getUserId(), gradeLevel, subjectId);
        }
        return false;
    }

    /**
     * 是否具有学段学科编辑权限
     */
    @Override
    public boolean hasEditorPermission(String userId, int gradeLevel, int subjectId) {
        return userService.editorHasPermission(userId, gradeLevel, subjectId);
    }
}
