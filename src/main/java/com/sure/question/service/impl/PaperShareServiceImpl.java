package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.CoachBook;
import com.sure.question.entity.CoachBookCustomPaper;
import com.sure.question.entity.CoachBookPaper;
import com.sure.question.entity.PaperShare;
import com.sure.question.mapper.CoachBookCustomPaperMapper;
import com.sure.question.mapper.CoachBookMapper;
import com.sure.question.mapper.CoachBookPaperMapper;
import com.sure.question.mapper.PaperShareMapper;
import com.sure.question.service.FeignService;
import com.sure.question.service.PaperShareService;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paperVo.SharePaperVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaperShareServiceImpl implements PaperShareService {

    private final PaperShareMapper paperShareMapper;
    private final FeignService feignService;
    private final CoachBookPaperMapper coachBookPaperMapper;
    private final CoachBookCustomPaperMapper coachBookCustomPaperMapper;
    private final CoachBookMapper coachBookMapper;

    @Override
    public IPage<SharePaperVo> getMyShares(TokenUserVo userVo, Integer type, Integer grade, Integer year, String region, Integer subject,
                                           Integer stage, String bookType, Integer flagCoachBookId, Integer page, Integer size) {
        IPage<SharePaperVo> p = paperShareMapper.getMyShares(new Page<>(page, size), userVo.getUserId(), type, grade, year, region, subject, stage, bookType, flagCoachBookId, page, size);
        if (!p.getRecords().isEmpty()) {
            addSharePaperVoNames(p.getRecords());
            setFlagCoachBookName(p.getRecords());
        }
        return p;
    }

    private void setFlagCoachBookName(List<SharePaperVo> list) {
        List<Integer> flagCoachBookIds = list.stream().map(SharePaperVo::getFlagCoachBookId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (flagCoachBookIds.isEmpty()) {
            return;
        }
        Map<Integer, String> coachBookIdCoachBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                        .in(CoachBook::getId, flagCoachBookIds)
                        .select(CoachBook::getId, CoachBook::getBookName))
                .stream().collect(Collectors.toMap(CoachBook::getId, CoachBook::getBookName));
        list.forEach(vo -> {
            if (vo.getFlagCoachBookId() != null) {
                vo.setFlagCoachBookName(coachBookIdCoachBookNameMap.get(vo.getFlagCoachBookId()));
            }
        });
    }

    @Override
    public List<SharePaperVo> getMyShareSpecifiedPaperId(String paperId, TokenUserVo userVo) {
        List<SharePaperVo> result = paperShareMapper.getMyShareSpecifiedPaperId(userVo.getUserId(), paperId);
        if (!result.isEmpty()) {
            addSharePaperVoNames(result);
        }
        return result;
    }

    @Override
    public IPage<SharePaperVo> getShareToMe(TokenUserVo userVo, Integer type, Integer grade, Integer year, String region,
                                            Integer subject, Integer stage, Integer page, Integer size, Integer bank,
                                            String keyword, String bookType, Integer flagCoachBookId, Integer paperGroupId) {
        IPage<SharePaperVo> p = paperShareMapper.getShareToMe(new Page<>(page, size), userVo.getUserId(), type, grade, year,
                region, subject, stage, page, size, bank, keyword, bookType, flagCoachBookId, paperGroupId);
        if (!p.getRecords().isEmpty()) {
            addSharePaperVoNames(p.getRecords());
            setFlagCoachBookName(p.getRecords());
        }
        return p;
    }

    @Override
    public IPage<SharePaperVo> getAvailableCoachBookPapersShareToMe(TokenUserVo userVo, Integer type, Integer grade, Integer year,
                                                                    String region, Integer subject, Integer stage, Integer page, Integer size,
                                                                    Integer bank, String keyword, Integer coachBookId, Integer flagCoachBookId, Integer paperGroupId) {
        String bookType = "";
        if (flagCoachBookId != null) {
            bookType = "coach";
        }
        IPage<SharePaperVo> result = paperShareMapper.getShareToMe(new Page<>(page, size), userVo.getUserId(), type, grade, year,
                region, subject, stage, page, size, bank, keyword, bookType, flagCoachBookId, paperGroupId);

        if (result.getTotal() > 0) {
            // 一份卷只能属于一本教辅的同步卷，但可以是多本教辅的练习卷
            Set<String> paperIdSet = coachBookPaperMapper.selectObjs(new LambdaQueryWrapper<CoachBookPaper>().select(CoachBookPaper::getPaperId))
                    .stream().map(String::valueOf).collect(Collectors.toSet());
            Set<String> paperIds = coachBookCustomPaperMapper.selectObjs(new LambdaQueryWrapper<CoachBookCustomPaper>()
                    .eq(CoachBookCustomPaper::getPaperId, coachBookId)
                    .select(CoachBookCustomPaper::getPaperId)).stream().map(String::valueOf).collect(Collectors.toSet());
            paperIdSet.addAll(paperIds);
            // 剔除同步卷、练习卷
            if (!paperIdSet.isEmpty()) {
                result.getRecords().removeIf(x -> paperIdSet.contains(x.getId()));
            }
        }

        if (!result.getRecords().isEmpty()) {
            addSharePaperVoNames(result.getRecords());
            setFlagCoachBookName(result.getRecords());
        }
        return result;
    }

    @Override
    public void shareTo(TokenUserVo userVo, String targetId, String paperId) {
        List<PaperShare> paperShares = paperShareMapper.selectList(new LambdaQueryWrapper<PaperShare>()
                .eq(PaperShare::getUserId, userVo.getUserId())
                .eq(PaperShare::getShareId, targetId)
                .eq(PaperShare::getPaperId, paperId));
        if (!paperShares.isEmpty()) {
            PaperShare paperShare = paperShares.get(0);
            paperShare.setShareDelete(false);
            paperShare.setCreateTime(new Date());
            paperShareMapper.updateById(paperShare);
            return;
        }

        PaperShare build = PaperShare.builder()
                .paperId(paperId)
                .userId(userVo.getUserId())
                .shareId(targetId)
                .createTime(new Date())
                .shareDelete(false)
                .build();
        paperShareMapper.insert(build);

    }

    @Override
    public void recall(TokenUserVo userVo, Integer id) {
        PaperShare paperShare = paperShareMapper.selectById(id);
        if (paperShare == null) throw new ApiException("id为空");
        if (!paperShare.getUserId().equals(userVo.getUserId()))
            throw new ApiException("权限不足");
        paperShareMapper.delete(new LambdaQueryWrapper<PaperShare>()
                .eq(PaperShare::getId, id));
    }

    @Override
    public void hidePaper(TokenUserVo userVo, Integer id) {
        PaperShare paperShare = paperShareMapper.selectById(id);
        if (paperShare != null && paperShare.getShareId().equals(userVo.getUserId())) {
            paperShare.setShareDelete(true);
            paperShareMapper.updateById(paperShare);
        }
    }


    private void addSharePaperVoNames(List<SharePaperVo> records) {
        List<String> userIds = new ArrayList<>();
        userIds.addAll(records.stream().map(SharePaperVo::getSrcUserId).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
        userIds.addAll(records.stream().map(SharePaperVo::getDestUserId).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
        if (userIds.isEmpty()) {
            return;
        }
        Map<String, String> userIdNameMobileMap = feignService.getUserIdNameMobileMap(userIds);
        records.forEach(vo -> {
            vo.setSrcUserName(userIdNameMobileMap.get(vo.getSrcUserId()));
            vo.setDestUserName(userIdNameMobileMap.get(vo.getDestUserId()));
        });
    }
}
