package com.sure.question.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.*;
import com.sure.question.enums.CoachBookTypeEnum;
import com.sure.question.mapper.*;
import com.sure.question.service.FeignService;
import com.sure.question.service.OssManager;
import com.sure.question.service.QuestionSearchService;
import com.sure.question.service.SchoolCoachService;
import com.sure.question.util.CacheUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.BookSchoolVo;
import com.sure.question.vo.dataVo.IntIntVo;
import com.sure.question.vo.paperVo.SchoolCoachPaperVo;
import com.sure.question.vo.question.FindQuestionPageParam;
import com.sure.question.vo.question.UserQuestionVo;
import com.sure.question.vo.schoolCoachBook.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SchoolCoachServiceImpl extends ServiceImpl<SchoolCoachBookMapper, SchoolCoachBook> implements SchoolCoachService {
    private final CoachBookMapper coachBookMapper;
    private final CoachBookPaperMapper coachBookPaperMapper;
    private final CoachBookCustomPaperMapper coachBookCustomPaperMapper;
    private final SchoolCoachBookMapper schoolCoachBookMapper;
    private final QuestionMapper questionMapper;
    private final FavoritePaperMapper favoritePaperMapper;
    private final QuestionSearchService questionSearchService;
    private final FeignService feignService;
    private final PaperMainMapper paperMainMapper;
    private final OssManager ossManager;

    @Value("${aliyun.ossTkImg.coachBookDefaultCover}")
    private String coachBookDefaultCover;

    @Override
    public Page<BookInfo> getBookPage(QueryBookVo queryVo, Integer page, Integer size) {
        if (queryVo.getOnlyOpen() == null) {
            queryVo.setOnlyOpen(Boolean.FALSE);
        }
        // 查教辅
        Page<BookInfo> result = new Page<>(page, size);
        result.setOrders(Arrays.asList(
                new OrderItem().setColumn("year").setAsc(Boolean.FALSE),
                new OrderItem().setColumn("term").setAsc(Boolean.FALSE),
                new OrderItem().setColumn("grade_id").setAsc(Boolean.TRUE),
                new OrderItem().setColumn("subject_id").setAsc(Boolean.TRUE),
                new OrderItem().setColumn("book_code").setAsc(Boolean.TRUE)
        ));
        schoolCoachBookMapper.getBookPage(result, queryVo, CoachBookTypeEnum.SYS.getCode());
        if (result.getTotal() > 0) {
            // 统计同步卷、练习卷数量
            List<Integer> coachBookIds = result.getRecords().stream().map(BookInfo::getId).collect(Collectors.toList());
            Map<Integer, Integer> bookIdAndPaperCountMap = coachBookPaperMapper.selectBookIdAndPaperCount(coachBookIds, queryVo.getOnlyOpen()).stream().collect(Collectors.toMap(IntIntVo::getId, IntIntVo::getValue));
            Map<Integer, Integer> bookIdAndCustomPaperCountMap = coachBookCustomPaperMapper.selectBookIdAndPaperCount(coachBookIds).stream().collect(Collectors.toMap(IntIntVo::getId, IntIntVo::getValue));
            Map<Integer, SchoolCoachBook> bookIdSchoolCoachBookMap = schoolCoachBookMapper.selectList(new LambdaQueryWrapper<SchoolCoachBook>()
                            .eq(SchoolCoachBook::getSchoolId, queryVo.getSchoolId())
                            .in(SchoolCoachBook::getCoachBookId, coachBookIds))
                    .stream().collect(Collectors.toMap(SchoolCoachBook::getCoachBookId, x -> x));
            for (BookInfo bookInfo : result.getRecords()) {
                bookInfo.setPaperCount(bookIdAndPaperCountMap.getOrDefault(bookInfo.getId(), 0));
                bookInfo.setCustomPaperCount(bookIdAndCustomPaperCountMap.getOrDefault(bookInfo.getId(), 0));
                // 错题标记方式
                SchoolCoachBook schoolCoachBook = bookIdSchoolCoachBookMap.get(bookInfo.getId());
                bookInfo.setWrongQuesQuickMark(schoolCoachBook.getWrongQuesQuickMark());
                bookInfo.setWrongQuesPaperMark(schoolCoachBook.getWrongQuesPaperMark());
                bookInfo.setCover(bookInfo.getCover() != null ? ossManager.getPublicPathTK(bookInfo.getCover()) : null);
            }
        }

        return result;
    }

    @Override
    public Page<SchoolCoachPaperVo> getPapersPage(QueryPaperVo queryVo, Integer page, Integer size, String schoolId, String userId) {
        Page<SchoolCoachPaperVo> result = new Page<>(page, size);
        schoolCoachBookMapper.getPaperPage(result, schoolId, queryVo, CoachBookTypeEnum.SYS.getCode());
        if (result.getTotal() > 0) {
            // 标记是否被收藏
            Set<String> paperIds = result.getRecords().stream().map(SchoolCoachPaperVo::getId).collect(Collectors.toSet());
            List<String> favorPaperIds = favoritePaperMapper.selectPaperIdListByUserIdAndPaperIds(paperIds, userId);
            for (SchoolCoachPaperVo p : result.getRecords()) {
                p.setIfFavorite(favorPaperIds.contains(p.getId()));
            }
        }
        return result;
    }

    /**
     * 定制教辅教材选题或知识点选题
     */
    @Override
    public IPage<UserQuestionVo> findQuestionByChapterOrKnowledge(TokenUserVo userVo, QueryQuestionVo queryVo) {
        FindQuestionPageParam param = new FindQuestionPageParam();
        param.setStage(queryVo.getGradeLevel());
        param.setSubject(queryVo.getSubjectId());
        param.setBookId(queryVo.getBookId());
        param.setChapter(queryVo.getChapter());
        param.setKnowledge(queryVo.getKnowledge());
        if (!questionSearchService.normalizeFindQuestionPageParam(param)) {
            return new Page<>(queryVo.getPage(), queryVo.getSize());
        }
        List<String> questionIds = questionMapper.selectSchoolCoachBookQuestionIds(userVo.getSchoolId(), param.getStage(), param.getSubject(),
                queryVo.getSemesterId(), queryVo.getTerm(), queryVo.getGradeId(),
                queryVo.getDifficulty(), queryVo.getQuesType(),
                param.getSelfAndDescendantChapterIds(), param.getSelfAndDescendantKnowledgeIds());
        return questionSearchService.buildQuesSearchVoPage(questionIds, queryVo.getPage(), queryVo.getSize(), userVo.getUserId());
    }

    @Override
    public SchoolCoachPaperVo getSimplePaper(String schoolId, Integer semesterId, String paperId) {
        return schoolCoachBookMapper.getSimplePaper(schoolId, semesterId, paperId);
    }


    @Override
    public Page<BookSchoolVo> getSchoolPage(Integer coachBookId, String keyword, Integer page, Integer size) {
        // 查教辅
        Page<BookSchoolVo> result = new Page<>(page, size);
        result.setOrders(Arrays.asList(
                new OrderItem().setColumn("semester_id").setAsc(Boolean.FALSE),
                new OrderItem().setColumn("term").setAsc(Boolean.FALSE),
                new OrderItem().setColumn("grade_id").setAsc(Boolean.TRUE)
        ));

        Map<String, String> schoolIdNameMap = null;
        if (StringUtils.isEmpty(keyword)) {
            schoolCoachBookMapper.getSchoolPageByBookId(result, coachBookId);
        } else {
            List<String> schoolIds = schoolCoachBookMapper.selectSchoolIdByBookId(coachBookId);
            if (schoolIds.isEmpty()) {
                result.setRecords(Collections.emptyList());
                return result;
            }

            schoolIdNameMap = feignService.getSchoolIdNameMap(schoolIds);
            schoolIdNameMap.entrySet().removeIf(x -> !keyword.contains(x.getValue()));
            if (schoolIdNameMap.isEmpty()) {
                result.setRecords(Collections.emptyList());
                return result;
            }
            schoolCoachBookMapper.getSchoolPageByBookIdAndSchoolIds(result, coachBookId, schoolIdNameMap.keySet());
        }

        if (result.getTotal() > 0) {
            if (schoolIdNameMap == null) {
                List<String> schoolIds = result.getRecords().stream().map(BookSchoolVo::getSchoolId).distinct().collect(Collectors.toList());
                schoolIdNameMap = feignService.getSchoolIdNameMap(schoolIds);
            }
            List<String> teacherIds = result.getRecords().stream().map(BookSchoolVo::getCreateBy).distinct().collect(Collectors.toList());
            Map<String, String> userIdNameMap = feignService.getUserIdNameMap(teacherIds);
            for (BookSchoolVo record : result.getRecords()) {
                record.setSchoolName(schoolIdNameMap.get(record.getSchoolId()));
                record.setCreator(userIdNameMap.get(record.getCreateBy()));
            }
        }

        return result;
    }

    @Override
    public String addBookToSchools(Integer coachBookId, Integer semesterId, Set<String> schoolIds, String userId) {
        CoachBook book = coachBookMapper.selectById(coachBookId);
        if (book == null) {
            throw new ApiException("找不到该教辅");
        }

        List<String> existsSchoolIds = schoolCoachBookMapper.selectObjs(new QueryWrapper<SchoolCoachBook>()
                        .select("DISTINCT school_id")
                        .lambda()
                        .eq(SchoolCoachBook::getCoachBookId, coachBookId)
                        .in(SchoolCoachBook::getSchoolId, schoolIds)
                        .eq(SchoolCoachBook::getSemesterId, semesterId)
                        .eq(SchoolCoachBook::getGradeId, book.getGradeId()))
                .stream().map(String::valueOf).collect(Collectors.toList());
        schoolIds.removeIf(existsSchoolIds::contains);
        if (schoolIds.isEmpty()) {
            throw new ApiException("指定的学校已有该教辅");
        }
        List<SchoolCoachBook> schoolCoachBookList = new LinkedList<>();
        Date now = new Date();
        for (String schoolId : schoolIds) {
            schoolCoachBookList.add(SchoolCoachBook.builder()
                    .schoolId(schoolId)
                    .coachBookId(coachBookId)
                    .semesterId(semesterId)
                    .term(book.getTerm())
                    .gradeLevel(book.getGradeLevel())
                    .gradeId(book.getGradeId())
                    .subjectId(book.getSubjectId())
                    .wrongQuesQuickMark(book.getWrongQuesQuickMark())
                    .wrongQuesPaperMark(book.getWrongQuesPaperMark())
                    .isMarkWayEdited(Boolean.FALSE)
                    .createBy(userId)
                    .createTime(now)
                    .build());
        }
        this.saveBatch(schoolCoachBookList);
        return "已成功为 " + schoolCoachBookList.size() + " 所学校定制教辅【".concat(book.getBookName()).concat("】");
    }

    @Override
    public int delSchoolByBook(Integer coachBookId, List<Integer> ids) {
        return schoolCoachBookMapper.delete(new LambdaQueryWrapper<SchoolCoachBook>()
                .in(SchoolCoachBook::getId, ids)
                .eq(SchoolCoachBook::getCoachBookId, coachBookId));
    }

    @Override
    public void editSchoolBook(SchoolCoachBook vo, String userId) {
        if (vo.getId() == null || vo.getCoachBookId() == null) {
            throw new ApiException("参数错误！");
        }

        SchoolCoachBook updateVo = SchoolCoachBook.builder()
                .id(vo.getId())
                .semesterId(vo.getSemesterId())
                .term(vo.getTerm())
                .gradeId(vo.getGradeId())
                .updateBy(userId)
                .updateTime(new Date())
                .build();
        if (vo.getGradeId() != null) {
            updateVo.setGradeLevel(feignService.getSysGrade(vo.getGradeId()).getGradeLevel());
        }
        this.updateById(updateVo);
    }

    @Override
    public void savePaperToSchoolCoach(String paperId, Integer gradeId, Integer semesterId, Integer term, String schoolId, String userId) {
        long beginTick = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder();
        sb.append("【添加试卷到校本教辅】paperId=").append(paperId)
                .append("，gradeId=").append(gradeId)
                .append("，semesterId=").append(semesterId)
                .append("，term=").append(term)
                .append("，schoolId=").append(schoolId)
                .append("，userId=").append(userId);
        try {
            // 检查试卷
            PaperMain paperMain = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>().eq(PaperMain::getId, paperId)
                    .select(PaperMain::getId, PaperMain::getSubjectId, PaperMain::getSchoolId));
            if (paperMain == null) {
                throw new ApiException("找不到该试卷");
            }
            if (!schoolId.equals(paperMain.getSchoolId())) {
                throw new ApiException("非法操作！该试卷不属于本校");
            }

            Integer subjectId = paperMain.getSubjectId();
            SysGrade sysGrade = feignService.getSysGrade(gradeId);
            if (sysGrade == null) {
                throw new ApiException("找不到该年级信息");
            }

            String subjectName = CacheUtil.getSubjectName(subjectId);
            String bookName = "校本教辅《" + subjectName + "》" + sysGrade.getGradeName() + (1 == term ? "上" : "下");
            sb.append("\nbookName=").append(bookName);

            // 检查试卷是否已被加入到定制教辅
            CoachBookPaper coachBookPaper = coachBookPaperMapper.selectOne(new LambdaQueryWrapper<CoachBookPaper>()
                    .eq(CoachBookPaper::getPaperId, paperId)
                    .select(CoachBookPaper::getId, CoachBookPaper::getCoachBookId));
            if (coachBookPaper != null) {
                CoachBook coachBook = coachBookMapper.selectOne(new LambdaQueryWrapper<CoachBook>().eq(CoachBook::getId, coachBookPaper.getCoachBookId()).select(CoachBook::getBookType));
                if (coachBook != null && Objects.equals(coachBook.getBookType(), CoachBookTypeEnum.SYS.getCode())) {
                    throw new ApiException("该试卷已被加入到定制教辅，禁止再制作错题反馈卡");
                }
            }

            // 加教辅
            Date now = new Date();
            CoachBook coachBook = coachBookMapper.selectOne(new LambdaQueryWrapper<CoachBook>()
                    .eq(CoachBook::getSubjectId, subjectId)
                    .eq(CoachBook::getGradeId, gradeId)
                    .eq(CoachBook::getSemesterId, semesterId)
                    .eq(CoachBook::getTerm, term)
                    .eq(CoachBook::getBookType, CoachBookTypeEnum.SCHOOL.getCode())
                    .eq(CoachBook::getSchoolId, schoolId)
                    .eq(CoachBook::getBookName, bookName)
                    .select(CoachBook::getId));
            if (coachBook == null) {
                sb.append("\n创建教辅");
                coachBook = CoachBook.builder()
                        .subjectId(subjectId)
                        .gradeLevel(sysGrade.getGradeLevel())
                        .gradeId(gradeId)
                        .semesterId(semesterId)
                        .term(term)
                        .bookName(bookName)
                        .pressName(Constants.EMPTY)
                        .bookType(CoachBookTypeEnum.SCHOOL.getCode())
                        .schoolId(schoolId)
                        .createBy(userId)
                        .createTime(now)
                        .build();
                coachBookMapper.insert(coachBook);
            } else {
                sb.append("\n校本教辅已存在");
            }

            // 教辅加到学校
            int bookId = coachBook.getId();
            if (!schoolCoachBookMapper.existsBySchoolIdAndCoachBookId(schoolId, bookId)) {
                sb.append("\n为学校添加教辅");
                SchoolCoachBook schoolCoachBook = SchoolCoachBook.builder()
                        .schoolId(schoolId)
                        .coachBookId(bookId)
                        .semesterId(semesterId)
                        .term(term)
                        .gradeLevel(sysGrade.getGradeLevel())
                        .gradeId(gradeId)
                        .subjectId(subjectId)
                        .createBy(userId)
                        .createTime(now)
                        .build();
                schoolCoachBookMapper.insert(schoolCoachBook);
            } else {
                sb.append("\n学校已有该教辅");
            }

            // 试卷加到教辅
            if (coachBookPaper != null) {
                if (ObjectUtil.equals(coachBookPaper.getCoachBookId(), bookId)) {
                    sb.append("\n教辅已有该卷，不用修改");
                    return;
                }
                sb.append("\n修改试卷");
                coachBookPaper.setCoachBookId(bookId);
                coachBookPaper.setCreateBy(userId);
                coachBookPaper.setCreateTime(now);
                coachBookPaperMapper.updateById(coachBookPaper);
            } else {
                sb.append("\n添加试卷");
                coachBookPaper = CoachBookPaper.builder()
                        .paperId(paperId)
                        .coachBookId(bookId)
                        .sortCode(1000)
                        .createBy(userId)
                        .createTime(now)
                        .build();
                coachBookPaperMapper.insert(coachBookPaper);
            }
            sb.append("\n处理成功，总耗时：").append(System.currentTimeMillis() - beginTick).append(" ms");
            log.info(sb.toString());
        } catch (Exception e) {
            sb.append("\n异常：").append(ExceptionUtils.getStackTrace(e));
            log.error(sb.toString());
            throw e;
        }
    }

    @Override
    public List<SchoolCoachVo> listSchoolBooks(String schoolId) {
        List<SchoolCoachBook> schoolCoachBookList = schoolCoachBookMapper.selectList(new LambdaQueryWrapper<SchoolCoachBook>().eq(SchoolCoachBook::getSchoolId, schoolId));
        if (schoolCoachBookList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Integer, SchoolCoachBook> bookIdSchoolCoachBookMap = schoolCoachBookList.stream().collect(Collectors.toMap(SchoolCoachBook::getCoachBookId, x -> x));
        Map<Integer, CoachBook> coachBookIdCoachBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                        .in(CoachBook::getId, bookIdSchoolCoachBookMap.keySet())
                        .select(CoachBook::getId, CoachBook::getBookName, CoachBook::getCover))
                .stream().collect(Collectors.toMap(CoachBook::getId, x -> x));
        // 补充封面完整的封面地址
        coachBookIdCoachBookNameMap.values().forEach(book -> {
            if (book.getCover() != null) {
                book.setCover(ossManager.getPublicPathTK(book.getCover()));
            } else {
                // 没有封面图片，使用默认图片
                book.setCover(coachBookDefaultCover);
            }
        });
        List<SchoolCoachVo> voList = new ArrayList<>(schoolCoachBookList.size());
        for (SchoolCoachBook schoolCoachBook : schoolCoachBookList) {
            SchoolCoachBook coachBook = bookIdSchoolCoachBookMap.get(schoolCoachBook.getCoachBookId());
            SchoolCoachVo vo = BeanUtil.copyProperties(schoolCoachBook, SchoolCoachVo.class);
            vo.setCoachBookName(coachBookIdCoachBookNameMap.get(schoolCoachBook.getCoachBookId()).getBookName());
            vo.setCoachBookCover(coachBookIdCoachBookNameMap.get(schoolCoachBook.getCoachBookId()).getCover());
            vo.setWrongQuesQuickMark(coachBook.getWrongQuesQuickMark());
            vo.setWrongQuesPaperMark(coachBook.getWrongQuesPaperMark());
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public void setWrongQuesMark(List<WrongQuesMarkVo> vos, TokenUserVo userVo) {
        // 校验是不是学校管理员
        if (!userVo.isSchoolManager()) {
            throw new ApiException("非法操作");
        }
        for (WrongQuesMarkVo vo : vos) {
            schoolCoachBookMapper.update(null, new LambdaUpdateWrapper<SchoolCoachBook>()
                    .eq(SchoolCoachBook::getCoachBookId, vo.getId())
                    .eq(SchoolCoachBook::getSchoolId, userVo.getSchoolId())
                    .set(SchoolCoachBook::getWrongQuesQuickMark, vo.getWrongQuesQuickMark())
                    .set(SchoolCoachBook::getWrongQuesPaperMark, vo.getWrongQuesPaperMark())
                    .set(SchoolCoachBook::getIsMarkWayEdited, Boolean.TRUE)
            );
        }
    }

    @Override
    public String getLastPaperId(String schoolId, Integer semesterId, Integer term, String paperId) {
        Integer coachBookId = schoolCoachBookMapper.queryLastPaperId(schoolId, semesterId, term, paperId);
        List<CoachBookPaper> papers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .orderByAsc(CoachBookPaper::getSortCode)
                .select(CoachBookPaper::getPaperId, CoachBookPaper::getSortCode));
        for (int i = 0; i < papers.size(); i++) {
            if (Objects.equals(papers.get(i).getPaperId(), paperId)) {
                if (i == 0) {
                    return null;
                }
                return papers.get(i - 1).getPaperId();
            }
        }
        return null;
    }

}
