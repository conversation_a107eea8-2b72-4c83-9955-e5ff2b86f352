package com.sure.question.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sure.common.entity.Result;
import com.sure.common.entity.StatusCode;
import com.sure.common.exception.ApiException;
import com.sure.question.cache.Cache;
import com.sure.question.dto.paper.structure.CheckPaperStructureOption;
import com.sure.question.dto.paper.structure.PaperStructure;
import com.sure.question.dto.question.CheckQuestionOption;
import com.sure.question.entity.*;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.PaperUploadTypeEnum;
import com.sure.question.feign.MarkService;
import com.sure.question.mapper.*;
import com.sure.question.service.*;
import com.sure.question.util.IdUtil;
import com.sure.question.vo.Basket;
import com.sure.question.vo.basket.SavePaperResultVo;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.SmallQuestionVo;
import com.sure.question.vo.sheet.SimpleSheetVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class BasketServiceImpl implements BasketService {
    private final PaperMainMapper paperMainMapper;
    private final PaperQuestionMapper paperQuestionMapper;
    private final BasketPaperStyleMapper basketPaperStyleMapper;
    private final QuestionMapper questionMapper;
    private final QuestionService questionService;
    private final QuestionBuildService questionBuildService;
    private final SmallQuestionMapper smallQuestionMapper;
    private final ListeningService listeningService;
    private final CoachBookService coachBookService;
    private final CoachBookCustomPaperMapper coachBookCustomPaperMapper;
    private final QuestionEsSyncMapper questionEsSyncMapper;
    private final MarkService markService;
    private final FeignService feignService;
    private final Cache cache;


    /**
     * 从缓存中获取试题篮数据，如果为空，则设置一个默认的试题篮
     */
    private Basket getBasketFromCacheDefaultEmpty(String userId) {
        Basket cacheBasket = cache.getCacheBasket(userId);
        if (cacheBasket == null) {
            cacheBasket = new Basket();
            cache.setCacheBasket(userId, cacheBasket);
        }
        // 空字段替换为初始值
        if (cacheBasket.getQuestions() == null) {
            cacheBasket.setQuestions(new ArrayList<>());
        }
        if (cacheBasket.getBasketPaperStyle() == null) {
            cacheBasket.setBasketPaperStyle(new BasketPaperStyle());
        }
        if (cacheBasket.getPaperInfo() == null) {
            cacheBasket.setPaperInfo(new HashMap<>());
        }
        return cacheBasket;
    }

    /**
     * 获取试题篮，补充题目等内容
     */
    @Override
    public Basket getBasketAddContent(String userId) {
        // 从缓存中取试题篮数据
        Basket basket = getBasketFromCacheDefaultEmpty(userId);
        // 提取题目Id
        List<String> questionIds = basket.extractQuestionIdsFromPaperStructure();
        // 补充题目
        if (!questionIds.isEmpty()) {
            basket.getQuestions().addAll(questionBuildService.selectQuestionVos(questionIds));
        }
        return basket;
    }

    /**
     * 获取用户试题篮缓存原始数据
     */
    @Override
    public Basket getUserBasketRawCache(String userId) {
        return cache.getCacheBasket(userId);
    }

    /**
     * 更新试题篮
     */
    @Override
    public void updateBasket(Basket basket, String userId) {
        Basket oldBasket = getBasketFromCacheDefaultEmpty(userId);

        // 复制原试卷信息
        basket.setPaperInfo(oldBasket.getPaperInfo());

        // 若试卷Id不存在，则新生成一个
        if (StringUtils.isBlank(basket.getPaperId())) {
            basket.setPaperId(IdUtil.StringId());
        }

        // 复制原未保存题目，剔除试卷结构中未关联的
        List<String> deletedUnSaveQuestionIds = new ArrayList<>();
        List<QuestionVo> questions = new ArrayList<>();
        Set<String> questionIdSet = new HashSet<>(basket.extractQuestionIdsFromPaperStructure());
        for (QuestionVo q : oldBasket.getQuestions()) {
            if (questionIdSet.contains(q.getId())) {
                questions.add(q);
            } else {
                deletedUnSaveQuestionIds.add(q.getId());
            }
        }

        basket.setQuestions(questions);
        basket.getBasketPaperStyle().setQuestionCategoryJson(basket.getPaperStructure());

        if (!questionIdSet.isEmpty()) {
            checkQuestionsAndPaperStructure(basket);
        }

        if (!deletedUnSaveQuestionIds.isEmpty()) {
            listeningService.deleteQuestionsListening(deletedUnSaveQuestionIds);
        }

        // 保存
        cache.setCacheBasket(userId, basket);
    }


    /**
     * 清空试题篮
     */
    @Override
    public void clearBasket(String userId) {
        cache.setCacheBasket(userId, new Basket());
    }

    /**
     * 替换试题篮
     */
    @Override
    public void replaceBasket(Basket basket, String userId) {
        cache.setCacheBasket(userId, basket);
    }

    /**
     * 添加题目
     */
    @Override
    public List<QuestionVo> addQuestions(List<QuestionVo> questionVos, String paperStructure, String userId) {
        questionVos.forEach(questionVo -> {
            questionVo.check(CheckQuestionOption.checkAllExceptScore());
            listeningService.saveQuestionListening(questionVo);
        });
        Basket basket = getBasketFromCacheDefaultEmpty(userId);
        basket.getQuestions().addAll(questionVos);
        basket.setPaperStructure(paperStructure);
        basket.getBasketPaperStyle().setQuestionCategoryJson(paperStructure);
        checkQuestionsAndPaperStructure(basket);
        cache.setCacheBasket(userId, basket);
        return questionVos;
    }

    /**
     * 更改题目
     * @param oldQuestionId 原题Id
     * @param questionVo 修改后的新题
     */
    @Override
    public QuestionVo updateQuesVo(String oldQuestionId, QuestionVo questionVo, String paperStructure, String userId) {
        questionVo.check(CheckQuestionOption.checkAllExceptScore());

        // 从原题复制听力
        listeningService.copyFromOldQuestionAndSaveNewQuestionListening(questionVo, oldQuestionId);

        Basket basket = getBasketFromCacheDefaultEmpty(userId);
        basket.getQuestions().removeIf(q -> q.getId().equals(oldQuestionId));
        basket.getQuestions().add(questionVo);
        basket.setPaperStructure(paperStructure);
        basket.getBasketPaperStyle().setQuestionCategoryJson(paperStructure);
        checkQuestionsAndPaperStructure(basket);
        cache.setCacheBasket(userId, basket);
        return questionVo;
    }

    /**
     * 存为试卷
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SavePaperResultVo saveAsPaper(String userId, String schoolId, Integer gradeLevel, Integer subjectId) {
        Basket basket = cache.getCacheBasket(userId);
        checkQuestionsAndPaperStructure(basket);

        // 查现有试卷
        PaperMain oldPaper = paperMainMapper.selectById(basket.getPaperId());
        boolean isUpdate = false;
        if (oldPaper != null) {
            // 只允许个人已组试卷再次编辑
            if (StringUtils.equals(userId, oldPaper.getUserId()) && PaperUploadTypeEnum.MAKE.getId().toString().equals(oldPaper.getUploadType())) {
                checkCanUpdate(oldPaper);
                isUpdate = true;
            } else {
                throw new ApiException("试卷已存在");
            }
        }

        PaperMain paperMain;
        // 若为更新，需先更新属性、删除旧数据
        if (isUpdate) {
            updatePaperMain(oldPaper, basket);
            paperMain = oldPaper;
            deletePaper(paperMain.getId());
        }
        // 否则新建试卷
        else {
            paperMain = createPaperMain(userId, schoolId, basket, gradeLevel, subjectId);
        }

        // 保存试卷
        savePaper(paperMain, basket);

        // 清空试题篮
        cache.setCacheBasket(userId, new Basket());

        // 若为更新，还需检查有无答题卡、清除反馈卡
        // 因为是修改其他服务数据，出错回滚不方便，所以最后执行
        String warning = null;
        if (isUpdate) {
            Map<String, SimpleSheetVo> paperIdAnswerSheetMap = feignService.getPaperIdAnswerSheetMap(Collections.singletonList(paperMain.getId()));
            if (!paperIdAnswerSheetMap.isEmpty()) {
                warning = "试卷【" + paperMain.getPaperName() + "】" + "已生成答题卡。\n\n请适时修改试卷对应答题卡！";
            }
            Result response = markService.cleanFeedbackSheet(paperMain.getId());
            if (!Objects.equals(response.getCode(), StatusCode.OK)) {
                throw new ApiException("清除反馈卡失败");
            }
        }

        return new SavePaperResultVo(paperMain.getId(), warning);
    }

    private void checkQuestionsAndPaperStructure(Basket basket) {
        // 检查试题篮
        if (StringUtils.isBlank(basket.getPaperId())) {
            throw new ApiException("试题篮无试卷Id");
        }
        if (basket.getBasketPaperStyle() == null || StringUtils.isBlank(basket.getBasketPaperStyle().getTitle())) {
            throw new ApiException("试题篮无试卷标题");
        }
        PaperStructure structure = basket.extractStructure();
        if (structure == null) {
            throw new ApiException("试题篮无试卷结构");
        }
        if (CollUtil.isEmpty(structure.getVolumes())) {
            throw new ApiException("试题篮无题目");
        }
        structure.check(CheckPaperStructureOption.allowScoreNull());

        // 检查新题目
        Map<String, Set<String>> questionIdBranchIdsMap = new HashMap<>();
        if (basket.getQuestions() != null) {
            basket.getQuestions().forEach(q -> {
                q.check(CheckQuestionOption.checkAllExceptScore());
                questionIdBranchIdsMap.put(q.getId(), q.getBranches().stream().map(SmallQuestionVo::getId).collect(Collectors.toSet()));
            });
        }

        // 检查题目、小题存在
        Set<String> questionIds = structure.extractQuestionIdBranchIdsMap().keySet();
        Map<String, Set<String>> dbQuestionIdBranchIdsMap = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                        .in(SmallQuestion::getQuestionId, questionIds)
                        .select(SmallQuestion::getQuestionId, SmallQuestion::getId))
                .stream().collect(Collectors.groupingBy(SmallQuestion::getQuestionId, Collectors.mapping(SmallQuestion::getId, Collectors.toSet())));
        questionIdBranchIdsMap.putAll(dbQuestionIdBranchIdsMap);
        if (!structure.compareStructureQuestionsWithPaperQuestions(questionIdBranchIdsMap)) {
            throw new ApiException("试卷试题与试题篮试题不匹配");
        }
    }

    private void checkCanUpdate(PaperMain paperMain) {
        if (PaperBankEnum.Public.getCode().toString().equals(paperMain.getPaperBank())) {
            throw new ApiException("公共库试卷禁止修改");
        }
        if (coachBookService.isCoachBookPaperAndLocked(paperMain.getId())) {
            throw new ApiException("教辅试卷已锁定，禁止修改");
        }
        if (paperMain.getAnswerSheetDownloadCount() > 0) {
            throw new ApiException("该试卷答题卡已被下载，无法修改");
        }
        if (paperMain.getFeedbackSheetDownloadCount() > 0) {
            throw new ApiException("该试卷反馈卡已被下载，无法修改");
        }
        // 检查是否绑定到考试科目
        Result response = markService.checkPaperExamSubjectBinding(paperMain.getId());
        if (!Objects.equals(response.getCode(), StatusCode.OK)) {
            throw new ApiException(response.getMessage());
        }
    }

    private void updatePaperMain(PaperMain paperMain, Basket basket) {
        paperMain.setPaperName(basket.getBasketPaperStyle().getTitle());
        paperMain.setPaperStyleJson(basket.getPaperStructureJson());
        paperMain.setUpdateTime(System.currentTimeMillis());
    }

    private void deletePaper(String paperId) {
        paperMainMapper.deleteById(paperId);

        paperQuestionMapper.delete(new LambdaQueryWrapper<PaperQuestion>()
                .eq(PaperQuestion::getPaperId, paperId));

        basketPaperStyleMapper.deleteById(paperId);

        // 清空练习卷PDF状态和路径
        coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                .eq(CoachBookCustomPaper::getPaperId, paperId)
                .set(CoachBookCustomPaper::getPdfStatus, null)
                .set(CoachBookCustomPaper::getPdfPath, null));
    }

    private PaperMain createPaperMain(String userId, String schoolId, Basket basket, Integer gradeLevel, Integer subjectId) {
        PaperMain paperMain = new PaperMain();
        paperMain.setId(basket.getPaperId());
        paperMain.setPaperName(basket.getBasketPaperStyle().getTitle());
        paperMain.setGradeLevel(gradeLevel);
        paperMain.setSubjectId(subjectId);
        paperMain.setPaperBank(PaperBankEnum.Personal.getCode().toString());
        paperMain.setUploadType(PaperUploadTypeEnum.MAKE.getId().toString());
        paperMain.setPaperTypeId(0);
        paperMain.setPaperStyleJson(basket.getPaperStructureJson());
        paperMain.setSchoolId(schoolId);
        paperMain.setUserId(userId);
        paperMain.setCreateTime(System.currentTimeMillis());
        paperMain.setUpdateTime(System.currentTimeMillis());
        return paperMain;
    }

    private void savePaper(PaperMain paperMain, Basket basket) {
        // 保存修改的题目
        List<QuestionVo> questions = basket.getQuestions();
        if (CollUtil.isNotEmpty(questions)) {
            questionService.saveQuestionsOfPaper(questions, paperMain);
        }

        // 插入paper_question
        List<String> questionIds = basket.extractQuestionIdsFromPaperStructure();
        List<PaperQuestion> paperQuestions = questionService.createPaperQuestionRelations(paperMain.getId(), questionIds);
        paperQuestionMapper.insertList(paperQuestions);

        // 更新试卷样式表
        BasketPaperStyle paperStyle = basket.getBasketPaperStyle();
        paperStyle.setPaperId(paperMain.getId());
        paperStyle.setSchoolId(paperMain.getSchoolId());
        paperStyle.setUserId(paperMain.getUserId());
        paperStyle.setCreateTime(System.currentTimeMillis());
        basketPaperStyleMapper.insert(paperStyle);

        // 组卷次数自增1
        questionMapper.useCountIncr(questionIds);

        // 同步es
        questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);

        // 插入试卷
        paperMainMapper.insert(paperMain);
    }
}
