package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.GradeSubject;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.PaperType;
import com.sure.question.enums.BaseEnum;
import com.sure.question.enums.GradeLevelEnum;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.mapper.PaperTypeMapper;
import com.sure.question.service.PaperTypeService;
import com.sure.question.util.CacheUtil;
import com.sure.question.util.RedissonLockUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaperTypeServiceImpl implements PaperTypeService {
    private final PaperTypeMapper paperTypeMapper;
    private final PaperMainMapper paperMainMapper;

    @Override
    public List<PaperType> listPaperTypes(Integer gradeLevel) {
        return paperTypeMapper.selectList(new LambdaQueryWrapper<PaperType>()
                .eq(gradeLevel != null, PaperType::getGradeLevel, gradeLevel)
                .orderByAsc(PaperType::getGradeLevel)
                .orderByAsc(PaperType::getOrderId));
    }

    @Override
    public int addPaperType(int gradeLevel, String paperTypeName, String userId) {
        String finalPaperTypeName = StringUtils.trim(paperTypeName);
        if (StringUtils.isBlank(finalPaperTypeName)) {
            throw new ApiException("试卷类型名称不能为空");
        }
        if (BaseEnum.getNameById(GradeLevelEnum.class, gradeLevel) == null) {
            throw new ApiException("学段错误");
        }
        return RedissonLockUtil.executeAfterLocked("Ques:Lock:PaperType", () -> {
            List<PaperType> paperTypes = paperTypeMapper.selectList(new LambdaQueryWrapper<PaperType>()
                    .eq(PaperType::getGradeLevel, gradeLevel));
            if (paperTypes.stream().anyMatch(t -> t.getPaperTypeName().equals(finalPaperTypeName))) {
                throw new ApiException("试卷类型已存在");
            }
            int maxId = paperTypes.stream().mapToInt(PaperType::getPaperTypeId).max().orElse(0);
            int maxOrder = paperTypes.stream().mapToInt(PaperType::getOrderId).max().orElse(0);
            PaperType newPaperType = new PaperType();
            newPaperType.setGradeLevel(gradeLevel);
            newPaperType.setPaperTypeId(maxId + 1);
            newPaperType.setPaperTypeName(finalPaperTypeName);
            newPaperType.setOrderId(maxOrder + 1);
            newPaperType.setIsSpecial(false);
            paperTypeMapper.insert(newPaperType);

            log.info("添加试卷类型, userId = {}, newPaperType = {}", userId, newPaperType);
            return newPaperType.getPaperTypeId();
        });
    }

    @Override
    public void updatePaperType(int gradeLevel, int paperTypeId, String paperTypeName, int orderId, String userId) {
        String finalPaperTypeName = StringUtils.trim(paperTypeName);
        if (StringUtils.isBlank(finalPaperTypeName)) {
            throw new ApiException("试卷类型名称不能为空");
        }
        RedissonLockUtil.executeAfterLocked("Ques:Lock:PaperType", () -> {
            List<PaperType> gradeLevelPaperTypes = paperTypeMapper.selectList(new LambdaQueryWrapper<PaperType>()
                    .eq(PaperType::getGradeLevel, gradeLevel));
            PaperType paperType = gradeLevelPaperTypes.stream().filter(t -> t.getPaperTypeId().equals(paperTypeId)).findFirst().orElse(null);
            if (paperType == null) {
                throw new ApiException("不存在该试卷类型");
            } else if (gradeLevelPaperTypes.stream().anyMatch(t -> !t.getPaperTypeId().equals(paperTypeId)
                    && t.getPaperTypeName().equals(finalPaperTypeName))) {
                throw new ApiException("已存在同名试卷类型");
            }
            paperTypeMapper.update(null, new LambdaUpdateWrapper<PaperType>()
                    .eq(PaperType::getGradeLevel, gradeLevel)
                    .eq(PaperType::getPaperTypeId, paperTypeId)
                    .set(PaperType::getPaperTypeName, finalPaperTypeName)
                    .set(PaperType::getOrderId, orderId));
            log.info("修改试卷类型, userId = {}, gradeLevel = {}, paperTypeId = {}, paperTypeName({} -> {}), orderId({} -> {})",
                    userId, gradeLevel, paperTypeId, paperType.getPaperTypeName(), finalPaperTypeName, paperType.getOrderId(), orderId);
        });
    }

    @Override
    public void deletePaperType(int gradeLevel, int paperTypeId, String userId) {
        RedissonLockUtil.executeAfterLocked("Ques:Lock:PaperType", () -> {
            PaperType paperType = paperTypeMapper.selectOne(new LambdaQueryWrapper<PaperType>()
                    .eq(PaperType::getGradeLevel, gradeLevel)
                    .eq(PaperType::getPaperTypeId, paperTypeId));
            if (paperType == null) {
                throw new ApiException("试卷类型不存在");
            }
            PaperMain onePaper = null;
            Set<Integer> subjectIds = CacheUtil.getGradeSubjects().stream()
                    .filter(t -> t.getGradeLevel().equals(gradeLevel))
                    .map(GradeSubject::getSubjectId).collect(Collectors.toSet());
            for (Integer subjectId : subjectIds) {
                onePaper = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                        .eq(PaperMain::getGradeLevel, gradeLevel)
                        .eq(PaperMain::getSubjectId, subjectId)
                        .eq(PaperMain::getPaperTypeId, paperTypeId)
                        .last("limit 1"));
                if (onePaper != null) {
                    break;
                }
            }
            if (onePaper != null) {
                throw new ApiException("该试卷类型存在试卷，禁止删除");
            }
            paperTypeMapper.delete(new LambdaQueryWrapper<PaperType>()
                    .eq(PaperType::getGradeLevel, gradeLevel)
                    .eq(PaperType::getPaperTypeId, paperTypeId));
            log.info("删除试卷类型, userId = {}, paperType = {}", userId, paperType);
        });
    }
}
