package com.sure.question.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.question.common.Constants;
import com.sure.question.document.QuestionDoc;
import com.sure.question.dto.question.SelectQuestionVoOption;
import com.sure.question.entity.*;
import com.sure.question.enums.DifficultyEnum;
import com.sure.question.mapper.*;
import com.sure.question.service.ChapterService;
import com.sure.question.service.QuestionBuildService;
import com.sure.question.service.QuestionDocService;
import com.sure.question.service.QuestionSearchService;
import com.sure.question.vo.EntryVo;
import com.sure.question.vo.dataVo.IntStrVo;
import com.sure.question.vo.es.QueryParam;
import com.sure.question.vo.pushQuesVo.PushQuesParam;
import com.sure.question.vo.question.*;
import com.sure.question.vo.requestVo.intelligentVo.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.NodeVisitor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class QuestionSearchServiceImpl implements QuestionSearchService {
    private final QuestionBuildService questionBuildService;
    private final QuestionDocService questionDocService;
    private final PaperQuestionMapper paperQuestionMapper;
    private final SmallQuestionMapper smallQuestionMapper;
    private final ChapterMapper chapterMapper;
    private final ChapterService chapterService;
    private final QuestionKnowledgeMapper questionKnowledgeMapper;
    private final KnowledgeMainMapper knowledgeMainMapper;
    private final RegionMapper regionMapper;
    private final ExamSubjectPushQuesMapper examSubjectPushQuesMapper;


    /**
     * 规范化搜索题目请求参数
     * 设置地区前缀及后代，设置章节后代，设置知识点后代
     * @return 地区、章节、知识点找不到时返回false
     */
    @Override
    public boolean normalizeFindQuestionPageParam(FindQuestionPageParam param) {
        // 地区
        if (StringUtils.isNotBlank(param.getRegion())) {
            // 前缀
            param.setRegionPrefix(param.getRegion().replace("0+$", ""));
            Long regionId = Long.valueOf(param.getRegion());
            List<Long> regionIds = regionMapper.selectSelfAndDescendantIds(Collections.singletonList(regionId));
            if (regionIds.isEmpty()) {
                return false;
            }
            param.setSelfAndDescendantRegionIds(regionIds);
        } else {
            param.setRegionPrefix(null);
            param.setSelfAndDescendantRegionIds(null);
        }

        // 指定章节，语文、英语按章节搜索，其他科目按章节关联知识点搜索
        List<Integer> paramChapterIds = new ArrayList<>();
        if (param.getChapter() != null) {
            paramChapterIds.add(param.getChapter());
        }
        if (param.getChapterIds() != null) {
            paramChapterIds.addAll(param.getChapterIds());
        }
        if (paramChapterIds.isEmpty() && param.getBookId() != null) {
            chapterMapper.selectList(new LambdaQueryWrapper<Chapter>()
                            .eq(Chapter::getBookId, param.getBookId())
                            .eq(Chapter::getLevel, 0)
                            .select(Chapter::getId))
                    .forEach(cpt -> paramChapterIds.add(cpt.getId()));
        }
        if (!paramChapterIds.isEmpty()) {
            if (param.getSubject() == 1 || param.getSubject() == 3) {
                List<Integer> chapterIdList = chapterMapper.selectSelfAndDescendantIds(paramChapterIds);
                if (chapterIdList.isEmpty()) {
                    return false;
                }
                param.setSelfAndDescendantChapterIds(chapterIdList);
            } else {
                List<Integer> knowledgeIdList = chapterService.selectChaptersSelfAndDescendantKnowledgeIds(paramChapterIds);
                if (knowledgeIdList.isEmpty()) {
                    return false;
                }
                param.setSelfAndDescendantKnowledgeIds(knowledgeIdList);
            }
        } else {
            param.setSelfAndDescendantChapterIds(null);
        }

        // 知识点
        if (paramChapterIds.isEmpty()) {
            List<Integer> paramKnowledgeIds = new ArrayList<>();
            if (param.getKnowledge() != null) {
                paramKnowledgeIds.add(param.getKnowledge());
            }
            if (param.getKnowledgeIds() != null) {
                paramKnowledgeIds.addAll(param.getKnowledgeIds());
            }
            if (!paramKnowledgeIds.isEmpty()) {
                List<Integer> knowledgeIdList = knowledgeMainMapper.selectSelfAndDescendantIds(paramKnowledgeIds);
                if (knowledgeIdList.isEmpty()) {
                    return false;
                }
                param.setSelfAndDescendantKnowledgeIds(knowledgeIdList);
            } else {
                param.setSelfAndDescendantKnowledgeIds(null);
            }
        }

        return true;
    }

    /**
     * 构建QuesSearchVo分页
     */
    @Override
    public IPage<UserQuestionVo> buildQuesSearchVoPage(List<String> questionIds, int page, int size, String userId) {
        IPage<UserQuestionVo> result = new Page<>(page, size);
        result.setTotal(questionIds.size());
        List<String> pageQuestionIds = questionIds.stream()
                .skip((long) (page - 1) * size).limit(size).collect(Collectors.toList());
        if (pageQuestionIds.isEmpty()) {
            return result;
        }
        result.setRecords(questionBuildService.selectUserQuestionVos(pageQuestionIds, userId));
        return result;
    }

    /**
     * 教材选题或知识点选题
     */
    @Override
    public Page<UserQuestionVo> findByChapterOrKnowledge(FindQuestionPageParam findParam, String userId) {
        if (!normalizeFindQuestionPageParam(findParam)) {
            return new Page<>(findParam.getPage(), findParam.getSize(), 0);
        }
        QueryParam queryParam = buildQueryParamCommon(findParam);

        // 页码随日期偏移
        if (shouldChangePage(queryParam)) {
            int gradeLevelSubjectQuestionCount = questionDocService.selectGradeLevelSubjectQuestionCount(queryParam.getGradeLevel(), queryParam.getSubjectId());
            queryParam.setPage(changePageBaseOnDay(queryParam.getPage(), queryParam.getSize(), gradeLevelSubjectQuestionCount));
        }

        return searchQuestionsCommon(queryParam, userId);
    }

    /**
     * 构建查询参数
     */
    private QueryParam buildQueryParamCommon(FindQuestionPageParam findParam) {
        QueryParam param = new QueryParam();
        param.setPage(findParam.getPage());
        param.setSize(findParam.getSize());
        param.setGradeLevel(findParam.getStage());
        param.setSubjectId(findParam.getSubject());
        param.setDifficulty(findParam.getDifficulty());
        param.setQuestionTypeId(findParam.getQues_type());
        param.setPaperTypeId(findParam.getSource());
        param.setGradeId(findParam.getGradeId());
        param.setYear(findParam.getYear());
        param.setKeyword(findParam.getKeyword());
        param.setKeywordMinMatchPercent(60);
        param.setHighlight(true);
        param.setSortBy(findParam.getSort_by());
        param.setIsDesc(!Boolean.FALSE.equals(findParam.getIsDesc()));
        param.setRegionIds(findParam.getSelfAndDescendantRegionIds());
        if (findParam.getSelfAndDescendantChapterIds() != null) {
            param.setChapterIds(findParam.getSelfAndDescendantChapterIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        if (findParam.getSelfAndDescendantKnowledgeIds() != null) {
            param.setKnowledgeIds(findParam.getSelfAndDescendantKnowledgeIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        if (findParam.getPaperIds() != null) {
            param.setIncludePaperIds(findParam.getPaperIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        return param;
    }

    /**
     * 是否应该改变查询页码
     * 题库新题录入频率不高时，可能用户连续多日打开查题页面看到的是相同的题
     * 在没有指定查询条件时改变前端传入页码，使得用户每天看到的题均不同
     */
    private boolean shouldChangePage(QueryParam param) {
        return Stream.of(param.getQuestionTypeId(), param.getDifficulty(), param.getYear(), param.getPaperTypeId(), param.getGradeId()).allMatch(Objects::isNull)
                && Stream.of(param.getKnowledgeIds(), param.getChapterIds(), param.getRegionIds(), param.getExcludeQuestionIds()).allMatch(CollUtil::isEmpty)
                && StringUtils.isBlank(param.getKeyword())
                && !StringUtils.equalsAnyIgnoreCase(param.getSortBy(), "useCount", "answerCount")
                && !Boolean.FALSE.equals(param.getIsDesc());
    }

    /**
     * 基于日期改变页码
     */
    private int changePageBaseOnDay(int page, int size, int total) {
        if (total <= 0) {
            return page;
        }
        int pageCount = (total / size) + (total % size == 0 ? 0 : 1);
        // 最后一页不变
        if (page >= pageCount) {
            return page;
        }
        int day = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
        int[] offsetArray = new int[] {6, 10, 14, 8, 12, 9, 3, 1, 13, 5, 7, 0, 11, 2, 4};
        int offset = offsetArray[day % offsetArray.length];
        return (page + offset) % (pageCount - 1);
    }

    /**
     * 根据参数及用户查题
     */
    private Page<UserQuestionVo> searchQuestionsCommon(QueryParam param, String userId) {
        IPage<QuestionDoc> esResult = questionDocService.selectQuestionDocIdHighlightsPage(param);
        Page<UserQuestionVo> result = new Page<>(param.getPage(), param.getSize(), esResult.getTotal());
        List<QuestionDoc> esDocs = esResult.getRecords();
        List<UserQuestionVo> records = new ArrayList<>();
        if (!esDocs.isEmpty()) {
            List<String> questionIds = esDocs.stream().map(QuestionDoc::getId).map(String::valueOf).collect(Collectors.toList());
            if (!questionIds.isEmpty()) {
                records = questionBuildService.selectUserQuestionVos(questionIds, userId);
                // 高亮替换
                replaceHighlightContents(esDocs, records);
            }
        }
        result.setRecords(records);
        return result;
    }

    /**
     * 替换题目内容为es返回的高亮内容
     */
    private void replaceHighlightContents(List<QuestionDoc> esDocs, List<? extends QuestionVo> records) {
        Map<String, String> questionIdContentMap = new HashMap<>();
        Map<String, String> smallQuestionIdContentMap = new HashMap<>();
        Map<String, String> smallQuestionIdOptionsMap = new HashMap<>();
        Map<String, String> smallQuestionIdAnswerMap = new HashMap<>();
        Map<String, String> smallQuestionIdExplanationMap = new HashMap<>();
        esDocs.forEach(q -> {
            if (StringUtils.isNotBlank(q.getContent_html())) {
                questionIdContentMap.put(q.getId().toString(), q.getContent_html());
            }
            if (q.getSmall_questions() != null) {
                q.getSmall_questions().forEach(sq -> {
                    if (StringUtils.isNotBlank(sq.getContent_html())) {
                        smallQuestionIdContentMap.put(sq.getId().toString(), sq.getContent_html());
                    }
                    if (StringUtils.isNotBlank(sq.getOptions())) {
                        smallQuestionIdOptionsMap.put(sq.getId().toString(), sq.getOptions());
                    }
                    if (StringUtils.isNotBlank(sq.getAnswer_html())) {
                        smallQuestionIdAnswerMap.put(sq.getId().toString(), sq.getAnswer_html());
                    }
                    if (StringUtils.isNotBlank(sq.getExplanation())) {
                        smallQuestionIdExplanationMap.put(sq.getId().toString(), sq.getExplanation());
                    }
                });
            }
        });
        records.forEach(q -> {
            if (questionIdContentMap.containsKey(q.getId())) {
                q.setTrunk(questionIdContentMap.get(q.getId()));
            }
            if (q.getBranches() != null) {
                q.getBranches().forEach(branch -> {
                    if (smallQuestionIdContentMap.containsKey(branch.getId())) {
                        branch.setStem(smallQuestionIdContentMap.get(branch.getId()));
                    }
                    if (smallQuestionIdOptionsMap.containsKey(branch.getId())) {
                        branch.setOptionsJson(smallQuestionIdOptionsMap.get(branch.getId()));
                    }
                    if (smallQuestionIdAnswerMap.containsKey(branch.getId())) {
                        branch.setSolution(smallQuestionIdAnswerMap.get(branch.getId()));
                    }
                    if (smallQuestionIdExplanationMap.containsKey(branch.getId())) {
                        branch.setExplanation(smallQuestionIdExplanationMap.get(branch.getId()));
                    }
                });
            }
        });
    }

    /**
     * 添加变式题时选题
     */
    @Override
    public Page<UserQuestionVo> findByAddVariantsParam(FindQuestionPageParam findParam, String userId) {
        if (!normalizeFindQuestionPageParam(findParam)) {
            return new Page<>(findParam.getPage(), findParam.getSize(), 0);
        }
        QueryParam queryParam = buildQueryParamCommon(findParam);

        // 剔除原卷题目及原卷已包含变式题
        if (StringUtils.isNotEmpty(findParam.getIgnorePaperId())) {
            Set<Long> excludeQuestionIds = new HashSet<>();
            paperQuestionMapper.selectList(new LambdaQueryWrapper<PaperQuestion>()
                            .eq(PaperQuestion::getPaperId, findParam.getIgnorePaperId())
                            .select(PaperQuestion::getQuestionId))
                    .forEach(pq -> excludeQuestionIds.add(Long.valueOf(pq.getQuestionId())));
            examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                            .eq(ExamsubjectPushQues::getPaperId, findParam.getIgnorePaperId())
                            .eq(ExamsubjectPushQues::getExamSubjectId, "")
                            .select(ExamsubjectPushQues::getPushId))
                    .forEach(examSubjectPushQues -> excludeQuestionIds.add(Long.valueOf(examSubjectPushQues.getPushId())));
            queryParam.setExcludeQuestionIds(new ArrayList<>(excludeQuestionIds));
        }

        // 按匹配到的知识点个数排序
        queryParam.setSortBy("knowledge_match");
        queryParam.setIsDesc(true);

        return searchQuestionsCommon(queryParam, userId);
    }

    /**
     * 指定题目Id查相似题
     * @param questionId 题目Id
     * @param size 相似题个数
     * @param userId 用户Id
     * @return 相似题
     */
    @Override
    public List<UserQuestionVo> findSimilarQuestions(String questionId, int size, String userId) {
        QuestionVo questionVo = questionBuildService.selectQuestionVo(questionId, SelectQuestionVoOption.addNothing());
        if (questionVo == null) {
            return Collections.emptyList();
        }
        return findSimilarQuestions(questionVo, size, userId);
    }

    /**
     * 根据题目内容查相似题
     */
    @Override
    public List<UserQuestionVo> findSimilarQuestions(QuestionVo questionVo, int size, String userId) {
        QueryParam queryParam = buildFindSimilarQuestionParam(questionVo, size);
        if (queryParam == null) {
            return Collections.emptyList();
        }
        queryParam.setHighlight(true);
        return searchQuestionsCommon(queryParam, userId).getRecords();
    }

    // 构建搜索相似题查询参数
    private QueryParam buildFindSimilarQuestionParam(QuestionVo questionVo, int size) {
        if (questionVo == null || questionVo.getStage() == null || questionVo.getSubjectId() == null || questionVo.getType() == null) {
            return null;
        }
        QueryParam queryParam = new QueryParam();
        queryParam.setGradeLevel(questionVo.getStage());
        queryParam.setSubjectId(questionVo.getSubjectId());
        queryParam.setQuestionTypeId(questionVo.getType());
        queryParam.setPage(1);
        queryParam.setSize(size);
        queryParam.setKeyword(buildQuestionSearchContent(questionVo));
        // 要匹配答案解析
        queryParam.setMatchAnswerExplanation(true);
        // 移除自身
        if (StringUtils.isNotBlank(questionVo.getId())) {
            queryParam.setExcludeQuestionIds(Collections.singletonList(Long.parseLong(questionVo.getId())));
        }
        return queryParam;
    }

    // 将整个题目拼成搜索内容
    private String buildQuestionSearchContent(QuestionVo questionVo) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(questionVo.getTrunk())) {
            sb.append(sliceContent(stripHtml(questionVo.getTrunk()), 400));
        }
        if (questionVo.getBranches() != null) {
            for (SmallQuestionVo branch : questionVo.getBranches()) {
                if (StringUtils.isNotBlank(branch.getStem())) {
                    sb.append(sliceContent(stripHtml(branch.getStem()), 200));
                }
                if (branch.getOptions() != null) {
                    for (SmallQuestionOptionVo option : branch.getOptions()) {
                        if (StringUtils.isNotBlank(option.getContent())) {
                            sb.append(sliceContent(stripHtml(option.getContent()), 100));
                        }
                    }
                }
                if (StringUtils.isNotBlank(branch.getSolution())) {
                    sb.append(sliceContent(stripHtml(branch.getSolution()), 200));
                }
                if (StringUtils.isNotBlank(branch.getExplanation())) {
                    sb.append(sliceContent(stripHtml(branch.getExplanation()), 200));
                }
            }
        }
        return sliceContent(sb.toString(), 2000);
    }

    // 截取长度
    private String sliceContent(String content, int maxLength) {
        if (content.length() > maxLength) {
            return content.substring(0, maxLength);
        }
        return content;
    }

    // 去除html标签
    private String stripHtml(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }
        Document doc = Jsoup.parse(content);

        // 删除所有 <math> 元素
        doc.select("math").remove();

        // 遍历所有文本节点，删除其中以 $ 开头和结尾的 LaTeX 公式
        doc.traverse(new NodeVisitor() {
            @Override
            public void head(Node node, int depth) {
                if (node instanceof TextNode) {
                    TextNode textNode = (TextNode) node;
                    // 使用正则表达式删除 $...$ 之间的内容
                    String newText = textNode.getWholeText().replaceAll("\\$[^$]+\\$", "");
                    textNode.text(newText);
                }
            }

            @Override
            public void tail(Node node, int depth) {
                // 不需要在尾部处理
            }
        });

        return doc.text();
    }

    /**
     * 指定题目Id查相似题知识点
     */
    @Override
    public List<IntStrVo> findSimilarQuestionsKnowledge(String questionId, int size) {
        QuestionVo questionVo = questionBuildService.selectQuestionVo(questionId, SelectQuestionVoOption.addNothing());
        if (questionVo == null) {
            return Collections.emptyList();
        }
        return findSimilarQuestionsKnowledge(questionVo, size);
    }

    /**
     * 根据题目内容查相似题知识点
     */
    @Override
    public List<IntStrVo> findSimilarQuestionsKnowledge(QuestionVo questionVo, int size) {
        QueryParam queryParam = buildFindSimilarQuestionParam(questionVo, size);
        if (queryParam == null) {
            return Collections.emptyList();
        }
        IPage<QuestionDoc> page = questionDocService.selectQuestionDocIdHighlightsPage(queryParam);
        List<String> questionIds = page.getRecords().stream().map(QuestionDoc::getId).map(String::valueOf).collect(Collectors.toList());
        if (questionIds.isEmpty()) {
            return Collections.emptyList();
        }
        return getQuestionsKnowledgeList(questionIds);
    }

    // 查一批题目的知识点
    private List<IntStrVo> getQuestionsKnowledgeList(List<String> questionIds) {
        // 查小题
        List<SmallQuestion> smallQuestions = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                .in(SmallQuestion::getQuestionId, questionIds)
                .select(SmallQuestion::getId, SmallQuestion::getQuestionId));
        if (smallQuestions.isEmpty()) {
            return Collections.emptyList();
        }
        // 查小题知识点关联
        List<String> smallQuestionIds = smallQuestions.stream().map(SmallQuestion::getId).collect(Collectors.toList());
        List<QuestionKnowledge> questionKnowledgeList = questionKnowledgeMapper.selectList(new LambdaQueryWrapper<QuestionKnowledge>()
                .in(QuestionKnowledge::getQuestionId, smallQuestionIds));
        if (questionKnowledgeList.isEmpty()) {
            return Collections.emptyList();
        }
        // 查知识点
        Set<Integer> knowledgeIds = questionKnowledgeList.stream().map(QuestionKnowledge::getKnowledgeId).collect(Collectors.toSet());
        List<KnowledgeMain> knowledgeList = knowledgeMainMapper.selectList(new LambdaQueryWrapper<KnowledgeMain>()
                .in(KnowledgeMain::getId, knowledgeIds));

        // 按顺序加入各题的知识点
        Map<String, List<String>> questionIdSmallQuestionIdsMap = smallQuestions.stream().collect(
                Collectors.groupingBy(SmallQuestion::getQuestionId, Collectors.mapping(SmallQuestion::getId, Collectors.toList())));
        Map<String, Set<Integer>> smallQuestionIdKnowledgeIdsMap = questionKnowledgeList.stream().collect(
                Collectors.groupingBy(QuestionKnowledge::getQuestionId, Collectors.mapping(QuestionKnowledge::getKnowledgeId, Collectors.toSet())));
        Map<Integer, String> knowledgeMap = knowledgeList.stream().collect(Collectors.toMap(KnowledgeMain::getId, KnowledgeMain::getKnowledgeName));

        Map<Integer, EntryVo<IntStrVo, Integer>> knowledgeCountMap = new LinkedHashMap<>();
        for (String qId : questionIds) {
            // 该题知识点，不重复
            Set<Integer> kIds = new HashSet<>();
            List<String> sqIds = questionIdSmallQuestionIdsMap.getOrDefault(qId, new ArrayList<>());
            for (String sqId : sqIds) {
                if (smallQuestionIdKnowledgeIdsMap.containsKey(sqId)) {
                    kIds.addAll(smallQuestionIdKnowledgeIdsMap.get(sqId));
                }
            }
            for (Integer kId : kIds) {
                if (!knowledgeMap.containsKey(kId)) {
                    continue;
                }
                EntryVo<IntStrVo, Integer> entry = knowledgeCountMap.computeIfAbsent(kId, x -> new EntryVo<>(new IntStrVo(kId, knowledgeMap.get(kId)), 0));
                entry.setValue(entry.getValue() + 1);
            }
        }

        // 按知识点在各题中出现的次数排序
        return knowledgeCountMap.values().stream().sorted((a, b) -> b.getValue() - a.getValue()).map(EntryVo::getKey).collect(Collectors.toList());
    }

    /**
     * 智能组卷
     */
    @Override
    public List<QuestionVo> getIntelligentPaperQuestions(IntelligentRequest req, String userId) {
        // 构建查询参数
        FindQuestionPageParam findParam = new FindQuestionPageParam();
        // 查出前300道题
        findParam.setPage(1);
        findParam.setSize(300);
        findParam.setStage(req.getStage());
        findParam.setSubject(req.getSubject());
        if (req.getDifficulty() != null && req.getDifficulty() != 0) {
            findParam.setDifficulty(req.getDifficulty());
        }
        if (req.getRegion() != null && req.getRegion() != 0) {
            findParam.setRegion(req.getRegion().toString());
        }
        findParam.setChapterIds(req.getChapters());
        findParam.setKnowledgeIds(req.getKnowledges());
        if (!normalizeFindQuestionPageParam(findParam)) {
            return Collections.emptyList();
        }
        QueryParam queryParam = buildQueryParamCommon(findParam);

        // 分题型查
        List<String> questionIds = new ArrayList<>();
        for (ReqTopic topic : req.getTopics()) {
            queryParam.setQuestionTypeId(topic.getQuestionTypeId());
            IPage<QuestionDoc> esResult = questionDocService.selectQuestionDocIdHighlightsPage(queryParam);
            // 随机取指定数量题目
            Collections.shuffle(esResult.getRecords());
            esResult.getRecords().stream().limit(topic.getNumber())
                    .map(QuestionDoc::getId).map(String::valueOf).forEach(questionIds::add);
        }
        // 取题
        if (questionIds.isEmpty()) {
            return Collections.emptyList();
        }
        return questionBuildService.selectQuestionVos(questionIds);
    }

    /**
     * 学情组卷
     */
    @Override
    public List<QuestionVo> wrongQuesTrain(WrongQuesTrain wrongQuesTrain, String userId) {
        // 原题Id
        List<String> srcQuestionIds = wrongQuesTrain.getQuestions().stream().map(QuestionScoreRateVo::getQuestionId).collect(Collectors.toList());

        // 错题重做：直接返回原题
        if (Constants.WRONGQUESTRAIN_TYPE_REDO.equals(wrongQuesTrain.getTrainType())) {
            return questionBuildService.selectQuestionVos(srcQuestionIds);
        }

        // 错题拓展
        // 查出题目、考试科目、试卷相关变式题
        Set<String> paperIds = new HashSet<>();
        Set<String> examSubjectIds = new HashSet<>();
        for (QuestionScoreRateVo q : wrongQuesTrain.getQuestions()) {
            for (ExamSubjectQuestionScoreRateVo es : q.getExamSubjects()) {
                paperIds.add(es.getPaperId());
                examSubjectIds.add(es.getExamSubjectId());
            }
        }
        examSubjectIds.add(StringUtils.EMPTY);
        List<ExamsubjectPushQues> pushQuesList = examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .in(ExamsubjectPushQues::getPaperId, paperIds)
                .in(ExamsubjectPushQues::getExamSubjectId, examSubjectIds)
                .in(ExamsubjectPushQues::getQuesId, srcQuestionIds)
                .orderByAsc(ExamsubjectPushQues::getQuesId)
                .orderByDesc(ExamsubjectPushQues::getKnowMatchCount));
        Map<String, List<ExamsubjectPushQues>> srcQuestionidPushQuesListMap = pushQuesList.stream().collect(Collectors.groupingBy(ExamsubjectPushQues::getQuesId));

        // 对每道原题分别挑选一道变式题
        List<String> resultQuestionIds = new ArrayList<>();
        for (QuestionScoreRateVo q : wrongQuesTrain.getQuestions()) {
            // 有考试科目变式题列表
            List<ExamsubjectPushQues> examSubjectPushQuesList = new ArrayList<>();
            // 无考试科目变式题列表
            List<ExamsubjectPushQues> noExamSubjectPushQuesList = new ArrayList<>();
            List<ExamsubjectPushQues> thisQuestionPushQuesList = srcQuestionidPushQuesListMap.get(q.getQuestionId());
            if (thisQuestionPushQuesList == null) {
                continue;
            }
            for (ExamsubjectPushQues item : thisQuestionPushQuesList) {
                for (ExamSubjectQuestionScoreRateVo es : q.getExamSubjects()) {
                    if (!StringUtils.equals(item.getPaperId(), es.getPaperId())) {
                        continue;
                    }
                    if (StringUtils.equals(item.getExamSubjectId(), es.getExamSubjectId())) {
                        examSubjectPushQuesList.add(item);
                    } else if (StringUtils.equals(item.getExamSubjectId(), StringUtils.EMPTY)) {
                        noExamSubjectPushQuesList.add(item);
                    }
                }
            }

            // 先从有考试科目变式题列表中找，没找到再从无考试科目变式题列表中找
            outerLoop:
            for (List<ExamsubjectPushQues> examsubjectPushQuesList : Arrays.asList(examSubjectPushQuesList, noExamSubjectPushQuesList)) {
                // 知识点匹配个数从多到少排
                List<Integer> knowledgeMatchCountList = examsubjectPushQuesList.stream()
                        .map(ExamsubjectPushQues::getKnowMatchCount)
                        .distinct().sorted((a, b) -> b - a).collect(Collectors.toList());
                // 优先取知识点匹配个数多的，相同知识点匹配个数的题目随机取一个
                for (Integer knowledgeMatchCount : knowledgeMatchCountList) {
                    // 找出该知识点匹配个数的变式题
                    List<String> pushQuesIds = examsubjectPushQuesList.stream()
                            .filter(x -> x.getKnowMatchCount().equals(knowledgeMatchCount))
                            .map(ExamsubjectPushQues::getPushId).collect(Collectors.toList());
                    // 打乱顺序
                    Collections.shuffle(pushQuesIds);
                    // 取一道与原题及已推题不重复的
                    for (String pushQuesId : pushQuesIds) {
                        if (!srcQuestionIds.contains(pushQuesId) && !resultQuestionIds.contains(pushQuesId)) {
                            resultQuestionIds.add(pushQuesId);
                            break outerLoop;
                        }
                    }
                }
            }
        }

        if (resultQuestionIds.isEmpty()) {
            return Collections.emptyList();
        }
        return questionBuildService.selectQuestionVos(resultQuestionIds);
    }

    /**
     * 搜原题的变式题
     */
    @Override
    public List<String> findSrcQuestionVariants(PushQuesParam param) {
        QueryParam queryParam = new QueryParam();
        // 剔除原题
        if (StringUtils.isNotBlank(param.getQuestionId())) {
            queryParam.setExcludeQuestionIds(Collections.singletonList(Long.valueOf(param.getQuestionId())));
        }
        queryParam.setGradeLevel(param.getGradeLevel());
        queryParam.setSubjectId(param.getSubjectId());
        queryParam.setPage(1);
        queryParam.setSize(param.getQuestionCount());
        queryParam.setKnowledgeIds(param.getKnowledgeIds().stream().map(Integer::longValue).collect(Collectors.toList()));
        queryParam.setSortBy("knowledgeMatch");

        // 题型、难度、年级可以有多个
        List<Integer> questionTypes = buildParamListAddNoLimit(param.getQuestionTypeId(), param.isTryNoLimitQuestionType());
        // 难度优先使用得分率
        Integer difficulty = param.getDifficulty();
        if (param.getScoreRate() != null) {
            DifficultyEnum difficultyEnum = DifficultyEnum.fromScoreRate(param.getScoreRate());
            if (difficultyEnum != null) {
                difficulty = difficultyEnum.getId();
            }
        }
        List<Integer> difficulties = buildParamListAddNoLimit(difficulty, param.isTryNoLimitDifficulty());
        List<Integer> gradeIds = buildParamListAddNoLimit(param.getGradeId(), param.isTryNoLimitGrade());

        Set<Long> result = new HashSet<>();
        // 遍历多个参数查询，直到满足数量要求
        for (Integer questionType : questionTypes) {
            queryParam.setQuestionTypeId(questionType);
            for (Integer diff : difficulties) {
                queryParam.setDifficulty(diff);
                for (Integer gradeId : gradeIds) {
                    queryParam.setGradeId(gradeId);
                    IPage<QuestionDoc> questionDocPage = questionDocService.selectQuestionDocIdHighlightsPage(queryParam);
                    for (QuestionDoc record : questionDocPage.getRecords()) {
                        if (result.contains(record.getId())) {
                            continue;
                        }
                        result.add(record.getId());
                        if (result.size() >= param.getQuestionCount()) {
                            return result.stream().map(String::valueOf).collect(Collectors.toList());
                        }
                    }
                }
            }
        }
        return result.stream().map(String::valueOf).collect(Collectors.toList());
    }

    // 构建含指定参数与不限制参数的参数列表
    private <T> List<T> buildParamListAddNoLimit(T value, boolean tryNoLimit) {
        List<T> result = new ArrayList<>();
        result.add(value);
        if (value != null && tryNoLimit) {
            result.add(null);
        }
        return result;
    }
}
