package com.sure.question.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.sure.common.entity.Result;
import com.sure.common.entity.StatusCode;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.*;
import com.sure.question.feign.UserService;
import com.sure.question.mapper.*;
import com.sure.question.service.CoachStuCardService;
import com.sure.question.service.FeignService;
import com.sure.question.service.OssManager;
import com.sure.question.util.CacheUtil;
import com.sure.question.util.DesensitizedUtils;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.InfoVo;
import com.sure.question.vo.coachstucard.*;
import com.sure.question.vo.dataVo.IntIntVo;
import com.sure.question.vo.simple.SimpleParentVo;
import com.sure.question.vo.student.StudentInfoVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CoachStuCardServiceImpl extends ServiceImpl<CoachStuCardMapper, CoachStuCard> implements CoachStuCardService {
    private final CoachBookMapper coachBookMapper;
    private final StuCardLibMapper stuCardLibMapper;
    private final CoachStuCardMapper coachStuCardMapper;
    private final UserService userService;
    private final SchoolCoachBookMapper schoolCoachBookMapper;
    private final OssManager ossManager;
    private final SubjectMapper subjectMapper;
    private final FeignService feignService;

    @Value("${aliyun.ossTkImg.coachBookDefaultCover}")
    private String coachBookDefaultCover;

    @Override
    public void generateCardBatch(Integer coachBookId, Integer total, String userId) {
        // 查出教辅
        CoachBook book = coachBookMapper.selectById(coachBookId);
        if (book == null) {
            throw new ApiException("找不到该教辅");
        }

        String prefix = parseSubjectTag(book.getSubjectId());
        List<StuCardLib> stuCardLibs = stuCardLibMapper.selectList(new LambdaQueryWrapper<StuCardLib>().orderByAsc(StuCardLib::getId).last("LIMIT " + total));
        if (stuCardLibs.size() < total) {
            throw new ApiException("可预分配的卡号不足");
        }
        int maxId = stuCardLibs.get(stuCardLibs.size() - 1).getId();
        List<CoachStuCard> coachStuCardList = new LinkedList<>();
        Date now = new Date();
        for (StuCardLib item : stuCardLibs) {
            coachStuCardList.add(CoachStuCard.builder()
                    .subjectId(book.getSubjectId())
                    .coachBookId(coachBookId)
                    .cardNo(prefix.concat(item.getCardNo()))
                    .createBy(userId)
                    .createTime(now)
                    .build());
        }
        this.saveBatch(coachStuCardList);
        stuCardLibMapper.delete(new LambdaQueryWrapper<StuCardLib>().le(StuCardLib::getId, maxId));
    }

    @Override
    public List<CoachStuCard> listCardByNos(List<String> cardNos) {
        return coachStuCardMapper.selectList(new LambdaQueryWrapper<CoachStuCard>().in(CoachStuCard::getCardNo, cardNos));
    }

    @Override
    public void activeCardBatch(String parentId, String studentId, List<String> cardNos) {
        log.info("【家长激活教辅激活卡】parentId={}, studentId={}, cardNos={}", parentId, studentId, StringUtils.join(cardNos, ", "));
        StudentInfoVo studentInfo = userService.getStudentInfo(studentId);
        coachStuCardMapper.update(null, new LambdaUpdateWrapper<CoachStuCard>()
                .in(CoachStuCard::getCardNo, cardNos)
                .set(CoachStuCard::getParentid, parentId)
                .set(CoachStuCard::getStudentId, studentId)
                .set(CoachStuCard::getSchoolId, studentInfo.getSchoolId())
                .set(CoachStuCard::getStatus, 1)
                .set(CoachStuCard::getActiveTime, new Date())
        );
    }

    @Override
    public void addCustomCardBatch(List<CustomCardVo> customCardVos) {
        log.info("【内定学校家长激活教辅】{}", JSON.toJSON(customCardVos));
        coachStuCardMapper.addCustomCardBatch(customCardVos);
    }

    @Override
    public List<CoachBook> listCoachBook(String schoolId, String studentId, Integer semesterId, Integer term) {
        List<Integer> bookIds = coachStuCardMapper.lstStudentActivedBookId(schoolId, studentId, semesterId, term);
        if (bookIds.isEmpty()) {
            return Collections.emptyList();
        }

        List<CoachBook> coachBooks = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                .in(CoachBook::getId, bookIds)
                .select(CoachBook::getId,
                        CoachBook::getBookName,
                        CoachBook::getGradeLevel,
                        CoachBook::getGradeId,
                        CoachBook::getTerm,
                        CoachBook::getSubjectId,
                        CoachBook::getYear,
                        CoachBook::getPressName,
                        CoachBook::getCover));
        coachBooks.sort(Comparator.comparing(CoachBook::getGradeId).reversed().thenComparing(CoachBook::getTerm).reversed().thenComparing(CoachBook::getSubjectId));
        for (CoachBook book : coachBooks) {
            book.setSubjectName(CacheUtil.getSubjectName(book.getSubjectId()));
            book.setGradeName(CacheUtil.getGradeName(book.getGradeId()));
            if (book.getCover() != null) {
                book.setCover(ossManager.getPublicPathTK(book.getCover()));
            } else {
                // 没有封面图片，使用默认图片
                book.setCover(coachBookDefaultCover);
            }
        }
        return coachBooks;
    }


    @Override
    public List<CardCoachBookInfoVo> cardCoachBookInfos(String cardNos, String parentId) {
        List<String> cardNoList = new ArrayList<>(Arrays.asList(cardNos.split("-")));

        // 特殊处理 宜宾市第五初级中学校 的激活码
        if (cardNoList.removeIf(x -> StringUtils.equalsIgnoreCase(x, "YBWZ"))) {
            DateTime beginTime = DateUtil.offsetSecond(new Date(), -10);
            List<String> nos = coachStuCardMapper.selectObjs(new LambdaQueryWrapper<CoachStuCard>()
                    .eq(CoachStuCard::getParentid, parentId)
                    .ge(CoachStuCard::getCreateTime, beginTime)
                    .likeRight(CoachStuCard::getCardNo, "W")
                    .select(CoachStuCard::getCardNo)).stream().map(String::valueOf).collect(Collectors.toList());
            cardNoList.addAll(nos);
        }

        if (cardNoList.isEmpty()) {
            return Collections.emptyList();
        }
        List<CoachStuCard> coachStuCards = coachStuCardMapper.selectList(new LambdaQueryWrapper<CoachStuCard>().in(CoachStuCard::getCardNo, cardNoList));
        if (coachStuCards.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Integer, CoachBook> coachBookIdCoachBookMap = coachBookMapper.selectBatchIds(coachStuCards.stream().map(CoachStuCard::getCoachBookId).distinct().collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(CoachBook::getId, Function.identity()));

        Map<String, StudentInfoVo> studentIdStudentInfoVoMap = new HashMap<>();
        coachStuCards.stream().map(CoachStuCard::getStudentId).collect(Collectors.toSet()).forEach(studentId -> studentIdStudentInfoVoMap.put(studentId, userService.getStudentInfo(studentId)));

        List<String> schoolIds = coachStuCards.stream().map(CoachStuCard::getSchoolId).distinct().collect(Collectors.toList());
        Map<String, String> schoolIdNameMap = feignService.getSchoolIdNameMap(schoolIds);

        List<CardCoachBookInfoVo> voList = new LinkedList<>();
        for (CoachStuCard coachStuCard : coachStuCards) {
            StudentInfoVo studentInfoVo = studentIdStudentInfoVoMap.get(coachStuCard.getStudentId());
            CardCoachBookInfoVo vo = new CardCoachBookInfoVo();
            vo.setStudentId(coachStuCard.getStudentId());
            vo.setStudentName(studentInfoVo.getRealName());
            vo.setCardNo(coachStuCard.getCardNo());
            vo.setCoachBookInfo(assembleCoachBookInfo(coachBookIdCoachBookMap.get(coachStuCard.getCoachBookId())));
            vo.setSchoolId(coachStuCard.getSchoolId());
            vo.setSchoolName(schoolIdNameMap.get(coachStuCard.getSchoolId()));
            vo.setClassId(studentInfoVo.getClassId());
            vo.setClassName(studentInfoVo.getClassName());
            voList.add(vo);
        }

        // 根据coachBookInfo中的orderId排序
        voList.sort(Comparator.comparingInt(o -> o.getCoachBookInfo().getOrderId()));

        return voList;
    }

    @Override
    public ActiveVo getActiveStatus(String studentId) {
        ActiveVo vo = ActiveVo.builder()
                .status(0)
                .subjects(new LinkedList<>())
                .build();
        StudentInfoVo studentInfo = userService.getStudentInfo(studentId);
        if (studentInfo == null || studentInfo.getSchoolCurrentSemester() == null || studentInfo.getSchoolCurrentTerm() == null) {
            return vo;
        }

        // 查学生已激活的教辅
        Set<Integer> coachBookIds = coachStuCardMapper.selectList(new LambdaQueryWrapper<CoachStuCard>()
                        .eq(CoachStuCard::getStudentId, studentId)
                        .select(CoachStuCard::getCoachBookId))
                .stream().map(CoachStuCard::getCoachBookId).collect(Collectors.toSet());

        if (coachBookIds.isEmpty()) {
            return vo;
        }

        List<SchoolCoachBook> coachBooks = schoolCoachBookMapper.selectList(new LambdaQueryWrapper<SchoolCoachBook>()
                .eq(SchoolCoachBook::getSchoolId, studentInfo.getSchoolId())
                .in(SchoolCoachBook::getCoachBookId, coachBookIds)
                .select(SchoolCoachBook::getSemesterId, SchoolCoachBook::getTerm, SchoolCoachBook::getSubjectId));
        if (coachBooks.isEmpty()) {
            return vo;
        }

        // 看有没有当前学年学期的教辅
        List<Integer> subjectIds = coachBooks.stream()
                .filter(x -> studentInfo.getSchoolCurrentSemester().equals(x.getSemesterId()) && studentInfo.getSchoolCurrentTerm().equals(x.getTerm()))
                .map(SchoolCoachBook::getSubjectId).collect(Collectors.toList());
        if (!subjectIds.isEmpty()) {
            // 有，状态为已激活
            vo.setStatus(1);
            List<String> subjects = subjectMapper.selectList(new LambdaQueryWrapper<Subject>()
                            .in(Subject::getId, subjectIds)
                            .orderByAsc(Subject::getOrderId).select(Subject::getSubjectName))
                    .stream().map(Subject::getSubjectName).collect(Collectors.toList());
            vo.setSubjects(subjects);
        } else {
            // 当前学期没激活，之前有，状态为已过期
            vo.setStatus(2);
        }
        return vo;
    }

    @Override
    public List<StudentSubjectVo> listActiveStudentIds(String schoolId, Integer semesterId, Integer term, Integer subjectId, List<String> studentIds) {
        return coachStuCardMapper.queryActiveStudentIds(schoolId, semesterId, term, subjectId, studentIds);
    }

    @Override
    public PageInfo<CardInfoVo> listCard(Integer coachBookId, String coachBookName, Integer subjectId, Integer gradeLevel, Integer gradeId,
                                         Integer semesterId, Integer term, String cardNo, String studentName, String schoolId, Long regionId,
                                         Integer status, String beginTime, String endTime, Integer pageNum, Integer pageSize) {
        List<String> schoolIds = new ArrayList<>();
        if (!checkoutSchoolIdsByRegionIdAndSchoolId(regionId, schoolId, schoolIds)) {
            return new PageInfo<>();
        }

        return listCardCommon(coachBookId, coachBookName, subjectId, gradeLevel, gradeId, semesterId, term, cardNo, studentName, status, beginTime, endTime, pageNum, pageSize, schoolIds);
    }

    @Override
    public PageInfo<CardInfoVo> listCardByOrg(Integer coachBookId, String coachBookName, Integer subjectId, Integer gradeLevel,
                                              Integer gradeId, Integer semesterId, Integer term, String cardNo, String studentName, String schoolId,
                                              Integer status, String beginTime, String endTime, Integer pageNum, Integer pageSize, TokenUserVo userVo) {
        Collection<String> schoolIds = checkoutOrgSchoolIds(userVo.getSchoolId(), schoolId);
        return listCardCommon(coachBookId, coachBookName, subjectId, gradeLevel, gradeId, semesterId, term, cardNo, studentName, status, beginTime, endTime, pageNum, pageSize, schoolIds);
    }

    private PageInfo<CardInfoVo> listCardCommon(Integer coachBookId, String coachBookName, Integer subjectId, Integer gradeLevel,
                                                Integer gradeId, Integer semesterId, Integer term, String cardNo, String studentName,
                                                Integer status, String beginTime, String endTime, Integer pageNum,
                                                Integer pageSize, Collection<String> schoolIds) {
        Date beginDate = null;
        if (StringUtils.isNotBlank(beginTime)) {
            beginDate = DateUtil.parseDate(beginTime);
        }
        Date endDate = null;
        if (StringUtils.isNotBlank(endTime)) {
            endDate = DateUtil.parseDate(endTime);
        }

        Set<String> studentIds = new HashSet<>();
        if (StringUtils.isNotBlank(studentName) && schoolIds.size() == 1) {
            // 限制在单所学校内查询学生
            Result result = userService.listStudentIdsBySchoolIdAndStudentNameKey(schoolIds.stream().findFirst().get(), studentName.trim());
            if (!Objects.equals(result.getCode(), StatusCode.OK)) {
                log.error("【教辅激活卡列表】获取学生ID列表失败，学校ID：{}，学生姓名：{}，错误信息：{}", JSONUtil.toJsonStr(schoolIds), studentName, result.getMessage());
                throw new ApiException("服务异常，请稍后重试");
            }
            studentIds.addAll(JSONUtil.toList(JSONUtil.toJsonStr(result.getData()), String.class));
            if (studentIds.isEmpty()) {
                // 根据名字查不到学生，直接返回空
                return new PageInfo<>();
            }
        }

        PageMethod.startPage(pageNum, pageSize);
        List<CardInfoVo> cardInfoVos = coachStuCardMapper.listCard(coachBookId, coachBookName, subjectId, gradeLevel, gradeId, semesterId, term, cardNo, status, beginDate, endDate, schoolIds, studentIds);
        PageInfo<CardInfoVo> pageInfo = new PageInfo<>(cardInfoVos);
        if (pageInfo.hasContent()) {
            // 补充教辅信息
            List<CardInfoVo> list = pageInfo.getList();
            Set<Integer> coachBookIds = list.stream().map(CardInfoVo::getCoachBookId).collect(Collectors.toSet());
            Map<Integer, CoachBook> coachBookIdCoachBookMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                            .in(CoachBook::getId, coachBookIds))
                    .stream().collect(Collectors.toMap(CoachBook::getId, Function.identity()));
            for (CoachBook coachBook : coachBookIdCoachBookMap.values()) {
                fixCoachBookInfo(coachBook);
            }
            list.forEach(x -> x.setCoachBook(coachBookIdCoachBookMap.get(x.getCoachBookId())));

            // 补充学生信息
            for (CardInfoVo vo : list) {
                StudentInfoVo studentInfo = userService.getStudentInfo(vo.getStudentId());
                vo.setStudentName(studentInfo.getRealName());
                vo.setSchoolName(studentInfo.getSchoolName());
            }

            // 补充家长信息
            List<String> parentIds = list.stream().map(CardInfoVo::getParentId).distinct().collect(Collectors.toList());
            Result result = userService.listParentInfo(parentIds);
            if (!Objects.equals(result.getCode(), StatusCode.OK)) {
                log.error("【教辅激活卡列表】获取家长信息失败，parentIds={}", parentIds);
                throw new ApiException("服务异常，请稍后重试");
            }
            List<SimpleParentVo> parentVoList = JSONUtil.toList(JSONUtil.toJsonStr(result.getData()), SimpleParentVo.class);
            Map<String, SimpleParentVo> parentIdParentVoMap = parentVoList.stream().collect(Collectors.toMap(SimpleParentVo::getId, Function.identity()));
            for (CardInfoVo vo : list) {
                SimpleParentVo simpleParentVo = parentIdParentVoMap.get(vo.getParentId());
                if (simpleParentVo != null) {
                    vo.setParentName(simpleParentVo.getName());
                    vo.setParentMobile(DesensitizedUtils.desValue(simpleParentVo.getMobile(), 3, 4, "*"));
                }
            }
        }
        return pageInfo;
    }

    @Override
    public List<ActiveStatVo> activeStat(Integer semesterId, Integer term, Integer subjectId, Integer gradeLevel,
                                         Integer gradeId, String coachBookName, String schoolId, Long regionId) {
        List<String> schoolIds = new ArrayList<>();
        if (!checkoutSchoolIdsByRegionIdAndSchoolId(regionId, schoolId, schoolIds)) {
            return Collections.emptyList();
        }

        return bookActiveStatCommon(semesterId, term, subjectId, gradeLevel, gradeId, coachBookName, schoolIds);
    }

    @Override
    public List<ActiveStatVo> activeStatByOrg(Integer semesterId, Integer term, Integer subjectId, Integer gradeLevel,
                                              Integer gradeId, String coachBookName, String schoolId, TokenUserVo userVo) {
        Collection<String> schoolIds = checkoutOrgSchoolIds(userVo.getSchoolId(), schoolId);
        return bookActiveStatCommon(semesterId, term, subjectId, gradeLevel, gradeId, coachBookName, schoolIds);
    }

    private List<ActiveStatVo> bookActiveStatCommon(Integer semesterId, Integer term, Integer subjectId, Integer gradeLevel,
                                                    Integer gradeId, String coachBookName, Collection<String> schoolIds) {
        // 查教辅
        List<ActiveStatVo> result = coachStuCardMapper.listCoachBookActiveStat(schoolIds, semesterId, term, gradeId, subjectId, StringUtils.trim(coachBookName));
        if (result.isEmpty()) {
            return Collections.emptyList();
        }

        // 补教辅信息
        for (ActiveStatVo item : result) {
            fixCoachBookInfo(item.getCoachBook());
        }

        // 补教辅激活码总量
        if (schoolIds.isEmpty()) {
            List<Integer> bookIds = result.stream().map(x -> x.getCoachBook().getId()).collect(Collectors.toList());
            Map<Integer, Integer> bookIdCountMap = coachStuCardMapper.selectGroupByBookIdCount(bookIds).stream().collect(Collectors.toMap(IntIntVo::getId, IntIntVo::getValue));
            for (ActiveStatVo vo : result) {
                vo.setTotalCount(bookIdCountMap.getOrDefault(vo.getCoachBook().getId(), 0));
            }
        }

        // 排序
        result.sort(Comparator.comparing((ActiveStatVo vo) -> vo.getCoachBook().getGradeId(), Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing((ActiveStatVo vo) -> vo.getCoachBook().getSubjectId(), Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing((ActiveStatVo vo) -> vo.getCoachBook().getOrderId(), Comparator.nullsLast(Comparator.naturalOrder())));

        return result;
    }

    @Override
    public List<CoachSchoolStatVo> schoolStat(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, String schoolId, Long regionId) {
        List<String> schoolIds = new ArrayList<>();
        if (!checkoutSchoolIdsByRegionIdAndSchoolId(regionId, schoolId, schoolIds)) {
            return Collections.emptyList();
        }
        return schoolActiveCardStatCommon(semesterId, term, gradeId, subjectId, coachBookName, schoolIds);
    }

    @Override
    public List<CoachSchoolStatVo> schoolStatByOrg(Integer semesterId, Integer term, Integer gradeId, Integer subjectId,
                                                   String coachBookName, String schoolId, TokenUserVo userVo) {
        Collection<String> schoolIds = checkoutOrgSchoolIds(userVo.getSchoolId(), schoolId);
        return schoolActiveCardStatCommon(semesterId, term, gradeId, subjectId, coachBookName, schoolIds);
    }

    /**
     * 教辅学校激活码统计
     */
    private List<CoachSchoolStatVo> schoolActiveCardStatCommon(Integer semesterId, Integer term, Integer gradeId, Integer subjectId, String coachBookName, Collection<String> schoolIds) {
        // 查学校教辅
        List<CoachSchoolStatVo> result = schoolCoachBookMapper.listCoachSchoolActiveStat(schoolIds, semesterId, term, gradeId, subjectId, StringUtils.trim(coachBookName));
        if (result.isEmpty()) {
            return Collections.emptyList();
        }

        // 补教辅信息
        for (CoachSchoolStatVo item : result) {
            fixCoachBookInfo(item.getCoachBook());
        }

        // 补学校信息
        List<String> schlIds = result.stream().map(CoachSchoolStatVo::getSchoolId).distinct().collect(Collectors.toList());
        Map<String, String> schoolIdSchoolNameMap = feignService.getSchoolIdNameMap(schlIds);
        for (CoachSchoolStatVo item : result) {
            item.setSchoolName(schoolIdSchoolNameMap.get(item.getSchoolId()));
        }

        // 排序
        result.sort(Comparator.comparing(CoachSchoolStatVo::getLatestActiveTime).reversed());
        return result;
    }


    @Override
    public CountStatVo countStat(Integer semesterId, Integer term, Integer subjectId, Integer gradeId, String coachBookName, String schoolId, Long regionId) {
        List<String> schoolIds = new ArrayList<>();
        if (!checkoutSchoolIdsByRegionIdAndSchoolId(regionId, schoolId, schoolIds)) {
            return CountStatVo.builder().bookCount(0).cardCount(0).schoolCount(0).build();
        }

        return countStatCommon(semesterId, term, subjectId, gradeId, coachBookName, schoolIds);
    }

    @Override
    public CountStatVo countStatByOrg(Integer semesterId, Integer term, Integer subjectId, Integer gradeId, String coachBookName, String schoolId, TokenUserVo userVo) {
        Collection<String> schoolIds = checkoutOrgSchoolIds(userVo.getSchoolId(), schoolId);
        return countStatCommon(semesterId, term, subjectId, gradeId, coachBookName, schoolIds);
    }

    /**
     * 学情分析资源包激活数量统计
     */
    private CountStatVo countStatCommon(Integer semesterId, Integer term, Integer subjectId, Integer gradeId, String coachBookName, Collection<String> schoolIds) {
        return coachStuCardMapper.countStatCommon(schoolIds, semesterId, term, gradeId, subjectId, StringUtils.trim(coachBookName));
    }

    private Collection<String> checkoutOrgSchoolIds(String orgSchoolId, String oneChildSchoolId) {
        Result result = userService.getSubordinateSchoolIds(orgSchoolId);
        if (!Objects.equals(result.getCode(), StatusCode.OK)) {
            log.error("【获取学校列表】获取可见学校列表失败，学校ID：{}，错误信息：{}", orgSchoolId, result.getMessage());
            throw new ApiException("获取学校列表失败");
        }
        Set<String> accessibleSchoolIds = new HashSet<>(JSONUtil.toList(JSONUtil.toJsonStr(result.getData()), String.class));

        // 稽查指定学校
        if (!accessibleSchoolIds.isEmpty() && StringUtils.isNotBlank(oneChildSchoolId)) {
            if (!accessibleSchoolIds.contains(oneChildSchoolId)) {
                throw new ApiException("非法 schoolId");
            }
            accessibleSchoolIds.clear();
            accessibleSchoolIds.add(oneChildSchoolId);
        }

        return accessibleSchoolIds;
    }

    @Override
    public boolean checkoutSchoolIdsByRegionIdAndSchoolId(Long regionId, String schoolId, List<String> refSchoolIds) {
        if (regionId == null) {
            if (schoolId != null) {
                refSchoolIds.add(schoolId);
            }
        } else {
            List<String> schoolIds = feignService.checkoutSchoolIdsByRegionId(regionId, null, null);
            if (schoolIds.isEmpty() || (StringUtils.isNotBlank(schoolId) && !schoolIds.contains(schoolId))) {
                return Boolean.FALSE;
            }
            refSchoolIds.addAll(schoolIds);
        }
        return Boolean.TRUE;
    }

    private void fixCoachBookInfo(CoachBook book) {
        book.setSubjectName(CacheUtil.getSubjectName(book.getSubjectId()));
        book.setGradeName(CacheUtil.getGradeName(book.getGradeId()));
        if (book.getCover() != null) {
            book.setCover(ossManager.getPublicPathTK(book.getCover()));
        } else {
            // 没有封面图片，使用默认图片
            book.setCover(coachBookDefaultCover);
        }
        book.setCreateBy(null);
        book.setCreateTime(null);
        book.setUpdateTime(null);
    }

    private InfoVo assembleCoachBookInfo(CoachBook coachBook) {
        return InfoVo.builder()
                .id(coachBook.getId())
                .gradeLevel(coachBook.getGradeLevel())
                .id(coachBook.getId())
                .gradeLevel(coachBook.getGradeLevel())
                .subjectId(coachBook.getSubjectId())
                .gradeId(coachBook.getGradeId())
                .term(coachBook.getTerm())
                .year(coachBook.getYear())
                .bookName(coachBook.getBookName())
                .pressName(coachBook.getPressName())
                .createTime(coachBook.getCreateTime())
                .orderId(coachBook.getOrderId())
                .build();
    }

    private String parseSubjectTag(Integer subjectId) {
        switch (subjectId) {
            case 2:
                // 数学
                return "A";
            case 3:
                // 英语
                return "B";
            case 7:
                // 物理
                return "C";
            case 8:
                // 化学
                return "D";
            case 9:
                // 生物
                return "E";
            default:
                throw new ApiException("不支持该科目");
        }
    }
}
