package com.sure.question.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.common.Constants;
import com.sure.question.entity.Listening;
import com.sure.question.enums.ListeningAudioSourceEnum;
import com.sure.question.enums.ListeningVoiceGenderEnum;
import com.sure.question.mapper.ListeningMapper;
import com.sure.question.service.ListeningService;
import com.sure.question.service.OssManager;
import com.sure.question.service.TtsManager;
import com.sure.question.vo.question.QuestionListeningVo;
import com.sure.question.vo.question.QuestionVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ListeningServiceImpl implements ListeningService {
    @Resource
    private ListeningMapper listeningMapper;
    @Resource
    private OssManager ossManager;
    @Resource
    private TtsManager ttsManager;
    @Value("${tempPath.listeningTts}")
    private String listeningTtsTempSaveDirectory;
    @Value("${aliyun.ossTkImg.prefix.listening}")
    private String listeningPrefix;

    /**
     * 创建听力
     */
    private Listening createListening(QuestionListeningVo listeningVo) {
        listeningVo.checkThrowEx();

        Listening listening = new Listening();
        listening.setQuestionId(listeningVo.getQuestionId());
        listening.setSmallQuestionId(listeningVo.getSmallQuestionId());
        listening.setMaterial(listeningVo.getMaterial());
        listening.setRepeatCount(listeningVo.getRepeatCount());
        listening.setAudioSource(listeningVo.getAudioSource());
        listening.setIsDel(false);

        if (listeningVo.getAudioSource().equals(ListeningAudioSourceEnum.UPLOAD.getCode())) {
            listening.setAudioUrl(uploadListeningAudioFile(listeningVo));
        } else if (listeningVo.getAudioSource().equals(ListeningAudioSourceEnum.TTS.getCode())) {
            listening.setAudioUrl(generateListeningAudio(listeningVo));
            listening.setSpeechRate(listeningVo.getSpeechRate());
            listening.setVoiceGender(listeningVo.getVoiceGender());
        }

        listening.setCreateTime(new Date());
        return listening;
    }

    /**
     * 上传听力文件
     */
    private String uploadListeningAudioFile(QuestionListeningVo listeningVo) {
        String fileName = getRandomListeningFileName();
        byte[] audioFileBytes = Base64.getDecoder().decode(listeningVo.getAudioFileBase64());
        ossManager.uploadTK(fileName, audioFileBytes);
        return ossManager.getPublicPathTK(fileName);
    }

    private String getRandomListeningFileName() {
        return listeningPrefix + RandomUtil.randomString(32) + ".mp3";
    }

    /**
     * 合成语音
     */
    private String generateListeningAudio(QuestionListeningVo listeningVo) {
        String listeningAudioPath = generateListeningAudioFile(listeningVo);
        String fileName = getRandomListeningFileName();
        ossManager.uploadTK(fileName, listeningAudioPath);
        return ossManager.getPublicPathTK(fileName);
    }

    /**
     * 合成语音，返回本地文件路径
     */
    private String generateListeningAudioFile(QuestionListeningVo listeningVo) {
        // 删除不可见字符
        String material = listeningVo.getMaterial().trim().replaceAll("\u200B", "");

        // 指定一种声音
        if (!ListeningVoiceGenderEnum.ACCORDING_TO_TEXT.getCode().equals(listeningVo.getVoiceGender())) {
            return processListening(material, listeningVo.getVoiceGender(), listeningVo.getSpeechRate());
        }

        // 有男女两种声音，如对话
        // 需将材料拆分成段落，从每段开头提取性别，分别合成语音，最后合并各段语音
        String[] paragraphs = StringUtils.split(material, "\n");
        if (paragraphs.length == 0) {
            throw new ApiException("听力材料为空");
        }
        Pattern pattern = Pattern.compile("^\\s*([mw])\\s*[:：]\\s*(.+)", Pattern.CASE_INSENSITIVE);
        List<String> mp3Files = new ArrayList<>(paragraphs.length);
        for (String paragraph : paragraphs) {
            Matcher m = pattern.matcher(paragraph);
            if (!m.find()) {
                throw new ApiException("无法从听力材料判断声音性别，请检查每一段是否以M:或者W:开头");
            }
            String voiceGender = ListeningVoiceGenderEnum.MAN.getCode();
            if (ListeningVoiceGenderEnum.WOMAN.getCode().equalsIgnoreCase(m.group(1))) {
                voiceGender = ListeningVoiceGenderEnum.WOMAN.getCode();
            }
            String text = m.group(2);
            // 设置停顿
            text = "<speak>" + text + "<break time=\"500ms\"/></speak>";
            mp3Files.add(processListening(text, voiceGender, listeningVo.getSpeechRate()));
        }
        if (mp3Files.size() == 1) {
            return mp3Files.get(0);
        }
        return mergeMp3(mp3Files);
    }

    /**
     * 根据参数执行合成
     */
    private String processListening(String text, String voiceGender, int wordsPerMinute) {
        String voice = this.getTtsVoice(voiceGender);
        int speechRate = this.getTtsSpeechRate(wordsPerMinute);
        return ttsManager.process(text, voice, speechRate);
    }

    /**
     * 根据声音性别设定语音合成发音人
     */
    private String getTtsVoice(String voiceGender) {
        if (ListeningVoiceGenderEnum.WOMAN.getCode().equals(voiceGender)) {
            // 美音女声
            return "abby";
        } else {
            // 美音男声
            return "andy";
        }
    }

    /**
     * 将每分钟单词数转换为语音合成语速
     */
    private int getTtsSpeechRate(int wordsPerMinute) {
        double defaultWordsPerMinute = 200;
        double times = wordsPerMinute / defaultWordsPerMinute;
        int speechRate;
        if (times < 1) {
            speechRate = (int) ((1 - 1 / times) / 0.002);
        } else {
            speechRate = (int) ((1 - 1 / times) / 0.001);
        }
        return Math.min(500, Math.max(-500, speechRate));
    }

    /**
     * 合并MP3
     */
    private String mergeMp3(List<String> mp3Files) {
        String mergeMp3FilePath = listeningTtsTempSaveDirectory + RandomUtil.randomString(32) + ".mp3";
        try (FileOutputStream fileOutputStream = new FileOutputStream(mergeMp3FilePath)) {
            for (String mp3FilePath : mp3Files) {
                try (FileInputStream fileInputStream = new FileInputStream(mp3FilePath)) {
                    byte[] buffer = new byte[8192];
                    int length;
                    while ((length = fileInputStream.read(buffer)) != -1) {
                        fileOutputStream.write(buffer, 0, length);
                    }
                }
            }
        } catch (IOException e) {
            log.error("复制MP3文件失败: \n{}", ExceptionUtils.getStackTrace(e));
            throw new ApiException("合并多段语音失败");
        }
        return mergeMp3FilePath;
    }


    /**
     * 查询题目听力（多道题）
     */
    @Override
    public Map<String, List<QuestionListeningVo>> getQuestionsListeningList(Collection<String> questionIds) {
        if (questionIds.isEmpty()) {
            return new HashMap<>();
        }
        return listeningMapper.selectList(new LambdaQueryWrapper<Listening>()
                        .in(Listening::getQuestionId, questionIds)
                        .eq(Listening::getIsDel, false))
                .stream().map(ListeningServiceImpl::buildListeningVo)
                .collect(Collectors.groupingBy(QuestionListeningVo::getQuestionId));
    }

    private static QuestionListeningVo buildListeningVo(Listening listening) {
        QuestionListeningVo vo = new QuestionListeningVo();
        vo.setId(listening.getId().toString());
        vo.setQuestionId(listening.getQuestionId());
        vo.setSmallQuestionId(listening.getSmallQuestionId());
        vo.setMaterial(listening.getMaterial());
        vo.setAudioUrl(listening.getAudioUrl());
        vo.setAudioSource(listening.getAudioSource());
        vo.setRepeatCount(listening.getRepeatCount());
        vo.setSpeechRate(listening.getSpeechRate());
        vo.setVoiceGender(listening.getVoiceGender());
        return vo;
    }

    /**
     * 保存题目听力
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveQuestionListening(QuestionVo questionVo) {
        // 只有英语有听力
        if (!Objects.equals(Constants.ENGLISH_SUBJECT_ID, questionVo.getSubjectId())) {
            return;
        }

        List<Listening> dbList = listeningMapper.selectList(new LambdaQueryWrapper<Listening>()
                                .eq(Listening::getQuestionId, Long.valueOf(questionVo.getId()))
                                .eq(Listening::getIsDel, false));
        List<Listening> addList = new ArrayList<>();
        List<Listening> deleteList = new ArrayList<>();
        List<Listening> updateList = new ArrayList<>();

        compareListening(questionVo, dbList, addList, deleteList, updateList, new ArrayList<>());

        saveAdd(addList);
        saveUpdate(updateList);
        saveDelete(deleteList);

        updateQuestionVoListeningFromDB(questionVo);
    }

    /**
     * 从主库中查出听力更新到题目中
     */
    private void updateQuestionVoListeningFromDB(QuestionVo questionVo) {
        List<QuestionListeningVo> newListening = listeningMapper.selectQuestionListeningForceMaster(questionVo.getId())
                .stream().map(ListeningServiceImpl::buildListeningVo).collect(Collectors.toList());
        questionVo.setListening(newListening);
    }

    /**
     * 从原题复制听力后再保存到新题上
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyFromOldQuestionAndSaveNewQuestionListening(QuestionVo questionVo, String oldQuestionId) {
        // 只有英语有听力
        if (!Objects.equals(Constants.ENGLISH_SUBJECT_ID, questionVo.getSubjectId())) {
            return;
        }

        List<Listening> oldQuestionListeningList = listeningMapper.selectList(new LambdaQueryWrapper<Listening>()
                .eq(Listening::getQuestionId, Long.valueOf(oldQuestionId))
                .eq(Listening::getIsDel, false));

        List<Listening> addList = new ArrayList<>();
        List<Listening> updateList = new ArrayList<>();

        compareListening(questionVo, oldQuestionListeningList, addList, new ArrayList<>(), updateList, new ArrayList<>());

        // 更新的也需插入
        addList.addAll(updateList);
        addList.forEach(listening -> listening.setId(null));
        saveAdd(addList);

        updateQuestionVoListeningFromDB(questionVo);
    }


    /**
     * 比较传入听力与现有听力
     */
    private void compareListening(QuestionVo questionVo,
                                  List<Listening> dbList,
                                  List<Listening> addList,
                                  List<Listening> deleteList,
                                  List<Listening> updateList,
                                  List<Listening> noChangeList) {
        // 现有听力列表
        List<Listening> currentListeningList = new ArrayList<>(dbList);

        List<QuestionListeningVo> listeningVos = ObjectUtils.defaultIfNull(questionVo.getListening(), new ArrayList<>());
        for (QuestionListeningVo listeningVo : listeningVos) {
            // 找音频不变的
            Listening current = currentListeningList.stream().filter(listening -> {
                // 音频来源相同
                if (!Objects.equals(listening.getAudioSource(), listeningVo.getAudioSource())) {
                    return false;
                }
                // 材料相同
                if (!StringUtils.equals(listening.getMaterial(), listeningVo.getMaterial())) {
                    return false;
                }
                // 语音合成语速或性别相同
                if (ListeningAudioSourceEnum.TTS.getCode().equals(listeningVo.getAudioSource())) {
                    return StringUtils.equals(listening.getVoiceGender(), listeningVo.getVoiceGender())
                            && Objects.equals(listening.getSpeechRate(), listeningVo.getSpeechRate());
                }
                // 没有重新上传音频文件
                else if (ListeningAudioSourceEnum.UPLOAD.getCode().equals(listeningVo.getAudioSource())) {
                    return StringUtils.isBlank(listeningVo.getAudioFileBase64());
                } else {
                    return false;
                }
            }).findFirst().orElse(null);

            // 找不到则新增
            if (current == null) {
                addList.add(createListening(listeningVo));
            } else {
                // 从现有列表中移除
                currentListeningList.remove(current);

                // 比较重复次数、题目Id、小题Id，一致则保持不变，否则更新
                if (Objects.equals(listeningVo.getRepeatCount(), current.getRepeatCount())
                        && Objects.equals(listeningVo.getQuestionId(), current.getQuestionId())
                        && Objects.equals(listeningVo.getSmallQuestionId(), current.getSmallQuestionId())) {
                    noChangeList.add(current);
                } else {
                    current.setRepeatCount(listeningVo.getRepeatCount());
                    current.setQuestionId(listeningVo.getQuestionId());
                    current.setSmallQuestionId(listeningVo.getSmallQuestionId());
                    updateList.add(current);
                }
            }
        }

        // 现有列表中多余的删除
        deleteList.addAll(currentListeningList);
    }

    private void saveAdd(List<Listening> addList) {
        addList.forEach(listeningMapper::insert);
    }

    private void saveUpdate(List<Listening> updateRepeatCountList) {
        updateRepeatCountList.forEach(listeningMapper::updateById);
    }

    private void saveDelete(List<Listening> deleteList) {
        List<Long> deleteIds = deleteList.stream().map(Listening::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (!deleteIds.isEmpty()) {
            listeningMapper.update(null, new LambdaUpdateWrapper<Listening>()
                    .in(Listening::getId, deleteIds)
                    .set(Listening::getIsDel, true));
        }
    }

    /**
     * 批量删除题目听力
     */
    @Override
    public void deleteQuestionsListening(Collection<String> questionIds) {
        if (questionIds == null || questionIds.isEmpty()) {
            return;
        }
        listeningMapper.update(null, new LambdaUpdateWrapper<Listening>()
                .in(Listening::getQuestionId, questionIds)
                .set(Listening::getIsDel, true));
    }

    /**
     * 保存一条听力
     */
    @Override
    public void saveOneListening(QuestionListeningVo vo) {
        // 题目现有听力
        List<Listening> dbListening = listeningMapper.selectList(new LambdaQueryWrapper<Listening>()
                .eq(Listening::getQuestionId, vo.getQuestionId())
                .eq(Listening::getSmallQuestionId, vo.getSmallQuestionId())
                .eq(Listening::getIsDel, false)
                .select(Listening::getId, Listening::getQuestionId, Listening::getSmallQuestionId));

        // 创建
        Listening newListening = createListening(vo);

        // 保存
        saveAdd(Collections.singletonList(newListening));
        saveDelete(dbListening);
    }
}
