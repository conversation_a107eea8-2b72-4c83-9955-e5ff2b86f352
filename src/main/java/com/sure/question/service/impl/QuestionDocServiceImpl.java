package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.question.document.PaperDoc;
import com.sure.question.document.QuestionDoc;
import com.sure.question.document.SmallQuestionDoc;
import com.sure.question.entity.*;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.PaperStatusEnum;
import com.sure.question.mapper.*;
import com.sure.question.service.QuestionDocService;
import com.sure.question.vo.es.QueryParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.*;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightBuilder;
import org.elasticsearch.search.fetch.subphase.highlight.HighlightField;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.ScriptSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class QuestionDocServiceImpl implements QuestionDocService {
    private final RestHighLevelClient client;
    private final QuestionMapper questionMapper;
    private final PaperQuestionMapper paperQuestionMapper;
    private final PaperMainMapper paperMainMapper;
    private final SmallQuestionMapper smallQuestionMapper;
    private final QuestionKnowledgeMapper questionKnowledgeMapper;
    private final QuestionBookMapper questionBookMapper;

    @Override
    public IPage<QuestionDoc> selectQuestionDocIdHighlightsPage(QueryParam param) {
        // 构建查询
        SearchRequest searchRequest = buildSearchRequest(param);

        // 执行查询
        SearchResponse response;
        try {
            response = client.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 构建查询结果
        return buildSearchResult(response, param.getPage(), param.getSize());
    }

    // 构建查询
    private SearchRequest buildSearchRequest(QueryParam param) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        // 只返回Id
        sourceBuilder.fetchSource(new String[]{"id"}, null);
        // 返回全部匹配的总数
        sourceBuilder.trackTotalHitsUpTo(1000);
        // 分页
        sourceBuilder.from((param.getPage() - 1) * param.getSize());
        sourceBuilder.size(param.getSize());

        // 主查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        sourceBuilder.query(boolQuery);
        // 字段过滤
        addTermsQuery(boolQuery, param);
        // 关键字过滤
        addMatchQuery(sourceBuilder, boolQuery, param);

        // 排序
        addSorts(sourceBuilder, param);

        // 将 sourceBuilder 绑定到 searchRequest
        SearchRequest searchRequest = new SearchRequest("question");
        searchRequest.source(sourceBuilder);
        return searchRequest;
    }

    // 字段过滤
    private void addTermsQuery(BoolQueryBuilder boolQuery, QueryParam param) {
        // 学段
        boolQuery.must(QueryBuilders.termQuery("grade_level", param.getGradeLevel()));
        // 学科
        boolQuery.must(QueryBuilders.termQuery("subject_id", param.getSubjectId()));
        // 题型
        if (param.getQuestionTypeId() != null) {
            boolQuery.must(QueryBuilders.termQuery("question_type_id", param.getQuestionTypeId()));
        }
        // 难度
        if (param.getDifficulty() != null) {
            boolQuery.must(QueryBuilders.termQuery("difficulty", param.getDifficulty()));
        }
        // 知识点
        if (param.getKnowledgeIds() != null && !param.getKnowledgeIds().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("knowledge_ids", param.getKnowledgeIds()));
        }
        // 章节
        if (param.getChapterIds() != null && !param.getChapterIds().isEmpty()) {
            boolQuery.must(QueryBuilders.termsQuery("chapter_ids", param.getChapterIds()));
        }
        // 排除题目
        if (param.getExcludeQuestionIds() != null && !param.getExcludeQuestionIds().isEmpty()) {
            boolQuery.mustNot(QueryBuilders.termsQuery("id", param.getExcludeQuestionIds()));
        }

        // 嵌套试卷查询
        BoolQueryBuilder papersBool = QueryBuilders.boolQuery();
        // 试卷类型
        if (param.getPaperTypeId() != null) {
            papersBool.must(QueryBuilders.termQuery("papers.paper_type_id", param.getPaperTypeId()));
        }
        // 年级
        if (param.getGradeId() != null) {
            papersBool.must(QueryBuilders.termQuery("papers.grade_id", param.getGradeId()));
        }
        // 年份
        if (param.getYear() != null) {
            papersBool.must(QueryBuilders.termQuery("papers.year", param.getYear()));
        }
        // 地区
        if (param.getRegionIds() != null && !param.getRegionIds().isEmpty()) {
            papersBool.must(QueryBuilders.termsQuery("papers.region_id", param.getRegionIds()));
        }
        // 指定Id
        if (param.getIncludePaperIds() != null && !param.getIncludePaperIds().isEmpty()) {
            papersBool.must(QueryBuilders.termsQuery("papers.id", param.getIncludePaperIds()));
        }
        if (papersBool.hasClauses()) {
            NestedQueryBuilder papersNestedQuery = QueryBuilders.nestedQuery("papers", papersBool, ScoreMode.None);
            boolQuery.must(papersNestedQuery);
        }
    }

    // 关键字过滤
    private void addMatchQuery(SearchSourceBuilder sourceBuilder, BoolQueryBuilder boolQuery, QueryParam param) {
        if (StringUtils.isBlank(param.getKeyword())) {
            return;
        }
        // 材料
        MatchQueryBuilder matchQueryBuilder = QueryBuilders.matchQuery("content_html", param.getKeyword()).analyzer("ik_smart");
        if (param.getKeywordMinMatchPercent() != null) {
            matchQueryBuilder.minimumShouldMatch(param.getKeywordMinMatchPercent() + "%");
        }
        boolQuery.should(matchQueryBuilder);
        // 材料高亮
        if (param.isHighlight()) {
            sourceBuilder.highlighter(createHighlightBuilder(Collections.singletonList("content_html")));
        }

        // 嵌套小题题干、选项、答案、解析
        List<String> fields = new ArrayList<>();
        fields.add("small_questions.content_html");
        fields.add("small_questions.options");
        if (param.isMatchAnswerExplanation()) {
            fields.add("small_questions.answer_html");
            fields.add("small_questions.explanation");
        }
        MultiMatchQueryBuilder multiMatchQueryBuilder = QueryBuilders.multiMatchQuery(param.getKeyword(), fields.toArray(new String[0]));
        multiMatchQueryBuilder.analyzer("ik_smart");
        if (param.getKeywordMinMatchPercent() != null) {
            multiMatchQueryBuilder.minimumShouldMatch(param.getKeywordMinMatchPercent() + "%");
        }
        // 嵌套小题题干、选项、答案、解析高亮
        InnerHitBuilder innerHitBuilder = new InnerHitBuilder();
        innerHitBuilder.setFetchSourceContext(new FetchSourceContext(true, new String[]{"small_questions.id"}, null));
        innerHitBuilder.setHighlightBuilder(createHighlightBuilder(fields));

        NestedQueryBuilder smallQuestionsNestedQuery = QueryBuilders.nestedQuery("small_questions", multiMatchQueryBuilder, ScoreMode.Max);
        if (param.isHighlight()) {
            smallQuestionsNestedQuery.innerHit(innerHitBuilder);
        }
        boolQuery.should(smallQuestionsNestedQuery);

        // should 子句至少匹配1个条件
        boolQuery.minimumShouldMatch(1);
    }

    private HighlightBuilder createHighlightBuilder(List<String> fields) {
        HighlightBuilder highlightBuilder = new HighlightBuilder();
        highlightBuilder.preTags("<em style='color:red;font-style:normal'>");
        highlightBuilder.postTags("</em>");
        for (String field : fields) {
            highlightBuilder.field(field).requireFieldMatch(false).numOfFragments(0);
        }
        return highlightBuilder;
    }

    // 排序
    private void addSorts(SearchSourceBuilder sourceBuilder, QueryParam param) {
        // 关键字匹配度排序
        if (StringUtils.isNotBlank(param.getKeyword())) {
            sourceBuilder.sort("_score", SortOrder.DESC);
        }
        // 组卷次数
        SortOrder sortOrder = Boolean.FALSE.equals(param.getIsDesc()) ? SortOrder.ASC : SortOrder.DESC;
        if (StringUtils.equalsAnyIgnoreCase(param.getSortBy(), "useCount", "use_count")) {
            sourceBuilder.sort(new FieldSortBuilder("use_count").order(sortOrder));
        }
        // 作答次数
        else if (StringUtils.equalsAnyIgnoreCase(param.getSortBy(), "answerCount", "answer_count")) {
            sourceBuilder.sort(new FieldSortBuilder("answer_count").order(sortOrder));
        }
        // 知识点匹配个数
        else if (StringUtils.equalsAnyIgnoreCase(param.getSortBy(), "knowledgeMatch", "knowledge_match")
                && param.getKnowledgeIds() != null && !param.getKnowledgeIds().isEmpty()) {
            Map<String, Object> scriptParams = new HashMap<>();
            scriptParams.put("kIds", new HashSet<>(param.getKnowledgeIds()));
            Script script = new Script(ScriptType.INLINE, "painless",
                    "double count = 0; for (long id : params.kIds) { if (doc['knowledge_ids'].contains(id)) { count++; } } int total = doc['knowledge_ids'].size(); if (total == 0) { return 0.0; } double ratio = count / total; return count + ratio * 0.5;",
                    scriptParams);
            ScriptSortBuilder scriptSort = new ScriptSortBuilder(script, ScriptSortBuilder.ScriptSortType.NUMBER);
            scriptSort.order(sortOrder);
            sourceBuilder.sort(scriptSort);
        }
        // 试卷权重
        sourceBuilder.sort(new FieldSortBuilder("combine_paper_weights").order(SortOrder.DESC));
        // 题目Id
        sourceBuilder.sort(new FieldSortBuilder("id").order(SortOrder.DESC));
    }

    // 构建查询结果
    private IPage<QuestionDoc> buildSearchResult(SearchResponse response, int currentPage, int pageSize) {
        IPage<QuestionDoc> page = new Page<>(currentPage, pageSize);
        int total = 0;
        if (response.getHits().getTotalHits() != null) {
            total = (int) response.getHits().getTotalHits().value;
        }
        page.setTotal(total);
        List<QuestionDoc> records = new ArrayList<>();
        for (SearchHit hit : response.getHits().getHits()) {
            records.add(buildQuestionDocFromSearchHit(hit));
        }
        page.setRecords(records);
        return page;
    }

    // 从命中记录构建QuestionDoc
    private QuestionDoc buildQuestionDocFromSearchHit(SearchHit hit) {
        QuestionDoc doc = new QuestionDoc();

        // 提取题目Id
        doc.setId(extractIdFromSearchHit(hit));
        // 材料高亮
        doc.setContent_html(extractHighlightContentFromSearchHit(hit, "content_html"));

        // 小题题干、选项高亮
        Map<String, SearchHits> innerHitsMap = hit.getInnerHits();
        if (innerHitsMap != null && innerHitsMap.containsKey("small_questions")) {
            SearchHits smallQuestionsHits = innerHitsMap.get("small_questions");
            for (SearchHit innerHit : smallQuestionsHits.getHits()) {
                SmallQuestionDoc sqDoc = new SmallQuestionDoc();
                if (doc.getSmall_questions() == null) {
                    doc.setSmall_questions(new ArrayList<>());
                }
                doc.getSmall_questions().add(sqDoc);

                // 小题Id
                sqDoc.setId(extractIdFromSearchHit(innerHit));
                // 小题题干高亮
                sqDoc.setContent_html(extractHighlightContentFromSearchHit(innerHit, "small_questions.content_html"));
                // 小题选项高亮
                sqDoc.setOptions(extractHighlightContentFromSearchHit(innerHit, "small_questions.options"));
                sqDoc.setOptions(SmallQuestionDoc.convertOptionsHtmlToJson(sqDoc.getOptions()));
                // 小题答案高亮
                sqDoc.setAnswer_html(extractHighlightContentFromSearchHit(innerHit, "small_questions.answer_html"));
                sqDoc.setAnswer_html(SmallQuestionDoc.convertAnswerHtmlToJson(sqDoc.getAnswer_html()));
                // 小题解析高亮
                sqDoc.setExplanation(extractHighlightContentFromSearchHit(innerHit, "small_questions.explanation"));
            }
        }

        return doc;
    }

    // 提取Id
    private Long extractIdFromSearchHit(SearchHit hit) {
        Map<String, Object> sourceAsMap = hit.getSourceAsMap();
        return Long.valueOf(sourceAsMap.get("id").toString());
    }

    // 提取高亮文本
    private String extractHighlightContentFromSearchHit(SearchHit hit, String field) {
        Map<String, HighlightField> highlightFields = hit.getHighlightFields();
        if (highlightFields == null || !highlightFields.containsKey(field)) {
            return null;
        }
        HighlightField highlightField = highlightFields.get(field);
        String[] innerFragments = Arrays.stream(highlightField.getFragments())
                .map(Object::toString)
                .toArray(String[]::new);
        return StringUtils.join(innerFragments, "");
    }

    @Override
    public int selectGradeLevelSubjectQuestionCount(int gradeLevel, int subjectId) {
        CountRequest countRequest = new CountRequest("question");
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must(QueryBuilders.termQuery("grade_level", gradeLevel));
        boolQuery.must(QueryBuilders.termQuery("subject_id", subjectId));
        countRequest.query(boolQuery);
        try {
            CountResponse countResponse = client.count(countRequest, RequestOptions.DEFAULT);
            return (int) countResponse.getCount();
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    @Override
    public Map<String, String> sync(Collection<String> questionIdCollection) {
        if (questionIdCollection == null || questionIdCollection.isEmpty()) {
            return Collections.emptyMap();
        }

        // 找出需更新的、需删除的
        List<QuestionDoc> updateDocs = buildQuestionDocs(questionIdCollection);
        Set<Long> updateDocIds = updateDocs.stream().map(QuestionDoc::getId).collect(Collectors.toSet());
        Set<Long> deleteDocIds = questionIdCollection.stream().map(Long::parseLong).filter(qId -> !updateDocIds.contains(qId)).collect(Collectors.toSet());

        // 创建批量操作
        BulkRequest bulkRequest = new BulkRequest();
        for (Long docId : deleteDocIds) {
            DeleteRequest deleteRequest = new DeleteRequest("question", docId.toString());
            bulkRequest.add(deleteRequest);
        }
        for (QuestionDoc doc : updateDocs) {
            // 注意不能剔除值为null的字段
            String docJson = JSON.toJSONString(doc, SerializerFeature.WriteMapNullValue);
            UpdateRequest updateRequest = new UpdateRequest("question", doc.getId().toString())
                    .doc(docJson, XContentType.JSON).upsert(docJson, XContentType.JSON);
            bulkRequest.add(updateRequest);
        }

        // 执行批量操作
        Map<String, String> failedMap = new HashMap<>();
        try {
            BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
            // 提取操作失败的题目Id及原因
            for (BulkItemResponse itemResponse : bulkResponse.getItems()) {
                if (itemResponse.isFailed()) {
                    BulkItemResponse.Failure failure = itemResponse.getFailure();
                    failedMap.put(failure.getId(), failure.getMessage());
                }
            }
        } catch (Exception ex) {
            String reason = ExceptionUtils.getStackTrace(ex);
            questionIdCollection.forEach(qId -> failedMap.put(qId, reason));
        }

        return failedMap;
    }

    // 构建题目文档
    private List<QuestionDoc> buildQuestionDocs(Collection<String> questionIdCollection) {
        List<Question> questions = questionMapper.selectList(new LambdaQueryWrapper<Question>()
                .in(Question::getId, questionIdCollection)
                .orderByAsc(Question::getId)
                .select(Question::getAnswerCount, Question::getContentHtml, Question::getDifficult, Question::getId,
                        Question::getQuestionTypeId, Question::getScoreRate, Question::getUseCount));
        if (questions.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> questionIds = questions.stream().map(Question::getId).collect(Collectors.toList());

        // 查题目关联的公共库未删除试卷
        List<PaperQuestion> paperQuestions = paperQuestionMapper.selectList(new LambdaQueryWrapper<PaperQuestion>()
                .in(PaperQuestion::getQuestionId, questionIds));
        if (paperQuestions.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> paperIds = paperQuestions.stream().map(PaperQuestion::getPaperId).distinct().collect(Collectors.toList());
        List<PaperMain> papers = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .eq(PaperMain::getPaperBank, PaperBankEnum.Public.getCode())
                .eq(PaperMain::getStatus, PaperStatusEnum.Status0.getCode())
                .select(PaperMain::getGradeId, PaperMain::getGradeLevel, PaperMain::getId, PaperMain::getPaperName, PaperMain::getPaperTypeId,
                        PaperMain::getRegionId, PaperMain::getSubjectId, PaperMain::getTerm, PaperMain::getYear));
        if (papers.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, List<String>> questionIdPaperIdsMap = paperQuestions.stream().collect(Collectors.groupingBy(PaperQuestion::getQuestionId,
                Collectors.mapping(PaperQuestion::getPaperId, Collectors.toList())));
        Map<String, PaperMain> paperMap = papers.stream().collect(Collectors.toMap(PaperMain::getId, Function.identity()));
        Map<String, List<PaperMain>> questionIdPapersMap = new HashMap<>();
        questionIdPaperIdsMap.forEach((qId, pIds) -> {
            List<PaperMain> thisQuestionPapers = pIds.stream().map(paperMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            if (!thisQuestionPapers.isEmpty()) {
                questionIdPapersMap.put(qId, thisQuestionPapers);
            }
        });
        Set<String> publicQuestionIds = questionIdPapersMap.keySet();
        if (publicQuestionIds.isEmpty()) {
            return Collections.emptyList();
        }
        questions.removeIf(q -> !publicQuestionIds.contains(q.getId()));
        questionIds = questions.stream().map(Question::getId).collect(Collectors.toList());

        // 查小题
        List<SmallQuestion> smallQuestions = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                .in(SmallQuestion::getQuestionId, questionIds)
                .select(SmallQuestion::getAnswerHtml, SmallQuestion::getContentHtml, SmallQuestion::getExplanation, SmallQuestion::getId,
                        SmallQuestion::getQuestionId, SmallQuestion::getQuestionNo, SmallQuestion::getOptions, SmallQuestion::getQuestionTypeId));
        Map<String, List<SmallQuestion>> questionIdSmallQuestionsMap = smallQuestions.stream().collect(Collectors.groupingBy(SmallQuestion::getQuestionId));
        List<String> smallQuestionIds = smallQuestions.stream().map(SmallQuestion::getId).collect(Collectors.toList());

        // 查知识点
        List<QuestionKnowledge> questionKnowledgeList = new ArrayList<>();
        if (!smallQuestionIds.isEmpty()) {
            questionKnowledgeList = questionKnowledgeMapper.selectList(new LambdaQueryWrapper<QuestionKnowledge>()
                    .in(QuestionKnowledge::getQuestionId, smallQuestionIds));
        }
        Map<String, List<Integer>> smallQuestionIdKnowledgeIdsMap = questionKnowledgeList.stream().collect(Collectors.groupingBy(QuestionKnowledge::getQuestionId,
                Collectors.mapping(QuestionKnowledge::getKnowledgeId, Collectors.toList())));

        // 查章节
        List<QuestionBook> questionBooks = questionBookMapper.selectList(new LambdaQueryWrapper<QuestionBook>()
                .in(QuestionBook::getQuestionId, questionIds));
        Map<String, List<Integer>> questionIdChapterIdsMap = questionBooks.stream().collect(Collectors.groupingBy(QuestionBook::getQuestionId,
                Collectors.mapping(QuestionBook::getChapterId, Collectors.toList())));

        List<QuestionDoc> result = new ArrayList<>();
        for (Question q : questions) {
            QuestionDoc qDoc = new QuestionDoc(q);
            result.add(qDoc);

            List<SmallQuestion> theSmallQuestions = questionIdSmallQuestionsMap.get(q.getId());
            if (theSmallQuestions != null && !theSmallQuestions.isEmpty()) {
                theSmallQuestions.sort(Comparator.comparing(sq -> ObjectUtils.defaultIfNull(sq.getQuestionNo(), 0)));
                for (SmallQuestion sq : theSmallQuestions) {
                    SmallQuestionDoc sqDoc = new SmallQuestionDoc(sq);
                    qDoc.getSmall_questions().add(sqDoc);

                    List<Integer> knowledgeIds = smallQuestionIdKnowledgeIdsMap.get(sq.getId());
                    if (knowledgeIds != null) {
                        for (Integer knowledgeId : knowledgeIds) {
                            sqDoc.getKnowledge_ids().add(knowledgeId.longValue());
                        }
                    }
                }
            }
            qDoc.setKnowledge_ids(qDoc.getSmall_questions().stream().flatMap(x -> x.getKnowledge_ids().stream()).distinct().collect(Collectors.toList()));
            qDoc.setChapter_ids(questionIdChapterIdsMap.getOrDefault(q.getId(), new ArrayList<>()).stream().map(Integer::longValue).collect(Collectors.toList()));
            questionIdPapersMap.get(q.getId()).forEach(p -> qDoc.getPapers().add(new PaperDoc(p)));
            qDoc.setGradeLevelSubjectId();
            qDoc.calcCombinePaperWeights();
        }
        return result;
    }
}
