package com.sure.question.service.impl;

import com.sure.question.common.IdWorker;
import com.sure.question.service.OssManager;
import com.sure.question.service.WaterMarkManager;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.Base64;

@Service("waterMarkManager")
@RequiredArgsConstructor
public class WaterMarkManagerImpl implements WaterMarkManager {
    private final OssManager ossManager;
    private final IdWorker idWorker;

    @Override
    public void rollback(String sourceUrl) {
        doRollback(sourceUrl);
    }

    @Override
    public void urlRemoveAddWaterMark(String sourceUrl, Boolean addWaterMark, Boolean needBackup) {
        String sourceIdentityName = ossManager.getObjectNameTK(sourceUrl);
        int index = sourceIdentityName.lastIndexOf(".");
        String backupIdentityName = sourceIdentityName.substring(0, index).concat("_backup").concat(sourceIdentityName.substring(index));
        try {
            BufferedImage image = ImageIO.read(new URL(sourceUrl));
            if (image == null) {
                return;
            }
            //对于数据库的题目，采用备份，对于编辑上传的题目，不采用备份
            if (needBackup && !ossManager.checkExistsTK(backupIdentityName)) {
                InputStream inputStream = bufferedImageToInputStream(image);
                if (inputStream != null) {
                    ossManager.uploadTK(backupIdentityName, inputStream);
                }
            }
            //去除水印
            removeWaterMark(image);
            //是否添加水印
            if (addWaterMark) {
                addWaterMark(image);
            }
            ossManager.uploadTK(sourceIdentityName, bufferedImageToInputStream(image));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public String baseStrRemoveAddWaterMark(String baseStr, Boolean addWaterMark) {
        String formatName = "";
        String content = "";
        String prefix = "";
        if (baseStr.contains("data:image/jpg;base64,")) {
            content = baseStr.replaceFirst("data:image/jpg;base64,", "");
            prefix = "data:image/jpg;base64,";
            formatName = "jpg";
        } else if (baseStr.contains("data:image/png;base64,")) {
            content = baseStr.replaceFirst("data:image/png;base64,", "");
            prefix = "data:image/png;base64,";
            formatName = "png";
        } else if (baseStr.contains("data:image/gif;base64,")) {
            content = baseStr.replaceFirst("data:image/gif;base64,", "");
            prefix = "data:image/gif;base64,";
            formatName = "gif";
        } else if (baseStr.contains("data:image/jpeg;base64,")) {
            content = baseStr.replaceFirst("data:image/jpeg;base64,", "");
            prefix = "data:image/jpeg;base64,";
            formatName = "jpeg";
        }
        BufferedImage image = base64ToImage(content);
        removeWaterMark(image);
        if (addWaterMark) {
            addWaterMark(image);
        }
        return prefix + imageToBase64(image, formatName);
    }

    @Override
    public String baseStrToUrlRemoveAndWaterMark(String baseStr, Boolean addWaterMark, String prePath) {
        String content = "";
        String newPath = "";
        if (baseStr.contains("data:image/jpg;base64,")) {
            content = baseStr.replaceFirst("data:image/jpg;base64,", "");
            newPath = prePath + "_" + idWorker.nextId() + ".jpg";
        } else if (baseStr.contains("data:image/png;base64,")) {
            content = baseStr.replaceFirst("data:image/png;base64,", "");
            newPath = prePath + "_" + idWorker.nextId() + ".png";
        } else if (baseStr.contains("data:image/gif;base64,")) {
            content = baseStr.replaceFirst("data:image/gif;base64,", "");
            newPath = prePath + "_" + idWorker.nextId() + ".gif";
        } else if (baseStr.contains("data:image/jpeg;base64,")) {
            content = baseStr.replaceFirst("data:image/jpeg;base64,", "");
            newPath = prePath + "_" + idWorker.nextId() + ".jpeg";
        }
        BufferedImage image = base64ToImage(content);
        removeWaterMark(image);
        if (addWaterMark) {
            addWaterMark(image);
        }
        ossManager.uploadTK(newPath, bufferedImageToInputStream(image));
        return ossManager.getPublicPathTK(newPath);
    }

    private BufferedImage base64ToImage(String baseStr) {
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] imageByte = decoder.decode(baseStr);
            InputStream in = new ByteArrayInputStream(imageByte);
            return ImageIO.read(in);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private String imageToBase64(BufferedImage image, String formatName) {
        final ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, formatName, os);
            return Base64.getEncoder().encodeToString(os.toByteArray());
        } catch (final IOException ioe) {
            throw new UncheckedIOException(ioe);
        }
    }

    private void doRollback(String sourceUrl) {
        String sourceIdentityName = ossManager.getObjectNameTK(sourceUrl);
        int index = sourceIdentityName.lastIndexOf(".");
        String backupIdentityName = sourceIdentityName.substring(0, index).concat("_backup").concat(sourceIdentityName.substring(index));
        try {
            BufferedImage image = ImageIO.read(ossManager.downloadTK(backupIdentityName));
            ossManager.uploadTK(sourceIdentityName, bufferedImageToInputStream(image));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void addWaterMark(BufferedImage bi) {
        doAddWaterMark(bi);
    }

    private void doAddWaterMark(BufferedImage bi) {
//        try {
//            BufferedImage waterimage = ImageIO.read(this.getClass().getClassLoader().getResourceAsStream("watermark.png"));
//            if (waterimage.getHeight() > bi.getHeight() || waterimage.getWidth() > bi.getWidth()) {
//                return;
//            }
//            int maxWidthOffset = bi.getWidth() - waterimage.getWidth();
//            int maxHeightOffset = bi.getHeight() - waterimage.getHeight();
//            for (int i = 0; i < waterimage.getWidth(); i++) {
//                for (int j = 0; j < waterimage.getHeight(); j++) {
//                    int color = waterimage.getRGB(i, j) & 0xFF;
//                    int rgb = bi.getRGB(i, j + maxHeightOffset) & 0xFF;
//                    if (color < rgb) {
//                        bi.setRGB(i, j + maxHeightOffset, waterimage.getRGB(i, j));
//                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    private void removeWaterMark(BufferedImage bi) {
        doRemoveWaterMark(bi);
    }

    private void doRemoveWaterMark(BufferedImage bi) {
        Color wColor = new Color(254, 254, 254);
        //白底水印
        for (int i = 0; i < bi.getWidth(); i++) {
            for (int j = 0; j < bi.getHeight(); j++) {
                int color = bi.getRGB(i, j);
                Color oriColor = new Color(color);
                int red = oriColor.getRed();
                int greed = oriColor.getGreen();
                int blue = oriColor.getBlue();
                if (red == 254 && greed == 254 && blue == 254) {
                    continue;
                }
                if (red > 235 && greed > 210 && blue > 210) {
                    bi.setRGB(i, j, wColor.getRGB());
                }
            }
        }
    }


    private InputStream bufferedImageToInputStream(BufferedImage image) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "png", os);
            return new ByteArrayInputStream(os.toByteArray());
        } catch (IOException e) {
        }
        return null;
    }
}
