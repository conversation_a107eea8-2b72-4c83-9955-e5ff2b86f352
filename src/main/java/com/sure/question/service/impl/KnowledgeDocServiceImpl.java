package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sure.question.document.KnowledgeDoc;
import com.sure.question.service.KnowledgeDocService;
import com.sure.question.service.KnowledgeMainService;
import com.sure.question.util.TreeUtil;
import com.sure.question.vo.KnowledgeMainVo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class KnowledgeDocServiceImpl implements KnowledgeDocService {
    private final KnowledgeMainService knowledgeMainService;
    private final RestHighLevelClient client;

    @Override
    public void sync(int gradeLevel, int subjectId) {
        List<KnowledgeDoc> docs = buildDocs(gradeLevel, subjectId);
        deleteDocs(gradeLevel, subjectId);
        insertDocs(docs);
    }

    private List<KnowledgeDoc> buildDocs(int gradeLevel, int subjectId) {
        List<KnowledgeMainVo> knowledgeTree = knowledgeMainService.getGradeLevelSubjectKnowledgeTree(gradeLevel, subjectId);
        List<KnowledgeDoc> docs = new ArrayList<>();
        BiPredicate<KnowledgeMainVo, TreeUtil.TraverseContext<KnowledgeMainVo>> preOrderWithContext = (node, context) -> {
            KnowledgeDoc doc = new KnowledgeDoc();
            doc.setId(node.getId());
            doc.setParent_id(context.parent == null ? 0 : context.parent.getId());
            doc.setGrade_level(gradeLevel);
            doc.setSubject_id(subjectId);
            doc.setName(node.getKnowledgeName());
            String path = context.path.stream().map(KnowledgeMainVo::getKnowledgeName).collect(Collectors.joining("/"));
            doc.setPath(path);
            doc.setDepth(context.depth);
            doc.setChildren_count(context.children.size());
            doc.setIs_leaf(context.isLeaf);
            List<Integer> ancestorIds = context.path.stream().map(KnowledgeMainVo::getId).filter(id -> !Objects.equals(id, node.getId())).collect(Collectors.toList());
            doc.setAncestor_ids(ancestorIds);
            docs.add(doc);
            return true;
        };
        TreeUtil.TraverseOptions<KnowledgeMainVo> traverseOptions = TreeUtil.TraverseOptions.<KnowledgeMainVo>builder()
                        .preOrderWithContext(preOrderWithContext)
                        .childrenGetter(KnowledgeMainVo::getKnowledgeVoList)
                        .build();
        TreeUtil.traverse(knowledgeTree, traverseOptions);
        return docs;
    }

    @SneakyThrows
    private void deleteDocs(int gradeLevel, int subjectId) {
        BoolQueryBuilder q = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("grade_level", gradeLevel))
                .filter(QueryBuilders.termQuery("subject_id", subjectId));

        DeleteByQueryRequest req = new DeleteByQueryRequest("knowledge");
        req.setQuery(q);
        req.setConflicts("proceed");          // 遇到版本冲突也继续
        req.setRefresh(true);                 // 删除后刷新索引

        BulkByScrollResponse resp = client.deleteByQuery(req, RequestOptions.DEFAULT);

        // 可选：检查失败
        if (!resp.getBulkFailures().isEmpty() || !resp.getSearchFailures().isEmpty()) {
            throw new RuntimeException("从ES删除知识点失败: " + resp.getBulkFailures() + " " + resp.getSearchFailures());
        }
    }

    @SneakyThrows
    private void insertDocs(List<KnowledgeDoc> docs) {
        BulkRequest bulkRequest = new BulkRequest();
        for (KnowledgeDoc doc : docs) {
            String docJson = JSON.toJSONString(doc, SerializerFeature.WriteMapNullValue);
            UpdateRequest updateRequest = new UpdateRequest("knowledge", doc.getId().toString())
                    .doc(docJson, XContentType.JSON).upsert(docJson, XContentType.JSON);
            bulkRequest.add(updateRequest);
        }

        // 执行批量操作
        Map<String, String> failedMap = new HashMap<>();
        BulkResponse bulkResponse = client.bulk(bulkRequest, RequestOptions.DEFAULT);
        // 提取操作失败的知识点Id及原因
        for (BulkItemResponse itemResponse : bulkResponse.getItems()) {
            if (itemResponse.isFailed()) {
                BulkItemResponse.Failure failure = itemResponse.getFailure();
                failedMap.put(failure.getId(), failure.getMessage());
            }
        }
        if (!failedMap.isEmpty()) {
            throw new RuntimeException("插入知识点到ES失败：" + JSON.toJSONString(failedMap));
        }
    }
}
