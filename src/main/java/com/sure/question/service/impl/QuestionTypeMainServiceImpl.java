package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.Question;
import com.sure.question.entity.QuestionTypeMain;
import com.sure.question.enums.BranchTypeEnum;
import com.sure.question.mapper.QuestionMapper;
import com.sure.question.mapper.QuestionTypeMainMapper;
import com.sure.question.service.QuestionTypeMainService;
import com.sure.question.util.RedissonLockUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class QuestionTypeMainServiceImpl implements QuestionTypeMainService {
    private final QuestionTypeMainMapper questionTypeMainMapper;
    private final QuestionMapper questionMapper;

    /**
     * 指定学段学科题型
     */
    @Override
    public List<QuestionTypeMain> findByStageAndSubject(int gradeLevelId, int subjectId) {
        return questionTypeMainMapper.selectList(new LambdaQueryWrapper<QuestionTypeMain>()
                .eq(QuestionTypeMain::getGradeLevel, gradeLevelId)
                .eq(QuestionTypeMain::getSubjectId, subjectId)
                .orderByAsc(QuestionTypeMain::getOrderId));
    }

    /**
     * 新增题型
     */
    @Override
    public void add(QuestionTypeMain newType, String userId) {
        // 基本检查
        checkQuestionType(newType);

        RedissonLockUtil.executeAfterLocked("Ques:Lock:QuestionType", () -> {
            // 检查题型名称是否已存在
            List<QuestionTypeMain> dbQuestionTypeList = questionTypeMainMapper.selectGradeLevelSubjectQuestionTypes(newType.getGradeLevel(), newType.getSubjectId());
            if (dbQuestionTypeList.stream().anyMatch(x -> x.getQuestionTypeName().equals(newType.getQuestionTypeName()))) {
                throw new ApiException("该题型已存在");
            }
            // 设置顺序、状态
            int dbMaxOrder = dbQuestionTypeList.stream().mapToInt(QuestionTypeMain::getOrderId).max().orElse(0);
            newType.setOrderId(dbMaxOrder + 1);
            // 设置Id
            int dbMaxId = ObjectUtils.defaultIfNull(questionTypeMainMapper.selectMaxId(), 0);
            newType.setId(dbMaxId + 1);
            // 插入数据库
            questionTypeMainMapper.insert(newType);
            log.info("添加题型, userId = {}, questionType = {}", userId, newType);
        });
    }

    /**
     * 题型基本检查
     */
    private void checkQuestionType(QuestionTypeMain questionType) {
        if (questionType.getGradeLevel() == null || questionType.getSubjectId() == null) {
            throw new ApiException("学段学科不能为空");
        }
        if (StringUtils.isBlank(questionType.getQuestionTypeName())) {
            throw new ApiException("题型名称不能为空");
        } else {
            questionType.setQuestionTypeName(questionType.getQuestionTypeName().trim());
        }
        if (questionType.getCategoryId() == null) {
            throw new ApiException("小题题型不能为空");
        } else {
            if (Arrays.stream(BranchTypeEnum.values()).noneMatch(x -> x.getId().equals(questionType.getCategoryId()))) {
                throw new ApiException("小题题型错误");
            }
        }
    }

    /**
     * 更新题型
     */
    @Override
    public void update(QuestionTypeMain newType, String userId) {
        // 基本检查
        checkQuestionType(newType);
        if (newType.getOrderId() == null) {
            throw new ApiException("顺序不能为空");
        }

        RedissonLockUtil.executeAfterLocked("Ques:Lock:QuestionType", () -> {
            // 检查题型存在
            QuestionTypeMain dbType = questionTypeMainMapper.selectOne(new LambdaQueryWrapper<QuestionTypeMain>()
                    .eq(QuestionTypeMain::getId, newType.getId())
                    .eq(QuestionTypeMain::getGradeLevel, newType.getGradeLevel())
                    .eq(QuestionTypeMain::getSubjectId, newType.getSubjectId()));
            if (dbType == null) {
                throw new ApiException("题型不存在");
            }
            // 检查小题题型，有题目时小题题型只能改为解答题
            if (!newType.getCategoryId().equals(dbType.getCategoryId())) {
                Question oneQuestion = questionMapper.selectOne(new LambdaQueryWrapper<Question>()
                        .eq(Question::getQuestionTypeId, newType.getId())
                        .last("limit 1"));
                if (oneQuestion != null) {
                    if (!newType.getCategoryId().equals(BranchTypeEnum.Essay.getId())) {
                        throw new ApiException("该题型已存在题目，其小题题型只能改为解答题");
                    }
                }
            }
            // 保存
            questionTypeMainMapper.updateById(newType);
            log.info("修改题型, userId = {}, oldQuestionType = {}, newQuestionType = {}", userId, dbType, newType);
        });
    }

    /**
     * 删除题型
     */
    @Override
    public void del(int gradeLevelId, int subjectId, int id, String userId) {
        RedissonLockUtil.executeAfterLocked("Ques:Lock:QuestionType", () -> {
            // 检查题型是否存在
            QuestionTypeMain dbQuestionType = questionTypeMainMapper.selectOne(new LambdaQueryWrapper<QuestionTypeMain>()
                    .eq(QuestionTypeMain::getGradeLevel, gradeLevelId)
                    .eq(QuestionTypeMain::getSubjectId, subjectId)
                    .eq(QuestionTypeMain::getId, id));
            if (dbQuestionType == null) {
                throw new ApiException("题型不存在");
            }

            // 检查是否关联题目
            Question oneQuestion = questionMapper.selectOne(new LambdaQueryWrapper<Question>()
                    .eq(Question::getQuestionTypeId, id)
                    .last("limit 1"));
            if (oneQuestion != null) {
                throw new ApiException("存在该题型的题目，禁止删除");
            }

            // 删除
            questionTypeMainMapper.deleteById(id);

            log.info("删除题型, userId = {}, questionType = {}", userId, dbQuestionType);
        });
    }
}
