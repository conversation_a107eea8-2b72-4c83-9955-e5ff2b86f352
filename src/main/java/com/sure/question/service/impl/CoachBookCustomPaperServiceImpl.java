package com.sure.question.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.entity.Result;
import com.sure.common.entity.StatusCode;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.CoachBookCustomPaper;
import com.sure.question.entity.PaperMain;
import com.sure.question.feign.FeignPdfService;
import com.sure.question.mapper.CoachBookCustomPaperMapper;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.service.CoachBookCustomPaperService;
import com.sure.question.service.OssManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/6/8
 */
@Slf4j
@Service("coachBookCustomPaperService")
@RequiredArgsConstructor
public class CoachBookCustomPaperServiceImpl extends ServiceImpl<CoachBookCustomPaperMapper, CoachBookCustomPaper> implements CoachBookCustomPaperService {

    private final CoachBookCustomPaperMapper coachBookCustomPaperMapper;
    private final PaperMainMapper paperMainMapper;
    private final RedisTemplate redisTemplate;
    private final FeignPdfService feignPdfService;
    private final OssManager ossManager;

    @Value("${pdfPath.paperH5}")
    private String paperH5PdfPath;
    @Value("${aliyun.ossSureMark.prefix.customPaperPdf}")
    private String customPaperPdfPrefix;

    @Override
    public void generateCustomPaperPdf(CoachBookCustomPaper coachBookCustomPaper) {
        // 抢锁
        String redisKey = "Report:coachBookCustomPaper:" + coachBookCustomPaper.getPaperId();
        Boolean success = redisTemplate.opsForValue().setIfAbsent(redisKey, "1");
        if (Boolean.FALSE.equals(success)) {
            return;
        }
        // 设置过期时间
        redisTemplate.expire(redisKey, 2, TimeUnit.SECONDS);

        // 更新状态
        int count = coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                .eq(CoachBookCustomPaper::getId, coachBookCustomPaper.getId())
                .eq(CoachBookCustomPaper::getPdfStatus, 0)
                .set(CoachBookCustomPaper::getPdfStatus, 1).set(CoachBookCustomPaper::getUpdateTime, new Date()));
        if (count == 0) {
            return;
        }

        // 组装url
        String url = paperH5PdfPath + "?paperId=" + coachBookCustomPaper.getPaperId();

        // 试卷名称，显示在页眉右上角
        String paperName = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, coachBookCustomPaper.getPaperId())
                .select(PaperMain::getPaperName)).getPaperName();

        try {
            Result result = feignPdfService.generatePdf(Base64.encode(url), Boolean.TRUE, paperName, "");
            if (Objects.equals(result.getCode(), StatusCode.ERROR)) {
                log.error("生成定制试卷pdf失败,url: {} ,{}", url, result.getMessage());
                throw new ApiException("生成定制试卷pdf失败");
            }
            if (2414 != result.getCode() && !Objects.equals(StatusCode.OK, result.getCode())) {
                log.info("生成定制试卷pdf返回：\n{}", JSONUtil.toJsonPrettyStr(result));
            }
            // 如果返回码是2414,则表示系统繁忙，需要重试一次
            if (2414 == result.getCode()) {
                result = feignPdfService.generatePdf(Base64.encode(url), Boolean.TRUE, paperName, "");
                if (Objects.equals(result.getCode(), StatusCode.ERROR)) {
                    log.error("生成定制试卷pdf失败,url: {} ,{}", url, result.getMessage());
                    throw new ApiException("生成定制试卷pdf失败");
                }
                if (2414 != result.getCode() && !Objects.equals(StatusCode.OK, result.getCode())) {
                    log.info("生成定制试卷pdf返回：\n{}", JSONUtil.toJsonPrettyStr(result));
                }
                // 如果返回码还是2414,则表示系统繁忙，更改回原来的状态
                if (2414 == result.getCode()) {
                    coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                            .eq(CoachBookCustomPaper::getId, coachBookCustomPaper.getId())
                            .set(CoachBookCustomPaper::getPdfStatus, 0));
                    return;
                }
            }

            Date now = new Date();
            String dateStr = DateUtil.format(now, "yyyyMMdd");
            String savePath = getOssCustomPaperPdfSavePath(coachBookCustomPaper.getPaperId(), paperName, dateStr);

            String base64Content = result.getData().toString();
            // 解码
            byte[] bytes = Base64.decode(base64Content);
            ossManager.uploadMark(savePath, bytes);
            // 更新状态为已生成
            coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                    .eq(CoachBookCustomPaper::getId, coachBookCustomPaper.getId())
                    .set(CoachBookCustomPaper::getPdfStatus, 2)
                    .set(CoachBookCustomPaper::getPdfPath, savePath)
                    .set(CoachBookCustomPaper::getUpdateTime, now));
        } catch (Exception e) {
            log.error("生成定制试卷pdf失败,url: {}", url, e);
            coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                    .eq(CoachBookCustomPaper::getId, coachBookCustomPaper.getId())
                    .set(CoachBookCustomPaper::getPdfStatus, 3)
                    .set(CoachBookCustomPaper::getUpdateTime, new Date()));
        }
    }

    private String getOssCustomPaperPdfSavePath(String paperId, String paperName, String dateStr) {
        return customPaperPdfPrefix.concat(paperId).concat("/").concat(paperName).concat("_").concat(dateStr).concat(".pdf");
    }
}
