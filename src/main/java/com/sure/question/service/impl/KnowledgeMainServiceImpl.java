package com.sure.question.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.cache.Cache;
import com.sure.question.common.IdWorker;
import com.sure.question.entity.ChapterKnowledge;
import com.sure.question.entity.KnowledgeMain;
import com.sure.question.entity.QuestionKnowledge;
import com.sure.question.enums.BaseEnum;
import com.sure.question.enums.GradeLevelEnum;
import com.sure.question.mapper.ChapterKnowledgeMapper;
import com.sure.question.mapper.KnowledgeMainMapper;
import com.sure.question.mapper.QuestionEsSyncMapper;
import com.sure.question.mapper.QuestionKnowledgeMapper;
import com.sure.question.service.ChapterService;
import com.sure.question.service.KnowledgeMainService;
import com.sure.question.util.CacheUtil;
import com.sure.question.util.RedissonLockUtil;
import com.sure.question.util.excelExport.Column;
import com.sure.question.util.excelExport.Sheet;
import com.sure.question.util.excelExport.Style;
import com.sure.question.util.excelExport.Workbook;
import com.sure.question.vo.KnowledgeMainVo;
import com.sure.question.vo.dataVo.IntIntVo;
import com.sure.question.vo.dataVo.StrStrVo;
import com.sure.question.vo.excelImport.IImportOperation;
import com.sure.question.vo.excelImport.IImportRow;
import com.sure.question.vo.excelImport.IOperationItem;
import com.sure.question.vo.excelImport.TreeNode;
import com.sure.question.vo.knowledgeMainVo.KnowledgeImportVo;
import com.sure.question.vo.knowledgeMainVo.KnowledgeOperationItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RequiredArgsConstructor
@Service
public class KnowledgeMainServiceImpl implements KnowledgeMainService {
    private final KnowledgeMainMapper knowledgeMainMapper;
    private final ChapterKnowledgeMapper chapterKnowledgeMapper;
    private final QuestionKnowledgeMapper questionKnowledgeMapper;
    private final QuestionEsSyncMapper questionEsSyncMapper;
    private final Cache cache;
    private final IdWorker idWorker;
    private final TransactionTemplate transactionTemplate;
    @Resource
    @Lazy
    private ChapterService chapterService;


    /**
     * 从缓存查学段学科知识点树
     */
    @Override
    public List<KnowledgeMainVo> getGradeLevelSubjectKnowledgeTreeWithCache(int gradeLevel, int subjectId) {
        String key = cache.getGradeLevelSubjectKnowledgeTreeKey(gradeLevel, subjectId);
        String treeJson = cache.redisTemplateGet(key);
        if (StringUtils.isNotEmpty(treeJson)) {
            return JSON.parseArray(treeJson, KnowledgeMainVo.class);
        }
        List<KnowledgeMainVo> knowledgeTree = getGradeLevelSubjectKnowledgeTree(gradeLevel, subjectId);
        if (!knowledgeTree.isEmpty()) {
            cache.redisTemplateSet(key, JSON.toJSONString(knowledgeTree));
        }
        return knowledgeTree;
    }

    /**
     * 清除缓存
     */
    public void deleteCache(int gradeLevel, int subjectId) {
        String key = cache.getGradeLevelSubjectKnowledgeTreeKey(gradeLevel, subjectId);
        cache.redisTemplateDelete(key);
    }

    /**
     * 查学段学科知识点树
     */
    @Override
    public List<KnowledgeMainVo> getGradeLevelSubjectKnowledgeTree(int gradeLevel, int subjectId) {
        Map<Integer, List<KnowledgeMain>> parentIdKnowledgeMap = getParentIdKnowledgeMap(gradeLevel, subjectId);
        return buildKnowledgeTree(0, parentIdKnowledgeMap);
    }

    private Map<Integer, List<KnowledgeMain>> getParentIdKnowledgeMap(int gradeLevel, int subjectId) {
        // 查学段学科所有知识点
        List<KnowledgeMain> knowledgeMainFlatList = selectGradeLevelSubjectKnowledgeList(gradeLevel, subjectId);
        // 按上级Id分组
        return knowledgeMainFlatList.stream()
                .filter(x -> x.getPid() != null)
                .collect(Collectors.groupingBy(KnowledgeMain::getPid));
    }

    /**
     * 查学段学科所有知识点
     */
    private List<KnowledgeMain> selectGradeLevelSubjectKnowledgeList(int gradeLevel, int subjectId) {
        return knowledgeMainMapper.selectList(new LambdaQueryWrapper<KnowledgeMain>()
                .eq(KnowledgeMain::getGradeLevel, gradeLevel)
                .eq(KnowledgeMain::getSubjectId, subjectId));
    }

    private static List<KnowledgeMainVo> buildKnowledgeTree(int parentId, Map<Integer, List<KnowledgeMain>> parentIdKnowledgeMap) {
        List<KnowledgeMain> knowledgeMains = parentIdKnowledgeMap.get(parentId);
        if (knowledgeMains == null || knowledgeMains.isEmpty()) {
            return Collections.emptyList();
        }
        return knowledgeMains.stream().map(knowledgeMain -> {
            KnowledgeMainVo vo = new KnowledgeMainVo();
            vo.setId(knowledgeMain.getId());
            vo.setKnowledgeName(knowledgeMain.getKnowledgeName());
            vo.setOrderId(knowledgeMain.getOrderId());
            vo.setKnowledgeVoList(buildKnowledgeTree(knowledgeMain.getId(), parentIdKnowledgeMap));
            return vo;
        }).sorted(Comparator.comparing(KnowledgeMainVo::getOrderId)).collect(Collectors.toList());
    }

    /**
     * 查学段学科知识点列表
     */
    @Override
    public List<KnowledgeMain> getGradeLevelSubjectKnowledgeList(int gradeLevel, int subjectId) {
        Map<Integer, List<KnowledgeMain>> parentIdKnowledgeMap = getParentIdKnowledgeMap(gradeLevel, subjectId);
        List<KnowledgeMain> result = new ArrayList<>();
        addKnowledgeList(parentIdKnowledgeMap, result, 0);
        return result;
    }

    private void addKnowledgeList(Map<Integer, List<KnowledgeMain>> parentIdKnowledgeMap, List<KnowledgeMain> result, int parentId) {
        List<KnowledgeMain> children = parentIdKnowledgeMap.get(parentId);
        if (children == null) {
            return;
        }
        children.sort(Comparator.comparing(KnowledgeMain::getOrderId));
        for (KnowledgeMain child : children) {
            result.add(child);
            addKnowledgeList(parentIdKnowledgeMap, result, child.getId());
        }
    }

    /**
     * 导出知识点
     */
    @Override
    public void exportExcel(int gradeLevel, int subjectId, OutputStream outputStream) throws IOException {
        List<KnowledgeMainVo> tree = getGradeLevelSubjectKnowledgeTree(gradeLevel, subjectId);
        List<String[]> items = tree.stream().map(node -> extractNodeItems(node, 0))
                .flatMap(Collection::stream).collect(Collectors.toList());

        String gradeLevelName = BaseEnum.getNameById(GradeLevelEnum.class, gradeLevel);
        String subjectName = CacheUtil.getSubjectName(subjectId);

        List<Column<String[]>> columns = new ArrayList<>();
        columns.add(new Column<>("学段", r -> gradeLevelName));
        columns.add(new Column<>("学科", r -> subjectName));
        columns.add(new Column<>("一级知识点", r -> r[0]));
        columns.add(new Column<>("二级知识点", r -> r[1]));
        columns.add(new Column<>("三级知识点", r -> r[2]));
        columns.forEach(Column::setColumnAutoWidth);

        Sheet<String[]> sheet = new Sheet<>();
        sheet.setSheetName(gradeLevelName + subjectName);
        sheet.setColumns(columns);
        sheet.setFreezeHeader(true);

        Workbook workbook = new Workbook();
        workbook.addSheet(sheet, items);
        workbook.write(outputStream);
    }

    private List<String[]> extractNodeItems(KnowledgeMainVo node, int level) {
        List<String[]> items = new ArrayList<>();
        boolean hasChildren = node.getKnowledgeVoList() != null && !node.getKnowledgeVoList().isEmpty();
        if (!hasChildren) {
            String[] self = new String[3];
            items.add(self);
        } else {
            for (KnowledgeMainVo child : node.getKnowledgeVoList()) {
                items.addAll(extractNodeItems(child, level + 1));
            }
        }
        items.forEach(item -> item[level] = node.getKnowledgeName());
        return items;
    }

    /**
     * 导入知识点
     */
    @Override
    public void importExcel(int gradeLevelId, int subjectId, InputStream inputStream, OutputStream outputStream, boolean save, String userId) {
        RedissonLockUtil.executeAfterLocked("Ques:Lock:ChangeBookChapterKnowledge",
                () -> doImportExcel(gradeLevelId, subjectId, inputStream, outputStream, save, userId));
    }

    private void doImportExcel(int gradeLevelId, int subjectId, InputStream inputStream, OutputStream outputStream, boolean save, String userId) {
        DoImportContext context = new DoImportContext();
        context.gradeLevelId = gradeLevelId;
        context.subjectId = subjectId;

        // 读取excel
        context.records = extractExcelRecords(inputStream);
        // 检查每行
        checkRows(context.records, gradeLevelId, subjectId);
        // 构建操作树
        context.operationTree = IImportOperation.buildOperationItemTree(context.records, null);

        // 查系统知识点
        context.dbKnowledgeList = selectGradeLevelSubjectKnowledgeList(gradeLevelId, subjectId);
        // 与系统知识点对比，添加知识点Id
        context.dbKnowledgeTree = buildDBKnowledgeTree(gradeLevelId, subjectId);
        IOperationItem.addOperationItemTreeIdThrowEx(context.operationTree, context.dbKnowledgeTree, KnowledgeMain::getId, KnowledgeMain::getKnowledgeName, "知识点");
        // 检查操作
        IOperationItem.checkOperation(context.operationTree, "知识点");

        // 提取变更
        context.dbKnowledgeMap = context.dbKnowledgeList.stream().collect(Collectors.toMap(KnowledgeMain::getId, Function.identity()));
        Changes changes = extractChanges(context);
        // 整理变更后的新版知识点
        buildNewKnowledgeList(changes, context.dbKnowledgeList);
        // 检查知识点名称重复
        checkNameDuplicate(changes.newList);

        // 查关联删除
        selectRelativeDeletes(changes);
        // 查关联新增
        selectRelativeAdds(changes);

        // 保存
        if (save) {
            saveDB(changes, userId);
            deleteCache(gradeLevelId, subjectId);
            chapterService.deleteCache(gradeLevelId, subjectId);
        }

        // 导出变更
        exportChanges(context, changes, outputStream);
    }

    /**
     * 上下文数据
     */
    private static class DoImportContext {
        public int gradeLevelId;
        public int subjectId;
        public List<KnowledgeImportVo> records;
        public List<TreeNode<KnowledgeOperationItem>> operationTree;
        public List<KnowledgeMain> dbKnowledgeList;
        public List<TreeNode<KnowledgeMain>> dbKnowledgeTree;
        public Map<Integer, KnowledgeMain> dbKnowledgeMap;
        public Supplier<Integer> knowledgeIdGenerator;
    }

    /**
     * 从Excel中提取数据
     */
    private List<KnowledgeImportVo> extractExcelRecords(InputStream inputStream) {
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        reader.addHeaderAlias("学段", "gradeLevelName");
        reader.addHeaderAlias("学科", "subjectName");
        reader.addHeaderAlias("一级知识点", "knowledge1Name");
        reader.addHeaderAlias("二级知识点", "knowledge2Name");
        reader.addHeaderAlias("三级知识点", "knowledge3Name");
        reader.addHeaderAlias("操作", "operation");
        List<KnowledgeImportVo> records = reader.setIgnoreEmptyRow(false).readAll(KnowledgeImportVo.class);
        if (records.isEmpty()) {
            throw new ApiException("excel中无数据");
        }
        IImportRow.setSequentialRowNo(records, 2);
        return records;
    }

    /**
     * 检查每行
     */
    private void checkRows(List<KnowledgeImportVo> vos, int gradeLevelId, int subjectId) {
        vos.forEach(KnowledgeImportVo::clean);

        IImportOperation.checkGradeLevel(vos, gradeLevelId);

        IImportOperation.checkSubject(vos, subjectId);

        IImportOperation.checkRuleThrowEx("一级知识点不能为空", vos, vo -> StringUtils.isEmpty(vo.getKnowledge1Name()));
        IImportOperation.checkRuleThrowEx("二级知识点为空时不能有三级知识点", vos, vo -> StringUtils.isEmpty(vo.getKnowledge2Name()) && StringUtils.isNotEmpty(vo.getKnowledge3Name()));

        Map<String, List<KnowledgeImportVo>> groupByFullKnowledgeName = vos.stream().collect(Collectors.groupingBy(KnowledgeImportVo::getFullKnowledgeName));
        Set<String> duplicateFullKnowledgeNameSet = new HashSet<>();
        groupByFullKnowledgeName.forEach((fullName, list) -> {
            if (list.size() > 1) {
                duplicateFullKnowledgeNameSet.add(fullName);
            }
        });
        if (!duplicateFullKnowledgeNameSet.isEmpty()) {
            IImportOperation.checkRuleThrowEx("知识点重复", vos, vo -> duplicateFullKnowledgeNameSet.contains(vo.getFullKnowledgeName()));
        }

        List<String> validOperationNames = Arrays.asList("新增", "删除", "改名称", "移入", "移出", "合并", "拆分");
        List<String> noValueOperationNames = Arrays.asList("新增", "删除");
        IImportOperation.checkOperation(vos, validOperationNames, noValueOperationNames);
    }

    /**
     * 构建系统知识点数
     */
    private List<TreeNode<KnowledgeMain>> buildDBKnowledgeTree(int gradeLevelId, int subjectId) {
        List<KnowledgeMain> dbKnowledgeList = selectGradeLevelSubjectKnowledgeList(gradeLevelId, subjectId);
        return TreeNode.buildTree(dbKnowledgeList, KnowledgeMain::getId, KnowledgeMain::getPid);
    }

    /**
     * 提取变更
     */
    private Changes extractChanges(DoImportContext context) {
        context.knowledgeIdGenerator = createIdGenerator();

        Changes changes = new Changes();
        int order = 0;
        for (TreeNode<KnowledgeOperationItem> node : context.operationTree) {
            if (getOneChange(node, 0, order, context, changes)) {
                order++;
            }
        }
        changes.updateOldList = changes.updateList.stream().map(KnowledgeMain::getId).map(context.dbKnowledgeMap::get).collect(Collectors.toList());
        return changes;
    }

    /**
     * 变更
     */
    private static class Changes {
        /**
         * 新增知识点
         */
        public List<KnowledgeMain> addList = new ArrayList<>();
        /**
         * 更新知识点
         */
        public List<KnowledgeMain> updateList = new ArrayList<>();
        /**
         * 更新前的旧知识点
         */
        public List<KnowledgeMain> updateOldList = new ArrayList<>();
        /**
         * 删除知识点
         */
        public List<KnowledgeMain> deleteList = new ArrayList<>();
        /**
         * 新版知识点
         */
        public List<KnowledgeMain> newList = new ArrayList<>();
        /**
         * 所有知识点（新版知识点与删除知识点）
         */
        public List<KnowledgeMain> fullList = new ArrayList<>();
        /**
         * 知识点合并
         */
        public Map<KnowledgeMain, List<KnowledgeMain>> mergeMap = new HashMap<>();
        /**
         * 新增题目知识点
         */
        public List<QuestionKnowledge> questionKnowledgeAddList = new ArrayList<>();
        /**
         * 删除题目知识点
         */
        public List<QuestionKnowledge> questionKnowledgeDeleteList = new ArrayList<>();
        /**
         * 新增章节知识点
         */
        public List<ChapterKnowledge> chapterKnowledgeAddList = new ArrayList<>();
        /**
         * 删除章节知识点
         */
        public List<ChapterKnowledge> chapterKnowledgeDeleteList = new ArrayList<>();
    }

    /**
     * 知识点Id生成器，在现有最大知识点Id基础上递增
     */
    private Supplier<Integer> createIdGenerator() {
        KnowledgeMain lastKnowledgeMain = knowledgeMainMapper.selectOne(new LambdaQueryWrapper<KnowledgeMain>().orderByDesc(KnowledgeMain::getId).last("limit 1"));
        ChapterKnowledge lastChapterKnowledge = chapterKnowledgeMapper.selectOne(new LambdaQueryWrapper<ChapterKnowledge>()
                .orderByDesc(ChapterKnowledge::getKnowledgeId).last("limit 1"));
        QuestionKnowledge lastQuestionKnowledge = questionKnowledgeMapper.selectOne(new LambdaQueryWrapper<QuestionKnowledge>()
                .orderByDesc(QuestionKnowledge::getKnowledgeId).last("limit 1"));
        int maxId = Stream.of(lastKnowledgeMain.getId(), lastChapterKnowledge.getKnowledgeId(), lastQuestionKnowledge.getKnowledgeId())
                .sorted().collect(Collectors.toList()).get(2);

        IntIntVo vo = new IntIntVo();
        vo.setValue(maxId);
        return () -> {
            Integer id = vo.getValue() + 1;
            vo.setValue(id);
            return id;
        };
    }

    /**
     * 比较一个节点的变更，删除节点返回false，其他返回true
     */
    private boolean getOneChange(TreeNode<KnowledgeOperationItem> node, Integer level, Integer order, DoImportContext context, Changes changes) {
        boolean deleted = false;

        KnowledgeOperationItem item = node.getItem();
        Integer id = item.getId();
        String operationName = item.getOperationName();
        String operationValue = item.getOperationValue();
        KnowledgeMain dbKnowledge = id == null ? null : context.dbKnowledgeMap.get(id);

        if ("新增".equals(operationName)) {
            KnowledgeMain newKnowledge = createNewKnowledge(node, level, order, context);
            item.setId(newKnowledge.getId());
            changes.addList.add(newKnowledge);
        } else if ("删除".equals(operationName)) {
            deleted = true;
            assert dbKnowledge != null;
            changes.deleteList.add(dbKnowledge);
        } else if ("改名称".equals(operationName)) {
            assert dbKnowledge != null;
            KnowledgeMain updateKnowledge = copyKnowledgeMain(dbKnowledge);
            updateKnowledge.setKnowledgeName(operationValue);
            updateKnowledge.setLevel(level);
            updateKnowledge.setOrderId(order);
            changes.updateList.add(updateKnowledge);
        } else if ("移入".equals(operationName)) {
            TreeNode<KnowledgeOperationItem> srcNode = IOperationItem.findNodeByFullName(operationValue, context.operationTree);
            assert srcNode != null;
            item.setId(srcNode.getItem().getId());
            KnowledgeMain moveKnowledge = copyKnowledgeMain(context.dbKnowledgeMap.get(item.getId()));
            moveKnowledge.setPid(level > 0 ? node.getParent().getItem().getId() : 0);
            moveKnowledge.setKnowledgeName(item.getName());
            moveKnowledge.setLevel(level);
            moveKnowledge.setOrderId(order);
            changes.updateList.add(moveKnowledge);
        } else if ("合并".equals(operationName)) {
            if (id == null) {
                dbKnowledge = createNewKnowledge(node, level, order, context);
                item.setId(dbKnowledge.getId());
                changes.addList.add(dbKnowledge);
            } else if (!level.equals(dbKnowledge.getLevel()) || !order.equals(dbKnowledge.getOrderId())) {
                KnowledgeMain copy = copyKnowledgeMain(dbKnowledge);
                copy.setLevel(level);
                copy.setOrderId(order);
                changes.updateList.add(copy);
            }
            List<String> fullNames = IOperationItem.splitOperationValueFullNames(operationValue);
            List<KnowledgeMain> srcList = new ArrayList<>();
            for (String fullName : fullNames) {
                TreeNode<KnowledgeOperationItem> srcNode = IOperationItem.findNodeByFullName(fullName, context.operationTree);
                assert srcNode != null;
                KnowledgeMain srcKnowledge = context.dbKnowledgeMap.get(srcNode.getItem().getId());
                assert srcKnowledge != null;
                srcList.add(srcKnowledge);
            }
            changes.mergeMap.put(dbKnowledge, srcList);
        } else if ("拆分".equals(operationName)) {
            assert dbKnowledge != null;
            String fullName = node.getItem().getFullName();
            List<String> operationValueFullNames = IOperationItem.splitOperationValueFullNames(operationValue);
            if (!operationValueFullNames.contains(fullName)) {
                deleted = true;
                changes.deleteList.add(dbKnowledge);
            }
        } else if ("".equals(operationName)) {
            assert dbKnowledge != null;
            if (!level.equals(dbKnowledge.getLevel()) || !order.equals(dbKnowledge.getOrderId())) {
                KnowledgeMain copy = copyKnowledgeMain(dbKnowledge);
                copy.setLevel(level);
                copy.setOrderId(order);
                changes.updateList.add(copy);
            }
        }

        int childOrder = 0;
        for (TreeNode<KnowledgeOperationItem> child : node.getChildren()) {
            if (getOneChange(child, level + 1, childOrder, context, changes)) {
                childOrder++;
            }
        }

        return !deleted;
    }

    /**
     * 创建新知识点
     */
    private KnowledgeMain createNewKnowledge(TreeNode<KnowledgeOperationItem> node, int level, int order, DoImportContext context) {
        return KnowledgeMain.builder()
                .id(context.knowledgeIdGenerator.get())
                .pid(level > 0 ? node.getParent().getItem().getId() : 0)
                .gradeLevel(context.gradeLevelId)
                .subjectId(context.subjectId)
                .knowledgeName(node.getItem().getName())
                .level(level)
                .orderId(order)
                .status("1")
                .build();
    }

    /**
     * 复制知识点
     */
    private KnowledgeMain copyKnowledgeMain(KnowledgeMain src) {
        return KnowledgeMain.builder()
                .id(src.getId())
                .pid(src.getPid())
                .gradeLevel(src.getGradeLevel())
                .subjectId(src.getSubjectId())
                .knowledgeName(src.getKnowledgeName())
                .level(src.getLevel())
                .orderId(src.getOrderId())
                .status(src.getStatus())
                .build();
    }

    /**
     * 整理变更后的新版知识点
     */
    private void buildNewKnowledgeList(Changes changes, List<KnowledgeMain> dbKnowledgeList) {
        TreeNode.buildNewList(changes.addList, changes.updateList, changes.deleteList, changes.fullList, changes.newList,
                dbKnowledgeList, KnowledgeMain::getId, KnowledgeMain::getPid, Comparator.comparing(KnowledgeMain::getOrderId));
    }

    /**
     * 检查知识点名称重复
     */
    private void checkNameDuplicate(List<KnowledgeMain> list) {
        Map<String, Integer> nameCountMap = new LinkedHashMap<>();
        Map<Integer, KnowledgeMain> map = list.stream().collect(Collectors.toMap(KnowledgeMain::getId, Function.identity()));
        for (KnowledgeMain k : list) {
            String name = k.getKnowledgeName();
            // 允许与父级知识点重名
            if (k.getPid() != null && k.getPid() != 0) {
                KnowledgeMain parent = map.get(k.getPid());
                if (parent.getKnowledgeName().equals(name)) {
                    continue;
                }
            }
            nameCountMap.put(name, nameCountMap.getOrDefault(name, 0) + 1);
        }

        List<String> duplicateNames = new ArrayList<>();
        nameCountMap.forEach((name, count) -> {
            if (count > 1) {
                duplicateNames.add(name);
            }
        });
        if (!duplicateNames.isEmpty()) {
            IOperationItem.throwLongEx("知识点名称重复", duplicateNames, Function.identity(), 100);
        }
    }

    /**
     * 查删除知识点关联删除题目知识点、章节知识点
     */
    private void selectRelativeDeletes(Changes changes) {
        if (changes.deleteList.isEmpty()) {
            return;
        }
        List<Integer> deleteIds = changes.deleteList.stream().map(KnowledgeMain::getId).collect(Collectors.toList());
        changes.questionKnowledgeDeleteList = questionKnowledgeMapper.selectList(new LambdaQueryWrapper<QuestionKnowledge>()
                .in(QuestionKnowledge::getKnowledgeId, deleteIds));
        changes.chapterKnowledgeDeleteList = chapterKnowledgeMapper.selectList(new LambdaQueryWrapper<ChapterKnowledge>()
                .in(ChapterKnowledge::getKnowledgeId, deleteIds));
    }

    /**
     * 查合并知识点新增题目知识点、章节知识点关联
     */
    private void selectRelativeAdds(Changes changes) {
        if (changes.mergeMap.isEmpty()) {
            return;
        }

        Set<Integer> ids = new HashSet<>();
        changes.mergeMap.forEach((dst, srcList) -> {
            ids.add(dst.getId());
            srcList.forEach(dest -> ids.add(dest.getId()));
        });
        Map<Integer, List<QuestionKnowledge>> knowledgeIdQuestionsMap = questionKnowledgeMapper.selectList(new LambdaQueryWrapper<QuestionKnowledge>()
                .in(QuestionKnowledge::getKnowledgeId, ids)).stream().collect(Collectors.groupingBy(QuestionKnowledge::getKnowledgeId));
        Map<Integer, List<ChapterKnowledge>> knowledgeIdChaptersMap = chapterKnowledgeMapper.selectList(new LambdaQueryWrapper<ChapterKnowledge>()
                .in(ChapterKnowledge::getKnowledgeId, ids)).stream().collect(Collectors.groupingBy(ChapterKnowledge::getKnowledgeId));

        changes.mergeMap.forEach((dst, srcList) -> {
            List<QuestionKnowledge> dstQuestions = knowledgeIdQuestionsMap.computeIfAbsent(dst.getId(), k -> new ArrayList<>());
            Set<String> dstQuestionIds = dstQuestions.stream().map(QuestionKnowledge::getQuestionId).collect(Collectors.toSet());

            List<ChapterKnowledge> dstChapters = knowledgeIdChaptersMap.computeIfAbsent(dst.getId(), k -> new ArrayList<>());
            Set<Integer> dstChapterIds = dstChapters.stream().map(ChapterKnowledge::getChapterId).collect(Collectors.toSet());

            for (KnowledgeMain src : srcList) {
                List<QuestionKnowledge> srcQuestions = knowledgeIdQuestionsMap.computeIfAbsent(src.getId(), k -> new ArrayList<>());
                for (QuestionKnowledge srcQuestion : srcQuestions) {
                    if (!dstQuestionIds.contains(srcQuestion.getQuestionId())) {
                        QuestionKnowledge copy = copyQuestionKnowledge(srcQuestion);
                        copy.setId(idWorker.nextId());
                        copy.setKnowledgeId(dst.getId());
                        dstQuestions.add(copy);
                        dstQuestionIds.add(copy.getQuestionId());
                        changes.questionKnowledgeAddList.add(copy);
                    }
                }

                List<ChapterKnowledge> srcChapters = knowledgeIdChaptersMap.computeIfAbsent(src.getId(), k -> new ArrayList<>());
                for (ChapterKnowledge srcChapter : srcChapters) {
                    if (!dstChapterIds.contains(srcChapter.getChapterId())) {
                        ChapterKnowledge copy = copyChapterKnowledge(srcChapter);
                        copy.setKnowledgeId(dst.getId());
                        dstChapters.add(copy);
                        dstChapterIds.add(copy.getChapterId());
                        changes.chapterKnowledgeAddList.add(copy);
                    }
                }
            }
        });
    }

    private QuestionKnowledge copyQuestionKnowledge(QuestionKnowledge src) {
        return QuestionKnowledge.builder()
                .id(src.getId())
                .questionId(src.getQuestionId())
                .questionNo(src.getQuestionNo())
                .knowledgeId(src.getKnowledgeId())
                .updateTime(src.getUpdateTime())
                .build();
    }

    private ChapterKnowledge copyChapterKnowledge(ChapterKnowledge src) {
        return ChapterKnowledge.builder()
                .chapterId(src.getChapterId())
                .knowledgeId(src.getKnowledgeId())
                .build();
    }

    /**
     * 导出变更
     */
    private void exportChanges(DoImportContext context, Changes changes, OutputStream outputStream) {
        if (outputStream == null) {
            return;
        }

        Set<Integer> addIds = changes.addList.stream().map(KnowledgeMain::getId).collect(Collectors.toSet());
        Set<Integer> updateIds = changes.updateList.stream().map(KnowledgeMain::getId).collect(Collectors.toSet());
        Set<Object> deleteIds = changes.deleteList.stream().map(KnowledgeMain::getId).collect(Collectors.toSet());
        Function<KnowledgeMain, Object> operationNameGetter = k -> {
            if (addIds.contains(k.getId())) {
                return "新增";
            } else if (updateIds.contains(k.getId())) {
                return "修改";
            } else if (deleteIds.contains(k.getId())) {
                return "删除";
            } else {
                return null;
            }
        };

        Function<KnowledgeMain, Object> operationValueGetter = k -> {
            if (!updateIds.contains(k.getId())) {
                return null;
            }
            KnowledgeMain old = context.dbKnowledgeMap.get(k.getId());
            Map<String, StrStrVo> changeMap = new LinkedHashMap<>();
            if (!old.getPid().equals(k.getPid())) {
                changeMap.put("上级", new StrStrVo(old.getPid().toString(), k.getPid().toString()));
            }
            if (!old.getKnowledgeName().equals(k.getKnowledgeName())) {
                changeMap.put("名称", new StrStrVo(old.getKnowledgeName(), k.getKnowledgeName()));
            }
            if (!old.getLevel().equals(k.getLevel())) {
                changeMap.put("层级", new StrStrVo(old.getLevel().toString(), k.getLevel().toString()));
            }
            if (!old.getOrderId().equals(k.getOrderId())) {
                changeMap.put("顺序", new StrStrVo(old.getOrderId().toString(), k.getOrderId().toString()));
            }
            return changeMap.entrySet().stream()
                    .map(entry -> entry.getKey() + "(" + entry.getValue().getKey() + "->" + entry.getValue().getValue() + ")")
                    .collect(Collectors.joining("；"));
        };

        Map<Integer, List<String>> mergeMap = new HashMap<>();
        changes.mergeMap.forEach((dst, srcList) -> mergeMap.put(dst.getId(), srcList.stream().map(KnowledgeMain::getKnowledgeName).collect(Collectors.toList())));
        Function<KnowledgeMain, Object> mergeValueGetter = k -> {
            List<String> mergeList = mergeMap.get(k.getId());
            if (mergeList == null) {
                return null;
            }
            return StringUtils.join(mergeList, ";");
        };

        List<Column<KnowledgeMain>> columns = new ArrayList<>();
        columns.add(new Column<>("知识点ID", k -> k.getId().toString(), false, null));
        Map<Integer, KnowledgeMain> fullMap = changes.fullList.stream().collect(Collectors.toMap(KnowledgeMain::getId, Function.identity()));
        columns.add(new Column<>("一级知识点", k -> getKnowledgeParentName(k, 0, fullMap), false, null));
        columns.add(new Column<>("二级知识点", k -> getKnowledgeParentName(k, 1, fullMap), false, null));
        columns.add(new Column<>("三级知识点", k -> getKnowledgeParentName(k, 2, fullMap), false, null));
        columns.add(new Column<>("层级", KnowledgeMain::getLevel));
        columns.add(new Column<>("顺序", KnowledgeMain::getOrderId));
        columns.add(new Column<>("更改类型", operationNameGetter));
        columns.add(new Column<>("更改详情", operationValueGetter, false, null));
        columns.add(new Column<>("合并知识点", mergeValueGetter, false, null));

        Function<KnowledgeMain, Style> conditionalRowStyle = k -> {
            if (updateIds.contains(k.getId())) {
                Style style = new Style();
                style.setBackgroundColor("FFE7BA");
                style.setFontColor("873800");
                return style;
            } else if (addIds.contains(k.getId())) {
                Style style = new Style();
                style.setBackgroundColor("D9F7BE");
                style.setFontColor("135200");
                return style;
            } else if (deleteIds.contains(k.getId())) {
                Style style = new Style();
                style.setBackgroundColor("FFCCC7");
                style.setFontColor("F5222d");
                return style;
            } else {
                return null;
            }
        };

        String stats = getChangesStats(changes);

        Sheet<KnowledgeMain> sheet = new Sheet<>();
        sheet.setSheetName("更改列表");
        sheet.setColumns(columns);
        sheet.setFreezeHeader(true);
        sheet.setConditionalRowStyleGetter(conditionalRowStyle);
        sheet.setFooter(stats);

        Workbook workbook = new Workbook();
        workbook.addSheet(sheet, changes.fullList);
        try {
            workbook.write(outputStream);
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * 上级知识点名称
     */
    private String getKnowledgeParentName(KnowledgeMain k, int level, Map<Integer, KnowledgeMain> map) {
        if (k.getLevel() < level) {
            return null;
        }
        KnowledgeMain p = k;
        while (p.getLevel() > level) {
            p = map.get(p.getPid());
        }
        return p.getKnowledgeName();
    }

    /**
     * 更改统计
     */
    private String getChangesStats(Changes changes) {
        List<KnowledgeMain> addList = changes.addList;
        List<KnowledgeMain> updateList = changes.updateList;
        List<KnowledgeMain> deleteList = changes.deleteList;
        List<QuestionKnowledge> qKAddList = changes.questionKnowledgeAddList;
        List<QuestionKnowledge> qKDeleteList = changes.questionKnowledgeDeleteList;
        List<ChapterKnowledge> cKAddList = changes.chapterKnowledgeAddList;
        List<ChapterKnowledge> cKDeleteList = changes.chapterKnowledgeDeleteList;

        StringBuilder sb = new StringBuilder();
        if (!addList.isEmpty()) {
            sb.append("；新增 ").append(addList.size()).append(" 个知识点");
        }
        if (!updateList.isEmpty()) {
            sb.append("；更新 ").append(updateList.size()).append(" 个知识点");
        }
        if (!deleteList.isEmpty()) {
            sb.append("；删除 ").append(deleteList.size()).append(" 个知识点");
        }
        if (!qKAddList.isEmpty()) {
            sb.append("；新增 ").append(qKAddList.size()).append(" 个题目知识点关联");
        }
        if (!qKDeleteList.isEmpty()) {
            sb.append("；删除 ").append(qKDeleteList.size()).append(" 个题目知识点关联");
        }
        if (!cKAddList.isEmpty()) {
            sb.append("；新增 ").append(cKAddList.size()).append(" 个章节知识点关联");
        }
        if (!cKDeleteList.isEmpty()) {
            sb.append("；删除 ").append(cKDeleteList.size()).append(" 个章节知识点关联");
        }
        if (sb.length() > 0) {
            return sb.substring(1);
        } else {
            return "无更改";
        }
    }

    /**
     * 保存
     */
    private void saveDB(Changes changes, String userId) {
        List<KnowledgeMain> addList = changes.addList;
        List<KnowledgeMain> updateList = changes.updateList;
        List<KnowledgeMain> updateOldList = changes.updateOldList;
        List<KnowledgeMain> deleteList = changes.deleteList;
        List<QuestionKnowledge> qKAddList = changes.questionKnowledgeAddList;
        List<QuestionKnowledge> qKDeleteList = changes.questionKnowledgeDeleteList;
        List<ChapterKnowledge> cKAddList = changes.chapterKnowledgeAddList;
        List<ChapterKnowledge> cKDeleteList = changes.chapterKnowledgeDeleteList;

        long operationId = idWorker.nextId();
        Date time = new Date();

        transactionTemplate.execute(status -> {
            if (!addList.isEmpty()) {
                knowledgeMainMapper.insertKnowledgeOperationLog(operationId, time, userId, "ADD", addList);
                knowledgeMainMapper.insertList(addList);
            }
            if (!updateList.isEmpty()) {
                knowledgeMainMapper.insertKnowledgeOperationLog(operationId, time, userId, "UPDATE", updateOldList);
                updateList.forEach(knowledgeMainMapper::updateById);
            }
            if (!deleteList.isEmpty()) {
                knowledgeMainMapper.insertKnowledgeOperationLog(operationId, time, userId, "DELETE", deleteList);
                List<Integer> deleteIds = deleteList.stream().map(KnowledgeMain::getId).collect(Collectors.toList());
                knowledgeMainMapper.delete(new LambdaQueryWrapper<KnowledgeMain>().in(KnowledgeMain::getId, deleteIds));
            }
            Set<String> questionIds = new HashSet<>();
            if (!qKAddList.isEmpty()) {
                questionKnowledgeMapper.insertQuestionKnowledgeOperationLog(operationId, time, userId, "ADD", qKAddList);
                questionKnowledgeMapper.insertList(qKAddList);
                qKAddList.forEach(qk -> questionIds.add(qk.getQuestionId()));
            }
            if (!qKDeleteList.isEmpty()) {
                questionKnowledgeMapper.insertQuestionKnowledgeOperationLog(operationId, time, userId, "DELETE", qKDeleteList);
                Map<Integer, List<String>> groupByKnowledgeId = qKDeleteList.stream().collect(Collectors.groupingBy(QuestionKnowledge::getKnowledgeId, Collectors.mapping(QuestionKnowledge::getQuestionId, Collectors.toList())));
                groupByKnowledgeId.forEach(questionKnowledgeMapper::deleteByKnowledgeIdAndSmallQuestionIds);
                qKDeleteList.forEach(qk -> questionIds.add(qk.getQuestionId()));
            }
            if (!questionIds.isEmpty()) {
                CollUtil.split(questionIds, 1000).forEach(questionEsSyncMapper::insertPendingQuestionsBatch);
            }
            if (!cKAddList.isEmpty()) {
                chapterKnowledgeMapper.insertChapterKnowledgeOperationLog(operationId, time, userId, "ADD", cKAddList);
                chapterKnowledgeMapper.insertList(cKAddList);
            }
            if (!cKDeleteList.isEmpty()) {
                chapterKnowledgeMapper.insertChapterKnowledgeOperationLog(operationId, time, userId, "DELETE", cKDeleteList);
                cKDeleteList.forEach(ck -> chapterKnowledgeMapper.deleteByChapterIdAndKnowledgeId(ck.getChapterId(), ck.getKnowledgeId()));
            }
            return null;
        });
    }
}
