package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.CoachStuCard;
import com.sure.question.entity.SchoolCoachBook;
import com.sure.question.feign.UserService;
import com.sure.question.mapper.CoachStuCardMapper;
import com.sure.question.mapper.SchoolCoachBookMapper;
import com.sure.question.service.SubjectService;
import com.sure.question.util.CacheUtil;
import com.sure.question.vo.student.StudentInfoVo;
import com.sure.question.vo.subject.StudentSubjectVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SubjectServiceImpl implements SubjectService {

    private final UserService userService;
    private final SchoolCoachBookMapper schoolCoachBookMapper;
    private final CoachStuCardMapper coachStuCardMapper;

    @Override
    public List<StudentSubjectVo> studentSubject(String studentId, String userId, Integer semesterId, Integer term) {
        // 鉴权，校验当前学生是不是当前用户的家长
        StudentInfoVo studentInfo = userService.getStudentInfo(studentId);
        if (studentInfo.getParentIds() == null || !studentInfo.getParentIds().contains(userId)) {
            throw new ApiException("无权限！");
        }
        if (studentInfo.getSchoolCurrentSemester() == null || studentInfo.getSchoolCurrentTerm() == null) {
            return Collections.emptyList();
        }

        // 学生所在年级
        int gradeId = studentInfo.getGradeId() - (studentInfo.getSchoolCurrentSemester() - semesterId);

        // 查找学校当前学期的教辅
        List<SchoolCoachBook> coachBooks = schoolCoachBookMapper.selectList(new LambdaQueryWrapper<SchoolCoachBook>()
                .eq(SchoolCoachBook::getSchoolId, studentInfo.getSchoolId())
                .eq(SchoolCoachBook::getSemesterId, semesterId)
                .eq(SchoolCoachBook::getTerm, term)
                .eq(SchoolCoachBook::getGradeId, gradeId)
                .select(SchoolCoachBook::getCoachBookId, SchoolCoachBook::getSubjectId));
        if (coachBooks.isEmpty()) {
            return Collections.emptyList();
        }

        // 查找学生激活的科目
        Set<Integer> subjectIds = coachStuCardMapper.selectList(new LambdaQueryWrapper<CoachStuCard>()
                        .eq(CoachStuCard::getStudentId, studentId)
                        .in(CoachStuCard::getCoachBookId, coachBooks.stream().map(SchoolCoachBook::getCoachBookId).collect(Collectors.toList()))
                        .select(CoachStuCard::getSubjectId))
                .stream().map(CoachStuCard::getSubjectId).collect(Collectors.toSet());

        List<StudentSubjectVo> voList = new ArrayList<>(coachBooks.size());
        Set<Integer> existSubjectIds = new HashSet<>();
        for (SchoolCoachBook coachBook : coachBooks) {
            if (existSubjectIds.contains(coachBook.getSubjectId())) {
                continue;
            }
            existSubjectIds.add(coachBook.getSubjectId());

            voList.add(StudentSubjectVo.builder()
                    .subjectId(coachBook.getSubjectId())
                    .subjectName(CacheUtil.getSubjectName(coachBook.getSubjectId()))
                    .sortCode(CacheUtil.getSubjectSortCode(coachBook.getSubjectId()))
                    .isActivated(subjectIds.contains(coachBook.getSubjectId()))
                    .build());
        }
        voList.sort(Comparator.comparing(StudentSubjectVo::getSortCode));
        return voList;
    }
}
