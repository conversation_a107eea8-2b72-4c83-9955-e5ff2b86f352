package com.sure.question.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.sure.base.log.util.IpUtils;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.*;
import com.sure.question.mapper.*;
import com.sure.question.service.FeignService;
import com.sure.question.service.QuesLogService;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.queslog.PaperStatVo;
import com.sure.question.vo.queslog.QuesLogDetailVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@Service("quesLogService")
@RequiredArgsConstructor
public class QuesLogServiceImpl extends ServiceImpl<QuesLogMapper, QuesLog> implements QuesLogService {

    private final QuesLogMapper quesLogMapper;
    private final FeignService feignService;
    private final PaperMainMapper paperMainMapper;
    private final RegionMapper regionMapper;
    private final CoachBookMapper coachBookMapper;
    private final PaperTypeMapper paperTypeMapper;

    @Async
    @Override
    public void saveQuesLog(TokenUserVo userVo, String paperId, HttpServletRequest request, String logType) {
        if ("view".equals(logType)) {
            QuesLog quesLog = QuesLog.builder().build();
            quesLog.setUserId(userVo.getUserId());
            quesLog.setPaperId(paperId);
            quesLog.setLogType(1);
            quesLog.setSchoolId(userVo.getSchoolId());
            quesLog.setCreateTime(new Date());
            quesLog.setIp(ServletUtil.getClientIP(request));
            quesLogMapper.insert(quesLog);
        } else if ("download".equals(logType)) {
            QuesLog quesLog = quesLogMapper.selectOne(new LambdaQueryWrapper<QuesLog>()
                    .eq(QuesLog::getPaperId, paperId)
                    .eq(QuesLog::getUserId, userVo.getUserId())
                    .orderByDesc(QuesLog::getCreateTime)
                    .last(" limit 1"));
            if (quesLog != null) {
                quesLog.setLogType(2);
                quesLogMapper.updateById(quesLog);
            } else {
                quesLog = QuesLog.builder().build();
                quesLog.setUserId(userVo.getUserId());
                quesLog.setPaperId(paperId);
                quesLog.setLogType(2);
                quesLog.setSchoolId(userVo.getSchoolId());
                quesLog.setCreateTime(new Date());
                quesLog.setIp(ServletUtil.getClientIP(request));
                quesLogMapper.insert(quesLog);
            }
        } else {
            throw new ApiException("logType必须为view或download");
        }

    }

    @Override
    public PageInfo<QuesLogDetailVo> listQuesLog(String schoolId, Integer type, String beginTime, String endTime, String paperId, String paperName, Integer pageNum, Integer pageSize, TokenUserVo userVo) {
        // todo鉴权
        Date beginDate = null;
        if (beginTime != null) {
            beginDate = DateUtil.parse(beginTime);
        }
        Date endDate = null;
        if (endTime != null) {
            endDate = DateUtil.parse(endTime);
        }

        List<String> paperIds = new ArrayList<>();
        if (StringUtils.isNotBlank(paperName)) {
            paperIds = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                            .like(PaperMain::getPaperName, paperName)
                            .select(PaperMain::getId))
                    .stream().map(PaperMain::getId).collect(Collectors.toList());
            if (paperIds.isEmpty()) {
                return new PageInfo<>();
            }
        }

        PageMethod.startPage(pageNum, pageSize);
        List<QuesLog> quesLogList = quesLogMapper.selectList(new LambdaQueryWrapper<QuesLog>()
                .eq(StringUtils.isNotBlank(schoolId), QuesLog::getSchoolId, schoolId)
                .eq(type != null, QuesLog::getLogType, type)
                .ge(beginDate != null, QuesLog::getCreateTime, beginDate)
                .le(endDate != null, QuesLog::getCreateTime, endDate)
                .eq(StringUtils.isNotBlank(paperId), QuesLog::getPaperId, paperId)
                .in(!paperIds.isEmpty(), QuesLog::getPaperId, paperIds)
                .orderByDesc(QuesLog::getId));
        PageInfo pageInfo = new PageInfo<>(quesLogList);
        if (!pageInfo.hasContent()) {
            return pageInfo;
        }

        List<String> userIds = quesLogList.stream().map(QuesLog::getUserId).distinct().collect(Collectors.toList());
        Map<String, String> userIdNameMap = feignService.getUserIdNameMap(userIds);
        List<String> schoolIds = quesLogList.stream().map(QuesLog::getSchoolId).distinct().collect(Collectors.toList());
        Map<String, String> schoolIdNameMap = feignService.getSchoolIdNameMap(schoolIds);
        paperIds = quesLogList.stream().map(QuesLog::getPaperId).distinct().collect(Collectors.toList());
        Map<String, String> paperIdPaperNameMap = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                        .in(PaperMain::getId, paperIds)
                        .select(PaperMain::getId, PaperMain::getPaperName))
                .stream().collect(Collectors.toMap(PaperMain::getId, PaperMain::getPaperName));
        List<QuesLogDetailVo> quesLogDetailVoList = new ArrayList<>(quesLogList.size());
        for (QuesLog quesLog : quesLogList) {
            quesLogDetailVoList.add(QuesLogDetailVo.builder()
                    .id(quesLog.getId())
                    .userId(quesLog.getUserId())
                    .userName(userIdNameMap.get(quesLog.getUserId()))
                    .logType(quesLog.getLogType())
                    .schoolId(quesLog.getSchoolId())
                    .schoolName(schoolIdNameMap.get(quesLog.getSchoolId()))
                    .paperId(quesLog.getPaperId())
                    .paperName(paperIdPaperNameMap.get(quesLog.getPaperId()))
                    .ip(quesLog.getIp())
                    .address(IpUtils.getAddress(quesLog.getIp()))
                    .createTime(quesLog.getCreateTime())
                    .build());
        }
        pageInfo.setList(quesLogDetailVoList);
        return pageInfo;
    }

    @Override
    public PageInfo<PaperStatVo> statistic(String beginTime, String endTime, Integer gradeLevel, Integer subjectId, String paperName, Integer pageNum, Integer pageSize, TokenUserVo userVo) {
        Date beginDate = null;
        if (beginTime != null) {
            beginDate = DateUtil.parse(beginTime);
        }
        Date endDate = null;
        if (endTime != null) {
            endDate = DateUtil.parse(endTime);
        }
        PageMethod.startPage(pageNum, pageSize);
        List<PaperStatVo> voList = quesLogMapper.queryStatistic(beginDate, endDate, gradeLevel, subjectId, paperName);
        PageInfo pageInfo = new PageInfo<>(voList);
        if (pageInfo.hasContent()) {
            Map<Integer, String> paperTypeIdPaperTypeNameMap = paperTypeMapper.selectList(new LambdaQueryWrapper<PaperType>().eq(PaperType::getGradeLevel, gradeLevel))
                    .stream().collect(Collectors.toMap(PaperType::getPaperTypeId, PaperType::getPaperTypeName));
            Map<Long, String> regionIdNameMap = new HashMap<>();
            List<Long> regionIds = voList.stream().filter(x -> x.getRegionId() != null).map(x -> Long.parseLong(x.getRegionId())).distinct().collect(Collectors.toList());
            if (!regionIds.isEmpty()) {
                regionIdNameMap = regionMapper.selectList(new LambdaQueryWrapper<Region>()
                                .in(Region::getId, regionIds)
                                .select(Region::getId, Region::getName))
                        .stream().collect(Collectors.toMap(Region::getId, Region::getName));
            }
            Map<Integer, String> coachBookIdCoachBookNameMap = new HashMap<>();
            List<Integer> coachBookIds = voList.stream().map(PaperStatVo::getFlagCoachBookId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!coachBookIds.isEmpty()) {
                coachBookIdCoachBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                                .in(CoachBook::getId, coachBookIds)
                                .select(CoachBook::getId, CoachBook::getBookName))
                        .stream().collect(Collectors.toMap(CoachBook::getId, CoachBook::getBookName));
            }
            for (PaperStatVo vo : voList) {
                if (vo.getPaperTypeId() != null) {
                    vo.setPaperTypeName(paperTypeIdPaperTypeNameMap.get(vo.getPaperTypeId()));
                }
                if (vo.getRegionId() != null) {
                    vo.setRegionName(regionIdNameMap.get(Long.parseLong(vo.getRegionId())));
                }
                if (vo.getFlagCoachBookId() != null) {
                    vo.setFlagCoachBookName(coachBookIdCoachBookNameMap.get(vo.getFlagCoachBookId()));
                }
            }
        }
        return pageInfo;
    }

}
