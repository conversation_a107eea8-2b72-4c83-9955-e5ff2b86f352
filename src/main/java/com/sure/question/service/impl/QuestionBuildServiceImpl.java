package com.sure.question.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.sure.question.common.Constants;
import com.sure.question.dto.question.QuestionChapterDto;
import com.sure.question.dto.question.QuestionPublicPaperDto;
import com.sure.question.dto.question.SelectQuestionVoOption;
import com.sure.question.dto.question.SmallQuestionKnowledgeDto;
import com.sure.question.entity.PaperQuestion;
import com.sure.question.entity.Question;
import com.sure.question.entity.QuestionCorrect;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.mapper.*;
import com.sure.question.service.ListeningService;
import com.sure.question.service.QuestionBuildService;
import com.sure.question.service.RenderedQuestionService;
import com.sure.question.vo.question.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class QuestionBuildServiceImpl implements QuestionBuildService {
    private final QuestionMapper questionMapper;
    private final SmallQuestionMapper smallQuestionMapper;
    private final QuestionKnowledgeMapper questionKnowledgeMapper;
    private final QuestionBookMapper questionBookMapper;
    private final ListeningService listeningService;
    private final PaperQuestionMapper paperQuestionMapper;
    private final PaperMainMapper paperMainMapper;
    private final FavoriteQuesMapper favoriteQuesMapper;
    private final QuestionCorrectMapper questionCorrectMapper;
    private final RenderedQuestionService renderedQuestionService;


    /**
     * 查单个QuestionVo
     */
    @Override
    public QuestionVo selectQuestionVo(String questionId) {
        return selectQuestionVo(questionId, new SelectQuestionVoOption());
    }

    /**
     * 查单个QuestionVo
     */
    @Override
    public QuestionVo selectQuestionVo(String questionId, SelectQuestionVoOption option) {
        List<QuestionVo> vos = selectQuestionVos(Collections.singletonList(questionId), option);
        if (vos.isEmpty()) {
            return null;
        }
        return vos.get(0);
    }

    /**
     * 查单个UserQuestionVo
     */
    @Override
    public UserQuestionVo selectUserQuestionVo(String questionId, String userId) {
        return selectUserQuestionVo(questionId, userId, new SelectQuestionVoOption());
    }

    /**
     * 查单个UserQuestionVo
     */
    @Override
    public UserQuestionVo selectUserQuestionVo(String questionId, String userId, SelectQuestionVoOption option) {
        List<UserQuestionVo> vos = selectUserQuestionVos(Collections.singletonList(questionId), userId, option);
        if (vos.isEmpty()) {
            return null;
        }
        return vos.get(0);
    }

    /**
     * 查多个QuestionVos
     */
    @Override
    public List<QuestionVo> selectQuestionVos(Collection<String> questionIds) {
        return selectQuestionVos(questionIds, new SelectQuestionVoOption());
    }

    /**
     * 查多个QuestionVos，公式已渲染
     */
    @Override
    public List<QuestionVo> selectRenderedFormulaQuestionVos(List<String> questionIds) {
        List<QuestionVo> questionVos = selectQuestionVos(questionIds);
        renderedQuestionService.replaceRenderedContent(questionVos, true);
        return questionVos;
    }

    /**
     * 查多个QuestionVos
     */
    @Override
    public List<QuestionVo> selectQuestionVos(Collection<String> questionIds, SelectQuestionVoOption option) {
        return selectQuestionVosCommon(questionIds, QuestionVo::new, option);
    }

    /**
     * 查多个UserQuestionVos
     */
    @Override
    public List<UserQuestionVo> selectUserQuestionVos(Collection<String> questionIds, String userId) {
        return selectUserQuestionVos(questionIds, userId, new SelectQuestionVoOption());
    }

    /**
     * 查多个UserQuestionVos
     */
    @Override
    public List<UserQuestionVo> selectUserQuestionVos(Collection<String> questionIds, String userId, SelectQuestionVoOption option) {
        List<UserQuestionVo> result = selectQuestionVosCommon(questionIds, UserQuestionVo::new, option);
        addQuestionVosUserData(result, userId);
        return result;
    }

    /**
     * 查试卷题目QuestionVos，题目无序
     */
    @Override
    public List<QuestionVo> selectPaperQuestionVos(String paperId) {
        return selectPaperQuestionVos(paperId, new SelectQuestionVoOption());
    }

    /**
     * 查试卷题目QuestionVos，题目无序，公式已渲染
     */
    @Override
    public List<QuestionVo> selectRenderedFormulaPaperQuestionVos(String paperId) {
        List<QuestionVo> questionVos = selectPaperQuestionVos(paperId);
        renderedQuestionService.replaceRenderedContent(questionVos, true);
        return questionVos;
    }

    /**
     * 查试卷题目QuestionVos，题目无序
     */
    @Override
    public List<QuestionVo> selectPaperQuestionVos(String paperId, SelectQuestionVoOption option) {
        return selectQuestionVos(selectPaperQuestionIds(paperId), option);
    }

    /**
     * 查试卷题目UserQuestionVos，题目无序
     */
    @Override
    public List<UserQuestionVo> selectPaperUserQuestionVos(String paperId, String userId) {
        return selectPaperUserQuestionVos(paperId, userId, new SelectQuestionVoOption());
    }

    /**
     * 查试卷题目UserQuestionVos，题目无序
     */
    @Override
    public List<UserQuestionVo> selectPaperUserQuestionVos(String paperId, String userId, SelectQuestionVoOption option) {
        return selectUserQuestionVos(selectPaperQuestionIds(paperId), userId, option);
    }

    /**
     * 查多个QuestionVos公共方法
     */
    private <T extends QuestionVo> List<T> selectQuestionVosCommon(Collection<String> questionIds, Supplier<T> constructor, SelectQuestionVoOption option) {
        if (questionIds.isEmpty()) {
            return Collections.emptyList();
        }
        // 题目
        List<SFunction<Question, ?>> selectColumns = Arrays.asList(Question::getId, Question::getGradeLevel, Question::getSubjectId, Question::getQuestionTypeId,
                Question::getDifficult, Question::getScoreRate, Question::getUseCount, Question::getAnswerCount,
                Question::getYear, Question::getScore, Question::getContentHtml, Question::getComment);
        if (option.isAddDivideContent()) {
            selectColumns = new ArrayList<>(selectColumns);
            selectColumns.add(Question::getDivideHtml);
        }
        @SuppressWarnings("unchecked")
        SFunction<Question, ?>[] cols = selectColumns.toArray(new SFunction[0]);

        Map<String, Question> questionMap = questionMapper.selectList(new LambdaQueryWrapper<Question>()
                        .in(Question::getId, questionIds)
                        .select(cols))
                .stream().collect(Collectors.toMap(Question::getId, Function.identity()));
        if (questionMap.isEmpty()) {
            return Collections.emptyList();
        }
        // 小题
        Map<String, List<SmallQuestion>> questionIdSmallQuestionsMap = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                        .in(SmallQuestion::getQuestionId, questionIds))
                .stream().collect(Collectors.groupingBy(SmallQuestion::getQuestionId));
        List<String> smallQuestionIds = questionIdSmallQuestionsMap.values().stream().flatMap(Collection::stream)
                .map(SmallQuestion::getId).collect(Collectors.toList());
        // 知识点
        Map<String, List<SmallQuestionKnowledgeDto>> smallQuestionIdKnowledgeListMap;
        if (!option.isAddKnowledge() || smallQuestionIds.isEmpty()) {
            smallQuestionIdKnowledgeListMap = new HashMap<>();
        } else {
            smallQuestionIdKnowledgeListMap = questionKnowledgeMapper.selectSmallQuestionsKnowledge(smallQuestionIds)
                    .stream().collect(Collectors.groupingBy(SmallQuestionKnowledgeDto::getSmallQuestionId));
        }
        // 章节，只语文英语存在
        List<String> chineseEnglishQuestionIds = questionMap.values().stream()
                .filter(q -> Constants.CHINESE_SUBJECT_ID.equals(q.getSubjectId()) || Constants.ENGLISH_SUBJECT_ID.equals(q.getSubjectId()))
                .map(Question::getId).collect(Collectors.toList());
        Map<String, List<QuestionChapterDto>> questionIdChapterIdsMap;
        if (!option.isAddChapter() || chineseEnglishQuestionIds.isEmpty()) {
            questionIdChapterIdsMap = new HashMap<>();
        } else {
            questionIdChapterIdsMap = questionBookMapper.selectQuestionsChapter(chineseEnglishQuestionIds)
                    .stream().collect(Collectors.groupingBy(QuestionChapterDto::getQuestionId));
        }
        // 听力，只英语存在
        List<String> englishQuestionIds = questionMap.values().stream()
                .filter(q -> Constants.ENGLISH_SUBJECT_ID.equals(q.getSubjectId()))
                .map(Question::getId).collect(Collectors.toList());
        Map<String, List<QuestionListeningVo>> questionListeningMap;
        if (!option.isAddListening() || englishQuestionIds.isEmpty()) {
            questionListeningMap = new HashMap<>();
        } else {
            questionListeningMap = listeningService.getQuestionsListeningList(englishQuestionIds);
        }
        // 关联公共库试卷
        Map<String, List<QuestionPublicPaperDto>> questionIdPublicPapersMap;
        if (!option.isAddPublicPaper()) {
            questionIdPublicPapersMap = new HashMap<>();
        } else {
            questionIdPublicPapersMap = paperQuestionMapper.selectQuestionsPublicPaper(questionIds)
                    .stream().collect(Collectors.groupingBy(QuestionPublicPaperDto::getQuestionId));
        }

        // 组装
        List<T> result = new ArrayList<>();
        for (String questionId : questionIds) {
            Question question = questionMap.get(questionId);
            if (question == null) {
                continue;
            }

            T vo = constructor.get();
            vo.setQuestion(question);
            result.add(vo);

            List<SmallQuestion> smallQuestions = questionIdSmallQuestionsMap.get(questionId);
            if (CollUtil.isNotEmpty(smallQuestions)) {
                smallQuestions.sort(Comparator.comparing(sq -> ObjectUtils.defaultIfNull(sq.getQuestionNo(), 0)));
                for (SmallQuestion sq : smallQuestions) {
                    SmallQuestionVo sqVo = new SmallQuestionVo(sq);
                    vo.getBranches().add(sqVo);

                    List<SmallQuestionKnowledgeDto> knowledgeList = smallQuestionIdKnowledgeListMap.get(sq.getId());
                    if (CollUtil.isNotEmpty(knowledgeList)) {
                        knowledgeList.sort(Comparator.comparing(SmallQuestionKnowledgeDto::getKnowledgeId));
                        for (SmallQuestionKnowledgeDto knowledgeDto : knowledgeList) {
                            SmallQuestionKnowledgeVo smallQuestionKnowledgeVo = new SmallQuestionKnowledgeVo(knowledgeDto.getKnowledgeId(), knowledgeDto.getKnowledgeName());
                            sqVo.getKnowledges().add(smallQuestionKnowledgeVo);
                        }
                    }
                }
            }

            List<QuestionChapterDto> chapterDtoList = questionIdChapterIdsMap.get(questionId);
            if (CollUtil.isNotEmpty(chapterDtoList)) {
                chapterDtoList.sort(Comparator.comparing(QuestionChapterDto::getBookId).thenComparing(QuestionChapterDto::getChapterId));
                for (QuestionChapterDto chapterDto : chapterDtoList) {
                    QuestionChapterVo questionChapterVo = new QuestionChapterVo(chapterDto.getChapterId(), chapterDto.getChapterName(), chapterDto.getBookId());
                    vo.getChapters().add(questionChapterVo);
                }
            }

            List<QuestionListeningVo> listeningVos = questionListeningMap.get(questionId);
            if (CollUtil.isNotEmpty(listeningVos)) {
                vo.setListening(listeningVos);
            }

            List<QuestionPublicPaperDto> publicPaperDtoList = questionIdPublicPapersMap.get(questionId);
            if (CollUtil.isNotEmpty(publicPaperDtoList)) {
                Comparator<QuestionPublicPaperDto> comparator = Comparator.comparing(p -> ObjectUtils.defaultIfNull(p.getYear(), 0));
                comparator = comparator.reversed();
                comparator = comparator.thenComparing(QuestionPublicPaperDto::getPaperId);
                publicPaperDtoList.sort(comparator);
                for (QuestionPublicPaperDto paperDto : publicPaperDtoList) {
                    QuestionReferPaperVo questionReferPaperVo = new QuestionReferPaperVo(paperDto.getPaperId(), paperDto.getPaperName(), paperDto.getYear(), paperDto.getPaperTypeId());
                    vo.getReferPaper().add(questionReferPaperVo);
                }
            }
        }
        return result;
    }

    /**
     * 添加用户题目数据
     */
    private void addQuestionVosUserData(List<UserQuestionVo> questionVos, String userId) {
        if (questionVos.isEmpty()) {
            return;
        }
        List<String> questionIds = questionVos.stream().map(QuestionVo::getId).collect(Collectors.toList());
        // 收藏题目
        Set<String> favoriteQuestionIds = favoriteQuesMapper.selectUserFavoriteQuestionIds(userId, questionIds);
        // 报错题目
        Set<String> correctQuestionIds = questionCorrectMapper.selectList(new LambdaQueryWrapper<QuestionCorrect>()
                        .in(QuestionCorrect::getQuestionId, questionIds)
                        .eq(QuestionCorrect::getUserId, userId)
                        .eq(QuestionCorrect::getStatus, 0)
                        .select(QuestionCorrect::getQuestionId))
                .stream().map(QuestionCorrect::getQuestionId).collect(Collectors.toSet());
        // 已组卷题目
        Set<String> usedQuestionIds = paperMainMapper.queryUsedQuesIds(userId, questionIds);

        for (UserQuestionVo vo : questionVos) {
            vo.setIfFavorite(favoriteQuestionIds.contains(vo.getId()));
            vo.setIfCorrect(correctQuestionIds.contains(vo.getId()));
            vo.setUsed(usedQuestionIds.contains(vo.getId()));
        }
    }

    /**
     * 查试卷题目Id列表
     */
    private List<String> selectPaperQuestionIds(String paperId) {
        return paperQuestionMapper.selectList(new LambdaQueryWrapper<PaperQuestion>()
                        .eq(PaperQuestion::getPaperId, paperId)
                        .select(PaperQuestion::getQuestionId))
                .stream().map(PaperQuestion::getQuestionId).collect(Collectors.toList());
    }

    /**
     * 查出刚刚保存的题目（等待数据库主从同步）
     */
    @Override
    public List<QuestionVo> selectJustSavedQuestionVos(Collection<String> questionIds, SelectQuestionVoOption option) {
        // 先等待数据库主从同步
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        // 再查出数据库中题目
        return selectQuestionVos(questionIds, option);
    }

    /**
     * 查出刚刚保存的题目（等待数据库主从同步）
     */
    @Override
    public List<QuestionVo> selectJustSavedQuestionVos(List<QuestionVo> srcQuestionVos, SelectQuestionVoOption option) {
        List<String> questionIds = srcQuestionVos.stream().map(QuestionVo::getId).collect(Collectors.toList());
        return selectJustSavedQuestionVos(questionIds, option);
    }
}
