package com.sure.question.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.paper.PaperContent;
import com.sure.question.dto.paper.structure.CheckPaperStructureOption;
import com.sure.question.dto.question.CheckQuestionOption;
import com.sure.question.dto.question.SelectQuestionVoOption;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.Question;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.PaperStatusEnum;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.mapper.PaperQuestionMapper;
import com.sure.question.mapper.QuestionEsSyncMapper;
import com.sure.question.mapper.QuestionMapper;
import com.sure.question.service.*;
import com.sure.question.vo.EntryVo;
import com.sure.question.vo.QuesUserVo;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paper.FindPaperPageParam;
import com.sure.question.vo.paper.stat.EditorStatVo;
import com.sure.question.vo.paper.stat.PaperQuestionCountVo;
import com.sure.question.vo.paper.stat.PeriodVo;
import com.sure.question.vo.paperVo.EditorSubmitPaperQuestionStatsVo;
import com.sure.question.vo.question.QuestionVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.sure.question.common.Constants.SYS_USER_ID;

@Service
@RequiredArgsConstructor
public class PaperReviewServiceImpl implements PaperReviewService {
    private final PaperMainMapper paperMainMapper;
    private final QuestionBuildService questionBuildService;
    private final QuestionService questionService;
    private final QuestionMapper questionMapper;
    private final PaperQuestionService paperQuestionService;
    private final PaperService paperService;
    private final PaperQuestionMapper paperQuestionMapper;
    private final QuestionEsSyncMapper questionEsSyncMapper;
    private final FeignService feignService;
    private final TransactionTemplate transactionTemplate;

    /**
     * 分页查询试卷
     */
    private IPage<PaperMain> selectPaperPage(LambdaQueryWrapper<PaperMain> queryWrapper, int page, int size) {
        queryWrapper.orderByDesc(PaperMain::getUpdateTime);
        return paperMainMapper.selectPage(new Page<>(page, size), queryWrapper);
    }

    /**
     * 查待分配试卷
     */
    @Override
    public IPage<PaperMain> getUnassigned(FindPaperPageParam param) {
        LambdaQueryWrapper<PaperMain> lambdaQueryWrapper = param.buildPaperQueryWrapper();
        addUnassignedCondition(lambdaQueryWrapper);
        return selectPaperPage(lambdaQueryWrapper, param.getPage(), param.getSize());
    }

    /**
     * 待分配条件
     */
    private void addUnassignedCondition(LambdaQueryWrapper<PaperMain> queryWrapper) {
        queryWrapper.eq(PaperMain::getPaperBank, PaperBankEnum.Personal.getCode().toString())
                .eq(PaperMain::getUserId, SYS_USER_ID)
                .eq(PaperMain::getShowStatus, PaperStatusEnum.AllotStatus1.getCode())
                .isNull(PaperMain::getEditorId);
    }

    /**
     * 查已分配待提交试卷
     */
    @Override
    public IPage<PaperMain> getAssignedAndUnSubmitted(FindPaperPageParam param) {
        LambdaQueryWrapper<PaperMain> lambdaQueryWrapper = param.buildPaperQueryWrapper();
        addAssignedUnSubmittedCondition(lambdaQueryWrapper);
        return selectPaperPage(lambdaQueryWrapper, param.getPage(), param.getSize());
    }

    /**
     * 已分配待提交条件
     */
    private void addAssignedUnSubmittedCondition(LambdaQueryWrapper<PaperMain> queryWrapper) {
        queryWrapper.eq(PaperMain::getPaperBank, PaperBankEnum.Personal.getCode().toString())
                .eq(PaperMain::getUserId, SYS_USER_ID)
                .eq(PaperMain::getShowStatus, PaperStatusEnum.AllotStatus2.getCode())
                .eq(PaperMain::getShareStatus, PaperStatusEnum.ShareStatus0.getCode())
                .isNotNull(PaperMain::getEditorId);
    }

    /**
     * 查已提交待发布试卷
     */
    @Override
    public IPage<PaperMain> getSubmittedAndUnpublished(FindPaperPageParam param) {
        LambdaQueryWrapper<PaperMain> lambdaQueryWrapper = param.buildPaperQueryWrapper();
        addSubmittedUnpublishedCondition(lambdaQueryWrapper);
        return selectPaperPage(lambdaQueryWrapper, param.getPage(), param.getSize());
    }

    /**
     * 已提交待发布条件
     */
    private void addSubmittedUnpublishedCondition(LambdaQueryWrapper<PaperMain> queryWrapper) {
        queryWrapper.eq(PaperMain::getPaperBank, PaperBankEnum.Personal.getCode().toString())
                .eq(PaperMain::getUserId, SYS_USER_ID)
                .eq(PaperMain::getShareStatus, PaperStatusEnum.ShareStatus1.getCode())
                .isNotNull(PaperMain::getEditorId);
    }

    /**
     * 查个人未提交试卷
     */
    @Override
    public IPage<PaperMain> getSelfUnsubmitted(FindPaperPageParam param, String userId) {
        LambdaQueryWrapper<PaperMain> lambdaQueryWrapper = param.buildPaperQueryWrapper()
                .eq(PaperMain::getEditorId, userId);
        addAssignedUnSubmittedCondition(lambdaQueryWrapper);
        return selectPaperPage(lambdaQueryWrapper, param.getPage(), param.getSize());
    }

    /**
     * 查个人已提交试卷
     */
    @Override
    public IPage<PaperMain> getSelfSubmitted(FindPaperPageParam param, String userId) {
        // 含已发布试卷
        LambdaQueryWrapper<PaperMain> lambdaQueryWrapper = param.buildPaperQueryWrapper()
                .eq(PaperMain::getEditorId, userId)
                .eq(PaperMain::getShowStatus, PaperStatusEnum.AllotStatus2.getCode())
                .eq(PaperMain::getShareStatus, PaperStatusEnum.ShareStatus1.getCode())
                .eq(PaperMain::getUserId, SYS_USER_ID);
        return selectPaperPage(lambdaQueryWrapper, param.getPage(), param.getSize());
    }

    /**
     * 分配审核任务
     */
    @Override
    public void assign(String editorId, List<String> paperIds) {
        checkPaperStatus(paperIds, this::addUnassignedCondition);
        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .set(PaperMain::getEditorId, editorId)
                .set(PaperMain::getShowStatus, PaperStatusEnum.AllotStatus2.getCode().toString()));
    }

    /**
     * 检查试卷状态
     */
    private void checkPaperStatus(List<String> paperIds, Consumer<LambdaQueryWrapper<PaperMain>> addConditionFunction) {
        LambdaQueryWrapper<PaperMain> queryWrapper = new LambdaQueryWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .eq(PaperMain::getStatus, PaperStatusEnum.Status0.getCode());
        addConditionFunction.accept(queryWrapper);
        int paperCount = paperMainMapper.selectCount(queryWrapper);
        if (paperCount != paperIds.size()) {
            throw new ApiException("试卷状态已变化，请刷新页面后重试");
        }
    }

    /**
     * 取消分配任务
     */
    @Override
    public void removeAssignment(List<String> paperIds) {
        checkPaperStatus(paperIds, this::addAssignedUnSubmittedCondition);
        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .set(PaperMain::getEditorId, null)
                .set(PaperMain::getShowStatus, PaperStatusEnum.AllotStatus1.getCode().toString()));
    }

    /**
     * 提交审核试卷
     */
    @Override
    public void submit(String paperId, boolean allowNoScore, TokenUserVo userVo) {
        getReviewPaperCheckPermissionAndEditable(paperId, userVo);
        paperService.checkPaperAndQuestionsFinished(paperId, allowNoScore);
        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .set(PaperMain::getShareStatus, PaperStatusEnum.ShareStatus1.getCode().toString())
                .set(PaperMain::getSubmitTime, System.currentTimeMillis()));
    }

    /**
     * 驳回提交
     */
    @Override
    public void rejectSubmission(List<String> paperIds) {
        checkPaperStatus(paperIds, this::addSubmittedUnpublishedCondition);
        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .set(PaperMain::getShareStatus, PaperStatusEnum.ShareStatus0.getCode().toString()));
    }

    /**
     * 发布至公共库
     */
    @Override
    public void publish(String paperId, boolean allowNoScore) {
        PaperMain paperMain = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .eq(PaperMain::getStatus, PaperStatusEnum.Status0.getCode())
                .eq(PaperMain::getPaperBank, PaperBankEnum.Personal.getCode().toString()));
        if (paperMain == null) {
            throw new ApiException("试卷状态已变化，请刷新页面后重试");
        }

        // 检查试卷与题目是否完整
        paperService.checkPaperAndQuestionsFinished(paperId, allowNoScore);

        LambdaUpdateWrapper<PaperMain> paperUpdateWrapper = new LambdaUpdateWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .set(PaperMain::getPaperBank, PaperBankEnum.Public.getCode().toString())
                .set(PaperMain::getUpdateTime, System.currentTimeMillis());

        // 初始化试卷浏览量和下载量
        initBrowseCountAddAndDownloadCountAdd(paperMain, paperUpdateWrapper);

        // 初始化试卷题目组卷次数
        List<String> questionIds = paperQuestionMapper.selectPaperQuestionIds(paperId);
        List<LambdaUpdateWrapper<Question>> questionUpdateWrappers = initPaperQuestionsUseCount(questionIds);

        // 保存
        transactionTemplate.execute(status -> {
            paperMainMapper.update(null, paperUpdateWrapper);
            questionUpdateWrappers.forEach(questionUpdateWrapper -> questionMapper.update(null, questionUpdateWrapper));
            // 同步es
            questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);
            return null;
        });
    }

    /**
     * 初始化试卷浏览量和下载量
     */
    private void initBrowseCountAddAndDownloadCountAdd(PaperMain paperMain, LambdaUpdateWrapper<PaperMain> updateWrapper) {
        if (paperMain.getBrowseCountAdd() != null || paperMain.getDownloadCountAdd() != null) {
            return;
        }
        int browseCountAdd = RandomUtil.randomInt(0, 501);
        int downloadCountAddPercent = RandomUtil.randomInt(0, 16);
        float rate = (float) downloadCountAddPercent / 100;
        int downloadCountAdd = (int) (browseCountAdd * rate);

        Integer browseCount = paperMain.getBrowseCount();
        Integer downloadCount = paperMain.getDownloadCount();
        if (browseCount == null) {
            browseCount = 0;
        }
        if (downloadCount == null) {
            downloadCount = 0;
        }
        browseCount += browseCountAdd;
        downloadCount += downloadCountAdd;

        updateWrapper.set(PaperMain::getBrowseCount, browseCount)
                .set(PaperMain::getDownloadCount, downloadCount)
                .set(PaperMain::getBrowseCountAdd, browseCountAdd)
                .set(PaperMain::getDownloadCountAdd, downloadCountAdd);
    }

    /**
     * 初始化试卷题目组卷次数
     */
    private List<LambdaUpdateWrapper<Question>> initPaperQuestionsUseCount(List<String> questionIds) {
        List<LambdaUpdateWrapper<Question>> updateWrappers = new ArrayList<>();
        Random random = new Random();
        List<Question> questions = questionMapper.selectList(new LambdaQueryWrapper<Question>()
                .in(Question::getId, questionIds)
                .select(Question::getId, Question::getUseCount, Question::getUseCountAdd));
        for (Question question : questions) {
            if (question.getUseCountAdd() != null) {
                continue;
            }

            // 组卷次数增加量，40%概率1-10，30%概率11-20，20%概率21-30，10%概率31-40
            int useCountAdd;
            double randomNumber = random.nextDouble();
            if (randomNumber < 0.4) {
                useCountAdd = random.nextInt(10) + 1;
            } else if (randomNumber < 0.7) {
                useCountAdd = random.nextInt(10) + 11;
            } else if (randomNumber < 0.9) {
                useCountAdd = random.nextInt(10) + 21;
            } else {
                useCountAdd = random.nextInt(10) + 31;
            }

            // 组卷次数
            Integer useCount = question.getUseCount();
            if (useCount == null) {
                useCount = 0;
            }
            useCount += useCountAdd;

            updateWrappers.add(new LambdaUpdateWrapper<Question>()
                    .eq(Question::getId, question.getId())
                    .set(Question::getUseCount, useCount)
                    .set(Question::getUseCountAdd, useCountAdd));
        }
        return updateWrappers;
    }


    /**
     * 查出试卷并检查权限
     */
    private PaperMain getReviewPaperCheckPermission(String paperId, TokenUserVo userVo) {
        PaperMain paper = paperMainMapper.selectById(paperId);
        if (paper == null || !PaperStatusEnum.Status0.getCode().toString().equals(paper.getStatus())) {
            throw new ApiException("参数错误：找不到试卷");
        }
        if (!StringUtils.equalsIgnoreCase(SYS_USER_ID, paper.getUserId())) {
            throw new ApiException("非审核试卷");
        }
        // 若非题库管理员，则必须分配了该试卷审核任务
        if (!userVo.isSysManager()) {
            // 本人审卷任务
            if (!StringUtils.equals(userVo.getUserId(), paper.getEditorId()) || !PaperStatusEnum.AllotStatus2.getCode().toString().equals(paper.getShowStatus())) {
                throw new ApiException("非本人任务");
            }
        }
        return paper;
    }

    /**
     * 查出试卷并检查权限，且未提交
     */
    private PaperMain getReviewPaperCheckPermissionAndEditable(String paperId, TokenUserVo userVo) {
        PaperMain paper = getReviewPaperCheckPermission(paperId, userVo);
        if (!PaperBankEnum.Personal.getCode().toString().equals(paper.getPaperBank())) {
            throw new ApiException("非个人库试卷");
        }
        // 非题库管理员，提交后不能修改
        if (!userVo.isSysManager() && !PaperStatusEnum.ShareStatus0.getCode().toString().equals(paper.getShareStatus())) {
            throw new ApiException("试卷已提交");
        }
        return paper;
    }

    /**
     * 撤回发布至公共库
     */
    @Override
    public void unpublish(String paperId) {
        PaperMain paperMain = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .eq(PaperMain::getStatus, PaperStatusEnum.Status0.getCode())
                .eq(PaperMain::getPaperBank, PaperBankEnum.Public.getCode().toString()));
        if (paperMain == null) {
            throw new ApiException("试卷状态已变化，请刷新页面后重试");
        }

        List<String> questionIds = paperQuestionMapper.selectPaperQuestionIds(paperId);

        transactionTemplate.execute(status -> {
            paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                    .eq(PaperMain::getId, paperId)
                    .set(PaperMain::getPaperBank, PaperBankEnum.Personal.getCode().toString())
                    .set(PaperMain::getUpdateTime, System.currentTimeMillis()));
            // 同步es
            questionEsSyncMapper.insertPendingQuestionsBatch(questionIds);
            return null;
        });
    }

    /**
     * 审卷获取试卷内容
     */
    @Override
    public PaperContent getReviewPaperContent(String paperId, TokenUserVo userVo) {
        PaperMain paperMain = getReviewPaperCheckPermission(paperId, userVo);

        PaperContent result = new PaperContent();
        result.setInfo(paperMain);
        result.setStructureJson(paperMain.getPaperStyleJson());
        result.setQuestions(questionBuildService.selectPaperQuestionVos(paperId, SelectQuestionVoOption.addAll()));

        paperMain.setUserId(null);
        paperMain.setSchoolId(null);
        paperMain.setUploadFile(null);
        paperMain.setPaperLocation(null);
        paperMain.setPaperStyleJson(null);

        return result;
    }

    /**
     * 审卷添加题目
     */
    @Override
    public List<QuestionVo> addReviewPaperQuestions(String paperId, String paperStructure, List<QuestionVo> questionVos, TokenUserVo userVo) {
        PaperMain paper = getReviewPaperCheckPermissionAndEditable(paperId, userVo);
        questionService.addPaperQuestions(questionVos, CheckQuestionOption.skipAll(), paper, paperStructure, CheckPaperStructureOption.allowScoreNull());
        return questionBuildService.selectJustSavedQuestionVos(questionVos, SelectQuestionVoOption.addAll());
    }

    /**
     * 审卷修改题目
     */
    @Override
    public List<QuestionVo> changeReviewPaperQuestions(String paperId, String paperStructure, List<QuestionVo> questionVos, TokenUserVo userVo) {
        getReviewPaperCheckPermissionAndEditable(paperId, userVo);
        questionService.changePaperQuestions(questionVos, CheckQuestionOption.skipAll(), false, paperId, paperStructure, CheckPaperStructureOption.allowScoreNull());
        return questionBuildService.selectJustSavedQuestionVos(questionVos, SelectQuestionVoOption.addAll());
    }

    /**
     * 审卷删除题目
     */
    @Override
    public void deleteReviewPaperQuestions(String paperId, String paperStructure, List<String> questionIds, TokenUserVo userVo) {
        getReviewPaperCheckPermissionAndEditable(paperId, userVo);
        questionService.deletePaperQuestions(questionIds, paperId, paperStructure, CheckPaperStructureOption.allowScoreNull());
    }

    /**
     * 审卷修改试卷结构
     */
    @Override
    public void changeReviewPaperStructure(String paperId, String paperStructure, TokenUserVo userVo) {
        getReviewPaperCheckPermissionAndEditable(paperId, userVo);
        paperQuestionService.checkPaperStructure(paperId, paperStructure, CheckPaperStructureOption.allowScoreNull(), null, null, null);
        paperMainMapper.updatePaperStructure(paperId, paperStructure);
    }


    /**
     * 题库管理员查看编辑审核试卷统计
     */
    @Override
    public List<EditorStatVo> getEditorsReviewPaperStats(Integer gradeLevel, Integer subjectId) {
        List<EditorStatVo> editors = getEditorsPaperStats(gradeLevel, subjectId, null, this::selectEditorViewPaperStats);
        // 添加已分配量
        if (!editors.isEmpty()) {
            List<String> editorIds = editors.stream().map(EditorStatVo::getEditorId).collect(Collectors.toList());
            Map<String, Long> editorAllotViewPaperTaskMap = paperQuestionMapper.selectEditorAllotViewPaperTaskList(editorIds, gradeLevel, subjectId)
                    .stream().collect(Collectors.toMap(entry -> entry.getKey().toLowerCase(), EntryVo::getValue));
            editors.forEach(editor -> {
                Long count = editorAllotViewPaperTaskMap.get(editor.getEditorId());
                if (count != null) {
                    editor.setAssignedPaperCount(count.intValue());
                }
            });
        }
        return editors;
    }

    /**
     * 编辑员查看个人看题统计
     */
    @Override
    public EditorStatVo getEditorSelfReviewPaperStats(Integer gradeLevel, Integer subjectId, String userId) {
        EditorStatVo vo = getEditorSelfPaperStats(gradeLevel, subjectId, null, userId, this::selectEditorViewPaperStats);
        vo.setAssignedPaperCount(paperMainMapper.getEditorAllotPaperCount(userId, subjectId, gradeLevel));
        return vo;
    }

    private List<EditorSubmitPaperQuestionStatsVo> selectEditorViewPaperStats(List<String> editorIds, Integer gradeLevel, Integer subjectId, String bookType) {
        return paperQuestionMapper.selectEditorViewPaperStats(editorIds, gradeLevel, subjectId);
    }

    /**
     * 题库管理员查看编辑组卷统计
     */
    @Override
    public List<EditorStatVo> getEditorsMakePaperStats(Integer gradeLevel, Integer subjectId, String bookType) {
        return getEditorsPaperStats(gradeLevel, subjectId, bookType, paperQuestionMapper::selectEditorMakePaperStats);
    }

    /**
     * 编辑员查看个人组卷统计
     */
    @Override
    public EditorStatVo getEditorSelfMakePaperStats(Integer gradeLevel, Integer subjectId, String bookType, String userId) {
        return getEditorSelfPaperStats(gradeLevel, subjectId, bookType, userId, paperQuestionMapper::selectEditorMakePaperStats);
    }

    /**
     * 题库管理员查看编辑上传统计
     */
    @Override
    public List<EditorStatVo> getEditorsUploadPaperStats(Integer gradeLevel, Integer subjectId, String bookType) {
        return getEditorsPaperStats(gradeLevel, subjectId, bookType, paperQuestionMapper::selectEditorUploadPaperStats);
    }

    /**
     * 编辑员查看个人上传统计
     */
    @Override
    public EditorStatVo getEditorSelfUploadPaperStats(Integer gradeLevel, Integer subjectId, String bookType, String userId) {
        return getEditorSelfPaperStats(gradeLevel, subjectId, bookType, userId, paperQuestionMapper::selectEditorUploadPaperStats);
    }

    private List<EditorStatVo> getEditorsPaperStats(Integer gradeLevel, Integer subjectId, String bookType, ISelectEditorsPaperStatsFunction selectEditorsPaperStatsFunction) {
        // 查系统所有编辑员
        List<QuesUserVo> editorUsers = feignService.getEditorList(null, null);
        if (editorUsers.isEmpty()) {
            return Collections.emptyList();
        }
        List<EditorStatVo> editors = editorUsers.stream().map(user -> {
            EditorStatVo vo = new EditorStatVo();
            vo.setEditorId(user.getId());
            vo.setEditorName(user.getRealName());
            return vo;
        }).collect(Collectors.toList());

        // 查统计数据
        List<String> editorIds = editors.stream().map(EditorStatVo::getEditorId).collect(Collectors.toList());
        List<EditorSubmitPaperQuestionStatsVo> editorMakePaperStats = selectEditorsPaperStatsFunction.apply(editorIds, gradeLevel, subjectId, ensureBookType(bookType));
        Map<String, List<EditorSubmitPaperQuestionStatsVo>> editorStatsMap = editorMakePaperStats.stream().collect(Collectors.groupingBy(EditorSubmitPaperQuestionStatsVo::getEditorId));

        // 计算各编辑员各时间段内统计
        List<PeriodVo> periods = buildPeriods();
        editors.forEach(editor -> {
            List<EditorSubmitPaperQuestionStatsVo> editorStats = editorStatsMap.get(editor.getEditorId());
            if (editorStats != null) {
                calcStatsByPeriod(editor, editorStats, periods);
            }
        });

        // 非指定学段学科编辑员，且总量为0的，剔除
        List<String> gradeLevelSubjectEditorIds = feignService.getEditorList(gradeLevel, subjectId).stream().map(QuesUserVo::getId).collect(Collectors.toList());
        editors.removeIf(vo -> vo.getTotal().getPaperCount() == 0 && !gradeLevelSubjectEditorIds.contains(vo.getEditorId()));

        // 按已提交试卷倒序排
        editors.sort((a, b) -> b.getTotal().getPaperCount() - a.getTotal().getPaperCount());
        return editors;
    }

    private EditorStatVo getEditorSelfPaperStats(Integer gradeLevel, Integer subjectId, String bookType, String userId, ISelectEditorsPaperStatsFunction selectEditorsPaperStatsFunction) {
        EditorStatVo vo = new EditorStatVo();
        vo.setEditorId(userId);
        List<EditorSubmitPaperQuestionStatsVo> editorStats = selectEditorsPaperStatsFunction.apply(Collections.singletonList(userId), gradeLevel, subjectId, ensureBookType(bookType));
        calcStatsByPeriod(vo, editorStats, buildPeriods());
        return vo;
    }

    private interface ISelectEditorsPaperStatsFunction {
        List<EditorSubmitPaperQuestionStatsVo> apply(List<String> editorIds, Integer gradeLevel, Integer subjectId, String bookType);
    }

    private String ensureBookType(String bookType) {
        if (StringUtils.isNotEmpty(bookType)) {
            if (StringUtils.equalsAnyIgnoreCase(bookType, "normal", "coach")) {
                return bookType.toLowerCase();
            }
        }
        return null;
    }

    private List<PeriodVo> buildPeriods() {
        Date now = new Date();
        Date today = DateUtil.beginOfDay(now);
        Date yesterday = DateUtil.offsetDay(today, -1);
        Date thisWeek = DateUtil.beginOfWeek(now);
        Date lastWeek = DateUtil.offsetWeek(thisWeek, -1);
        Date thisMonth = DateUtil.beginOfMonth(now);
        Date lastMonth = DateUtil.offsetMonth(thisMonth, -1);

        List<PeriodVo> periods = new ArrayList<>();
        periods.add(new PeriodVo(0L, now.getTime(), EditorStatVo::getTotal));
        periods.add(new PeriodVo(today.getTime(), now.getTime(), EditorStatVo::getToday));
        periods.add(new PeriodVo(yesterday.getTime(), today.getTime(), EditorStatVo::getYesterday));
        periods.add(new PeriodVo(thisWeek.getTime(), now.getTime(), EditorStatVo::getThisWeek));
        periods.add(new PeriodVo(lastWeek.getTime(), thisWeek.getTime(), EditorStatVo::getLastWeek));
        periods.add(new PeriodVo(thisMonth.getTime(), now.getTime(), EditorStatVo::getThisMonth));
        periods.add(new PeriodVo(lastMonth.getTime(), thisMonth.getTime(), EditorStatVo::getLastMonth));
        return periods;
    }

    private void calcStatsByPeriod(EditorStatVo view, List<EditorSubmitPaperQuestionStatsVo> statsVoList, List<PeriodVo> periods) {
        statsVoList.forEach(vo -> periods.forEach(period -> {
            if (vo.getSubmitTime() >= period.getBeginTime() && vo.getSubmitTime() < period.getEndTime()) {
                PaperQuestionCountVo count = period.getGetPeriodFunc().apply(view);
                count.setPaperCount(count.getPaperCount() + 1);
                count.setQuestionCount(count.getQuestionCount() + vo.getQuestionCount());
                count.setSmallQuestionCount(count.getSmallQuestionCount() + vo.getSmallQuestionCount());
            }
        }));
    }
}
