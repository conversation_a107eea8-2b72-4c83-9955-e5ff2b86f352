package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.CoachBook;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.SchoolPaper;
import com.sure.question.enums.SchoolBankSourceEnum;
import com.sure.question.mapper.CoachBookMapper;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.mapper.QuestionMapper;
import com.sure.question.mapper.SchoolPaperMapper;
import com.sure.question.service.FeignService;
import com.sure.question.service.QuestionSearchService;
import com.sure.question.service.SchoolBankService;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paperVo.SchoolPaperVo;
import com.sure.question.vo.question.FindQuestionPageParam;
import com.sure.question.vo.question.UserQuestionVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class SchoolBankServiceImpl implements SchoolBankService {
    private final PaperMainMapper paperMainMapper;
    private final QuestionMapper questionMapper;
    private final QuestionSearchService questionSearchService;
    private final SchoolPaperMapper schoolPaperMapper;
    private final CoachBookMapper coachBookMapper;
    private final FeignService feignService;

    @Override
    public IPage<SchoolPaperVo> getPapers(TokenUserVo userVo,
                                          Integer stage, Integer grade, Integer term, Integer subject,
                                          Integer type, Integer year, String region,
                                          Integer page, Integer size) {
        IPage<SchoolPaperVo> paperPage = schoolPaperMapper.getSchoolPapers(new Page<>(page, size), userVo.getSchoolId(),
                stage, grade, term, subject, type, year, region);
        if (!paperPage.getRecords().isEmpty()) {
            addSchoolPaperVoUserNameAndSchoolNames(paperPage.getRecords());
        }
        return paperPage;
    }

    private void addSchoolPaperVoUserNameAndSchoolNames(List<SchoolPaperVo> records) {
        List<String> userIds = records.stream().map(SchoolPaperVo::getCreateUserId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (!userIds.isEmpty()) {
            Map<String, String> userIdNameMobileMap = feignService.getUserIdNameMobileMap(userIds);
            records.forEach(vo -> vo.setCreateUserName(userIdNameMobileMap.get(vo.getCreateUserId())));
        }
        List<String> schoolIds = records.stream().map(SchoolPaperVo::getSchoolId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (!schoolIds.isEmpty()) {
            Map<String, String> schoolIdNameMap = feignService.getSchoolIdNameMap(schoolIds);
            records.forEach(vo -> vo.setSchoolName(schoolIdNameMap.get(vo.getSchoolId())));
        }
    }

    /**
     * 校本题库教材选题或知识点选题
     */
    @Override
    public IPage<UserQuestionVo> findQuestionByChapterOrKnowledge(TokenUserVo userVo, FindQuestionPageParam param) {
        if (!questionSearchService.normalizeFindQuestionPageParam(param)) {
            return new Page<>(param.getPage(), param.getSize());
        }
        List<String> questionIds = questionMapper.selectSchoolPaperQuestionIds(userVo.getSchoolId(), param.getStage(), param.getSubject(),
                param.getSource(), param.getRegionPrefix(), param.getYear(),
                param.getDifficulty(), param.getQues_type(),
                param.getSelfAndDescendantChapterIds(), param.getSelfAndDescendantKnowledgeIds());
        return questionSearchService.buildQuesSearchVoPage(questionIds, param.getPage(), param.getSize(), userVo.getUserId());
    }

    @Override
    public void sharePaper(String paperId, String schoolId, String userId) {
        List<SchoolPaper> dbSchoolPapers = schoolPaperMapper.selectList(new LambdaQueryWrapper<SchoolPaper>()
                .eq(SchoolPaper::getSchoolId, schoolId)
                .eq(SchoolPaper::getPaperId, paperId));
        if (!dbSchoolPapers.stream().allMatch(SchoolPaper::getIsDelete)) {
            throw new ApiException("学校卷库中已存在该卷");
        }

        SchoolPaper deletedPaper = dbSchoolPapers.stream().filter(p -> userId.equals(p.getCreateId())).findFirst().orElse(null);
        if (deletedPaper != null) {
            deletedPaper.setIsDelete(false);
            deletedPaper.setDeleteId(null);
            deletedPaper.setDeleteTime(null);
            schoolPaperMapper.updateById(deletedPaper);
        } else {
            SchoolPaper newSchoolPaper = SchoolPaper.builder()
                    .schoolId(schoolId)
                    .paperId(paperId)
                    .source(SchoolBankSourceEnum.Share.getId())
                    .isPublic(true)
                    .createId(userId)
                    .createTime(new Date())
                    .build();
            schoolPaperMapper.insert(newSchoolPaper);
        }
    }

    @Override
    public IPage<SchoolPaperVo> getShareRecords(String userId, String schoolId, Integer gradeLevel, Integer gradeId, Integer term, Integer subjectId,
                                                Integer paperTypeId, Integer year, String regionId, String bookType, Integer flagCoachBookId,
                                                Integer paperGroupId, Integer page, Integer size) {
        IPage<SchoolPaperVo> paperPage = schoolPaperMapper.getShareRecords(new Page<>(page, size),
                userId, schoolId, gradeLevel, gradeId, term, subjectId, paperTypeId, year, regionId, bookType, flagCoachBookId, paperGroupId);
        if (!paperPage.getRecords().isEmpty()) {
            addSchoolPaperVoUserNameAndSchoolNames(paperPage.getRecords());
            setFlagCoachBookName(paperPage.getRecords());
        }
        return paperPage;
    }

    private void setFlagCoachBookName(List<SchoolPaperVo> list) {
        List<Integer> flagCoachBookIds = list.stream().map(SchoolPaperVo::getFlagCoachBookId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (flagCoachBookIds.isEmpty()) {
            return;
        }
        Map<Integer, String> coachBookIdCoachBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                        .in(CoachBook::getId, flagCoachBookIds)
                        .select(CoachBook::getId, CoachBook::getBookName))
                .stream().collect(Collectors.toMap(CoachBook::getId, CoachBook::getBookName));
        list.forEach(vo -> {
            if (vo.getFlagCoachBookId() != null) {
                vo.setFlagCoachBookName(coachBookIdCoachBookNameMap.get(vo.getFlagCoachBookId()));
            }
        });
    }

    @Override
    public void withdrawPaper(String paperId, String schoolId, String userId) {
        int deletedCount = schoolPaperMapper.delete(new LambdaQueryWrapper<SchoolPaper>()
                .eq(SchoolPaper::getPaperId, paperId)
                .eq(SchoolPaper::getSchoolId, schoolId)
                .eq(SchoolPaper::getCreateId, userId));
        if (deletedCount == 0) {
            throw new ApiException("试卷不存在");
        }
    }

    @Override
    public void deletePapers(List<String> paperIds, String schoolId, String userId) {
        schoolPaperMapper.updateToDeleted(paperIds, schoolId, userId);
    }

    @Override
    public void recoverPapers(List<String> paperIds, String schoolId) {
        // 检查恢复后是否引起同一份卷存在多条未删除记录
        List<SchoolPaper> collisionPapers = schoolPaperMapper.selectList(new LambdaQueryWrapper<SchoolPaper>()
                .eq(SchoolPaper::getSchoolId, schoolId)
                .eq(SchoolPaper::getIsDelete, false)
                .in(SchoolPaper::getPaperId, paperIds)
                .select(SchoolPaper::getPaperId));
        if (!collisionPapers.isEmpty()) {
            List<String> collisionPaperIds = collisionPapers.stream().map(SchoolPaper::getPaperId).collect(Collectors.toList());
            List<PaperMain> paperMains = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>().in(PaperMain::getId, collisionPaperIds).select(PaperMain::getPaperName));
            List<String> collisionPaperNames = paperMains.stream().map(PaperMain::getPaperName).collect(Collectors.toList());
            throw new ApiException("以下试卷已存在：\n" + StringUtils.join(collisionPaperNames, "\n"));
        }
        schoolPaperMapper.updateToUnDeleted(paperIds, schoolId);
    }

    @Override
    public void changePapersPublic(List<String> paperIds, String schoolId, boolean isPublic) {
        int updatedRowCount = schoolPaperMapper.updateIsPublicBatch(paperIds, schoolId, isPublic);
        if (updatedRowCount == 0) {
            throw new ApiException("参数错误");
        }
    }

    @Override
    public void paperViewIncr(Integer paperShareId) {
        schoolPaperMapper.paperViewIncr(paperShareId);
    }

    @Override
    public void paperDownIncr(Integer paperShareId) {
        schoolPaperMapper.paperDownIncr(paperShareId);
    }
}
