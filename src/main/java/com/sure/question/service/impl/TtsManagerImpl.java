package com.sure.question.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.nls.client.AccessToken;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.OutputFormatEnum;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizer;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerListener;
import com.alibaba.nls.client.protocol.tts.SpeechSynthesizerResponse;
import com.sure.common.exception.ApiException;
import com.sure.question.service.TtsManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;

@Slf4j
@Service
public class TtsManagerImpl implements TtsManager {
    private NlsClient client;
    private long accessTokenExpireTime;

    @Value("${aliyun.tts.appKey}")
    private String appKey;
    @Value("${aliyun.tts.accessKeyId}")
    private String accessKeyId;
    @Value("${aliyun.tts.accessKeySecret}")
    private String accessKeySecret;
    @Value("${aliyun.tts.url}")
    private String url;
    @Value("${tempPath.listeningTts}")
    private String listeningTtsTempSaveDirectory;

    @Override
    public String process(String text, String voice, int speechRate) {
        // 创建client
        ensureClient();

        SpeechSynthesizer synthesizer = null;
        FileUtil.mkdir(this.listeningTtsTempSaveDirectory);
        String path = this.listeningTtsTempSaveDirectory + RandomUtil.randomString(32) + ".mp3";
        try {
            // 创建实例，建立连接
            synthesizer = new SpeechSynthesizer(this.client, getSynthesizerListener(path));
            synthesizer.setAppKey(this.appKey);
            // 设置返回音频的编码格式
            synthesizer.setFormat(OutputFormatEnum.MP3);
            // 设置发音人
            synthesizer.setVoice(voice);
            // 设置语速
            synthesizer.setSpeechRate(speechRate);
            // 设置用于语音合成的文本
            if (text.length() > 300) {
                synthesizer.setLongText(text);
            } else {
                synthesizer.setText(text);
            }
            // 此方法将以上参数设置序列化为JSON发送给服务端，并等待服务端确认
            synthesizer.start();
            // 等待语音合成结束
            synthesizer.waitForComplete();
            return path;
        } catch (Exception e) {
            log.error("语音合成失败! text: {}\n", text, e);
            throw new ApiException("语音合成失败! " + e.getMessage());
        } finally {
            //关闭连接
            if (synthesizer != null) {
                synthesizer.close();
            }
        }
    }

    private void ensureClient() {
        if (this.client != null && System.currentTimeMillis() < this.accessTokenExpireTime) {
            return;
        }
        synchronized (TtsManagerImpl.class) {
            if (this.client != null && System.currentTimeMillis() < this.accessTokenExpireTime) {
                return;
            }
            if (this.client != null) {
                this.client.shutdown();
            }
            try {
                AccessToken accessToken = new AccessToken(this.accessKeyId, this.accessKeySecret);
                accessToken.apply();
                // AccessToken.ExpireTime: 令牌的有效期时间戳，单位为秒；提前1小时更新token
                this.accessTokenExpireTime = (accessToken.getExpireTime() - 3600) * 1000;
                log.info("阿里云语音合成Token过期时间戳: {}, 下次更新Token时间: {}", accessToken.getExpireTime(), DateUtil.date(this.accessTokenExpireTime).toString("yyyy-MM-dd HH:mm:ss"));
                this.client = new NlsClient(this.url, accessToken.getToken());
            } catch (IOException e) {
                log.error("请求语音合成失败，令牌无效！accessKeyId={}，accessKeySecret={}", this.accessKeyId, this.accessKeySecret);
                throw new ApiException("请求语音合成失败，令牌无效");
            }
        }
    }

    private SpeechSynthesizerListener getSynthesizerListener(String path) {
        SpeechSynthesizerListener listener;
        try {
            listener = new SpeechSynthesizerListener() {
                final FileOutputStream fileOutputStream = new FileOutputStream(path);

                // 接收到语音二进制数据
                @Override
                public void onMessage(ByteBuffer message) {
                    try {
                        byte[] bytesArray = new byte[message.remaining()];
                        message.get(bytesArray);
                        fileOutputStream.write(bytesArray);
                    } catch (IOException e) {
                        log.error("写入语音数据失败! File: {}\n", path, e);
                        throw new ApiException("保存语音合成数据失败");
                    }
                }

                // 语音合成成功
                @Override
                public void onComplete(SpeechSynthesizerResponse response) {
                    try {
                        fileOutputStream.close();
                    } catch (IOException e) {
                        log.error("写入语音数据失败! File: {}\n", path, e);
                        throw new ApiException("保存语音合成数据失败");
                    }
                    log.info("语音合成成功, name: {}, status: {}, path: {}", response.getName(), response.getStatus(), path);
                }

                // 语音合成失败
                @Override
                public void onFail(SpeechSynthesizerResponse response) {
                    log.error("语音合成失败, task_id: {}, status: {}, status_text: {}", response.getTaskId(), response.getStatus(), response.getStatusText());
                }
            };
        } catch (Exception e) {
            log.error("语音合成失败! File: {}\n", path, e);
            throw new ApiException("语音合成失败！" + e.getMessage());
        }
        return listener;
    }
}
