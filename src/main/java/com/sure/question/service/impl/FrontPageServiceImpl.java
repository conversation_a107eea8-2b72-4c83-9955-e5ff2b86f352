package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.common.IdWorker;
import com.sure.question.entity.FrontPageImage;
import com.sure.question.entity.FrontPagePaper;
import com.sure.question.entity.PaperMain;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.PaperStatusEnum;
import com.sure.question.mapper.FrontPageImageMapper;
import com.sure.question.mapper.FrontPagePaperMapper;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.service.FrontPageService;
import com.sure.question.service.OssManager;
import com.sure.question.util.MultipartFileUtil;
import com.sure.question.vo.frontPage.FrontPageImageVo;
import com.sure.question.vo.frontPage.FrontPagePaperVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FrontPageServiceImpl implements FrontPageService {
    private final FrontPageImageMapper frontPageImageMapper;
    private final FrontPagePaperMapper frontPagePaperMapper;
    private final PaperMainMapper paperMainMapper;
    private final OssManager ossManager;
    private final IdWorker idWorker;
    private final RedisTemplate<String, String> redisTemplate;
    @Value("${aliyun.ossTkImg.prefix.frontPageImage}")
    private String frontPageImagePrefix;

    @Override
    public String uploadFrontPageImage(int gradeLevel, int subjectId, MultipartFile file) {
        String extension = MultipartFileUtil.getImageExtension(file);
        if (StringUtils.isEmpty(extension)) {
            throw new ApiException("不支持的图片格式");
        }
        String ossPath = frontPageImagePrefix + gradeLevel + "-" + subjectId + "-" + Long.toString(idWorker.nextId(), 16).toLowerCase() + extension;
        ossManager.uploadTK(ossPath, file);
        return ossManager.getPublicPathTK(ossPath);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFrontPageImage(int gradeLevel, int subjectId, List<FrontPageImageVo> images, String userId) {
        // 新增
        List<FrontPageImage> addList = new ArrayList<>();
        if (images != null && !images.isEmpty()) {
            int sortCode = 1;
            Date createTime = new Date();
            for (FrontPageImageVo image : images) {
                String id = String.valueOf(idWorker.nextId());
                String url = ossManager.getObjectNameTK(image.getImageUrl());
                if (StringUtils.isEmpty(url)) {
                    throw new ApiException("图片Url错误");
                }
                FrontPageImage entity = FrontPageImage.builder()
                        .id(id)
                        .gradeLevel(gradeLevel)
                        .subjectId(subjectId)
                        .name(image.getName())
                        .description(image.getDescription())
                        .imageUrl(url)
                        .link(image.getLink())
                        .sortCode(sortCode++)
                        .createBy(userId)
                        .createTime(createTime)
                        .build();
                addList.add(entity);
            }
        }

        // 全部删除，然后插入
        frontPageImageMapper.delete(new LambdaQueryWrapper<FrontPageImage>()
                .eq(FrontPageImage::getGradeLevel, gradeLevel)
                .eq(FrontPageImage::getSubjectId, subjectId));
        if (!addList.isEmpty()) {
            addList.forEach(frontPageImageMapper::insert);
        }

        // 删缓存
        redisTemplate.delete(getFrontPageImageRedisKey(gradeLevel, subjectId));
    }

    @Override
    public List<FrontPageImageVo> getFrontPageImagesWithCache(int gradeLevel, int subjectId) {
        String key = getFrontPageImageRedisKey(gradeLevel, subjectId);
        String value = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(value)) {
            return JSON.parseArray(value, FrontPageImageVo.class);
        } else {
            List<FrontPageImageVo> result = getFrontPageImages(gradeLevel, subjectId);
            value = JSON.toJSONString(result);
            redisTemplate.opsForValue().set(key, value, 1, TimeUnit.DAYS);
            return result;
        }
    }

    private String getFrontPageImageRedisKey(int gradeLevel, int subjectId) {
        return "Ques:FrontPage:Image:" + gradeLevel + "-" + subjectId;
    }

    private List<FrontPageImageVo> getFrontPageImages(int gradeLevel, int subjectId) {
        return frontPageImageMapper.selectList(new LambdaQueryWrapper<FrontPageImage>()
                        .eq(FrontPageImage::getGradeLevel, gradeLevel)
                        .eq(FrontPageImage::getSubjectId, subjectId)
                        .orderByAsc(FrontPageImage::getSortCode))
                .stream().map(entity -> {
                    FrontPageImageVo vo = new FrontPageImageVo(entity);
                    vo.setImageUrl(ossManager.getPublicPathTK(vo.getImageUrl()));
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFrontPagePapers(int gradeLevel, int subjectId, String collectionName, int collectionSortCode, List<String> paperIds, String userId) {
        // 新增
        List<FrontPagePaper> addList = new ArrayList<>();
        if (paperIds != null && !paperIds.isEmpty()) {
            // 检查试卷存在
            paperIds = paperIds.stream().distinct().collect(Collectors.toList());
            Integer paperMainCount = paperMainMapper.selectCount(new LambdaQueryWrapper<PaperMain>()
                    .eq(PaperMain::getGradeLevel, gradeLevel)
                    .eq(PaperMain::getSubjectId, subjectId)
                    .eq(PaperMain::getPaperBank, PaperBankEnum.Public.getCode())
                    .eq(PaperMain::getStatus, PaperStatusEnum.Status0)
                    .in(PaperMain::getId, paperIds));
            if (paperMainCount < paperIds.size()) {
                throw new ApiException("找不到试卷！");
            }

            int sortCode = 1;
            Date createTime = new Date();
            for (String paperId : paperIds) {
                FrontPagePaper entity = FrontPagePaper.builder()
                        .id(String.valueOf(idWorker.nextId()))
                        .gradeLevel(gradeLevel)
                        .subjectId(subjectId)
                        .collectionName(collectionName)
                        .collectionSortCode(collectionSortCode)
                        .paperId(paperId)
                        .paperSortCode(sortCode++)
                        .createBy(userId)
                        .createTime(createTime)
                        .build();
                addList.add(entity);
            }
        }

        // 全部删除，然后插入
        frontPagePaperMapper.delete(new LambdaQueryWrapper<FrontPagePaper>()
                .eq(FrontPagePaper::getGradeLevel, gradeLevel)
                .eq(FrontPagePaper::getSubjectId, subjectId)
                .eq(FrontPagePaper::getCollectionName, collectionName));
        if (!addList.isEmpty()) {
            addList.forEach(frontPagePaperMapper::insert);
        }

        // 删缓存
        redisTemplate.delete(getFrontPagePaperRedisKey(gradeLevel, subjectId));
    }

    @Override
    public void deleteFrontPagePaperCollection(int gradeLevel, int subjectId, String collectionName) {
        int deleteCount = frontPagePaperMapper.delete(new LambdaQueryWrapper<FrontPagePaper>()
                .eq(FrontPagePaper::getGradeLevel, gradeLevel)
                .eq(FrontPagePaper::getSubjectId, subjectId)
                .eq(FrontPagePaper::getCollectionName, collectionName));
        if (deleteCount == 0) {
            throw new ApiException("该合集不存在");
        }
        // 删缓存
        redisTemplate.delete(getFrontPagePaperRedisKey(gradeLevel, subjectId));
    }

    @Override
    public void changePaperCollectionOrder(int gradeLevel, int subjectId, List<String> collectionNames) {
        for (int i = 0; i < collectionNames.size(); i++) {
            String collectionName = collectionNames.get(i);
            frontPagePaperMapper.update(null, new LambdaUpdateWrapper<FrontPagePaper>()
                    .eq(FrontPagePaper::getGradeLevel, gradeLevel)
                    .eq(FrontPagePaper::getSubjectId, subjectId)
                    .eq(FrontPagePaper::getCollectionName, collectionName)
                    .set(FrontPagePaper::getCollectionSortCode, i + 1));
        }
        // 删缓存
        redisTemplate.delete(getFrontPagePaperRedisKey(gradeLevel, subjectId));
    }

    @Override
    public List<FrontPagePaperVo> getFrontPagePapersWithCache(int gradeLevel, int subjectId, String collectionName) {
        List<FrontPagePaperVo> result;

        String key = getFrontPagePaperRedisKey(gradeLevel, subjectId);
        String value = redisTemplate.opsForValue().get(key);
        if (StringUtils.isNotEmpty(value)) {
            result = JSON.parseArray(value, FrontPagePaperVo.class);
        } else {
            result = getFrontPagePapers(gradeLevel, subjectId);
            value = JSON.toJSONString(result);
            redisTemplate.opsForValue().set(key, value, 1, TimeUnit.DAYS);
        }

        if (StringUtils.isNotEmpty(collectionName)) {
            result.removeIf(x -> !collectionName.equals(x.getCollectionName()));
        }
        return result;
    }

    private String getFrontPagePaperRedisKey(int gradeLevel, int subjectId) {
        return "Ques:FrontPage:Paper:" + gradeLevel + "-" + subjectId;
    }

    private List<FrontPagePaperVo> getFrontPagePapers(int gradeLevel, int subjectId) {
        List<FrontPagePaperVo> result = frontPagePaperMapper.selectList(new LambdaQueryWrapper<FrontPagePaper>()
                        .eq(FrontPagePaper::getGradeLevel, gradeLevel)
                        .eq(FrontPagePaper::getSubjectId, subjectId)
                        .orderByAsc(FrontPagePaper::getCollectionSortCode)
                        .orderByAsc(FrontPagePaper::getPaperSortCode))
                .stream().map(FrontPagePaperVo::new).collect(Collectors.toList());
        if (!result.isEmpty()) {
            addPaperNameBrowseCount(result);
        }
        return result;
    }

    private void addPaperNameBrowseCount(List<FrontPagePaperVo> vos) {
        Set<String> paperIds = vos.stream().map(FrontPagePaperVo::getPaperId).collect(Collectors.toSet());
        List<PaperMain> paperMainList = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .select(PaperMain::getId, PaperMain::getPaperName, PaperMain::getBrowseCount));
        Map<String, PaperMain> paperMainMap = paperMainList.stream().collect(Collectors.toMap(PaperMain::getId, Function.identity()));
        vos.forEach(vo -> {
            PaperMain paperMain = paperMainMap.get(vo.getPaperId());
            if (paperMain != null) {
                vo.setPaperName(paperMain.getPaperName());
                vo.setBrowseCount(paperMain.getBrowseCount());
            }
        });
    }
}
