package com.sure.question.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.common.entity.Result;
import com.sure.common.entity.StatusCode;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.coachBook.StandardQuestionCountDto;
import com.sure.question.entity.*;
import com.sure.question.enums.CoachBookTypeEnum;
import com.sure.question.enums.FeedbackSheetSourceTypeEnum;
import com.sure.question.enums.RoleEnum;
import com.sure.question.feign.MarkService;
import com.sure.question.feign.ReportService;
import com.sure.question.feign.UserService;
import com.sure.question.mapper.*;
import com.sure.question.service.*;
import com.sure.question.util.CacheUtil;
import com.sure.question.util.FeignResultUtil;
import com.sure.question.util.MultipartFileUtil;
import com.sure.question.util.PdfUtil;
import com.sure.question.vo.SimpleVo;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.*;
import com.sure.question.vo.dataVo.IntIntVo;
import com.sure.question.vo.dataVo.StrIntVo;
import com.sure.question.vo.dataVo.StrStrVo;
import com.sure.question.vo.mark.ExamStatisticsVo;
import com.sure.question.vo.schoolCoachBook.PaperWrongCountReqVo;
import com.sure.question.vo.sheet.SimpleSheetVo;
import com.sure.question.vo.student.StudentInfoVo;
import com.sure.question.vo.teaching.TeachingVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.Collator;
import java.util.AbstractMap.SimpleEntry;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
@Slf4j
public class CoachBookServiceImpl implements CoachBookService {
    private final CoachBookMapper coachBookMapper;
    private final CoachBookPaperMapper coachBookPaperMapper;
    private final CoachBookSheetMapper coachBookSheetMapper;
    private final CoachBookCustomPaperMapper coachBookCustomPaperMapper;
    private final CoachBookCustomPaperService coachBookCustomPaperService;
    private final SchoolCoachBookMapper schoolCoachBookMapper;
    private final PaperMainMapper paperMainMapper;
    private final FeignService feignService;
    private final ExamSubjectPushQuesMapper examSubjectPushQuesMapper;
    private final MarkService markService;
    private final OssManager ossManager;
    private final PaperStuAccessMapper paperStuAccessMapper;
    private final UserService userService;
    private final ReportService reportService;
    private final CoachStuCardMapper coachStuCardMapper;
    private final PaperQuestionMapper paperQuestionMapper;
    private final PermissionService permissionService;

    @Value("${tempPath.feedbackSheets}")
    private String feedbackSheetsTempPath;

    @Value("${aliyun.ossTkImg.coachBookDefaultCover}")
    private String coachBookDefaultCover;
    @Value("${aliyun.ossTkImg.prefix.coachBookCover}")
    private String coachBookCoverPrefix;
    @Value("${aliyun.ossTkImg.prefix.coachBookPaperContentAnswerPdf}")
    private String coachBookPaperContentAnswerPdfPrefix;

    @Override
    public Page<InfoVo> listPage(QueryVo queryVo, Page<CoachBook> page) {
        if (queryVo.getOnlyOpen() == null) {
            queryVo.setOnlyOpen(false);
        }
        // 查教辅
        LambdaQueryWrapper<CoachBook> q = new LambdaQueryWrapper<CoachBook>()
                .eq(StringUtils.isNotEmpty(queryVo.getPartner()), CoachBook::getPartner, queryVo.getPartner())
                .eq(queryVo.getGradeLevel() != null, CoachBook::getGradeLevel, queryVo.getGradeLevel())
                .eq(queryVo.getSubjectId() != null, CoachBook::getSubjectId, queryVo.getSubjectId())
                .eq(queryVo.getGradeId() != null, CoachBook::getGradeId, queryVo.getGradeId())
                .eq(CoachBook::getBookType, CoachBookTypeEnum.SYS.getCode())
                .eq(queryVo.getSemesterId() != null, CoachBook::getSemesterId, queryVo.getSemesterId())
                .eq(queryVo.getTerm() != null, CoachBook::getTerm, queryVo.getTerm())
                .eq(queryVo.getYear() != null, CoachBook::getYear, queryVo.getYear())
                .like(StringUtils.isNotEmpty(queryVo.getBookCode()), CoachBook::getBookCode, queryVo.getBookCode())
                .like(StringUtils.isNotEmpty(queryVo.getBookName()), CoachBook::getBookName, queryVo.getBookName())
                .like(StringUtils.isNotEmpty(queryVo.getPressName()), CoachBook::getPressName, queryVo.getPressName())
                .last("ORDER BY " +
                        "semester_id DESC, " +
                        "term DESC, " +
                        "CASE WHEN book_code IS NULL OR book_code = '' THEN 1 ELSE 0 END, book_code ASC, " +
                        "year DESC, " +
                        "grade_id, " +
                        "subject_id");
        coachBookMapper.selectPage(page, q);

        // 构建
        Page<InfoVo> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        if (page.getTotal() == 0) {
            result.setRecords(Collections.emptyList());
            return result;
        }

        List<InfoVo> records = page.getRecords().stream().map(this::buildInfoVo).collect(Collectors.toList());
        result.setRecords(records);

        // 统计同步卷、练习卷、学校数量
        List<Integer> coachBookIds = records.stream().map(InfoVo::getId).collect(Collectors.toList());
        Map<Integer, Integer> bookIdAndPaperCountMap = coachBookPaperMapper.selectBookIdAndPaperCount(coachBookIds, queryVo.getOnlyOpen()).stream().collect(Collectors.toMap(IntIntVo::getId, IntIntVo::getValue));
        Map<Integer, Integer> bookIdAndCustomPaperCountMap = coachBookCustomPaperMapper.selectBookIdAndPaperCount(coachBookIds).stream().collect(Collectors.toMap(IntIntVo::getId, IntIntVo::getValue));
        Map<Integer, Integer> bookIdAndSchoolCountMap = schoolCoachBookMapper.getBookIdAndSchoolCount(coachBookIds).stream().collect(Collectors.toMap(IntIntVo::getId, IntIntVo::getValue));
        records.forEach(item -> {
            item.setPaperCount(bookIdAndPaperCountMap.getOrDefault(item.getId(), 0));
            item.setCustomPaperCount(bookIdAndCustomPaperCountMap.getOrDefault(item.getId(), 0));
            item.setSchoolCount(bookIdAndSchoolCountMap.getOrDefault(item.getId(), 0));
        });

        return result;
    }

    @Override
    public InfoVo getInfo(Integer coachBookId) {
        CoachBook item = coachBookMapper.selectOne(new LambdaQueryWrapper<CoachBook>().eq(CoachBook::getId, coachBookId));
        return item == null ? null : buildInfoVo(item);
    }

    private InfoVo buildInfoVo(CoachBook item) {
        return InfoVo.builder()
                .id(item.getId())
                .partner(item.getPartner())
                .gradeLevel(item.getGradeLevel())
                .subjectId(item.getSubjectId())
                .gradeId(item.getGradeId())
                .semesterId(item.getSemesterId())
                .term(item.getTerm())
                .year(item.getYear())
                .bookCode(item.getBookCode())
                .bookName(item.getBookName())
                .pressName(item.getPressName())
                .cover(item.getCover() != null ? ossManager.getPublicPathTK(item.getCover()) : null)
                .wrongQuesQuickMark(item.getWrongQuesQuickMark())
                .wrongQuesPaperMark(item.getWrongQuesPaperMark())
                .createTime(item.getCreateTime())
                .build();
    }

    @Override
    public Integer addBook(InfoVo infoVo, MultipartFile coverFile) {
        checkBookNameInvalid(infoVo.getPartner(), infoVo.getGradeLevel(), infoVo.getSubjectId(), infoVo.getSemesterId(), infoVo.getPressName(), infoVo.getBookName(), null);
        String coverPath = coverFile == null ? null : uploadCoachBookCoverToOss(coverFile);
        CoachBook book = CoachBook.builder()
                .bookType(CoachBookTypeEnum.SYS.getCode())
                .cover(coverPath)
                .createTime(new Date())
                .build();
        setBookInfo(book, infoVo);
        coachBookMapper.insert(book);

        return book.getId();
    }

    private void checkBookNameInvalid(String partner, int gradeLevel, int subjectId, int semesterId, String pressName, String bookName, Integer exceptId) {
        if (!StringUtils.equalsAny(partner, "同级生", "文轩", "阅山海")) {
            throw new ApiException("教辅合作商错误");
        }
        int sameNameBookCount = coachBookMapper.selectCount(new LambdaQueryWrapper<CoachBook>()
                .eq(CoachBook::getPartner, partner)
                .eq(CoachBook::getGradeLevel, gradeLevel)
                .eq(CoachBook::getSubjectId, subjectId)
                .eq(CoachBook::getSemesterId, semesterId)
                .eq(CoachBook::getPressName, pressName)
                .eq(CoachBook::getBookName, bookName)
                .ne(exceptId != null, CoachBook::getId, exceptId));
        if (sameNameBookCount > 0) {
            throw new ApiException("在该学段学科下已存在同一出版社同一年份同一学年的另一本同名教辅");
        }
    }

    private void setBookInfo(CoachBook book, InfoVo infoVo) {
        book.setPartner(infoVo.getPartner());
        book.setSubjectId(infoVo.getSubjectId());
        book.setGradeLevel(infoVo.getGradeLevel());
        book.setGradeId(infoVo.getGradeId());
        book.setSemesterId(infoVo.getSemesterId());
        book.setTerm(infoVo.getTerm());
        book.setYear(infoVo.getYear());
        book.setBookCode(infoVo.getBookCode());
        book.setBookName(infoVo.getBookName());
        book.setPressName(infoVo.getPressName());
        book.setWrongQuesQuickMark(infoVo.getWrongQuesQuickMark());
        book.setWrongQuesPaperMark(infoVo.getWrongQuesPaperMark());
    }

    @Override
    public void updateBook(InfoVo infoVo, MultipartFile coverFile) {
        if (infoVo.getId() == null) {
            throw new ApiException("参数错误");
        }
        CoachBook book = coachBookMapper.selectById(infoVo.getId());
        if (book == null) {
            throw new ApiException("参数错误");
        }

        setBookInfo(book, infoVo);
        checkBookNameInvalid(book.getPartner(), book.getGradeLevel(), book.getSubjectId(), book.getSemesterId(), book.getPressName(), book.getBookName(), book.getId());

        // 如果封面图片文件不为空，存到OSS里
        if (coverFile != null) {
            String coverPath = uploadCoachBookCoverToOss(coverFile);
            book.setCover(coverPath);
        }

        coachBookMapper.updateById(book);
    }

    private String uploadCoachBookCoverToOss(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        // 获取图片后缀
        String suffix = originalFilename != null ? "jpg" : FileUtil.extName(originalFilename);
        String path = coachBookCoverPrefix.concat(IdUtil.fastSimpleUUID()).concat(".").concat(suffix);
        ossManager.uploadTK(path, file);
        return path;
    }

    @Override
    public void deleteBook(int coachBookId) {
        int paperCount = coachBookPaperMapper.selectCount(new LambdaQueryWrapper<CoachBookPaper>().eq(CoachBookPaper::getCoachBookId, coachBookId));
        if (paperCount > 0) {
            throw new ApiException("该教辅中存在试卷，禁止删除");
        }
        int schoolCount = schoolCoachBookMapper.selectCount(new LambdaQueryWrapper<SchoolCoachBook>().eq(SchoolCoachBook::getCoachBookId, coachBookId));
        if (schoolCount > 0) {
            throw new ApiException("该教辅已有应用学校，禁止删除");
        }
        CoachStuCard coachStuCard = coachStuCardMapper.selectOne(new LambdaQueryWrapper<CoachStuCard>().eq(CoachStuCard::getCoachBookId, coachBookId).last("limit 1"));
        if (coachStuCard != null) {
            throw new ApiException("该教辅已存在相关的激活码，禁止删除");
        }
        coachBookMapper.deleteById(coachBookId);
    }

    @Override
    public List<PaperVo> getPapersOfBook(int coachBookId, Integer paperGroupId, Boolean onlyOpen, TokenUserVo userInfo) {
        checkListBookPaperSheetPermission(coachBookId, userInfo);

        // 非系统用户只可见已开放的
        boolean isSysUser = isSystemUser(userInfo);
        if (!isSysUser) {
            onlyOpen = true;
        }
        List<PaperVo> voList = buildPaperVosByBookId(coachBookId, Boolean.TRUE, onlyOpen);
        if (paperGroupId != null && !voList.isEmpty()) {
            voList.removeIf(x -> !paperGroupId.equals(x.getPaperGroupId()));
        }

        if (isSysUser) {
            addStandardQuestionFinished(voList);
        }
        if (isSysUser || hasDownloadCoachBookAssetPermission(userInfo.getUserId())) {
            addPaperAssets(coachBookId, voList);
        }
        if (!isSysUser) {
            CoachBook book = coachBookMapper.selectById(coachBookId);
            // 同级生试卷不开放给学校用户下载，含小学题卡合一答题卡及中学上传的试卷pdf
            if (StringUtils.equals("同级生", book.getPartner())) {
                if (Objects.equals(book.getGradeLevel(), 1)) {
                    voList.forEach(vo -> {
                        vo.setFeedbackSheetId(null);
                        vo.setFeedbackSheetName(null);
                        vo.setFeedbackSheetImageUrls(null);
                        vo.setFeedbackSheetPdfUrl(null);
                    });
                } else {
                    voList.forEach(vo -> vo.setContentPdfUrl(null));
                }
            }
        }

        return voList;
    }

    /**
     * 标准变式题是否完成
     */
    private void addStandardQuestionFinished(List<PaperVo> papers) {
        if (papers.isEmpty()) {
            return;
        }
        List<String> paperIds = papers.stream().map(PaperVo::getId).collect(Collectors.toList());

        // 查试卷题目
        List<PaperQuestion> paperQuestionList = paperQuestionMapper.selectList(new LambdaQueryWrapper<PaperQuestion>()
                .in(PaperQuestion::getPaperId, paperIds)
                .eq(PaperQuestion::getStatus, 0)
                .select(PaperQuestion::getPaperId, PaperQuestion::getQuestionId));
        Map<String, List<String>> paperQuestionMap = paperQuestionList.stream().collect(
                Collectors.groupingBy(PaperQuestion::getPaperId, Collectors.mapping(PaperQuestion::getQuestionId, Collectors.toList())));
        // 查标准变式题数量
        List<StandardQuestionCountDto> standardQuestionCountList = examSubjectPushQuesMapper.selectEachQuestionStandardQuestionCount(paperIds);
        Map<String, Map<String, Integer>> paperStandardQuestionCountMap = standardQuestionCountList.stream().collect(
                Collectors.groupingBy(StandardQuestionCountDto::getPaperId, Collectors.toMap(StandardQuestionCountDto::getQuestionId, StandardQuestionCountDto::getStandardQuestionCount)));

        for (PaperVo paper : papers) {
            List<String> paperQuestionIds = paperQuestionMap.get(paper.getId());
            Map<String, Integer> standardQuestionCountMap = paperStandardQuestionCountMap.get(paper.getId());
            if (CollUtil.isEmpty(paperQuestionIds) || CollUtil.isEmpty(standardQuestionCountMap)) {
                paper.setStandardQuestionFinished(false);
                continue;
            }
            // 每道题至少有2道标准变式题
            boolean finished = paperQuestionIds.stream().allMatch(questionId -> {
                Integer standardQuestionCount = standardQuestionCountMap.get(questionId);
                return standardQuestionCount != null && standardQuestionCount >= 2;
            });
            paper.setStandardQuestionFinished(finished);
        }
    }

    /**
     * 检查用户是否具有下载教辅资料权限
     */
    private boolean hasDownloadCoachBookAssetPermission(String userId) {
        return Boolean.TRUE.equals(userService.checkUserHasDownloadCoachBookAssetPermission(userId).getData());
    }

    /**
     * 补充试卷资料
     */
    private void addPaperAssets(int coachBookId, List<PaperVo> voList) {
        if (voList.isEmpty()) {
            return;
        }
        List<String> paperIds = voList.stream().map(PaperVo::getId).collect(Collectors.toList());
        List<CoachBookPaper> papers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .in(CoachBookPaper::getPaperId, paperIds)
                .select(CoachBookPaper::getPaperId, CoachBookPaper::getContentPdfUrl, CoachBookPaper::getContentPdfUploadTime,
                        CoachBookPaper::getAnswerPdfUrl, CoachBookPaper::getAnswerPdfUploadTime,
                        CoachBookPaper::getListeningAudioUrl, CoachBookPaper::getListeningAudioUploadTime));
        Map<String, CoachBookPaper> paperMap = papers.stream().collect(Collectors.toMap(CoachBookPaper::getPaperId, Function.identity()));
        voList.forEach(vo -> {
            CoachBookPaper paper = paperMap.get(vo.getId());
            if (paper != null) {
                if (StringUtils.isNotEmpty(paper.getContentPdfUrl())) {
                    vo.setContentPdfUrl(ossManager.getPublicPathTK(paper.getContentPdfUrl()));
                }
                vo.setContentPdfUploadTime(paper.getContentPdfUploadTime());
                if (StringUtils.isNotEmpty(paper.getAnswerPdfUrl())) {
                    vo.setAnswerPdfUrl(ossManager.getPublicPathTK(paper.getAnswerPdfUrl()));
                }
                vo.setAnswerPdfUploadTime(paper.getAnswerPdfUploadTime());
                if (StringUtils.isNotEmpty(paper.getListeningAudioUrl())) {
                    vo.setListeningAudioUrl(ossManager.getPublicPathTK(paper.getListeningAudioUrl()));
                }
                vo.setListeningAudioUploadTime(paper.getListeningAudioUploadTime());
            }
        });
    }

    @Override
    public void downloadPaperFeedbackSheets(Integer coachBookId, List<String> downloadPaperIds, TokenUserVo userInfo, ServletOutputStream outputStream) {
        checkListBookPaperSheetPermission(coachBookId, userInfo);
        boolean isSystemUser = isSystemUser(userInfo);

        List<PaperVo> voList = buildPaperVosByBookId(coachBookId, Boolean.TRUE, Boolean.FALSE);
        if (voList.isEmpty()) {
            throw new ApiException("该教辅尚无同步卷");
        }
        CoachBook book = coachBookMapper.selectById(coachBookId);
        List<SimpleSheetVo> sheetVos = new ArrayList<>();
        int index = 0;
        for (PaperVo p : voList) {
            index++;
            // 跳过非指定试卷Id
            if (downloadPaperIds != null && !downloadPaperIds.isEmpty() && !downloadPaperIds.contains(p.getId())) {
                continue;
            }
            if (StringUtils.isEmpty(p.getFeedbackSheetPdfUrl())) {
                continue;
            }
            // 对学校用户，剔除未开放的反馈卡
            if (!isSystemUser && !isPaperOpen(p)) {
                continue;
            }
            SimpleSheetVo sheetVo = new SimpleSheetVo();
            sheetVo.setId(p.getId());
            if ("文轩".equals(book.getPartner())) {
                sheetVo.setName(p.getPaperName());
            } else {
                sheetVo.setName(StringUtils.leftPad(String.valueOf(index), 2, '0') + "-" + p.getPaperName());
            }
            sheetVo.setPdfUrl(p.getFeedbackSheetPdfUrl());
            sheetVos.add(sheetVo);
        }
        if (sheetVos.isEmpty()) {
            throw new ApiException("该教辅尚无反馈卡");
        }
        buildSheetsZip(book, sheetVos, outputStream);
    }

    private void buildSheetsZip(CoachBook book, List<SimpleSheetVo> sheetVos, OutputStream outputStream) {
        String baseDir = feedbackSheetsTempPath + book.getId() + "_" + RandomUtil.randomString(16);
        FileUtil.mkdir(baseDir);

        String dateStr = DateUtil.format(new Date(), "yyyyMMddHHmm");
        String zipName = (book.getBookCode() == null ? "" : book.getBookCode() + "_") + book.getBookName() + "_反馈卡_" + dateStr + ".zip";
        // 删除不合法的文件名字符
        zipName = zipName.replaceAll("[\\\\/:*?\"<>|]", "");
        String zipPath = baseDir + "/" + zipName;

        // 存放PDF的文件夹
        String pdfDir = baseDir + "/pdf";
        FileUtil.mkdir(pdfDir);

        try {
            for (SimpleSheetVo vo : sheetVos) {
                String pdfUrl = vo.getPdfUrl();
                if (StringUtils.isNotEmpty(pdfUrl)) {
                    String fileName;
                    if ("文轩".equals(book.getPartner())) {
                        fileName = (book.getBookCode() == null ? "" : book.getBookName() + "-") + vo.getName() + ".pdf";
                    } else {
                        fileName = (book.getBookCode() == null ? "" : book.getBookCode() + "-") + vo.getName() + ".pdf";
                        if (book.getGradeLevel() != null && book.getGradeLevel() > 1) {
                            fileName = "答题卡-" + fileName;
                        }
                    }
                    // 删除不合法的文件名字符
                    fileName = fileName.replaceAll("[\\\\/:*?\"<>|]", "");
                    String pdfPath = pdfDir + "/" + fileName;
                    // 根据url下载PDF，并存放到pdfPath
                    HttpUtil.downloadFile(pdfUrl, FileUtil.file(pdfPath));
                }
            }
            ZipUtil.zip(pdfDir, zipPath, StandardCharsets.UTF_8, false);

            // 反馈卡下载次数+1
            List<String> ids = sheetVos.stream().map(SimpleSheetVo::getId).collect(Collectors.toList());
            if (book.isWinShare()) {
                paperMainMapper.increaseFeedbackSheetDownloadCount(ids);
            } else {
                paperMainMapper.increaseAnswerSheetDownloadCount(ids);
            }

            FileUtil.writeToStream(zipPath, outputStream);
        } catch (Exception e) {
            String msg = "下载教辅《".concat(book.getBookName()).concat("》的反馈卡失败");
            log.error(msg + "！coach_book_id=" + book.getId(), e);
            throw new ApiException(msg);
        } finally {
            try {
                FileUtil.del(baseDir);
            } catch (Exception e) {
                log.error("删除临时文件夹失败！path=".concat(baseDir), e);
            }
        }
    }

    @Override
    public void downloadCoachBookFeedbackSheets(Integer coachBookId, List<String> downloadPaperIds, TokenUserVo userInfo, ServletOutputStream outputStream) {
        List<SheetVo> sheetsOfBook = getSheetsOfBook(coachBookId, userInfo);
        if (sheetsOfBook.isEmpty()) {
            throw new ApiException("尚无反馈卡");
        }

        CoachBook book = coachBookMapper.selectById(coachBookId);

        List<CoachBookPaper> papers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .orderByAsc(CoachBookPaper::getSortCode));
        List<String> paperIds = papers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());

        List<SimpleSheetVo> sheetVos = sheetsOfBook.stream().map(s -> {
            // 跳过非指定试卷Id
            if (downloadPaperIds != null && !downloadPaperIds.isEmpty() && !downloadPaperIds.contains(s.getPaperId())) {
                return null;
            }
            SimpleSheetVo sheetVo = new SimpleSheetVo();
            sheetVo.setId(s.getPaperId());
            if (StringUtils.isNotEmpty(s.getPaperName())) {
                sheetVo.setName(s.getPaperName());
                if (!"文轩".equals(book.getPartner())) {
                    int index = paperIds.indexOf(s.getPaperId());
                    sheetVo.setName(StringUtils.leftPad(String.valueOf(index + 1), 2, '0') + "-" + s.getPaperName());
                }
            } else {
                sheetVo.setName(s.getName());
            }
            sheetVo.setPdfUrl(s.getPdfUrl());
            return sheetVo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (sheetVos.isEmpty()) {
            throw new ApiException("无反馈卡");
        }

        buildSheetsZip(book, sheetVos, outputStream);
    }

    private void checkListBookPaperSheetPermission(int coachBookId, TokenUserVo userInfo) {
        if (!isSystemUser(userInfo)) {
            boolean exists = schoolCoachBookMapper.existsBySchoolIdAndCoachBookId(userInfo.getSchoolId(), coachBookId);
            if (!exists) {
                throw new ApiException("非法操作，本校未定制该教辅");
            }
        }
    }

    private boolean isSystemUser(TokenUserVo userInfo) {
        List<Integer> systemUserRoles = Arrays.asList(RoleEnum.SysManager.getCode(), RoleEnum.SysEditor.getCode(), RoleEnum.SuperManager.getCode());
        return userInfo.getRoles().stream().anyMatch(systemUserRoles::contains);
    }

    private boolean isPaperOpen(PaperVo paperVo) {
        return paperVo.getOpenTime() != null && paperVo.getOpenTime().before(new Date());
    }

    public void downloadCoachBookPaperAssetZip(int coachBookId, List<String> downloadPaperIds, OutputStream outputStream, String type, TokenUserVo userInfo) {
        PaperAsset asset = getPaperAssetMap().get(type);
        if (asset == null) {
            throw new ApiException("类型错误");
        }

        if (!isSystemUser(userInfo) && !hasDownloadCoachBookAssetPermission(userInfo.getUserId())) {
            throw new ApiException("无权限");
        }

        CoachBook book = coachBookMapper.selectById(coachBookId);
        if (book == null) {
            throw new ApiException("找不到教辅");
        }
        String namePrefix = book.getBookCode() == null ? "" : book.getBookCode() + "-";

        List<CoachBookPaper> papers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .orderByAsc(CoachBookPaper::getSortCode));
        if (papers.isEmpty()) {
            throw new ApiException("无教辅试卷");
        }

        List<String> paperIds = papers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());
        Map<String, String> paperIdNameMap = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                        .in(PaperMain::getId, paperIds)
                        .select(PaperMain::getId, PaperMain::getPaperName))
                .stream().collect(Collectors.toMap(PaperMain::getId, PaperMain::getPaperName));

        List<StrStrVo> nameUrls = new ArrayList<>();
        int idx = 0;
        for (CoachBookPaper paper : papers) {
            idx++;
            // 跳过非指定试卷Id
            if (downloadPaperIds != null && !downloadPaperIds.isEmpty() && !downloadPaperIds.contains(paper.getPaperId())) {
                continue;
            }
            String url = asset.getKey().apply(paper);
            if (StringUtils.isEmpty(url)) {
                continue;
            }
            String paperName = paperIdNameMap.getOrDefault(paper.getPaperId(), "【缺失试卷名】");
            String name = namePrefix + StringUtils.leftPad(String.valueOf(idx), 2, '0') + "-"
                    + paperName + "-" + asset.getName() + asset.getExtension();
            nameUrls.add(new StrStrVo(name, url));
        }
        if (nameUrls.isEmpty()) {
            throw new ApiException("无" + asset.getName());
        }

        try {
            ossManager.downloadTKObjectsBuildZip(nameUrls, outputStream);
        } catch (Exception e) {
            log.error("下载教辅试卷{}pdf失败", asset.getName(), e);
            throw new ApiException("下载失败");
        }
    }

    @Override
    public List<PaperVo> getSchoolBookPapers(Integer coachBookId, String schoolId, String studentId, Boolean isAsc, String userId) {
        // 确保学校已开通指定教辅
        boolean existsSchoolBook = schoolCoachBookMapper.existsBySchoolIdAndCoachBookId(schoolId, coachBookId);
        if (!existsSchoolBook) {
            log.error("本校尚未定制该教辅！schoolId={}, coachBookId={}", schoolId, coachBookId);
            throw new ApiException("本校尚未定制该教辅");
        }
        List<PaperVo> voList = buildPaperVosByBookId(coachBookId, isAsc, Boolean.FALSE);
        // 过滤掉openTime为空的或者openTime大于当前时间的
        voList = voList.stream().filter(this::isPaperOpen).collect(Collectors.toList());

        if (!voList.isEmpty() && StringUtils.isNotBlank(studentId)) {
            List<String> paperIds = voList.stream().map(PaperVo::getId).collect(Collectors.toList());
            // 这里不能直接用stream 的 toMap，因为可能有试卷的错题为null，会报错
            Map<String, Integer> paperIdWrongQuesCountMap = new HashMap<>();
            Map<String, String> paperIdExamSubjectIdMap = new HashMap<>();
            markService.listPaperWrongCount(PaperWrongCountReqVo.builder()
                    .studentId(studentId)
                    .paperIds(paperIds)
                    .build()).forEach(x -> {
                paperIdWrongQuesCountMap.put(x.getPaperId(), x.getWrongQuesCount());
                paperIdExamSubjectIdMap.put(x.getPaperId(), x.getExamSubjectId());
            });
            voList.forEach(x -> {
                x.setWrongQuesCount(paperIdWrongQuesCountMap.get(x.getId()));
                x.setExamSubjectId(paperIdExamSubjectIdMap.get(x.getId()));
            });
            // 对于禁用家长采集错题的学校，过滤掉未扫描的单元
            if (feignService.isDisableFlagStuWrong(schoolId)) {
                voList.removeIf(x -> x.getWrongQuesCount() == null);
            }
        }

        return voList;
    }

    private List<PaperVo> buildPaperVosByBookId(int coachBookId, boolean orderBySortCodeAsc, Boolean onlyOpen) {
        CoachBook book = coachBookMapper.selectById(coachBookId);
        if (book == null) {
            throw new ApiException("参数错误，找不到该定制资源");
        }

        // 查包含试卷
        LambdaQueryWrapper<CoachBookPaper> coachBookPapersWrapper = new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .le(onlyOpen, CoachBookPaper::getOpenTime, new Date());
        if (orderBySortCodeAsc) {
            coachBookPapersWrapper.orderByAsc(CoachBookPaper::getSortCode);
        } else {
            coachBookPapersWrapper.orderByDesc(CoachBookPaper::getSortCode);
        }
        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(coachBookPapersWrapper);


        if (coachBookPapers.isEmpty()) {
            return Collections.emptyList();
        }

        // 查试卷信息
        List<String> paperIds = coachBookPapers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());
        List<PaperMain> paperMains = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .select(PaperMain::getId,
                        PaperMain::getPaperName,
                        PaperMain::getGradeLevel,
                        PaperMain::getSubjectId,
                        PaperMain::getGradeId,
                        PaperMain::getYear,
                        PaperMain::getRegionId,
                        PaperMain::getPaperTypeId,
                        PaperMain::getTerm,
                        PaperMain::getPaperGroupId,
                        PaperMain::getUploadType,
                        PaperMain::getEditStatus));
        Map<String, PaperMain> paperMainMap = paperMains.stream().collect(Collectors.toMap(PaperMain::getId, Function.identity()));

        // 查错题反馈卡信息
        Map<String, SimpleSheetVo> paperIdSheetMap = getPaperIdSheetMap(paperIds, book);

        // 查试卷已有标准变式题数量
        Map<String, Integer> paperIdAndStandardCountMap = examSubjectPushQuesMapper.getPaperIdAndStandardCount(paperIds).stream().collect(Collectors.toMap(StrIntVo::getKey, StrIntVo::getValue));

        // 构建
        return coachBookPapers.stream().map(paper -> {
            PaperMain paperMain = paperMainMap.get(paper.getPaperId());
            if (paperMain == null) {
                throw new ApiException("该教辅书中包含了不存在的试卷");
            }
            SimpleSheetVo sheet = paperIdSheetMap.get(paperMain.getId());
            return PaperVo.builder()
                    .id(paperMain.getId())
                    .paperName(paperMain.getPaperName())
                    .paperAlias(paper.getPaperAlias())
                    .gradeLevel(paperMain.getGradeLevel())
                    .subjectId(paperMain.getSubjectId())
                    .gradeId(paperMain.getGradeId())
                    .term(StringUtils.isNotEmpty(paperMain.getTerm()) ? Integer.valueOf(paperMain.getTerm()) : null)
                    .year(paperMain.getYear())
                    .regionId(paperMain.getRegionId())
                    .paperTypeId(paperMain.getPaperTypeId())
                    .uploadType(paperMain.getUploadType())
                    .editStatus(paperMain.getEditStatus())
                    .coachBookId(paper.getCoachBookId())
                    .coachBookName(book.getBookName())
                    .coachBookCode(book.getBookCode())
                    .paperGroupId(paperMain.getPaperGroupId())
                    .standardQuestionCount(paperIdAndStandardCountMap.getOrDefault(paper.getPaperId(), 0))
                    .sortCode(paper.getSortCode())
                    .feedbackSheetId(sheet == null ? null : sheet.getId())
                    .feedbackSheetName(sheet == null ? null : sheet.getName())
                    .feedbackSheetPdfUrl(sheet == null ? null : sheet.getPdfUrl())
                    .feedbackSheetImageUrls(sheet == null ? null : sheet.getImageUrls())
                    .openTime(paper.getOpenTime())
                    .scoreAnalysis(paper.getScoreAnalysis())
                    .createTime(paper.getCreateTime())
                    .locked(paper.getLocked())
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 根据试卷Id查错题反馈卡信息，根据教辅区分是错题反馈卡还是答题卡
     */
    private Map<String, SimpleSheetVo> getPaperIdSheetMap(List<String> paperIds, CoachBook book) {
        if (book.isWinShare()) {
            return feignService.getPaperIdFeedbackSheetMap(paperIds);
        } else {
            return feignService.getPaperIdAnswerSheetMap(paperIds);
        }
    }

    @Override
    public List<PaperVo> getCustomPapersOfBook(int coachBookId, Integer paperGroupId, TokenUserVo userInfo) {
        checkListBookPaperSheetPermission(coachBookId, userInfo);

        CoachBook book = coachBookMapper.selectById(coachBookId);
        if (book == null) {
            throw new ApiException("参数错误");
        }

        // 查包含试卷
        List<CoachBookCustomPaper> coachBookCustomPapers = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>()
                .eq(CoachBookCustomPaper::getCoachBookId, coachBookId)
                .orderByAsc(CoachBookCustomPaper::getSortCode));
        if (coachBookCustomPapers.isEmpty()) {
            return Collections.emptyList();
        }

        // 查试卷信息
        List<String> paperIds = coachBookCustomPapers.stream().map(CoachBookCustomPaper::getPaperId).collect(Collectors.toList());
        List<PaperMain> paperMains = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .eq(paperGroupId != null, PaperMain::getPaperGroupId, paperGroupId)
                .select(PaperMain::getId,
                        PaperMain::getPaperName,
                        PaperMain::getGradeLevel,
                        PaperMain::getSubjectId,
                        PaperMain::getGradeId,
                        PaperMain::getYear,
                        PaperMain::getRegionId,
                        PaperMain::getPaperTypeId,
                        PaperMain::getTerm,
                        PaperMain::getPaperGroupId,
                        PaperMain::getUploadType,
                        PaperMain::getEditStatus));

        if (paperMains.isEmpty()) {
            return Collections.emptyList();
        }

        Map<String, PaperMain> paperMainMap = paperMains.stream().collect(Collectors.toMap(PaperMain::getId, Function.identity()));

        // 查错题反馈卡信息
        Map<String, SimpleSheetVo> paperIdSheetMap = getPaperIdSheetMap(paperIds, book);

        // 查试卷已有标准变式题数量
        Map<String, Integer> paperIdAndStandardCountMap = examSubjectPushQuesMapper.getPaperIdAndStandardCount(paperIds).stream().collect(Collectors.toMap(StrIntVo::getKey, StrIntVo::getValue));

        // 构建
        return coachBookCustomPapers.stream().map(paper -> {
            PaperMain paperMain = paperMainMap.get(paper.getPaperId());
            if (paperMain == null) {
                throw new ApiException("该教辅书中包含了不存在的试卷");
            }
            SimpleSheetVo sheet = paperIdSheetMap.get(paperMain.getId());
            return PaperVo.builder()
                    .id(paperMain.getId())
                    .paperName(paperMain.getPaperName())
                    .gradeLevel(paperMain.getGradeLevel())
                    .subjectId(paperMain.getSubjectId())
                    .gradeId(paperMain.getGradeId())
                    .term(StringUtils.isNotEmpty(paperMain.getTerm()) ? Integer.valueOf(paperMain.getTerm()) : null)
                    .year(paperMain.getYear())
                    .regionId(paperMain.getRegionId())
                    .paperTypeId(paperMain.getPaperTypeId())
                    .uploadType(paperMain.getUploadType())
                    .editStatus(paperMain.getEditStatus())
                    .coachBookId(paper.getCoachBookId())
                    .coachBookName(book.getBookName())
                    .paperGroupId(paperMain.getPaperGroupId())
                    .standardQuestionCount(paperIdAndStandardCountMap.getOrDefault(paper.getPaperId(), 0))
                    .sortCode(paper.getSortCode())
                    .feedbackSheetId(sheet == null ? null : sheet.getId())
                    .feedbackSheetName(sheet == null ? null : sheet.getName())
                    .openTime(paper.getOpenTime())
                    .createTime(paper.getCreateTime())
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public void addPapersToBook(int coachBookId, List<String> paperIds, TokenUserVo userInfo) {
        CoachBook book = getCoachBookCheckPermission(coachBookId, userInfo);
        List<PaperMain> paperMains = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .eq(PaperMain::getGradeLevel, book.getGradeLevel())
                .eq(PaperMain::getSubjectId, book.getSubjectId())
                .select(PaperMain::getId, PaperMain::getGradeLevel, PaperMain::getSubjectId));
        Set<String> dbPaperIds = paperMains.stream().map(PaperMain::getId).collect(Collectors.toSet());
        if (paperIds.stream().anyMatch(paperId -> !dbPaperIds.contains(paperId))) {
            throw new ApiException("参数错误");
        }

        // 查当前包含试卷
        List<CoachBookPaper> dbCoachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>().eq(CoachBookPaper::getCoachBookId, coachBookId));
        Set<String> dbCoachBookPaperIds = dbCoachBookPapers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toSet());
        // 定制试卷
        List<CoachBookCustomPaper> coachBookCustomPapers = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>().eq(CoachBookCustomPaper::getCoachBookId, coachBookId));
        coachBookCustomPapers.forEach(x -> dbCoachBookPaperIds.add(x.getPaperId()));
        // 查当前最大排序号
        int sortCode = dbCoachBookPapers.stream().filter(x -> x.getSortCode() != null).mapToInt(CoachBookPaper::getSortCode).max().orElse(0);

        // 构建新记录，排除已存在的
        List<CoachBookPaper> inserts = new ArrayList<>();
        for (String paperId : paperIds) {
            if (dbCoachBookPaperIds.contains(paperId)) {
                continue;
            }
            CoachBookPaper coachBookPaper = CoachBookPaper.builder()
                    .coachBookId(coachBookId)
                    .paperId(paperId)
                    .sortCode(++sortCode)
                    .createBy(userInfo.getUserId())
                    .createTime(new Date())
                    .build();
            inserts.add(coachBookPaper);
        }
        if (inserts.isEmpty()) {
            return;
        }
        // 文轩教辅添加后即开放
        if ("文轩".equals(book.getPartner())) {
            Date openTime = new Date();
            inserts.forEach(p -> p.setOpenTime(openTime));
        }

        // 检查试卷是否已包含于其他书中
        List<String> insertPaperIds = inserts.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());
        // 单元卷
        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>().in(CoachBookPaper::getPaperId, insertPaperIds));
        // 定制试卷
        coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>().in(CoachBookCustomPaper::getPaperId, insertPaperIds))
                .forEach(x -> coachBookPapers.add(CoachBookPaper.builder().coachBookId(x.getCoachBookId()).paperId(x.getPaperId()).build()));

        if (!coachBookPapers.isEmpty()) {
            Set<Integer> otherBookIds = coachBookPapers.stream().map(CoachBookPaper::getCoachBookId).collect(Collectors.toSet());
            Set<String> otherBookPaperIds = coachBookPapers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toSet());
            Map<Integer, String> otherBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                            .in(CoachBook::getId, otherBookIds)
                            .select(CoachBook::getId, CoachBook::getBookName, CoachBook::getBookType))
                    .stream().collect(Collectors.toMap(CoachBook::getId, CoachBook::getBookName));
            Map<String, String> otherBookPaperNameMap = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                            .in(PaperMain::getId, otherBookPaperIds)
                            .select(PaperMain::getId, PaperMain::getPaperName))
                    .stream().collect(Collectors.toMap(PaperMain::getId, PaperMain::getPaperName));

            String paperBookNames = coachBookPapers.stream().sorted(Comparator.comparing(CoachBookPaper::getCoachBookId)).map(coachBookPaper -> {
                String bookName = otherBookNameMap.getOrDefault(coachBookPaper.getCoachBookId(), StringUtils.EMPTY);
                String paperName = otherBookPaperNameMap.getOrDefault(coachBookPaper.getPaperId(), StringUtils.EMPTY);
                return paperName + "：" + bookName;
            }).collect(Collectors.joining("\n"));
            throw new ApiException("以下试卷已包含于其他教辅中：\n" + paperBookNames);
        }

        coachBookPaperMapper.insertBatch(inserts);
    }

    private CoachBook getCoachBookCheckPermission(int coachBookId, TokenUserVo userInfo) {
        CoachBook book = coachBookMapper.selectOne(new LambdaQueryWrapper<CoachBook>()
                .eq(CoachBook::getId, coachBookId)
                .select(CoachBook::getId, CoachBook::getGradeLevel, CoachBook::getSubjectId, CoachBook::getPartner));
        if (book == null) {
            throw new ApiException("参数错误");
        }
        permissionService.checkEditorPermission(userInfo, book.getGradeLevel(), book.getSubjectId());
        return book;
    }

    @Override
    public void addCustomPapersToBook(int coachBookId, List<String> paperIds, TokenUserVo userInfo) {
        CoachBook book = getCoachBookCheckPermission(coachBookId, userInfo);
        List<PaperMain> paperMains = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .in(PaperMain::getId, paperIds)
                .eq(PaperMain::getGradeLevel, book.getGradeLevel())
                .eq(PaperMain::getSubjectId, book.getSubjectId())
                .select(PaperMain::getId));
        Set<String> dbPaperIds = paperMains.stream().map(PaperMain::getId).collect(Collectors.toSet());
        if (paperIds.stream().anyMatch(paperId -> !dbPaperIds.contains(paperId))) {
            throw new ApiException("参数错误");
        }

        // 查当前包含试卷
        List<CoachBookPaper> dbCoachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>().eq(CoachBookPaper::getCoachBookId, coachBookId));
        Set<String> dbCoachBookPaperIds = dbCoachBookPapers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toSet());
        // 定制试卷
        List<CoachBookCustomPaper> coachBookCustomPapers = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>().eq(CoachBookCustomPaper::getCoachBookId, coachBookId));
        coachBookCustomPapers.forEach(x -> dbCoachBookPaperIds.add(x.getPaperId()));
        // 查当前最大排序号
        int sortCode = coachBookCustomPapers.stream().filter(x -> x.getSortCode() != null).mapToInt(CoachBookCustomPaper::getSortCode).max().orElse(0);

        // 构建新记录，排除已存在的
        List<CoachBookCustomPaper> inserts = new ArrayList<>();
        for (String paperId : paperIds) {
            if (dbCoachBookPaperIds.contains(paperId)) {
                continue;
            }
            CoachBookCustomPaper coachBookCustomPaper = CoachBookCustomPaper.builder()
                    .coachBookId(coachBookId)
                    .paperId(paperId)
                    .sortCode(++sortCode)
                    .createBy(userInfo.getUserId())
                    .createTime(new Date())
                    .build();
            inserts.add(coachBookCustomPaper);
        }
        if (inserts.isEmpty()) {
            return;
        }

        // 检查试卷是否已包含于其他书中
        List<String> insertPaperIds = inserts.stream().map(CoachBookCustomPaper::getPaperId).collect(Collectors.toList());
        // 单元卷
        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>().in(CoachBookPaper::getPaperId, insertPaperIds));
        // 定制试卷
//        coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>().in(CoachBookCustomPaper::getPaperId, insertPaperIds))
//                .forEach(x -> coachBookPapers.add(CoachBookPaper.builder().coachBookId(x.getCoachBookId()).paperId(x.getPaperId()).build()));

        if (!coachBookPapers.isEmpty()) {
            Set<Integer> otherBookIds = coachBookPapers.stream().map(CoachBookPaper::getCoachBookId).collect(Collectors.toSet());
            Set<String> otherBookPaperIds = coachBookPapers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toSet());
            Map<Integer, String> otherBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                            .in(CoachBook::getId, otherBookIds)
                            .select(CoachBook::getId, CoachBook::getBookName, CoachBook::getBookType))
                    .stream().collect(Collectors.toMap(CoachBook::getId, CoachBook::getBookName));
            Map<String, String> otherBookPaperNameMap = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                            .in(PaperMain::getId, otherBookPaperIds)
                            .select(PaperMain::getId, PaperMain::getPaperName))
                    .stream().collect(Collectors.toMap(PaperMain::getId, PaperMain::getPaperName));

            String paperBookNames = coachBookPapers.stream().sorted(Comparator.comparing(CoachBookPaper::getCoachBookId)).map(coachBookPaper -> {
                String bookName = otherBookNameMap.getOrDefault(coachBookPaper.getCoachBookId(), StringUtils.EMPTY);
                String paperName = otherBookPaperNameMap.getOrDefault(coachBookPaper.getPaperId(), StringUtils.EMPTY);
                return paperName + "：" + bookName;
            }).collect(Collectors.joining("\n"));
            throw new ApiException("以下试卷是其他教辅的同步卷，无法添加为练习卷：\n" + paperBookNames);
        }

        coachBookCustomPaperService.saveBatch(inserts);
    }

    @Override
    public void updateBookPaperSortCode(List<PaperVo> paperVos, TokenUserVo userInfo) {
        paperVos.stream().map(PaperVo::getCoachBookId).distinct().forEach(coachBookId -> {
            getCoachBookCheckPermission(coachBookId, userInfo);
        });
        List<CoachBookPaper> coachBookPapers = paperVos.stream().map(vo -> CoachBookPaper.builder()
                .coachBookId(vo.getCoachBookId())
                .paperId(vo.getId())
                .sortCode(vo.getSortCode()).build()).collect(Collectors.toList());
        coachBookPaperMapper.updateSortCodeBatch(coachBookPapers);
    }

    @Override
    public void updateBookCustomPaperSortCode(List<PaperVo> paperVos, TokenUserVo userInfo) {
        paperVos.stream().map(PaperVo::getCoachBookId).distinct().forEach(coachBookId -> {
            getCoachBookCheckPermission(coachBookId, userInfo);
        });
        List<CoachBookCustomPaper> coachBookCustomPapers = paperVos.stream().map(vo -> CoachBookCustomPaper.builder()
                .coachBookId(vo.getCoachBookId())
                .paperId(vo.getId())
                .sortCode(vo.getSortCode()).build()).collect(Collectors.toList());
        coachBookCustomPaperMapper.updateSortCodeBatch(coachBookCustomPapers);
    }

    @Override
    public void removePapersFromBook(int coachBookId, List<String> paperIds, TokenUserVo userInfo) {
        getCoachBookCheckPermission(coachBookId, userInfo);
        int lockedCount = coachBookPaperMapper.selectCount(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .in(CoachBookPaper::getPaperId, paperIds)
                .eq(CoachBookPaper::getLocked, true));
        if (lockedCount > 0) {
            throw new ApiException("已锁定试卷不能移除");
        }
        coachBookPaperMapper.deleteBatch(paperIds, coachBookId);
    }

    @Override
    public void removeCustomPapersFromBook(int coachBookId, List<String> paperIds, TokenUserVo userInfo) {
        getCoachBookCheckPermission(coachBookId, userInfo);
        coachBookCustomPaperMapper.deleteBatch(paperIds, coachBookId);
    }

    @Override
    public void updateBookPaperAlias(int coachBookId, List<StrStrVo> paperIdAliasList, TokenUserVo userInfo) {
        getCoachBookCheckPermission(coachBookId, userInfo);

        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .in(CoachBookPaper::getCoachBookId, coachBookId)
                .select(CoachBookPaper::getId, CoachBookPaper::getCoachBookId, CoachBookPaper::getPaperId, CoachBookPaper::getPaperAlias));

        List<CoachBookPaper> updated = new ArrayList<>();
        paperIdAliasList.forEach(idAlias -> {
            CoachBookPaper paper = coachBookPapers.stream()
                    .filter(p -> StringUtils.equalsAny(p.getPaperId(), idAlias.getKey())).findFirst().orElse(null);
            if (paper == null) {
                throw new ApiException("参数错误：找不到试卷");
            } else if (updated.contains(paper)) {
                throw new ApiException("参数错误：试卷Id重复");
            }
            paper.setPaperAlias(ObjectUtils.defaultIfNull(idAlias.getValue(), StringUtils.EMPTY));
            updated.add(paper);
        });

        // 检查教辅内试卷别名不重复
        Set<String> aliasSet = new HashSet<>();
        Set<String> duplicateAliasSet = new HashSet<>();
        coachBookPapers.forEach(paper -> {
            String alias = paper.getPaperAlias();
            if (StringUtils.isNotEmpty(alias)) {
                if (aliasSet.contains(alias)) {
                    duplicateAliasSet.add(alias);
                } else {
                    aliasSet.add(alias);
                }
            }
        });
        if (!duplicateAliasSet.isEmpty()) {
            throw new ApiException("以下试卷别名重复：" + StringUtils.join(duplicateAliasSet, "、"));
        }

        // 存库
        updated.forEach(coachBookPaperMapper::updateById);
    }

    @Override
    public void updateBookPaperScoreAnalysis(int coachBookId, List<StrStrVo> paperIdScoreAnalysisList, TokenUserVo userInfo) {
        getCoachBookCheckPermission(coachBookId, userInfo);

        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .in(CoachBookPaper::getCoachBookId, coachBookId)
                .select(CoachBookPaper::getId, CoachBookPaper::getCoachBookId, CoachBookPaper::getPaperId, CoachBookPaper::getPaperAlias));

        Map<String, Boolean> paperIdScoreAnalysisMap = new HashMap<>();
        paperIdScoreAnalysisList.forEach(vo -> {
            if (StringUtils.equalsIgnoreCase("true", vo.getValue())) {
                paperIdScoreAnalysisMap.put(vo.getKey(), true);
            } else if (StringUtils.equalsIgnoreCase("false", vo.getValue())) {
                paperIdScoreAnalysisMap.put(vo.getKey(), false);
            }
        });

        List<CoachBookPaper> updated = new ArrayList<>();
        coachBookPapers.forEach(paper -> {
            if (paperIdScoreAnalysisMap.containsKey(paper.getPaperId())) {
                paper.setScoreAnalysis(paperIdScoreAnalysisMap.get(paper.getPaperId()));
                updated.add(paper);
            }
        });

        updated.forEach(coachBookPaperMapper::updateById);
    }

    @Override
    public List<String> getOrderPaperIds(List<String> paperIds) {
        return coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                        .in(CoachBookPaper::getPaperId, paperIds)
                        .select(CoachBookPaper::getPaperId)
                        .orderByAsc(CoachBookPaper::getCoachBookId, CoachBookPaper::getSortCode))
                .stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());
    }

    @Override
    public void setPaperOpenTime(List<OpenTimeVo> vos) {
        // 查教辅
        Set<Integer> coachBookIds = vos.stream().map(OpenTimeVo::getCoachBookId).collect(Collectors.toSet());
        List<CoachBook> coachBooks = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>().in(CoachBook::getId, coachBookIds));
        Set<Integer> winShareCoachBookIds = coachBooks.stream().filter(CoachBook::isWinShare).map(CoachBook::getId).collect(Collectors.toSet());

        // 查错题反馈卡
        List<String> winSharePaperIds = new ArrayList<>();
        List<String> otherPaperIds = new ArrayList<>();
        for (OpenTimeVo vo : vos) {
            if (winShareCoachBookIds.contains(vo.getCoachBookId())) {
                winSharePaperIds.add(vo.getPaperId());
            } else {
                otherPaperIds.add(vo.getPaperId());
            }
        }
        Map<String, SimpleSheetVo> paperIdSheetMap = new HashMap<>();
        if (!winSharePaperIds.isEmpty()) {
            paperIdSheetMap.putAll(feignService.getPaperIdFeedbackSheetMap(winSharePaperIds));
        }
        if (!otherPaperIds.isEmpty()) {
            paperIdSheetMap.putAll(feignService.getPaperIdAnswerSheetMap(otherPaperIds));
        }

        // 没有错题反馈卡的同步卷禁止设置开放时间
        List<String> paperIds = vos.stream().map(OpenTimeVo::getPaperId).collect(Collectors.toList());
        if (paperIds.size() != paperIdSheetMap.size()) {
            paperIds.removeIf(paperIdSheetMap::containsKey);
            List<String> paperNames = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                            .in(PaperMain::getId, paperIds)
                            .select(PaperMain::getPaperName))
                    .stream().map(PaperMain::getPaperName).collect(Collectors.toList());
            throw new ApiException("以下试卷尚未生成错题反馈卡，请先生成后再设置开放时间：\n" + CollUtil.join(paperNames, "\n"));
        }
        for (OpenTimeVo vo : vos) {
            coachBookPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookPaper>()
                    .eq(CoachBookPaper::getCoachBookId, vo.getCoachBookId())
                    .eq(CoachBookPaper::getPaperId, vo.getPaperId())
                    .set(CoachBookPaper::getOpenTime, DateUtil.parse(vo.getOpenTime())));
        }
    }

    @Override
    public void setCustomPaperOpenTime(List<OpenTimeVo> vos) {
        for (OpenTimeVo vo : vos) {
            coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                    .eq(CoachBookCustomPaper::getCoachBookId, vo.getCoachBookId())
                    .eq(CoachBookCustomPaper::getPaperId, vo.getPaperId())
                    .set(CoachBookCustomPaper::getOpenTime, DateUtil.parse(vo.getOpenTime())));
        }
    }

    @Override
    public void setPaperLocked(Integer coachBookId, String paperId, Boolean locked) {
        coachBookPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .eq(CoachBookPaper::getPaperId, paperId)
                .set(CoachBookPaper::getLocked, locked));
    }

    /**
     * 上传教辅试卷资料
     *
     * @param type 资料类型：content-正文pdf, answer-答案pdf, listening-听力语音
     */
    @Override
    public String uploadPaperAsset(int coachBookId, String paperId, MultipartFile file, String type) {
        checkUpdateCoachBookPaperAssetCommon(coachBookId, paperId, type);

        PaperAsset asset = getPaperAssetMap().get(type);
        if (!asset.getFileChecker().test(file)) {
            throw new ApiException("文件类型错误");
        }
        String path = coachBookPaperContentAnswerPdfPrefix + coachBookId + "/" + paperId + "-" + type + "-" + RandomStringUtils.randomAlphabetic(12) + asset.getExtension();

        if (MultipartFileUtil.isPdf(file)) {
            byte[] bytes;
            try {
                bytes = PdfUtil.removeMetaData(file);
            } catch (IOException e) {
                log.error("清除pdf文件元数据失败", e);
                throw new ApiException("保存文件失败");
            }
            ossManager.uploadTK(path, bytes);
        } else {
            ossManager.uploadTK(path, file);
        }

        // 更新数据库
        coachBookPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .eq(CoachBookPaper::getPaperId, paperId)
                .set(asset.getKey(), path)
                .set(asset.getUploadTimeKey(), new Date()));

        return ossManager.getPublicPathTK(path);
    }

    private void checkUpdateCoachBookPaperAssetCommon(int coachBookId, String paperId, String type) {
        if (!getPaperAssetMap().containsKey(type)) {
            throw new ApiException("类型错误");
        }
        CoachBookPaper paper = coachBookPaperMapper.selectOne(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .eq(CoachBookPaper::getPaperId, paperId));
        if (paper == null) {
            throw new ApiException("找不到教辅试卷");
        }
        if (Boolean.TRUE.equals(paper.getLocked())) {
            throw new ApiException("教辅试卷已锁定");
        }
    }

    @Data
    @AllArgsConstructor
    private static class PaperAsset {
        private String type;
        private String name;
        private String extension;
        private SFunction<CoachBookPaper, String> key;
        private SFunction<CoachBookPaper, Date> uploadTimeKey;
        private Predicate<MultipartFile> fileChecker;
    }

    private Map<String, PaperAsset> getPaperAssetMap() {
        return Stream.of(
                        new PaperAsset("content", "正文", ".pdf", CoachBookPaper::getContentPdfUrl, CoachBookPaper::getContentPdfUploadTime, MultipartFileUtil::isPdf),
                        new PaperAsset("answer", "答案", ".pdf", CoachBookPaper::getAnswerPdfUrl, CoachBookPaper::getAnswerPdfUploadTime, MultipartFileUtil::isPdf),
                        new PaperAsset("listening", "听力", ".mp3", CoachBookPaper::getListeningAudioUrl, CoachBookPaper::getListeningAudioUploadTime, MultipartFileUtil::isAudio))
                .collect(Collectors.toMap(PaperAsset::getType, Function.identity()));
    }

    @Override
    public void deletePaperAsset(int coachBookId, String paperId, String type) {
        checkUpdateCoachBookPaperAssetCommon(coachBookId, paperId, type);
        PaperAsset asset = getPaperAssetMap().get(type);
        coachBookPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .eq(CoachBookPaper::getPaperId, paperId)
                .set(asset.getKey(), null)
                .set(asset.getUploadTimeKey(), null));
    }

    @Override
    public PdfVo getCustomPaperPdf(Integer coachBookId, String paperId, String studentId, String userId) {
        CoachBookCustomPaper coachBookCustomPaper = coachBookCustomPaperMapper.selectOne(new LambdaQueryWrapper<CoachBookCustomPaper>()
                .eq(CoachBookCustomPaper::getCoachBookId, coachBookId)
                .eq(CoachBookCustomPaper::getPaperId, paperId)
                .select(CoachBookCustomPaper::getId, CoachBookCustomPaper::getPdfStatus, CoachBookCustomPaper::getPdfPath));
        if (coachBookCustomPaper == null) {
            throw new ApiException("参数错误");
        }

        Integer pdfStatus = coachBookCustomPaper.getPdfStatus();
        if (pdfStatus == null) {
            // 更新pdf状态为0
            coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                    .eq(CoachBookCustomPaper::getId, coachBookCustomPaper.getId())
                    .set(CoachBookCustomPaper::getPdfStatus, 0));
            return PdfVo.builder()
                    .status(1)
                    .build();
        }

        if (pdfStatus == 2) {
            // 如果学生ID不为空，则是家长端查看，需要记录查看记录
            if (StringUtils.isNotBlank(studentId) && StringUtils.isNotBlank(userId)) {
                paperStuAccessMapper.insertIgnore(studentId, userId, paperId, coachBookId, 1);
            }
            return PdfVo.builder()
                    .status(2)
                    .pdfUrl(ossManager.getPublicPathMark(coachBookCustomPaper.getPdfPath() + "?v=" + System.currentTimeMillis()))
                    .build();
        } else if (pdfStatus == 3) {
            log.error("【下载教辅练习卷PDF】PDF 生成失败，尝试重新生成！coachBookId={}, paperId={}, studentId={}, userId={}", coachBookId, paperId, studentId, userId);
            coachBookCustomPaperMapper.update(null, new LambdaUpdateWrapper<CoachBookCustomPaper>()
                    .eq(CoachBookCustomPaper::getId, coachBookCustomPaper.getId())
                    .set(CoachBookCustomPaper::getPdfStatus, 0));
        }

        return PdfVo.builder()
                .status(1)
                .build();
    }

    @Override
    public List<SheetVo> getSheetsOfBook(int coachBookId, TokenUserVo userInfo) {
        checkListBookPaperSheetPermission(coachBookId, userInfo);

        CoachBook book = coachBookMapper.selectById(coachBookId);
        if (book == null) {
            throw new ApiException("参数错误");
        }

        // 查独立反馈卡
        List<CoachBookSheet> coachBookSheets = coachBookSheetMapper.selectList(new LambdaQueryWrapper<CoachBookSheet>()
                .eq(CoachBookSheet::getCoachBookId, coachBookId)
                .eq(CoachBookSheet::getIsDelete, false)
                .orderByAsc(CoachBookSheet::getSortCode));

        // 查试卷及对应反馈卡
        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .orderByAsc(CoachBookPaper::getSortCode));
        Map<String, String> paperIdSheetIdMap = new HashMap<>();
        Map<String, String> paperIdNameMap = new HashMap<>();
        if (!coachBookPapers.isEmpty()) {
            List<String> paperIds = coachBookPapers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());
            Map<String, SimpleSheetVo> paperIdSheetMap = getPaperIdSheetMap(paperIds, book);
            paperIdSheetMap.forEach((paperId, simpleVo) -> paperIdSheetIdMap.put(paperId, simpleVo.getId()));
            List<PaperMain> paperMains = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                    .in(PaperMain::getId, paperIds)
                    .select(PaperMain::getId, PaperMain::getPaperName));
            paperMains.forEach(paperMain -> paperIdNameMap.put(paperMain.getId(), paperMain.getPaperName()));
        }

        // 查反馈卡信息
        List<String> sheetIds = coachBookSheets.stream().map(CoachBookSheet::getSheetId).collect(Collectors.toList());
        sheetIds.addAll(paperIdSheetIdMap.values());
        if (sheetIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<SheetVo> sheetsInfo = getSheetsInfo(sheetIds, book);
        Map<String, SheetVo> sheetIdInfoMap = sheetsInfo.stream().collect(Collectors.toMap(SheetVo::getId, Function.identity()));

        // 组装, 独立反馈卡在前, 试卷对应反馈卡在后
        List<SheetVo> sheetVos = new ArrayList<>();
        coachBookSheets.forEach(coachBookSheet -> {
            SheetVo sheetVo = sheetIdInfoMap.get(coachBookSheet.getSheetId());
            if (sheetVo != null) {
                sheetVo.setCoachBookId(coachBookId);
                sheetVo.setSortCode(coachBookSheet.getSortCode());
                sheetVos.add(sheetVo);
            }
        });
        coachBookPapers.forEach(coachBookPaper -> {
            String paperId = coachBookPaper.getPaperId();
            String sheetId = paperIdSheetIdMap.get(paperId);
            if (sheetId != null) {
                SheetVo sheetVo = sheetIdInfoMap.get(sheetId);
                if (sheetVo != null) {
                    sheetVo.setCoachBookId(coachBookId);
                    sheetVo.setPaperId(paperId);
                    sheetVo.setPaperName(paperIdNameMap.get(paperId));
                    sheetVos.add(sheetVo);
                }
            }
        });
        return sheetVos;
    }

    /**
     * 根据答题卡Id查错题反馈卡信息，根据教辅区分是错题反馈卡还是答题卡
     */
    private List<SheetVo> getSheetsInfo(List<String> sheetIds, CoachBook book) {
        if (book.isWinShare()) {
            return feignService.getFeedbackSheetsInfo(sheetIds);
        } else {
            return feignService.getAnswerSheetsInfo(sheetIds).stream().map(vo -> {
                SheetVo sheetVo = new SheetVo();
                sheetVo.setId(vo.getId());
                sheetVo.setName(vo.getName());
                sheetVo.setSourceType(vo.getSourceType());
                sheetVo.setSourceId(vo.getSourceId());
                sheetVo.setBookType(1);
                sheetVo.setSchoolId(vo.getSchoolId());
                sheetVo.setPdfUrl(vo.getPdfUrl());
                sheetVo.setImageUrls(vo.getImageUrls());
                sheetVo.setCreateBy(vo.getCreateBy());
                sheetVo.setCreateTime(vo.getCreateTime());
                sheetVo.setUpdateTime(vo.getUpdateTime());
                return sheetVo;
            }).collect(Collectors.toList());
        }
    }

    @Override
    public void addSheetsToBook(int coachBookId, List<String> sheetIds, String userId) {
        sheetIds = sheetIds.stream().distinct().collect(Collectors.toList());

        // 检查教辅、反馈卡是否都存在
        CoachBook book = coachBookMapper.selectOne(new LambdaQueryWrapper<CoachBook>().eq(CoachBook::getId, coachBookId).select(CoachBook::getId, CoachBook::getPartner));
        if (book == null) {
            throw new ApiException("参数错误");
        }
        List<SheetVo> sheetsInfo = getSheetsInfo(sheetIds, book);
        if (sheetsInfo.size() != sheetIds.size()) {
            throw new ApiException("参数错误");
        }
        // 检查反馈卡来源
        if (!sheetsInfo.stream().allMatch(sheetVo -> FeedbackSheetSourceTypeEnum.MANUAL.getCode().equals(sheetVo.getSourceType()))) {
            throw new ApiException("不能添加由试卷生成的反馈卡");
        }
        // 查已包含反馈卡
        List<CoachBookSheet> coachBookSheets = coachBookSheetMapper.selectList(new LambdaQueryWrapper<CoachBookSheet>().in(CoachBookSheet::getSheetId, sheetIds));
        if (coachBookSheets.stream().anyMatch(coachBookSheet -> ObjectUtils.notEqual(coachBookSheet.getCoachBookId(), coachBookId))) {
            throw new ApiException("已有反馈卡存在于其他教辅中");
        }
        // 剔除已存在的，找出要恢复的
        List<String> recoverIds = new ArrayList<>();
        for (CoachBookSheet coachBookSheet : coachBookSheets) {
            String sheetId = coachBookSheet.getSheetId();
            if (Boolean.TRUE.equals(coachBookSheet.getIsDelete())) {
                recoverIds.add(sheetId);
            }
            sheetIds.remove(sheetId);
        }
        // 恢复已删除
        if (!recoverIds.isEmpty()) {
            coachBookSheetMapper.update(null, new LambdaUpdateWrapper<CoachBookSheet>()
                    .eq(CoachBookSheet::getCoachBookId, coachBookId)
                    .eq(CoachBookSheet::getIsDelete, true)
                    .in(CoachBookSheet::getSheetId, recoverIds)
                    .set(CoachBookSheet::getIsDelete, false));
        }
        // 插入新记录
        if (!sheetIds.isEmpty()) {
            Integer maxSortCode = coachBookSheetMapper.selectMaxSortCode(coachBookId);
            int sortCode = maxSortCode == null ? 0 : maxSortCode;
            Date now = new Date();
            List<CoachBookSheet> inserts = new ArrayList<>();
            for (String sheetId : sheetIds) {
                CoachBookSheet record = CoachBookSheet.builder()
                        .coachBookId(coachBookId)
                        .sheetId(sheetId)
                        .sortCode(++sortCode)
                        .createBy(userId)
                        .createTime(now)
                        .isDelete(false)
                        .build();
                inserts.add(record);
            }
            coachBookSheetMapper.insertBatch(inserts);
        }
    }

    @Override
    public void updateBookSheetSortCode(List<SheetVo> sheetVos) {
        List<CoachBookSheet> coachBookSheets = sheetVos.stream().map(vo -> {
            CoachBookSheet coachBookSheet = new CoachBookSheet();
            coachBookSheet.setCoachBookId(vo.getCoachBookId());
            coachBookSheet.setSheetId(vo.getId());
            coachBookSheet.setSortCode(vo.getSortCode());
            return coachBookSheet;
        }).collect(Collectors.toList());
        coachBookSheetMapper.updateSortCodeBatch(coachBookSheets);
    }

    @Override
    public void removeSheetsFromBook(int coachBookId, List<String> sheetIds) {
        coachBookSheetMapper.update(null, new LambdaUpdateWrapper<CoachBookSheet>()
                .eq(CoachBookSheet::getCoachBookId, coachBookId)
                .in(CoachBookSheet::getSheetId, sheetIds)
                .set(CoachBookSheet::getIsDelete, true));
    }

    @Override
    public void linkSheetToPaper(int coachBookId, String sheetId, String paperId) {
        // 检查参数
        CoachBook book = coachBookMapper.selectById(coachBookId);
        if (book == null) {
            throw new ApiException("找不到教辅");
        }
        CoachBookSheet coachBookSheet = coachBookSheetMapper.selectOne(new LambdaQueryWrapper<CoachBookSheet>()
                .eq(CoachBookSheet::getCoachBookId, coachBookId)
                .eq(CoachBookSheet::getSheetId, sheetId)
                .eq(CoachBookSheet::getIsDelete, false));
        if (coachBookSheet == null) {
            throw new ApiException("找不到反馈卡");
        }
        CoachBookPaper coachBookPaper = coachBookPaperMapper.selectOne(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .eq(CoachBookPaper::getPaperId, paperId));
        if (coachBookPaper == null) {
            throw new ApiException("找不到试卷");
        }
        int paperCount = paperMainMapper.selectCount(new LambdaQueryWrapper<PaperMain>().eq(PaperMain::getId, paperId));
        if (paperCount <= 0) {
            throw new ApiException("找不到试卷");
        }

        // 阅卷服务修改反馈卡来源
        Result result;
        if (book.isWinShare()) {
            result = markService.changeFeedbackSheetSourceToPaper(sheetId, paperId);
        } else {
            result = markService.changeAnswerSheetSourceToPaper(sheetId, paperId);
        }
        FeignResultUtil.checkThrowMessage(result);

        // 删除关联
        coachBookSheet.setIsDelete(true);
        coachBookSheetMapper.updateById(coachBookSheet);
    }

    @Override
    public Integer getUnreadPaperCount(String studentId, TokenUserVo userInfo) {
        StudentInfoVo studentInfo = userService.getStudentInfo(studentId);
        if (studentInfo.getSchoolCurrentSemester() == null || studentInfo.getSchoolCurrentTerm() == null) {
            return 0;
        }

        // 查询学生当前学期的所有教辅
        List<Integer> coachBookIds = coachStuCardMapper.lstStudentActivedBookId(studentInfo.getSchoolId(), studentId, studentInfo.getSchoolCurrentSemester(), studentInfo.getSchoolCurrentTerm());
        if (coachBookIds.isEmpty()) {
            return 0;
        }

        // 查询这些教辅的定制试卷数量
        Integer total = coachBookCustomPaperMapper.selectCount(new LambdaQueryWrapper<CoachBookCustomPaper>()
                .in(CoachBookCustomPaper::getCoachBookId, coachBookIds)
                .le(CoachBookCustomPaper::getOpenTime, new Date()));

        // 查询已读试卷数量
        Integer accessCount = paperStuAccessMapper.selectAccessPaperCount(studentId, userInfo.getUserId(), coachBookIds);

        int unreadCount = total - accessCount;
        return Math.max(unreadCount, 0);
    }

    @Override
    public List<PaperCoachVo> getBookNameBySyncPaperIds(List<String> paperIds) {
        Map<String, Integer> paperIdCoachBookIdMap = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                        .in(CoachBookPaper::getPaperId, paperIds)
                        .select(CoachBookPaper::getPaperId, CoachBookPaper::getCoachBookId))
                .stream().collect(Collectors.toMap(x -> x.getPaperId(), CoachBookPaper::getCoachBookId));
        if (paperIdCoachBookIdMap.isEmpty()) {
            return Collections.emptyList();
        }

        Map<Integer, String> coachBookIdCoachBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                        .in(CoachBook::getId, paperIdCoachBookIdMap)
                        .select(CoachBook::getId, CoachBook::getBookName))
                .stream().collect(Collectors.toMap(CoachBook::getId, CoachBook::getBookName));

        List<PaperCoachVo> voList = new ArrayList<>();
        for (String paperId : paperIds) {
            Integer coachBookId = paperIdCoachBookIdMap.get(paperId);
            if (coachBookId == null) {
                continue;
            }
            String coachBookName = coachBookIdCoachBookNameMap.get(coachBookId);
            voList.add(new PaperCoachVo(paperId, coachBookId, coachBookName));
        }

        return voList;
    }

    @Override
    public List<CoachBook> listCoachBook(Integer semesterId, Integer term) {
        List<CoachBook> list = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                .eq(semesterId != null, CoachBook::getSemesterId, semesterId)
                .eq(term != null, CoachBook::getTerm, term)
                .orderByDesc(CoachBook::getOrderId));
        // 补充封面完整的封面地址
        list.forEach(book -> {
            if (book.getCover() != null) {
                book.setCover(ossManager.getPublicPathTK(book.getCover()));
            } else {
                // 没有封面图片，使用默认图片
                book.setCover(coachBookDefaultCover);
            }
        });

        return list;
    }

    @Override
    public List<SimpleVo> listSchools(Integer semesterId, Integer term) {
        return listSchoolsCommon(semesterId, term, Collections.emptySet());
    }

    @Override
    public List<SimpleVo> listSchoolsByOrg(Integer semesterId, Integer term, TokenUserVo userInfo) {
        Result result = userService.getSubordinateSchoolIds(userInfo.getSchoolId());
        if (result.getCode() != 0) {
            log.error("【获取学校列表】获取可见学校列表失败，学校ID：{}，错误信息：{}", userInfo.getSchoolId(), result.getMessage());
            throw new ApiException("获取学校列表失败");
        }
        Set<String> accessibleSchoolIds = new HashSet<>(JSONUtil.toList(JSONUtil.toJsonStr(result.getData()), String.class));

        return listSchoolsCommon(semesterId, term, accessibleSchoolIds);
    }

    @Override
    public void setWrongQuesMark(List<WrongQuesMarkVo> vos) {
        for (WrongQuesMarkVo vo : vos) {
            coachBookMapper.update(null, new LambdaUpdateWrapper<CoachBook>()
                    .eq(CoachBook::getId, vo.getId())
                    .set(CoachBook::getWrongQuesQuickMark, vo.getWrongQuesQuickMark())
                    .set(CoachBook::getWrongQuesPaperMark, vo.getWrongQuesPaperMark()));
            // 更新没有编辑过的学校的标记方式
            schoolCoachBookMapper.update(null, new LambdaUpdateWrapper<SchoolCoachBook>()
                    .eq(SchoolCoachBook::getCoachBookId, vo.getId())
                    .eq(SchoolCoachBook::getIsMarkWayEdited, Boolean.FALSE)
                    .set(SchoolCoachBook::getWrongQuesQuickMark, vo.getWrongQuesQuickMark())
                    .set(SchoolCoachBook::getWrongQuesPaperMark, vo.getWrongQuesPaperMark()));
        }
    }

    @Override
    public List<CoachBook> teachingGradeSubjectCoachBooks(Integer semesterId, Integer term, TokenUserVo userInfo) {
        Result result = userService.getPersonalTeachingByTeacherIdAndSemesterId(userInfo.getUserId(), semesterId);
        if (result.getCode() != 0) {
            log.error("【获取教辅列表】获取指定教师指定学年任课信息失败，教师ID：{}，学期ID：{}，错误信息：{}", userInfo.getUserId(), semesterId, result.getMessage());
            throw new ApiException("获取教辅列表失败");
        }
        List<TeachingVo> teachingVos = JSONUtil.toList(JSONUtil.toJsonStr(result.getData()), TeachingVo.class);
        if (teachingVos.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用SimpleEntry对TeachingVo进行去重
        Set<SimpleEntry<Integer, Integer>> uniqueTeachingVos = teachingVos.stream().map(teachingVo -> new SimpleEntry<>(teachingVo.getGradeId(), teachingVo.getSubjectId())).collect(Collectors.toSet());

        Set<Integer> coachBookIds = new HashSet<>();
        for (SimpleEntry<Integer, Integer> entry : uniqueTeachingVos) {
            schoolCoachBookMapper.selectList(new LambdaQueryWrapper<SchoolCoachBook>()
                            .eq(SchoolCoachBook::getSchoolId, userInfo.getSchoolId())
                            .eq(SchoolCoachBook::getGradeId, entry.getKey())
                            .eq(SchoolCoachBook::getSubjectId, entry.getValue())
                            .eq(SchoolCoachBook::getSemesterId, semesterId)
                            .eq(SchoolCoachBook::getTerm, term)
                            .select(SchoolCoachBook::getCoachBookId))
                    .forEach(x -> coachBookIds.add(x.getCoachBookId()));
        }
        if (coachBookIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<CoachBook> coachBooks = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>().in(CoachBook::getId, coachBookIds));

        // 过滤掉没有反馈卡的教辅
        // 有反馈卡的教辅ID集合
        Set<Integer> hasFeedbackSheetBookIds = new HashSet<>();
        for (CoachBook coachBook : coachBooks) {
            List<String> paperIds = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                    .eq(CoachBookPaper::getCoachBookId, coachBook.getId())
                    .select(CoachBookPaper::getPaperId)).stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());
            if (paperIds.isEmpty()) {
                continue;
            }
            Map<String, SimpleSheetVo> paperIdSheetMap = getPaperIdSheetMap(paperIds, coachBook);
            if (!paperIdSheetMap.isEmpty()) {
                hasFeedbackSheetBookIds.add(coachBook.getId());
            }
        }
        coachBooks.removeIf(x -> !hasFeedbackSheetBookIds.contains(x.getId()));

        // 补充封面完整的封面地址
        coachBooks.forEach(x -> {
            if (x.getCover() != null) {
                x.setCover(ossManager.getPublicPathTK(x.getCover()));
            } else {
                // 没有封面图片，使用默认图片
                x.setCover(coachBookDefaultCover);
            }
        });

        // 补充年级科目名称
        coachBooks.forEach(x -> {
            x.setGradeName(CacheUtil.getGradeName(x.getGradeId()));
            x.setSubjectName(CacheUtil.getSubjectName(x.getSubjectId()));
        });

        // 补充标记方式
        schoolCoachBookMapper.selectList(new LambdaQueryWrapper<SchoolCoachBook>()
                        .eq(SchoolCoachBook::getSchoolId, userInfo.getSchoolId())
                        .in(SchoolCoachBook::getCoachBookId, coachBookIds))
                .forEach(x -> {
                    for (CoachBook coachBook : coachBooks) {
                        if (x.getCoachBookId().equals(coachBook.getId())) {
                            coachBook.setWrongQuesQuickMark(x.getWrongQuesQuickMark());
                            coachBook.setWrongQuesPaperMark(x.getWrongQuesPaperMark());
                            break;
                        }
                    }
                });

        // 排序
        coachBooks.sort(Comparator.comparing(CoachBook::getGradeId).thenComparing(CoachBook::getSubjectId));
        return coachBooks;
    }

    @Override
    public List<PaperExamInfoVo> paperExams(Integer semesterId, Integer term, Integer coachBookId, String classId, Boolean isAsc, TokenUserVo userInfo) {
        List<PaperVo> paperVos = buildPaperVosByBookId(coachBookId, isAsc, Boolean.FALSE);
        // 剔除没到开放时间的
        paperVos.removeIf(paperVo -> paperVo.getOpenTime() == null || paperVo.getOpenTime().after(new Date()));
        if (paperVos.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> paperIds = paperVos.stream().map(PaperVo::getId).collect(Collectors.toList());
        Result result = markService.examStatistics(ExamStatisticsVo.builder()
                .semesterId(semesterId)
                .term(term)
                .schoolId(userInfo.getSchoolId())
                .classId(classId)
                .paperIds(paperIds)
                .build());
        if (result.getCode() != 0) {
            throw new ApiException("系统异常，稍后再试");
        }
        List<FeedbackStatVo> statVos = JSONUtil.toList(JSONUtil.toJsonStr(result.getData()), FeedbackStatVo.class);
        int totalCount;
        if (statVos.isEmpty()) {
            // 查询班级学生人数
            result = userService.getStudentCountByClassId(classId);
            if (result.getCode() != 0) {
                throw new ApiException("系统异常，稍后再试");
            }
            totalCount = (Integer) result.getData();
        } else {
            totalCount = statVos.get(0).getTotalCount();
        }
        Map<String, FeedbackStatVo> paperIdStatVoMap = statVos.stream().collect(Collectors.toMap(FeedbackStatVo::getPaperId, x -> x));

        List<PaperExamInfoVo> voList = new ArrayList<>();
        for (PaperVo paperVo : paperVos) {
            PaperExamInfoVo infoVo = new PaperExamInfoVo();
            infoVo.setPaperId(paperVo.getId());
            infoVo.setPaperName(paperVo.getPaperName());
            infoVo.setPaperAlias(paperVo.getPaperAlias());
            infoVo.setFeedbackSheetPdfUrl(paperVo.getFeedbackSheetPdfUrl());
            infoVo.setTotal(totalCount);
            infoVo.setSubmitted(0);
            infoVo.setSortCode(paperVo.getSortCode());
            infoVo.setSubjectId(paperVo.getSubjectId());
            infoVo.setSubjectName(CacheUtil.getSubjectName(paperVo.getSubjectId()));

            FeedbackStatVo statVo = paperIdStatVoMap.get(paperVo.getId());
            if (statVo != null) {
                infoVo.setExamId(statVo.getExamId());
                infoVo.setExamSubjectId(statVo.getExamSubjectId());
                infoVo.setSubmitted(statVo.getParentMarkedCount() + statVo.getTeacherMarkedCount() + statVo.getScannedCount());
            }

            // 状态：0-没有数据；1-已完成；2-扫描处理中；3-已有家长提交；4-已有教师提交
            if (infoVo.getSubmitted() == 0) {
                infoVo.setStatus(0);
            } else if (infoVo.getSubmitted() == totalCount) {
                infoVo.setStatus(1);
            } else if (statVo.getScannedCount() > 0) {
                infoVo.setStatus(2);
            } else if (statVo.getParentMarkedCount() > 0) {
                infoVo.setStatus(3);
            } else {
                infoVo.setStatus(4);
            }
            voList.add(infoVo);
        }

        if (voList.stream().anyMatch(x -> x.getExamId() != null)) {
            // 查询当前教师访问过的试卷
            result = reportService.listTeacherAccessedFeedbackExamIds(userInfo.getUserId(), semesterId, term, classId);
            if (!Objects.equals(result.getCode(), StatusCode.OK)) {
                throw new ApiException("系统异常，稍后再试");
            }
            Set<String> accessedExamIds = new HashSet<>(JSONUtil.toList(JSONUtil.toJsonStr(result.getData()), String.class));
            voList.forEach(x -> {
                if (x.getExamId() != null) {
                    x.setIsAccessed(accessedExamIds.contains(x.getExamId()));
                }
            });
        }

        return voList;
    }

    @Override
    public List<BookPaperVo> paperCoachBookInfos(Set<String> paperIds) {
        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .in(CoachBookPaper::getPaperId, paperIds)
                .select(CoachBookPaper::getPaperId, CoachBookPaper::getCoachBookId, CoachBookPaper::getSortCode, CoachBookPaper::getPaperAlias));
        Map<Integer, List<CoachBookPaper>> bookIdPaperListMap = coachBookPapers.stream().collect(Collectors.groupingBy(CoachBookPaper::getCoachBookId));
        Map<Integer, CoachBook> bookIdBookNameMap = coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                        .in(CoachBook::getId, bookIdPaperListMap.keySet())
                        .select(CoachBook::getId, CoachBook::getBookName, CoachBook::getOrderId))
                .stream().collect(Collectors.toMap(CoachBook::getId, x -> x));
        Map<String, String> paperIdPaperNameMap = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                        .in(PaperMain::getId, paperIds)
                        .select(PaperMain::getId, PaperMain::getPaperName))
                .stream().collect(Collectors.toMap(PaperMain::getId, PaperMain::getPaperName));
        List<BookPaperVo> voList = new ArrayList<>(bookIdPaperListMap.size());
        for (Integer bookId : bookIdPaperListMap.keySet()) {
            List<SimplePaperVo> paperVos = bookIdPaperListMap.get(bookId)
                    .stream().sorted(Comparator.comparing(CoachBookPaper::getSortCode)).map(x -> SimplePaperVo.builder()
                            .paperId(x.getPaperId())
                            .paperName(paperIdPaperNameMap.get(x.getPaperId()))
                            .paperAlias(x.getPaperAlias())
                            .sortCode(x.getSortCode())
                            .build()).collect(Collectors.toList());
            voList.add(BookPaperVo.builder()
                    .bookId(bookId)
                    .bookName(bookIdBookNameMap.get(bookId).getBookName())
                    .bookSortCode(bookIdBookNameMap.get(bookId).getOrderId())
                    .papers(paperVos)
                    .build());
        }
        return voList;
    }

    @Override
    public CoachBook getSchoolCoachBookBySheetId(String schoolId, String userId, String sheetId) {
        CoachBookSheet coachBookSheet = coachBookSheetMapper.selectOne(new LambdaQueryWrapper<CoachBookSheet>()
                .eq(CoachBookSheet::getSheetId, sheetId)
                .select(CoachBookSheet::getCoachBookId)
                .last("limit 1"));
        if (coachBookSheet == null) {
            return null;
        }

        return getSchoolCoachBookByBookId(coachBookSheet.getCoachBookId(), schoolId, userId);
    }

    @Override
    public CoachBook getSchoolCoachBookByPaperId(String schoolId, String userId, String paperId) {
        Integer coachBookId = coachBookPaperMapper.selectCoachBookIdByPaperId(paperId);
        if (coachBookId == null) {
            return null;
        }
        return getSchoolCoachBookByBookId(coachBookId, schoolId, userId);
    }

    private CoachBook getSchoolCoachBookByBookId(Integer coachBookId, String schoolId, String userId) {
        SchoolCoachBook schoolCoachBook = schoolCoachBookMapper.selectOne(new LambdaQueryWrapper<SchoolCoachBook>()
                .eq(SchoolCoachBook::getSchoolId, schoolId)
                .eq(SchoolCoachBook::getCoachBookId, coachBookId)
                .select(SchoolCoachBook::getSemesterId, SchoolCoachBook::getTerm, SchoolCoachBook::getGradeLevel, SchoolCoachBook::getGradeId)
                .last("limit 1"));
        if (schoolCoachBook == null) {
            log.error("学校未应用教辅！coachBookId={}, schoolId={}, userId={}", coachBookId, schoolId, userId);
            return null;
        }
        CoachBook coachBook = coachBookMapper.selectById(coachBookId);
        if (coachBook != null) {
            // 替换为学校应用设置
            coachBook.setSemesterId(schoolCoachBook.getSemesterId());
            coachBook.setTerm(schoolCoachBook.getTerm());
            coachBook.setGradeLevel(schoolCoachBook.getGradeLevel());
            coachBook.setGradeId(schoolCoachBook.getGradeId());
        }

        return coachBook;
    }

    @Override
    public List<Long> listCoachBookPaperIds(Integer coachBookId) {
        return coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                        .eq(CoachBookPaper::getCoachBookId, coachBookId)
                        .orderByAsc(CoachBookPaper::getSortCode)
                        .select(CoachBookPaper::getPaperId))
                .stream().map(x -> Long.parseLong(x.getPaperId())).collect(Collectors.toList());
    }

    @Override
    public Boolean getScoreAnalysisStatus(Integer coachBookId, Long paperId) {
        CoachBookPaper coachBookPaper = coachBookPaperMapper.selectOne(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getCoachBookId, coachBookId)
                .eq(CoachBookPaper::getPaperId, paperId));
        if (coachBookPaper == null) {
            log.error("找不到该教辅试卷！coachBookId={}, paperId={}", coachBookId, paperId);
            throw new ApiException("找不到该教辅试卷");
        }
        return coachBookPaper.getScoreAnalysis();
    }

    @Override
    public List<SimpleVo> getCoachBookNames(List<Integer> coachBookIds) {
        return coachBookMapper.selectList(new LambdaQueryWrapper<CoachBook>()
                        .in(CoachBook::getId, coachBookIds)
                        .select(CoachBook::getId, CoachBook::getBookName))
                .stream().map(x -> new SimpleVo(x.getId().toString(), x.getBookName())).collect(Collectors.toList());
    }

    @Override
    public List<CoachBookPlanVo> getSchoolCoachBookPlans(String schoolId, Integer gradeId, Integer semesterId, Integer term) {
        List<CoachBook> coachBookList = coachBookMapper.querySchoolCoachBooks(schoolId, gradeId, semesterId, term);
        if (coachBookList.isEmpty()) {
            return Collections.emptyList();
        }

        List<CoachBookPlanVo> voList = new ArrayList<>(coachBookList.size());
        for (CoachBook coachBook : coachBookList) {
            voList.add(CoachBookPlanVo.builder()
                    .coachBookId(coachBook.getId())
                    .coachBookName(coachBook.getBookName())
                    .subjectId(coachBook.getSubjectId())
                    .sortCode(CacheUtil.getSubjectSortCode(coachBook.getSubjectId()))
                    .pkgs(Collections.singletonList(PkgVo.builder().name("").price(coachBook.getWrongQuesPrice().floatValue()).build()))
                    .isSubscribed(Boolean.FALSE)
                    .build());
        }

        // 根据sortCode排序
        voList.sort(Comparator.comparing(CoachBookPlanVo::getSortCode));
        return voList;
    }

    private List<SimpleVo> listSchoolsCommon(Integer semesterId, Integer term, Set<String> additionalFilter) {
        List<SchoolCoachBook> schoolCoachBookList = schoolCoachBookMapper.selectList(new LambdaQueryWrapper<SchoolCoachBook>()
                .eq(semesterId != null, SchoolCoachBook::getSemesterId, semesterId)
                .eq(term != null, SchoolCoachBook::getTerm, term)
                .select(SchoolCoachBook::getSchoolId));
        List<String> schoolIds = schoolCoachBookList.stream().map(SchoolCoachBook::getSchoolId).distinct().collect(Collectors.toList());
        // 如果additionalFilter不为空，则表示需要过滤不可见的学校
        if (!additionalFilter.isEmpty()) {
            // 只保留可见的学校
            schoolIds.removeIf(schoolId -> !additionalFilter.contains(schoolId));
        }

        if (schoolIds.isEmpty()) {
            return Collections.emptyList();
        }

        Map<String, String> schoolIdNameMap = feignService.getSchoolIdNameMap(schoolIds);
        List<SimpleVo> voList = new ArrayList<>();
        for (String schoolId : schoolIds) {
            String schoolName = schoolIdNameMap.get(schoolId);
            if (StringUtils.isNotBlank(schoolName)) {
                voList.add(new SimpleVo(schoolId, schoolName));
            }
        }

        // 学校名称按中文排序
        voList.sort(Comparator.comparing(SimpleVo::getName, Collator.getInstance(Locale.CHINA)));

        return voList;
    }

    @Override
    public FeignCoachBookPaperInfoVo getCoachBookPaperInfoByPaperId(String paperId) {
        FeignCoachBookPaperInfoVo result = new FeignCoachBookPaperInfoVo();
        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getPaperId, paperId));
        for (CoachBookPaper coachBookPaper : coachBookPapers) {
            if (Boolean.TRUE.equals(coachBookPaper.getLocked())) {
                result.setCoachBookId(coachBookPaper.getCoachBookId());
                result.setLocked(true);
                return result;
            }
        }
        return result;
    }

    @Override
    public boolean isCoachBookPaperAndLocked(String paperId) {
        return coachBookPaperMapper.selectCount(new LambdaQueryWrapper<CoachBookPaper>()
                .eq(CoachBookPaper::getPaperId, paperId)
                .eq(CoachBookPaper::getLocked, true)) > 0;
    }

}
