package com.sure.question.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.question.entity.PaperMain;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.service.PaperMainService;
import com.sure.question.service.QuestionBuildService;
import com.sure.question.service.WaterMarkManager;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.SmallQuestionOptionVo;
import com.sure.question.vo.question.SmallQuestionVo;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class PaperMainServiceImpl extends ServiceImpl<PaperMainMapper, PaperMain> implements PaperMainService {

    @Autowired
    private QuestionBuildService questionBuildService;

    @Autowired
    private WaterMarkManager waterMarkManager;

    @Value("${aliyun.ossTkImg.bucketHost}")
    private String imgHost;

    @Override
    public void paperRemoveAndAddWaterMark(String paperId) {
        List<String> imageUrls = getPaperMainImageUrls(paperId);
        doUrlRemoveAndAddWaterMark(imageUrls, true);
    }

    @Override
    public void rollback(String paperId) {
        List<String> imageUrls = getPaperMainImageUrls(paperId);
        for (String imageUrl : imageUrls) {
            waterMarkManager.rollback(imageUrl);
        }
    }

    @Override
    public void fileRemoveAddAddWaterMark(String filePath) throws IOException {
        StringBuilder builder = new StringBuilder();
        String content = "";
        InputStreamReader reader = null;
        BufferedReader bufferedReader = null;
        OutputStreamWriter writer = null;
        try {
            reader = new InputStreamReader(Files.newInputStream(Paths.get(filePath)), StandardCharsets.UTF_8);
            bufferedReader = new BufferedReader(reader);
            while ((content = bufferedReader.readLine()) != null) {
                builder.append(content);
            }
            content = builder.toString();
            content = removeAndAddHtmlImageWaterMark(content);
            writer = new OutputStreamWriter(Files.newOutputStream(Paths.get(filePath)), StandardCharsets.UTF_8);
            writer.write(content);
            writer.flush();
        } finally {
            if (reader != null) {
                reader.close();
            }
            if (bufferedReader != null) {
                bufferedReader.close();
            }
            if (writer != null) {
                writer.close();
            }
        }
    }

    @Override
    public String removeAndAddHtmlImageWaterMark(String bodyHtml) {
        if (StringUtils.isBlank(bodyHtml) || bodyHtml.equals("null")) return null;
        StringBuilder sb = new StringBuilder();
        String s = sb.append("<html><body>").append(bodyHtml).append("</body></html>").toString();
        Document document = Jsoup.parse(s);
        Elements imgs = document.getElementsByTag("img");
        Random random = new Random();
        Set<Integer> set = new HashSet<>();
        while (set.size() < (imgs.size() + 1) / 2) {
            set.add(random.nextInt(imgs.size()));
        }
        for (int i = 0; i < imgs.size(); i++) {
            Element element = imgs.get(i);
            String src = element.attr("src");
            if (src.startsWith("data")) {
                String baseStr = waterMarkManager.baseStrRemoveAddWaterMark(src, set.contains(i));
                element.attr("src", baseStr);
            }
        }
        Document.OutputSettings outputSettings = document.outputSettings();
        outputSettings.prettyPrint(false);
        document.outputSettings(outputSettings);
        return document.body().html();
    }

    private List<String> getPaperMainImageUrls(String id) {
        List<QuestionVo> questionVos = questionBuildService.selectPaperQuestionVos(id);
        List<String> urls = new ArrayList<>();
        for (QuestionVo questionVo : questionVos) {
            urls.addAll(getQuestionUrls(questionVo));
        }
        return urls;
    }

    private List<String> getQuestionUrls(QuestionVo questionVo) {
        String trunk = questionVo.getTrunk();
        List<String> urls = new ArrayList<>(findImageUrl(trunk));
        List<SmallQuestionVo> branches = questionVo.getBranches();
        for (SmallQuestionVo smallQuestionVo : branches) {
            String stem = smallQuestionVo.getStem();
            String explanation = smallQuestionVo.getExplanation();
            urls.addAll(findImageUrl(stem));
            urls.addAll(findImageUrl(explanation));
            List<SmallQuestionOptionVo> options = smallQuestionVo.getOptions();
            for (SmallQuestionOptionVo option : options) {
                urls.addAll(findImageUrl(option.getContent()));
            }
        }
        return urls;
    }

    private void doUrlRemoveAndAddWaterMark(List<String> urls, Boolean needBackup) {
        if (!urls.isEmpty()) {
            Random random = new Random();
            Set<Integer> set = new HashSet<>();
            while (set.size() < (urls.size() + 1) / 2) {
                set.add(random.nextInt(urls.size()));
            }
            for (int i = 0; i < urls.size(); i++) {
                String url = urls.get(i);
                waterMarkManager.urlRemoveAddWaterMark(url, set.contains(i), needBackup);
            }
        }
    }

    private List<String> findImageUrl(String data) {
        if (data == null) {
            return Collections.emptyList();
        }
        List<String> urls = new ArrayList<>();
        Matcher matcher = Pattern.compile(imgHost + "[^\"]+").matcher(data);
        while (matcher.find()) {
            String url = matcher.group();
            urls.add(url);
        }
        return urls;
    }


}
