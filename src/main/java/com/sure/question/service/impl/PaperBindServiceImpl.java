package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.paper.structure.*;
import com.sure.question.entity.*;
import com.sure.question.enums.BranchTypeEnum;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.mapper.*;
import com.sure.question.service.PaperBindService;
import com.sure.question.service.PermissionService;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paper.bind.BindPaperStructure;
import com.sure.question.vo.paper.bind.Knowledge;
import com.sure.question.vo.paper.bind.ObjectiveQuestion;
import com.sure.question.vo.paper.bind.SubjectiveQuestion;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaperBindServiceImpl implements PaperBindService {
    private final PaperMainMapper paperMainMapper;
    private final BasketPaperStyleMapper basketPaperStyleMapper;
    private final QuestionMapper questionMapper;
    private final SmallQuestionMapper smallQuestionMapper;
    private final QuestionTypeMainMapper questionTypeMainMapper;
    private final KnowledgeMainMapper knowledgeMainMapper;
    private final QuestionKnowledgeMapper questionKnowledgeMapper;
    private final PaperShareMapper paperShareMapper;
    private final PermissionService permissionService;

    @Override
    public BindPaperStructure extractMarkPaperStructure(String paperId, Boolean addQuestionType, Boolean addKnowledge) {
        // 查试卷及结构
        PaperMain paperMain = paperMainMapper.selectOne(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getId, paperId)
                .select(PaperMain::getId, PaperMain::getPaperName, PaperMain::getPaperStyleJson));
        if (paperMain == null) {
            throw new ApiException("找不到试卷：" + paperId);
        }
        String structureJson = paperMain.getPaperStyleJson();
        if (StringUtils.isBlank(structureJson)) {
            BasketPaperStyle basketPaperStyle = basketPaperStyleMapper.selectOne(new LambdaQueryWrapper<BasketPaperStyle>()
                    .eq(BasketPaperStyle::getPaperId, paperId)
                    .select(BasketPaperStyle::getPaperId, BasketPaperStyle::getQuestionCategoryJson));
            if (basketPaperStyle != null) {
                structureJson = basketPaperStyle.getQuestionCategoryJson();
            }
        }
        if (StringUtils.isBlank(structureJson)) {
            throw new ApiException("找不到试卷结构");
        }

        // 从结构中提取题目
        List<ObjectiveQuestion> objectiveQuestions = new ArrayList<>();
        List<SubjectiveQuestion> subjectiveQuestions = new ArrayList<>();
        try {
            parseStructureJson(structureJson, objectiveQuestions, subjectiveQuestions);
        }
        catch (Exception ex) {
            log.error("解析试卷结构失败, paperId = {}", paperId, ex);
            throw new ApiException("解析试卷结构失败");
        }

        // 补充题目数据
        if (Boolean.TRUE.equals(addQuestionType)) {
            addQuestionTypeInfo(objectiveQuestions, subjectiveQuestions);
        }
        if (Boolean.TRUE.equals(addKnowledge)) {
            addQuestionsKnowledgeList(objectiveQuestions, subjectiveQuestions);
        }

        BindPaperStructure result = new BindPaperStructure();
        result.setPaperId(paperId);
        result.setPaperName(paperMain.getPaperName());
        result.setObjectiveQuestions(objectiveQuestions);
        result.setSubjectiveQuestions(subjectiveQuestions);
        return result;
    }

    /**
     * 从试卷结构JSON中提取客观题、主观题
     */
    private void parseStructureJson(String structureJson, List<ObjectiveQuestion> objectiveQuestions, List<SubjectiveQuestion> subjectiveQuestions) {
        // 解析试卷结构
        PaperStructure structure = JSON.parseObject(structureJson, PaperStructure.class);

        // 查题型为客观题的小题
        List<String> allBranchIds = structure.extractQuestionIdBranchIdsMap().values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        Map<String, SmallQuestion> objectiveSmallQuestionMap = smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                        .in(SmallQuestion::getId, allBranchIds)
                        .in(SmallQuestion::getQuestionTypeId, BranchTypeEnum.SingleChoice.getId(), BranchTypeEnum.MultipleChoice.getId(), BranchTypeEnum.TrueOrFalse.getId())
                        .select(SmallQuestion::getId, SmallQuestion::getOptions, SmallQuestion::getQuestionTypeId, SmallQuestion::getAnswerHtml))
                .stream().collect(Collectors.toMap(SmallQuestion::getId, Function.identity()));

        for (StructureVolume volume : structure.getVolumes()) {
            for (StructureTopic topic : volume.getTopics()) {
                Integer topicCode = topic.getCode();
                String topicName = topic.getCodeInChinese();
                if (StringUtils.isNotBlank(topic.getName())) {
                    // 如：一、选择题
                    topicName += "、" + topic.getName();
                }

                for (StructureQuestion question : topic.getQuestions()) {
                    // 小题是否展开
                    boolean isBranchExpanded = question.getCode() == 0;
                    // 是否唯一小题
                    boolean isOnlyBranch = question.getBranches().size() == 1;

                    for (StructureBranch branch : question.getBranches()) {
                        int questionCode;
                        String questionName;
                        int branchCode;
                        String branchName;

                        if (isBranchExpanded) {
                            // 小题展开后其code是题号
                            questionCode = branch.getCode();
                            questionName = branch.getAlias();
                            // 小题展开后小题号为0
                            branchCode = 0;
                            branchName = questionName;
                        }
                        else if (isOnlyBranch) {
                            questionCode = question.getCode();
                            questionName = question.getAlias();
                            // 按阅卷服务业务要求，若仅有一个小题，其小题号为0
                            branchCode = 0;
                            branchName = questionName;
                        }
                        else {
                            questionCode = question.getCode();
                            questionName = question.getAlias();
                            branchCode = branch.getCode();
                            branchName = branch.getAlias();
                        }

                        // 阅卷客观题：小题题型是客观题，且是唯一小题或小题展开
                        boolean isObjective = objectiveSmallQuestionMap.containsKey(branch.getId()) && (isOnlyBranch || isBranchExpanded);

                        // 新建题目
                        com.sure.question.vo.paper.bind.Question newQuestion;
                        if (isObjective) {
                            ObjectiveQuestion obj = new ObjectiveQuestion();
                            objectiveQuestions.add(obj);
                            newQuestion = obj;
                            // 补充客观题信息（客观题型、选项个数、标准答案）
                            SmallQuestion smallQuestion = objectiveSmallQuestionMap.get(branch.getId());
                            obj.setQuestionType(smallQuestion.getQuestionTypeId());
                            obj.setAnswer(smallQuestion.getAnswerHtml());
                            if (obj.getQuestionType() == BranchTypeEnum.TrueOrFalse.getId()) {
                                obj.setOptionCount(2);
                            }
                            else {
                                obj.setOptionCount(JSON.parseArray(smallQuestion.getOptions()).size());
                            }
                        }
                        else {
                            SubjectiveQuestion subj = new SubjectiveQuestion();
                            subjectiveQuestions.add(subj);
                            newQuestion = subj;
                            subj.setBranchCode(branchCode);
                            subj.setBranchName(branchName);
                        }
                        newQuestion.setQuestionCode(questionCode);
                        newQuestion.setQuestionName(questionName);
                        newQuestion.setTopicCode(topicCode);
                        newQuestion.setTopicName(topicName);
                        if (branch.getScore() == null) {
                            newQuestion.setFullScore(0D);
                        } else {
                            newQuestion.setFullScore(branch.getScore().doubleValue());
                        }
                        newQuestion.setQuestionId(question.getId());
                        newQuestion.setBranchId(branch.getId());
                    }
                }
            }
        }
    }

    /**
     * 补充题型信息
     */
    private void addQuestionTypeInfo(List<ObjectiveQuestion> objectiveQuestions, List<SubjectiveQuestion> subjectiveQuestions) {
        // 收集所有题目Id
        List<com.sure.question.vo.paper.bind.Question> questionList = new ArrayList<>();
        questionList.addAll(objectiveQuestions);
        questionList.addAll(subjectiveQuestions);
        if (questionList.isEmpty()) {
            return;
        }
        Set<String> questionIds = questionList.stream().map(com.sure.question.vo.paper.bind.Question::getQuestionId).collect(Collectors.toSet());
        // 查题目对应题型
        List<com.sure.question.entity.Question> dbQuestions = questionMapper.selectList(new LambdaQueryWrapper<com.sure.question.entity.Question>()
                .in(com.sure.question.entity.Question::getId, questionIds)
                .select(com.sure.question.entity.Question::getId, com.sure.question.entity.Question::getQuestionTypeId));
        Map<String, Integer> questionIdTypeIdMap = dbQuestions.stream().collect(
                Collectors.toMap(com.sure.question.entity.Question::getId, com.sure.question.entity.Question::getQuestionTypeId));
        // 查题型名称
        Set<Integer> questionTypeIds = questionIdTypeIdMap.values().stream().filter(Objects::nonNull).collect(Collectors.toSet());
        if (questionTypeIds.isEmpty()) {
            return;
        }
        List<QuestionTypeMain> questionTypeMainList = questionTypeMainMapper.selectList(
                new LambdaQueryWrapper<QuestionTypeMain>().in(QuestionTypeMain::getId, questionTypeIds));
        Map<Integer, QuestionTypeMain> questionTypeMainMap = questionTypeMainList.stream().collect(
                Collectors.toMap(QuestionTypeMain::getId, Function.identity()));
        // 每题补充题型信息
        questionList.forEach(q -> {
            Integer questionTypeId = questionIdTypeIdMap.get(q.getQuestionId());
            if (questionTypeId != null) {
                q.setQuesTypeId(questionTypeId);
                QuestionTypeMain questionTypeMain = questionTypeMainMap.get(questionTypeId);
                if (questionTypeMain != null) {
                    q.setQuesTypeName(questionTypeMain.getQuestionTypeName());
                }
            }
        });
    }

    /**
     * 补充知识点
     */
    private void addQuestionsKnowledgeList(List<ObjectiveQuestion> objectiveQuestions, List<SubjectiveQuestion> subjectiveQuestions) {
        // 构建小题Id与题目的映射
        Map<String, com.sure.question.vo.paper.bind.Question> branchIdQuestionMap = new HashMap<>();
        objectiveQuestions.forEach(obj -> branchIdQuestionMap.put(obj.getBranchId(), obj));
        subjectiveQuestions.forEach(subj -> branchIdQuestionMap.put(subj.getBranchId(), subj));
        if (branchIdQuestionMap.isEmpty()) {
            return;
        }
        // 查小题知识点
        List<QuestionKnowledge> questionKnowledgeList = questionKnowledgeMapper.selectList(new LambdaQueryWrapper<QuestionKnowledge>()
                .in(QuestionKnowledge::getQuestionId, branchIdQuestionMap.keySet()));
        if (questionKnowledgeList.isEmpty()) {
            return;
        }
        Map<String, Set<Integer>> branchIdKnowledgeIdsMap = questionKnowledgeList.stream().collect(Collectors.groupingBy(QuestionKnowledge::getQuestionId,
                Collectors.mapping(QuestionKnowledge::getKnowledgeId, Collectors.toSet())));
        // 查知识点属性
        Set<Integer> knowledgeIds = questionKnowledgeList.stream().map(QuestionKnowledge::getKnowledgeId).collect(Collectors.toSet());
        List<KnowledgeMain> knowledgeMainList = knowledgeMainMapper.selectList(new LambdaQueryWrapper<KnowledgeMain>().in(KnowledgeMain::getId, knowledgeIds));
        if (knowledgeMainList.isEmpty()) {
            return;
        }
        Map<Integer, KnowledgeMain> knowledgeMap = knowledgeMainList.stream().collect(Collectors.toMap(KnowledgeMain::getId, Function.identity()));
        // 每个小题添加知识点
        branchIdQuestionMap.forEach((branchId, question) -> {
            Set<Integer> branchKnowledgeIds = branchIdKnowledgeIdsMap.get(branchId);
            if (branchKnowledgeIds != null) {
                branchKnowledgeIds.forEach(id -> {
                    KnowledgeMain knowledgeMain = knowledgeMap.get(id);
                    if (knowledgeMain != null) {
                        question.getKnowledgeList().add(new Knowledge(id, knowledgeMain.getKnowledgeName(), knowledgeMain.getLevel()));
                    }
                });
            }
        });
    }

    /**
     * 检查绑定试卷权限
     */
    @Override
    public boolean checkBindPaperPermission(String paperId, String schoolId, String userId) {
        TokenUserVo userVo = new TokenUserVo();
        userVo.setUserId(userId);
        userVo.setSchoolId(schoolId);
        return permissionService.hasViewPaperContentPermission(paperId, userVo);
    }

    /**
     * 查可绑定试卷
     */
    @Override
    public List<PaperMain> getAvailablePapers(Integer subjectId, Integer stage, Integer gradeId, Integer term, String userId, Integer type) {
        // 默认使用个人库
        if (type == null) {
            type = PaperBankEnum.Personal.getCode();
        }
        // 个人库组卷及上传
        if (PaperBankEnum.Personal.getCode().equals(type)) {
            return paperMainMapper.selectBangPaper(subjectId, stage, gradeId, term, userId);
        }
        // 分享记录
        else if (PaperBankEnum.Share.getCode().equals(type)) {
            return paperShareMapper.selectBangPaper(subjectId, stage, gradeId, term, userId);
        } else {
            return Collections.emptyList();
        }
    }
}
