package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.entity.Result;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.Question;
import com.sure.question.entity.QuestionCorrect;
import com.sure.question.enums.questionCorrect.ErrorTypeEnum;
import com.sure.question.feign.UserService;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.mapper.QuestionCorrectMapper;
import com.sure.question.mapper.QuestionMapper;
import com.sure.question.service.*;
import com.sure.question.vo.QuesUserVo;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.questioncorrect.QuestionCorrectRecordVo;
import com.sure.question.vo.questioncorrect.QuestionCorrectVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/24 11:37
 */
@Service("questionCorrectService")
@RequiredArgsConstructor
public class QuestionCorrectServiceImpl extends ServiceImpl<QuestionCorrectMapper, QuestionCorrect> implements QuestionCorrectService {

    private final QuestionCorrectMapper questionCorrectMapper;
    private final QuestionMapper questionMapper;
    private final PaperMainMapper paperMainMapper;
    private final UserService userService;
    private final FeignService feignService;
    private final QuestionBuildService questionBuildService;
    private final QuestionService questionService;
    private final PermissionService permissionService;

    @Override
    public void mark(QuestionCorrect params) {
        if (StringUtils.isAnyBlank(params.getQuestionId(), params.getErrorTypeJson(), params.getUserId())) {
            throw new ApiException("参数错误");
        }

        // 查询未处理的纠错记录
        QuestionCorrect questionCorrect = questionCorrectMapper.selectOne(new LambdaQueryWrapper<QuestionCorrect>()
                .eq(QuestionCorrect::getQuestionId, params.getQuestionId())
                .eq(QuestionCorrect::getUserId, params.getUserId())
                .eq(QuestionCorrect::getStatus, 0));
        if (questionCorrect != null) {
            throw new ApiException("您已提交此题纠错，无需再次提交");
        }
        Question question = questionMapper.selectById(params.getQuestionId());
        QuestionCorrect qc = new QuestionCorrect();
        qc.setQuestionId(params.getQuestionId());
        qc.setGradeLevel(question.getGradeLevel());
        qc.setSubjectId(question.getSubjectId());
        qc.setPaperId(params.getPaperId());
        qc.setErrorTypeJson(JSON.toJSONString(parseErrorTypeJsonThrowEx(params.getErrorTypeJson())));
        qc.setDescription(params.getDescription());
        qc.setUserId(params.getUserId());
        qc.setStatus(0);
        qc.setCreateTime(new Date());
        questionCorrectMapper.insert(qc);
    }

    private List<Integer> parseErrorTypeJsonThrowEx(String errorTypeJson) {
        List<Integer> errorTypes;
        try {
            errorTypes = JSON.parseArray(errorTypeJson, Integer.class);
        }
        catch (Exception ex) {
            throw new ApiException("参数【错误类型】有误");
        }
        List<Integer> result = new ArrayList<>();
        Set<Integer> validErrorTypeSet = Arrays.stream(ErrorTypeEnum.values()).map(ErrorTypeEnum::getCode).collect(Collectors.toSet());
        for (Integer errorType : errorTypes) {
            if (!validErrorTypeSet.contains(errorType)) {
                throw new ApiException("参数【错误类型】有误");
            }
            if (!result.contains(errorType)) {
                result.add(errorType);
            }
        }
        result.sort(Integer::compareTo);
        return result;
    }

    @Override
    public void unMark(String questionId, String userId) {
        QuestionCorrect questionCorrect = questionCorrectMapper.selectOne(new LambdaQueryWrapper<QuestionCorrect>()
                .eq(QuestionCorrect::getQuestionId, questionId)
                .eq(QuestionCorrect::getUserId, userId)
                .eq(QuestionCorrect::getStatus, 0));
        if (questionCorrect != null) {
            questionCorrectMapper.deleteById(questionCorrect.getId());
        }
    }

    @Override
    public QuestionCorrect getQuestionCorrectItem(String questionId, String userId) {
        return questionCorrectMapper.selectOne(new LambdaQueryWrapper<QuestionCorrect>()
                .eq(QuestionCorrect::getQuestionId, questionId)
                .eq(QuestionCorrect::getUserId, userId)
                .eq(QuestionCorrect::getStatus, 0));
    }

    @Override
    public Page<QuestionCorrectVo> listQuestion(Integer gradeLevel, Integer subjectId, int status, String beginTime,
                                                String endTime, Integer currentPage, Integer pageSize) {
        Page<QuestionCorrectVo> result = new Page<>(currentPage, pageSize);
        // 查报错题目总量
        int total = questionCorrectMapper.selectQuestionCount(gradeLevel, subjectId, status, beginTime, endTime);
        result.setTotal(total);
        if (total == 0) {
            return result;
        }
        // 查一页报错题目Id
        List<String> questionIds = questionCorrectMapper.selectQuestionIds(gradeLevel, subjectId, status, beginTime, endTime, (currentPage - 1) * pageSize, pageSize);
        if (questionIds.isEmpty()) {
            return result;
        }
        // 查报错记录
        List<QuestionCorrect> correctList = questionCorrectMapper.selectList(new LambdaQueryWrapper<QuestionCorrect>().in(QuestionCorrect::getQuestionId, questionIds));
        Map<String, List<QuestionCorrect>> questionCorrectListMap = correctList.stream().collect(Collectors.groupingBy(QuestionCorrect::getQuestionId));
        // 查题目内容
        List<QuestionVo> questionVos = questionBuildService.selectQuestionVos(questionIds);
        Map<String, QuestionVo> questionIdQuestionVoMap = questionVos.stream().collect(Collectors.toMap(QuestionVo::getId, x -> x));
        // 查试卷名称
        Map<String, String> paperIdPaperNameMap = new HashMap<>();
        Set<String> paperIds = correctList.stream().map(QuestionCorrect::getPaperId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (!paperIds.isEmpty()) {
            paperIdPaperNameMap = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                            .in(PaperMain::getId, paperIds)
                            .select(PaperMain::getId, PaperMain::getPaperName))
                    .stream().collect(Collectors.toMap(PaperMain::getId, PaperMain::getPaperName));
        }
        // 查报错人、处理人信息
        Set<String> userIds = new HashSet<>();
        correctList.forEach(item -> {
            userIds.add(item.getUserId());
            if (StringUtils.isNotEmpty(item.getHandlerId())) {
                userIds.add(item.getHandlerId());
            }
        });
        Result teacherInfos = userService.getTeacherInfos(new ArrayList<>(userIds));
        List<QuesUserVo> vos = JSON.parseArray(JSON.toJSONString(teacherInfos.getData()), QuesUserVo.class);
        Map<String, QuesUserVo> idQuesUserVoMap = vos.stream().collect(Collectors.toMap(QuesUserVo::getId, x -> x));

        List<String> schoolIds = vos.stream().map(QuesUserVo::getSchoolId).distinct().collect(Collectors.toList());
        Map<String, String> schoolIdNameMap = feignService.getSchoolIdNameMap(schoolIds);
        // 组装
        List<QuestionCorrectVo> records = new ArrayList<>(questionIds.size());
        for (String questionId : questionIds) {
            QuestionVo questionVo = questionIdQuestionVoMap.get(questionId);
            List<QuestionCorrect> corrects = questionCorrectListMap.get(questionId);
            if (questionVo == null || corrects == null) {
                continue;
            }

            QuestionCorrectVo vo = new QuestionCorrectVo();
            vo.setId(questionId);
            vo.setQuestion(questionVo);
            vo.setGradeLevel(questionVo.getStage());
            vo.setSubjectId(questionVo.getSubjectId());

            List<QuestionCorrectRecordVo> correctRecordVos = new ArrayList<>(corrects.size());
            vo.setRecords(correctRecordVos);
            for (QuestionCorrect correct : corrects) {
                QuestionCorrectRecordVo correctRecord = new QuestionCorrectRecordVo();
                correctRecordVos.add(correctRecord);

                correctRecord.setUserId(correct.getUserId());
                QuesUserVo user = idQuesUserVoMap.get(correct.getUserId());
                if (user != null) {
                    correctRecord.setUserName(user.getRealName());
                    correctRecord.setUserSchoolId(user.getSchoolId());
                    correctRecord.setUserSchoolName(schoolIdNameMap.get(user.getSchoolId()));
                }
                correctRecord.setHandlerId(correct.getHandlerId());
                if (StringUtils.isNotEmpty(correct.getHandlerId())) {
                    QuesUserVo handler = idQuesUserVoMap.get(correct.getHandlerId());
                    if (handler != null) {
                        correctRecord.setHandlerName(handler.getRealName());
                    }
                }
                if (correct.getPaperId() != null) {
                    correctRecord.setPaperId(correct.getPaperId());
                    correctRecord.setPaperName(paperIdPaperNameMap.get(correctRecord.getPaperId()));
                }
                correctRecord.setStatus(correct.getStatus());
                correctRecord.setCreateTime(correct.getCreateTime());
                correctRecord.setHandleTime(correct.getHandleTime());
                correctRecord.setErrorTypeJson(correct.getErrorTypeJson());
                correctRecord.setDescription(correct.getDescription());
            }
            records.add(vo);
        }

        result.setRecords(records);
        return result;
    }

    @Override
    public void updateStatusToHandled(String questionId, TokenUserVo userInfo) {
        Question question = questionMapper.selectOne(new LambdaQueryWrapper<Question>()
                .eq(Question::getId, questionId)
                .select(Question::getId, Question::getGradeLevel, Question::getSubjectId));
        if (question == null) {
            throw new ApiException("找不到题目");
        }
        permissionService.checkEditorPermission(userInfo, question.getGradeLevel(), question.getSubjectId());
        questionCorrectMapper.update(null, new LambdaUpdateWrapper<QuestionCorrect>()
                .eq(QuestionCorrect::getQuestionId, questionId)
                .eq(QuestionCorrect::getStatus, 0)
                .set(QuestionCorrect::getStatus, 1)
                .set(QuestionCorrect::getHandlerId, userInfo.getUserId())
                .set(QuestionCorrect::getHandleTime, new Date()));
    }

    /**
     * 纠正题目
     */
    @Override
    public QuestionVo correct(QuestionVo questionVo, String editorId) {
        QuestionVo result = questionService.editorChangeQuestion(questionVo, editorId);
        questionCorrectMapper.update(null, new LambdaUpdateWrapper<QuestionCorrect>()
                .eq(QuestionCorrect::getQuestionId, questionVo.getId())
                .eq(QuestionCorrect::getStatus, 0)
                .set(QuestionCorrect::getStatus, 1)
                .set(QuestionCorrect::getHandlerId, editorId)
                .set(QuestionCorrect::getHandleTime, new Date()));
        return result;
    }
}
