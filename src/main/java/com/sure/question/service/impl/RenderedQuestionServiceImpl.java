package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.entity.RenderedQuestion;
import com.sure.question.enums.BranchTypeEnum;
import com.sure.question.feign.RenderMathService;
import com.sure.question.mapper.RenderedQuestionMapper;
import com.sure.question.service.RenderedQuestionService;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.SmallQuestionOptionVo;
import com.sure.question.vo.question.SmallQuestionVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class RenderedQuestionServiceImpl implements RenderedQuestionService {
    @Resource
    private RenderedQuestionMapper renderedQuestionMapper;
    @Resource
    private RenderMathService renderMathService;

    /**
     * 替换题目中的公式
     * @param questionVos 题目列表
     * @param renderImmediately 对于未渲染公式的题目，是否立即渲染
     */
    @Override
    public void replaceRenderedContent(List<? extends QuestionVo> questionVos, boolean renderImmediately) {
        List<String> questionIdList = questionVos.stream().map(QuestionVo::getId).collect(Collectors.toList());
        List<RenderedQuestion> renderedQuestionList = renderedQuestionMapper.selectList(new LambdaQueryWrapper<RenderedQuestion>().in(RenderedQuestion::getQuestionId, questionIdList));
        Map<String, List<RenderedQuestion>> groupByQuestionId = renderedQuestionList.stream().collect(Collectors.groupingBy(RenderedQuestion::getQuestionId));
        for (QuestionVo questionVo : questionVos) {
            replaceRenderedContent(questionVo, groupByQuestionId.get(questionVo.getId()), renderImmediately);
        }
    }

    private void replaceRenderedContent(QuestionVo questionVo, List<RenderedQuestion> renderedQuestionList, boolean renderImmediately) {
        // 若无已渲染内容，则立即渲染或直接返回
        if (renderedQuestionList == null || renderedQuestionList.isEmpty()) {
            if (renderImmediately) {
                renderedQuestionList = renderQuestion(questionVo);
                if (renderedQuestionList.isEmpty()) {
                    return;
                }
            }
            else {
                return;
            }
        }
        Map<String, RenderedQuestion> smallQuestionIdMap = renderedQuestionList.stream().collect(Collectors.toMap(RenderedQuestion::getSmallQuestionId, Function.identity()));
        // 材料
        RenderedQuestion trunk = smallQuestionIdMap.get(questionVo.getId());
        if (trunk != null && StringUtils.isNotBlank(trunk.getContentHtml())) {
            questionVo.setTrunk(trunk.getContentHtml());
        }
        // 各小题
        for (SmallQuestionVo sq : questionVo.getBranches()) {
            RenderedQuestion branch = smallQuestionIdMap.get(sq.getId());
            if (branch == null) {
                continue;
            }
            if (StringUtils.isNotBlank(branch.getContentHtml())) {
                sq.setStem(branch.getContentHtml());
            }
            if (StringUtils.isNotBlank(branch.getOptions())) {
                sq.setOptionsJson(branch.getOptions());
            }
            if (StringUtils.isNotBlank(branch.getAnswerHtml())) {
                sq.setSolution(branch.getAnswerHtml());
            }
            if (StringUtils.isNotBlank(branch.getExplanation())) {
                sq.setExplanation(branch.getExplanation());
            }
        }
    }

    /**
     * 渲染题目公式
     * @param question 题目
     * @return 渲染后的内容
     */
    @Override
    public <T extends QuestionVo> List<RenderedQuestion> renderQuestion(T question) {
        String questionId = question.getId();
        List<RenderedQuestion> inserts = new ArrayList<>();
        // 材料
        if (containsMath(question.getTrunk())) {
            RenderedQuestion trunk = new RenderedQuestion();
            trunk.setSmallQuestionId(questionId);
            trunk.setContentHtml(renderMathService.renderHTMLMath(questionId, question.getTrunk()));
            inserts.add(trunk);
        }
        for (SmallQuestionVo sq : question.getBranches()) {
            RenderedQuestion branch = new RenderedQuestion();
            branch.setSmallQuestionId(sq.getId());
            // 题干
            if (containsMath(sq.getStem())) {
                branch.setContentHtml(renderMathService.renderHTMLMath(questionId, sq.getStem()));
            }
            // 选项
            boolean optionRendered = false;
            List<SmallQuestionOptionVo> options = new ArrayList<>();
            for (SmallQuestionOptionVo option : sq.getOptions()) {
                String content = option.getContent();
                if (containsMath(content)) {
                    content = renderMathService.renderHTMLMath(questionId, content);
                    optionRendered = true;
                }
                options.add(new SmallQuestionOptionVo(option.getLabel(), content));
            }
            if (optionRendered) {
                branch.setOptions(JSON.toJSONString(options));
            }
            // 答案
            if (BranchTypeEnum.FillBlank.getId().equals(sq.getQuestionTypeId())) {
                List<String> answers = JSON.parseArray(sq.getSolution(), String.class);
                boolean answerRendered = false;
                for (int i = 0; i < answers.size(); i++) {
                    String answer = answers.get(i);
                    if (containsMath(answer)) {
                        answers.set(i, renderMathService.renderHTMLMath(questionId, answer));
                        answerRendered = true;
                    }
                }
                if (answerRendered) {
                    branch.setAnswerHtml(JSON.toJSONString(answers));
                }
            }
            else {
                if (containsMath(sq.getSolution())) {
                    branch.setAnswerHtml(renderMathService.renderHTMLMath(questionId, sq.getSolution()));
                }
            }
            // 解析
            if (containsMath(sq.getExplanation())) {
                branch.setExplanation(renderMathService.renderHTMLMath(questionId, sq.getExplanation()));
            }
            if (!StringUtils.isAllBlank(branch.getContentHtml(), branch.getOptions(), branch.getAnswerHtml(), branch.getExplanation())) {
                inserts.add(branch);
            }
        }
        // 保存
        if (!inserts.isEmpty()) {
            Date createTime = new Date();
            for (RenderedQuestion insert : inserts) {
                insert.setQuestionId(questionId);
                insert.setCreateTime(createTime);
            }
            deleteRenderedQuestion(questionId);
            renderedQuestionMapper.insertBatch(inserts);
        }
        return inserts;
    }

    /**
     * 判断html字符串中是否含有公式
     */
    private static boolean containsMath(String html) {
        return StringUtils.isNotBlank(html) && (html.contains("<math") || html.contains("$"));
    }

    /**
     * 清除题目已渲染公式内容
     * @param questionId 题目Id
     */
    @Override
    public void deleteRenderedQuestion(String questionId) {
        deleteRenderedQuestions(Collections.singletonList(questionId));
    }

    /**
     * 批量清除题目已渲染公式内容
     * @param questionIds 题目Ids
     */
    @Override
    public void deleteRenderedQuestions(Collection<String> questionIds) {
        if (questionIds == null || questionIds.isEmpty()) {
            return;
        }
        // TODO 删除OSS中该题公式图片
        renderedQuestionMapper.delete(new LambdaQueryWrapper<RenderedQuestion>()
                .in(RenderedQuestion::getQuestionId, questionIds));
    }
}
