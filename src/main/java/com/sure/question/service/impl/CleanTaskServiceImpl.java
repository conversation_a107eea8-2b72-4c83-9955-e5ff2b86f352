package com.sure.question.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.*;
import com.sure.question.enums.BranchTypeEnum;
import com.sure.question.enums.PaperBankEnum;
import com.sure.question.enums.PaperStatusEnum;
import com.sure.question.enums.cleanTask.ConfirmStatusEnum;
import com.sure.question.mapper.*;
import com.sure.question.service.CleanTaskService;
import com.sure.question.service.QuestionBuildService;
import com.sure.question.vo.baseVo.GradeLevelSubjectVo;
import com.sure.question.vo.cleanTask.CheckQuestionVo;
import com.sure.question.vo.cleanTask.CleanTaskVo;
import com.sure.question.vo.cleanTask.StatsVo;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.SmallQuestionOptionVo;
import com.sure.question.vo.question.SmallQuestionVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CleanTaskServiceImpl implements CleanTaskService {
    @Resource
    private QuestionBuildService questionBuildService;
    @Resource
    private CleanTaskMapper cleanTaskMapper;
    @Resource
    private CleanTaskQuestionMapper cleanTaskQuestionMapper;
    @Resource
    private CleanTaskSmallQuestionMapper cleanTaskSmallQuestionMapper;
    @Resource
    private CleanTaskLogMapper cleanTaskLogMapper;
    @Resource
    private PaperMainMapper paperMainMapper;
    @Resource
    private CoachBookPaperMapper coachBookPaperMapper;
    @Resource
    private CoachBookCustomPaperMapper coachBookCustomPaperMapper;
    @Resource
    private ExamSubjectPushQuesMapper examSubjectPushQuesMapper;
    @Resource
    private QuestionEsSyncMapper questionEsSyncMapper;

    @Override
    public void runPublicBankTask(int taskId, IHandleQuestionHtml handleQuestionHtml) {
        runTask(taskId, this::selectPublicBankPaperIdBatch, questionBuildService::selectPaperQuestionVos, handleQuestionHtml);
    }

    @Override
    public void runCoachBookTask(int taskId, IHandleQuestionHtml handleQuestionHtml) {
        runTask(taskId, this::selectCoachBookPaperIdBatch, questionBuildService::selectPaperQuestionVos, handleQuestionHtml);
    }

    @Override
    public void runCoachBookCustomTask(int taskId, IHandleQuestionHtml handleQuestionHtml) {
        runTask(taskId, this::selectCoachBookCustomPaperIdBatch, questionBuildService::selectPaperQuestionVos, handleQuestionHtml);
    }

    @Override
    public void runPushQuestionTask(int taskId, IHandleQuestionHtml handleQuestionHtml) {
        runTask(taskId, this::selectPushPaperIdBatch, this::selectPushQuestions, handleQuestionHtml);
    }

    /**
     * 查公共库试卷
     */
    private List<String> selectPublicBankPaperIdBatch(int gradeLevel, int subjectId, String lastPaperId) {
        List<PaperMain> papers = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>()
                .eq(gradeLevel > 0, PaperMain::getGradeLevel, gradeLevel)
                .eq(subjectId > 0, PaperMain::getSubjectId, subjectId)
                .eq(PaperMain::getStatus, PaperStatusEnum.Status0.getCode())
                .eq(PaperMain::getPaperBank, PaperBankEnum.Public.getCode())
                .gt(lastPaperId != null, PaperMain::getId, lastPaperId)
                .orderByAsc(PaperMain::getId)
                .last("limit 100")
                .select(PaperMain::getId));
        return papers.stream().map(PaperMain::getId).collect(Collectors.toList());
    }

    /**
     * 查教辅试卷
     */
    private List<String> selectCoachBookPaperIdBatch(int gradeLevel, int subjectId, String lastPaperId) {
        List<CoachBookPaper> coachBookPapers = coachBookPaperMapper.selectList(new LambdaQueryWrapper<CoachBookPaper>()
                .gt(lastPaperId != null, CoachBookPaper::getPaperId, lastPaperId)
                .orderByAsc(CoachBookPaper::getPaperId)
                .last("limit 100")
                .select(CoachBookPaper::getPaperId));
        return coachBookPapers.stream().map(CoachBookPaper::getPaperId).collect(Collectors.toList());
    }

    /**
     * 查教辅定制卷
     */
    private List<String> selectCoachBookCustomPaperIdBatch(int gradeLevel, int subjectId, String lastPaperId) {
        List<CoachBookCustomPaper> coachBookPapers = coachBookCustomPaperMapper.selectList(new LambdaQueryWrapper<CoachBookCustomPaper>()
                .gt(lastPaperId != null, CoachBookCustomPaper::getPaperId, lastPaperId)
                .orderByAsc(CoachBookCustomPaper::getPaperId)
                .last("limit 100")
                .select(CoachBookCustomPaper::getPaperId));
        return coachBookPapers.stream().map(CoachBookCustomPaper::getPaperId).collect(Collectors.toList());
    }

    /**
     * 查推题卷
     */
    private List<String> selectPushPaperIdBatch(int gradeLevel, int subjectId, String lastPaperId) {
        List<ExamsubjectPushQues> examSubjectPushQuesList = examSubjectPushQuesMapper.selectList(new QueryWrapper<ExamsubjectPushQues>()
                .gt(lastPaperId != null, "paper_id", lastPaperId)
                .orderByAsc("paper_id")
                .last("limit 100")
                .select("DISTINCT paper_id"));
        return examSubjectPushQuesList.stream().map(ExamsubjectPushQues::getPaperId).collect(Collectors.toList());
    }

    /**
     * 查推题题目
     */
    private List<QuestionVo> selectPushQuestions(String paperId) {
        List<ExamsubjectPushQues> examSubjectPushQuesList = examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getPaperId, paperId));
        List<String> pushQuestionIds = examSubjectPushQuesList.stream().map(ExamsubjectPushQues::getPushId).collect(Collectors.toList());
        return questionBuildService.selectQuestionVos(pushQuestionIds);
    }

    @Override
    public void runTask(int taskId, IGetPaperIdBatch getPaperIdBatch, ISelectPaperQuestions selectPaperQuestions, IHandleQuestionHtml handleQuestionHtml) {
        log.info("清理任务开始, taskId = {}", taskId);
        CleanTask cleanTask = cleanTaskMapper.selectById(taskId);
        if (cleanTask == null) {
            log.info("清理任务不存在, taskId = {}", taskId);
            return;
        }
        List<String> paperIds;
        while (!(paperIds = getPaperIdBatch.apply(cleanTask.getGradeLevel(), cleanTask.getSubjectId(), cleanTask.getLastPaperId())).isEmpty()) {
            for (String paperId : paperIds) {
                log.info("处理试卷开始, taskId = {}, paperId = {}", taskId, paperId);
                handleOnePaper(cleanTask, paperId, selectPaperQuestions, handleQuestionHtml);
                log.info("处理试卷结束, taskId = {}, paperId = {}", taskId, paperId);
            }
        }
        log.info("清理任务结束, taskId = {}", taskId);
        System.out.println("清理任务结束, taskId = " + taskId);
    }

    private void handleOnePaper(CleanTask cleanTask, String paperId, ISelectPaperQuestions selectPaperQuestions, IHandleQuestionHtml handleQuestionHtml) {
        Integer taskId = cleanTask.getId();

        // 结果列表
        List<CleanTaskQuestion> taskQuestionList = new ArrayList<>();
        List<CleanTaskSmallQuestion> taskSmallQuestionList = new ArrayList<>();
        List<CleanTaskLog> taskLogList = new ArrayList<>();

        // 查题目数据
        List<QuestionVo> questionVoList = selectPaperQuestions.select(paperId);
        Set<String> questionIds = questionVoList.stream().map(QuestionVo::getId).collect(Collectors.toSet());
        if (questionIds.isEmpty()) {
            return;
        }

        // 多份试卷可能包含同一道题，需剔除已处理的题目
        List<CleanTaskQuestion> cleanTaskQuestionRecords = cleanTaskQuestionMapper.selectList(new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .in(CleanTaskQuestion::getQuestionId, questionIds)
                .select(CleanTaskQuestion::getTaskId, CleanTaskQuestion::getQuestionId));
        for (CleanTaskQuestion record : cleanTaskQuestionRecords) {
            questionVoList.removeIf(vo -> vo.getId().equals(record.getQuestionId()));
            questionIds.remove(record.getQuestionId());
        }

        // 遍历题目
        for (QuestionVo questionVo : questionVoList) {
            try {
                handleOneQuestion(questionVo, taskQuestionList, taskSmallQuestionList, handleQuestionHtml);
            }
            catch (Exception ex) {
                String message = ex.getMessage();
                String smallQuestionId = null;
                if (StringUtils.isNotEmpty(message)) {
                    Matcher matcher = Pattern.compile("^小题[I,i]d[:：]\\s*(\\d+)[,;\\s]*").matcher(message);
                    if (matcher.find()) {
                        smallQuestionId = matcher.group(1);
                        message = message.substring(matcher.group(0).length());
                    }
                }
                CleanTaskLog errorLog = new CleanTaskLog();
                errorLog.setTaskId(taskId);
                errorLog.setPaperId(paperId);
                errorLog.setQuestionId(questionVo.getId());
                errorLog.setSmallQuestionId(smallQuestionId);
                errorLog.setContent(message);
                errorLog.setCreateTime(new Date());
                taskLogList.add(errorLog);
            }
        }

        // 保存结果
        try {
            if (!taskQuestionList.isEmpty()) {
                taskQuestionList.forEach(taskQuestion -> {
                    taskQuestion.setTaskId(taskId);
                    taskQuestion.setPaperId(paperId);
                    taskQuestion.setConfirmStatus(ConfirmStatusEnum.Init.getCode());
                    taskQuestion.setIsUpdated(false);
                });
                cleanTaskQuestionMapper.insertBatch(taskQuestionList);
            }
            if (!taskSmallQuestionList.isEmpty()) {
                taskSmallQuestionList.forEach(taskSmallQuestion -> taskSmallQuestion.setTaskId(taskId));
                cleanTaskSmallQuestionMapper.insertBatch(taskSmallQuestionList);
            }
            if (!taskLogList.isEmpty()) {
                cleanTaskLogMapper.insertBatch(taskLogList);
            }
            cleanTask.setLastPaperId(paperId);
            cleanTask.setLastPaperCount(1 + (cleanTask.getLastPaperCount() == null ? 0 : cleanTask.getLastPaperCount()));
            cleanTask.setUpdateTime(new Date());
            cleanTaskMapper.updateById(cleanTask);
        }
        catch (Exception ex) {
            cleanTaskQuestionMapper.delete(new LambdaQueryWrapper<CleanTaskQuestion>()
                    .eq(CleanTaskQuestion::getTaskId, taskId)
                    .in(CleanTaskQuestion::getQuestionId, questionIds));
            cleanTaskSmallQuestionMapper.delete(new LambdaQueryWrapper<CleanTaskSmallQuestion>()
                    .eq(CleanTaskSmallQuestion::getTaskId, taskId)
                    .in(CleanTaskSmallQuestion::getQuestionId, questionIds));
            cleanTaskLogMapper.delete(new LambdaQueryWrapper<CleanTaskLog>()
                    .eq(CleanTaskLog::getTaskId, taskId)
                    .in(CleanTaskLog::getQuestionId, questionIds));
            throw ex;
        }
    }

    private void handleOneQuestion(QuestionVo questionVo,
                                   List<CleanTaskQuestion> paperTaskQuestions,
                                   List<CleanTaskSmallQuestion> paperTaskSmallQuestions,
                                   IHandleQuestionHtml handleQuestionHtml) throws Exception {
        CleanTaskQuestion taskQuestion = new CleanTaskQuestion();
        taskQuestion.setQuestionId(questionVo.getId());
        taskQuestion.setCreateTime(new Date());
        List<CleanTaskSmallQuestion> taskSmallQuestions = new ArrayList<>();

        // 处理材料
        handleContentAndSetValue(handleQuestionHtml, questionVo.getId(), questionVo.getTrunk(), taskQuestion::setContentHtml, taskQuestion::setOldContentHtml);

        // 遍历小题
        for (SmallQuestionVo smallQuestionVo : questionVo.getBranches()) {
            CleanTaskSmallQuestion t = new CleanTaskSmallQuestion();
            t.setQuestionId(questionVo.getId());
            t.setSmallQuestionId(smallQuestionVo.getId());
            try {
                handleOneBranch(smallQuestionVo, handleQuestionHtml, t);
            }
            catch (Exception ex) {
                throw new Exception("小题Id:" + smallQuestionVo.getId() + "," + ex.getMessage());
            }
            if (!StringUtils.isAllEmpty(t.getContentHtml(), t.getOptions(), t.getAnswerHtml(), t.getExplanation())) {
                t.setQuestionId(questionVo.getId());
                t.setSmallQuestionId(smallQuestionVo.getId());
                t.setCreateTime(new Date());
                taskSmallQuestions.add(t);
            }
        }

        if (StringUtils.isNotEmpty(taskQuestion.getContentHtml()) || !taskSmallQuestions.isEmpty()) {
            paperTaskQuestions.add(taskQuestion);
            paperTaskSmallQuestions.addAll(taskSmallQuestions);
        }
    }

    private void handleOneBranch(SmallQuestionVo smallQuestionVo, IHandleQuestionHtml handleQuestionHtml, CleanTaskSmallQuestion t) throws Exception {
        // 处理题干
        handleContentAndSetValue(handleQuestionHtml, t.getQuestionId(), smallQuestionVo.getStem(), t::setContentHtml, t::setOldContentHtml);
        // 处理选项
        boolean optionFlag = false;
        List<SmallQuestionOptionVo> newOptions = new ArrayList<>();
        for (SmallQuestionOptionVo option : smallQuestionVo.getOptions()) {
            SmallQuestionOptionVo newOption = new SmallQuestionOptionVo(option.getLabel(), option.getContent());
            String html = handleQuestionHtml.apply(option.getContent(), t.getQuestionId());
            if (StringUtils.isNotEmpty(html)) {
                optionFlag = true;
                newOption.setContent(html);
            }
            newOptions.add(newOption);
        }
        if (optionFlag) {
            t.setOptions(JSON.toJSONString(newOptions));
            t.setOldOptions(JSON.toJSONString(smallQuestionVo.getOptions()));
        }
        // 处理答案
        String answer = smallQuestionVo.getSolution();
        if (BranchTypeEnum.FillBlank.getId().equals(smallQuestionVo.getQuestionTypeId())) {
            List<String> answerList;
            try {
                answerList = JSON.parseArray(answer, String.class);
            }
            catch (Exception ex) {
                throw new Exception("解析填空题答案失败");
            }
            boolean answerFlag = false;
            List<String> newAnswerList = new ArrayList<>();
            for (String answerItem : answerList) {
                String html = handleQuestionHtml.apply(answerItem, t.getQuestionId());
                if (StringUtils.isNotEmpty(html)) {
                    newAnswerList.add(html);
                    answerFlag = true;
                }
                else {
                    newAnswerList.add(answerItem);
                }
            }
            if (answerFlag) {
                t.setAnswerHtml(JSON.toJSONString(newAnswerList));
                t.setOldAnswerHtml(answer);
            }
        }
        else {
            handleContentAndSetValue(handleQuestionHtml, t.getQuestionId(), answer, t::setAnswerHtml, t::setOldAnswerHtml);
        }
        // 处理解析
        handleContentAndSetValue(handleQuestionHtml, t.getQuestionId(), smallQuestionVo.getExplanation(), t::setExplanation, t::setOldExplanation);
    }

    private void handleContentAndSetValue(IHandleQuestionHtml handleQuestionHtml, String questionId, String content, Consumer<String> setNewValue, Consumer<String> setOldValue) throws Exception {
        String result = handleQuestionHtml.apply(content, questionId);
        if (StringUtils.isNotEmpty(result)) {
            if (setNewValue != null) {
                setNewValue.accept(result);
            }
            if (setOldValue != null) {
                setOldValue.accept(content);
            }
        }
    }

    @Override
    public void createCleanTask(String name, String description, int gradeLevel, int subjectId) {
        CleanTask newTask = new CleanTask();
        newTask.setName(name);
        newTask.setDescription(description);
        newTask.setGradeLevel(gradeLevel);
        newTask.setSubjectId(subjectId);
        newTask.setCreateTime(new Date());
        cleanTaskMapper.insert(newTask);
    }

    @Override
    public void createCleanTaskForAllGradeLevelSubject(String name, String description) {
        List<GradeLevelSubjectVo> gradeLevelSubjectVos = paperMainMapper.selectPublicBankAllGradeLevelSubjects();
        for (GradeLevelSubjectVo vo : gradeLevelSubjectVos) {
            createCleanTask(name, description, vo.getGradeLevel(), vo.getSubjectId());
        }
    }

    @Override
    public List<CleanTaskVo> getCleanTaskList(Integer gradeLevel, Integer subjectId, String name, boolean addStats) {
        List<CleanTask> cleanTaskList = cleanTaskMapper.selectList(new LambdaQueryWrapper<CleanTask>()
                .eq(gradeLevel != null, CleanTask::getGradeLevel, gradeLevel)
                .eq(subjectId != null, CleanTask::getSubjectId, subjectId)
                .eq(StringUtils.isNotEmpty(name), CleanTask::getName, name));
        return addTaskStatsAndBuildVo(cleanTaskList, addStats);
    }

    @Override
    public CleanTaskVo getCleanTaskInfo(int taskId) {
        List<CleanTask> cleanTaskList = cleanTaskMapper.selectList(new LambdaQueryWrapper<CleanTask>().eq(CleanTask::getId, taskId));
        List<CleanTaskVo> vos = addTaskStatsAndBuildVo(cleanTaskList, true);
        if (vos.isEmpty()) {
            return null;
        }
        return vos.get(0);
    }

    private List<CleanTaskVo> addTaskStatsAndBuildVo(List<CleanTask> tasks, boolean addStats) {
        if (tasks.isEmpty()) {
            return Collections.emptyList();
        }
        List<StatsVo> statsList = new ArrayList<>();
        if (addStats) {
            List<Integer> taskIds = tasks.stream().map(CleanTask::getId).collect(Collectors.toList());
            statsList = cleanTaskQuestionMapper.selectTaskStats(taskIds);
        }
        Map<Integer, StatsVo> statsMap = statsList.stream().collect(Collectors.toMap(StatsVo::getTaskId, Function.identity()));
        return tasks.stream().map(cleanTask -> {
            CleanTaskVo vo = CleanTaskVo.builder()
                    .id(cleanTask.getId())
                    .name(cleanTask.getName())
                    .description(cleanTask.getDescription())
                    .gradeLevel(cleanTask.getGradeLevel())
                    .subjectId(cleanTask.getSubjectId())
                    .lastPaperId(cleanTask.getLastPaperId())
                    .lastPaperCount(cleanTask.getLastPaperCount())
                    .createTime(cleanTask.getCreateTime())
                    .updateTime(cleanTask.getUpdateTime())
                    .build();
            StatsVo stats = statsMap.get(cleanTask.getId());
            if (stats != null) {
                vo.setCheckedQuestionCount(stats.getCheckedQuestionCount());
                vo.setConfirmedQuestionCount(stats.getConfirmedQuestionCount());
                vo.setIgnoredQuestionCount(stats.getIgnoredQuestionCount());
                vo.setUpdatedQuestionCount(stats.getUpdatedQuestionCount());
            }
            if (vo.getLastPaperCount() == null) {
                vo.setLastPaperCount(0);
            }
            if (vo.getCheckedQuestionCount() == null) {
                vo.setCheckedQuestionCount(0);
            }
            if (vo.getConfirmedQuestionCount() == null) {
                vo.setConfirmedQuestionCount(0);
            }
            if (vo.getIgnoredQuestionCount() == null) {
                vo.setIgnoredQuestionCount(0);
            }
            if (vo.getUpdatedQuestionCount() == null) {
                vo.setUpdatedQuestionCount(0);
            }
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public Page<CheckQuestionVo> getUnConfirmQuestionPage(int taskId, Integer page, Integer size) {
        return getCheckQuestionVoPage(taskId, page, size, new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Init.getCode())
                .eq(CleanTaskQuestion::getIsUpdated, false)
                .orderByAsc(CleanTaskQuestion::getPaperId)
                .orderByAsc(CleanTaskQuestion::getQuestionId));
    }

    @Override
    public Page<CheckQuestionVo> getIgnoredQuestionPage(int taskId, Integer page, Integer size) {
        return getCheckQuestionVoPage(taskId, page, size, new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Ignored.getCode())
                .eq(CleanTaskQuestion::getIsUpdated, false)
                .orderByAsc(CleanTaskQuestion::getPaperId)
                .orderByAsc(CleanTaskQuestion::getQuestionId));
    }

    @Override
    public Page<CheckQuestionVo> getConfirmedQuestionPage(int taskId, Integer page, Integer size) {
        return getCheckQuestionVoPage(taskId, page, size, new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Confirmed.getCode())
                .eq(CleanTaskQuestion::getIsUpdated, false)
                .orderByAsc(CleanTaskQuestion::getPaperId)
                .orderByAsc(CleanTaskQuestion::getQuestionId));
    }

    @Override
    public Page<CheckQuestionVo> getUpdatedQuestionPage(int taskId, Integer page, Integer size) {
        return getCheckQuestionVoPage(taskId, page, size, new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getIsUpdated, true)
                .orderByAsc(CleanTaskQuestion::getPaperId)
                .orderByAsc(CleanTaskQuestion::getQuestionId));
    }

    private Page<CheckQuestionVo> getCheckQuestionVoPage(int taskId, Integer page, Integer size, LambdaQueryWrapper<CleanTaskQuestion> query) {
        // 新建一页
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 1;
        }
        Page<CheckQuestionVo> result = new Page<>(page, size);
        Page<CleanTaskQuestion> taskQuestionPage = new Page<>(page, size);

        // 查一页题目
        cleanTaskQuestionMapper.selectPage(taskQuestionPage, query);
        result.setTotal(taskQuestionPage.getTotal());
        List<CleanTaskQuestion> taskQuestionList = taskQuestionPage.getRecords();
        if (taskQuestionList.isEmpty()) {
            return result;
        }
        List<String> questionIds = taskQuestionList.stream().map(CleanTaskQuestion::getQuestionId).collect(Collectors.toList());
        // 查小题
        List<CleanTaskSmallQuestion> taskSmallQuestionList = cleanTaskSmallQuestionMapper.selectList(new LambdaQueryWrapper<CleanTaskSmallQuestion>().eq(CleanTaskSmallQuestion::getTaskId, taskId).in(CleanTaskSmallQuestion::getQuestionId, questionIds));
        Map<String, List<CleanTaskSmallQuestion>> taskSmallQuestionMap = taskSmallQuestionList.stream().collect(Collectors.groupingBy(CleanTaskSmallQuestion::getQuestionId));
        // 查试卷
        Set<String> paperIds = taskQuestionList.stream().map(CleanTaskQuestion::getPaperId).collect(Collectors.toSet());
        List<PaperMain> paperMainList = paperMainMapper.selectList(new LambdaQueryWrapper<PaperMain>().in(PaperMain::getId, paperIds).select(PaperMain::getId, PaperMain::getPaperName));
        Map<String, String> paperIdNameMap = paperMainList.stream().collect(Collectors.toMap(PaperMain::getId, PaperMain::getPaperName));
        // 查原题
        List<QuestionVo> questionVoList = questionBuildService.selectQuestionVos(questionIds);
        Map<String, QuestionVo> questionVoMap = questionVoList.stream().collect(Collectors.toMap(QuestionVo::getId, Function.identity()));
        // 组装
        List<CheckQuestionVo> records = taskQuestionList.stream().map(taskQuestion -> {
            String questionId = taskQuestion.getQuestionId();
            String paperId = taskQuestion.getPaperId();

            CheckQuestionVo vo = new CheckQuestionVo();
            vo.setTaskId(taskId);
            vo.setQuestionId(questionId);
            vo.setPaperId(paperId);
            vo.setPaperName(paperIdNameMap.get(paperId));
            vo.setQuestionVo(questionVoMap.get(questionId));
            vo.setTaskQuestion(taskQuestion);
            List<CleanTaskSmallQuestion> taskSmallQuestions = taskSmallQuestionMap.get(questionId);
            if (taskSmallQuestions != null) {
                vo.setTaskSmallQuestion(taskSmallQuestions);
            }
            return vo;
        }).collect(Collectors.toList());
        result.setRecords(records);
        return result;
    }

    @Override
    public void confirmTaskQuestion(int taskId, String questionId) {
        CleanTaskQuestion taskQuestion = getOneTaskQuestionAndCheckCanConfirm(taskId, questionId);
        doConfirmOneQuestion(taskQuestion);
    }

    private CleanTaskQuestion getOneTaskQuestionAndCheckCanConfirm(int taskId, String questionId) {
        CleanTaskQuestion taskQuestion = cleanTaskQuestionMapper.selectOne(new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getQuestionId, questionId));
        if (taskQuestion == null) {
            throw new ApiException("找不到题目");
        }
        if (ConfirmStatusEnum.Confirmed.getCode().equals(taskQuestion.getConfirmStatus())) {
            throw new ApiException("该题目已确认");
        }
        return taskQuestion;
    }

    private void doConfirmOneQuestion(CleanTaskQuestion taskQuestion) {
        cleanTaskQuestionMapper.update(null, new LambdaUpdateWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskQuestion.getTaskId())
                .eq(CleanTaskQuestion::getQuestionId, taskQuestion.getQuestionId())
                .set(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Confirmed.getCode())
                .set(CleanTaskQuestion::getConfirmTime, new Date()));
    }

    @Override
    public void ignoreTaskQuestion(int taskId, String questionId) {
        CleanTaskQuestion taskQuestion = cleanTaskQuestionMapper.selectOne(new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getQuestionId, questionId)
                .select(CleanTaskQuestion::getConfirmStatus, CleanTaskQuestion::getIsUpdated));
        if (taskQuestion == null) {
            throw new ApiException("找不到题目");
        }
        if (ConfirmStatusEnum.Ignored.getCode().equals(taskQuestion.getConfirmStatus())) {
            throw new ApiException("该题目已忽略");
        }
        if (taskQuestion.getIsUpdated()) {
            throw new ApiException("该题目已更新");
        }
        cleanTaskQuestionMapper.update(null, new LambdaUpdateWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getQuestionId, questionId)
                .set(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Ignored.getCode())
                .set(CleanTaskQuestion::getConfirmTime, new Date()));
    }

    @Override
    public void updateOneQuestion(int taskId, String questionId) {
        CleanTaskQuestion taskQuestion = cleanTaskQuestionMapper.selectOne(new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getQuestionId, questionId));
        if (taskQuestion == null) {
            throw new ApiException("找不到题目");
        }
        if (!ConfirmStatusEnum.Confirmed.getCode().equals(taskQuestion.getConfirmStatus())) {
            throw new ApiException("该题目尚未确认");
        }
        if (Boolean.TRUE.equals(taskQuestion.getIsUpdated())) {
            throw new ApiException("该题目已更新");
        }
        doUpdateOneQuestion(taskQuestion);
    }

    @Transactional(rollbackFor = Exception.class)
    public void doUpdateOneQuestion(CleanTaskQuestion taskQuestion) {
        int taskId = taskQuestion.getTaskId();
        String questionId = taskQuestion.getQuestionId();
        if (StringUtils.isNotEmpty(taskQuestion.getContentHtml())) {
            cleanTaskQuestionMapper.updateQuestionContent(taskId, questionId);
        }
        cleanTaskSmallQuestionMapper.updateSmallQuestionContent(taskId, questionId);
        cleanTaskQuestionMapper.update(null, new LambdaUpdateWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getQuestionId, questionId)
                .set(CleanTaskQuestion::getIsUpdated, true)
                .set(CleanTaskQuestion::getUpdateTime, new Date()));
        questionEsSyncMapper.insertPendingQuestionsBatch(Collections.singletonList(questionId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmAndUpdateQuestion(int taskId, String questionId) {
        CleanTaskQuestion taskQuestion = getOneTaskQuestionAndCheckCanConfirm(taskId, questionId);
        doConfirmOneQuestion(taskQuestion);
        doUpdateOneQuestion(taskQuestion);
    }

    @Override
    public void updateAllConfirmedQuestions(int taskId) {
        LambdaQueryWrapper<CleanTaskQuestion> query = new LambdaQueryWrapper<CleanTaskQuestion>()
                .eq(CleanTaskQuestion::getTaskId, taskId)
                .eq(CleanTaskQuestion::getConfirmStatus, ConfirmStatusEnum.Confirmed.getCode())
                .eq(CleanTaskQuestion::getIsUpdated, false)
                .orderByAsc(CleanTaskQuestion::getQuestionId)
                .last("limit 100");
        List<CleanTaskQuestion> taskQuestionList;
        while (!(taskQuestionList = cleanTaskQuestionMapper.selectList(query)).isEmpty()) {
            for (CleanTaskQuestion taskQuestion : taskQuestionList) {
                doUpdateOneQuestion(taskQuestion);
            }
        }
    }
}
