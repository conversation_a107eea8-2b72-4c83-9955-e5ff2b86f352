package com.sure.question.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.paper.structure.CheckPaperStructureOption;
import com.sure.question.dto.paper.structure.PaperStructure;
import com.sure.question.entity.PaperQuestion;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.mapper.PaperQuestionMapper;
import com.sure.question.mapper.SmallQuestionMapper;
import com.sure.question.service.PaperQuestionService;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.SmallQuestionVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaperQuestionServiceImpl implements PaperQuestionService {
    private final PaperQuestionMapper paperQuestionMapper;
    private final SmallQuestionMapper smallQuestionMapper;

    /**
     * 查出试卷中题目Id与小题Id映射
     */
    @Override
    public Map<String, Set<String>> getPaperQuestionIdBranchIdsMap(String paperId) {
        List<String> questionIds = paperQuestionMapper.selectList(new LambdaQueryWrapper<PaperQuestion>()
                        .eq(PaperQuestion::getPaperId, paperId)
                        .select(PaperQuestion::getQuestionId))
                .stream().map(PaperQuestion::getQuestionId).collect(Collectors.toList());
        if (questionIds.isEmpty()) {
            return new HashMap<>();
        }
        return smallQuestionMapper.selectList(new LambdaQueryWrapper<SmallQuestion>()
                        .in(SmallQuestion::getQuestionId, questionIds)
                        .select(SmallQuestion::getQuestionId, SmallQuestion::getId))
                .stream().collect(Collectors.groupingBy(SmallQuestion::getQuestionId, Collectors.mapping(SmallQuestion::getId, Collectors.toSet())));
    }

    /**
     * 检查试卷结构
     */
    @Override
    public void checkPaperStructure(String paperId,
                                    String paperStructure,
                                    CheckPaperStructureOption option,
                                    Collection<QuestionVo> addQuestions,
                                    Collection<QuestionVo> changeQuestions,
                                    Collection<String> deleteQuestionIds) {
        if (StringUtils.isBlank(paperStructure)) {
            throw new ApiException("试卷结构不能为空");
        }

        PaperStructure structure;
        try {
            structure = JSON.parseObject(paperStructure, PaperStructure.class);
        } catch (Exception e) {
            throw new ApiException("试卷结构格式错误");
        }

        // 检查结构
        structure.check(option);

        // 查出试卷题目与小题
        Map<String, Set<String>> paperQuestionIdBranchIdsMap = getPaperQuestionIdBranchIdsMap(paperId);

        // 增删改试卷题目
        Set<String> addQuestionIds = CollUtil.isEmpty(addQuestions) ? Collections.emptySet() : addQuestions.stream().map(QuestionVo::getId).collect(Collectors.toSet());
        Set<String> changeQuestionIds = CollUtil.isEmpty(changeQuestions) ? Collections.emptySet() : changeQuestions.stream().map(QuestionVo::getId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(deleteQuestionIds)) {
            deleteQuestionIds = Collections.emptySet();
        }
        if (!addQuestionIds.isEmpty()) {
            for (QuestionVo q : addQuestions) {
                if (changeQuestionIds.contains(q.getId())) {
                    throw new ApiException("添加的题目不能同时修改");
                }
                if (deleteQuestionIds.contains(q.getId())) {
                    throw new ApiException("添加的题目不能同时删除");
                }
                if (paperQuestionIdBranchIdsMap.containsKey(q.getId())) {
                    throw new ApiException("添加的题目已存在");
                }
                paperQuestionIdBranchIdsMap.put(q.getId(), q.getBranches().stream().map(SmallQuestionVo::getId).collect(Collectors.toSet()));
            }
        }
        if (!changeQuestionIds.isEmpty()) {
            for (QuestionVo q : changeQuestions) {
                if (addQuestionIds.contains(q.getId())) {
                    throw new ApiException("修改的题目不能同时删除");
                }
                if (deleteQuestionIds.contains(q.getId())) {
                    throw new ApiException("修改的题目不能同时删除");
                }
                if (!paperQuestionIdBranchIdsMap.containsKey(q.getId())) {
                    throw new ApiException("修改的题目不存在");
                }
                paperQuestionIdBranchIdsMap.put(q.getId(), q.getBranches().stream().map(SmallQuestionVo::getId).collect(Collectors.toSet()));
            }
        }
        if (!deleteQuestionIds.isEmpty()) {
            for (String qId : deleteQuestionIds) {
                if (addQuestionIds.contains(qId)) {
                    throw new ApiException("删除的题目不能同时添加");
                }
                if (changeQuestionIds.contains(qId)) {
                    throw new ApiException("删除的题目不能同时修改");
                }
                if (!paperQuestionIdBranchIdsMap.containsKey(qId)) {
                    throw new ApiException("删除的题目不存在");
                }
                paperQuestionIdBranchIdsMap.remove(qId);
            }
        }

        // 比较结构题目与试卷题目
        if (!structure.compareStructureQuestionsWithPaperQuestions(paperQuestionIdBranchIdsMap)) {
            throw new ApiException("试卷结构与题目结构不一致");
        }
    }
}
