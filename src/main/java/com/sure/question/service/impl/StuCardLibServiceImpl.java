package com.sure.question.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.CoachStuCard;
import com.sure.question.entity.StuCardLib;
import com.sure.question.mapper.CoachStuCardMapper;
import com.sure.question.mapper.StuCardLibMapper;
import com.sure.question.service.StuCardLibService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StuCardLibServiceImpl extends ServiceImpl<StuCardLibMapper, StuCardLib> implements StuCardLibService {
    private final CoachStuCardMapper coachStuCardMapper;

    @Override
    public int generateCardBatch(Integer length, Integer totalCount) {
        // 校验参数
        if (length < 8) {
            throw new ApiException("length 不能小于 8");
        }

        // 查出已有的卡号
        List<String> existsNos = this.list(new LambdaQueryWrapper<StuCardLib>().select(StuCardLib::getCardNo)).stream().map(StuCardLib::getCardNo).collect(Collectors.toList());
        List<String> usedNos = coachStuCardMapper.selectList(new LambdaQueryWrapper<CoachStuCard>().select(CoachStuCard::getCardNo)).stream().map(x -> x.getCardNo().substring(1)).collect(Collectors.toList());
        existsNos.addAll(usedNos);

        // 预生成一批卡号
        List<String> newNos = new ArrayList<>();
        String elemData = "0o1ir";
        for (int i = 0; i < totalCount; i++) {
            for (int j = 0; j < 3; j++) {
                String no = RandomUtil.randomStringWithoutStr(length, elemData).toUpperCase();
                if (!existsNos.contains(no) && !newNos.contains(no)) {
                    newNos.add(no);
                    break;
                }
            }
        }
        if (!newNos.isEmpty()) {
            List<StuCardLib> newList = new LinkedList<>();
            for (String no : newNos) {
                newList.add(StuCardLib.builder().cardNo(no).build());
            }
            this.saveBatch(newList);
        }

        return newNos.size();
    }
}