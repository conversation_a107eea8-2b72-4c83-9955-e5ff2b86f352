package com.sure.question.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.ExamsubjectPushQues;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.PaperQuestion;
import com.sure.question.entity.PushQuesTask;
import com.sure.question.enums.PushQuesTaskStatusEnum;
import com.sure.question.mapper.ExamSubjectPushQuesMapper;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.mapper.PaperQuestionMapper;
import com.sure.question.mapper.PushQuesTaskMapper;
import com.sure.question.service.*;
import com.sure.question.util.SimilarUtil;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.dataVo.KeyValueInt;
import com.sure.question.vo.pushQuesVo.*;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.UserQuestionVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("examSubjectPushQuesService")
@RequiredArgsConstructor
@Slf4j
public class ExamSubjectPushQuesServiceImpl extends ServiceImpl<ExamSubjectPushQuesMapper, ExamsubjectPushQues> implements ExamSubjectPushQuesService {
    private final PushQuesTaskMapper pushQuesTaskMapper;
    private final ExamSubjectPushQuesMapper examSubjectPushQuesMapper;
    private final PaperMainMapper paperMainMapper;
    private final PaperQuestionMapper paperQuestionMapper;
    private final QuestionBuildService questionBuildService;
    private final QuestionSearchService questionSearchService;
    private final PermissionService permissionService;
    private final FeignService feignService;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 记录推题任务
     */
    @Override
    public void handlePushQuesTask(String examSubjectId, String paperId, String userId, String timestampMillis) {
        examSubjectId = StringUtils.trim(examSubjectId);
        paperId = StringUtils.trim(paperId);
        userId = StringUtils.trim(userId);
        timestampMillis = StringUtils.trim(timestampMillis);

        Date createTime = new Date(Long.parseLong(timestampMillis));
        // 查询是否有时间更晚的任务，有则跳过该任务(同一examSubjectId，只处理最新的任务)
        Integer count = pushQuesTaskMapper.selectCount(new LambdaQueryWrapper<PushQuesTask>()
                .eq(PushQuesTask::getExamSubjectId, examSubjectId)
                .ge(PushQuesTask::getCreateTime, createTime));
        if (count > 0) {
            return;
        }

        // 查看同examSubjectId 2两秒内有没有在处理的的任务，如果有，则等待2秒后再处理
        String redisKey = "Question:pushQuesTask:" + examSubjectId;
        Boolean absent = redisTemplate.opsForValue().setIfAbsent(redisKey, 0);
        if (Boolean.FALSE.equals(absent)) {
            // 直接抛异常，稍后mark还会再次调用该接口一直到成功为止
            throw new ApiException("请稍后再试");
        } else {
            redisTemplate.expire(redisKey, 2, TimeUnit.SECONDS);
        }

        // todo 清理之前推题任务生成的推题数据
        examSubjectPushQuesMapper.deleteByPaperIdAndExamSubjectId(paperId, examSubjectId);

        // 更新相同examSubjectId的其他绑定任务状态为已清空
        pushQuesTaskMapper.update(null, new LambdaUpdateWrapper<PushQuesTask>()
                .eq(PushQuesTask::getExamSubjectId, examSubjectId)
                .ne(PushQuesTask::getPaperId, "")
                .set(PushQuesTask::getStatus, PushQuesTaskStatusEnum.CLEANED.getCode())
                .set(PushQuesTask::getUpdateTime, new Date()));

        // 记录任务
        pushQuesTaskMapper.insert(PushQuesTask.builder()
                .examSubjectId(examSubjectId)
                .paperId(paperId)
                .status(StringUtils.isBlank(paperId) ? PushQuesTaskStatusEnum.SUCCESS.getCode() : PushQuesTaskStatusEnum.PENDING.getCode())
                .createBy(userId)
                .createTime(createTime)
                .build());
    }

    /**
     * 为试卷生成变式题并保存
     */
    @Override
    public void generateAndSavePaperPushQues(PaperPushRequestVo vo, TokenUserVo userInfo) {
        String paperId = vo.getPaperId();
        checkPermission(userInfo, paperId);

        Integer pushNum = vo.getPushNum();
        long beginTick = System.currentTimeMillis();
        String examSubjectId = StringUtils.EMPTY;
        StringBuilder sb = new StringBuilder("【为试卷生成变式题并保存（全库匹配）】paperId=").append(paperId)
                .append(", pushNum=").append(pushNum);
        String questionId = vo.getQuestionId();
        boolean onlyOneQuestion = StringUtils.isNotEmpty(questionId);
        if (onlyOneQuestion) {
            sb.append(", questionId=").append(questionId);
        }
        try {
            List<ExamsubjectPushQues> questionList = generatePaperPushQues(paperId, examSubjectId, pushNum, onlyOneQuestion ? questionId : null);
            if (onlyOneQuestion) {
                examSubjectPushQuesMapper.delete(new LambdaQueryWrapper<ExamsubjectPushQues>()
                        .eq(ExamsubjectPushQues::getPaperId, paperId)
                        .eq(ExamsubjectPushQues::getExamSubjectId, examSubjectId)
                        .eq(ExamsubjectPushQues::getQuesId, questionId));
            } else {
                examSubjectPushQuesMapper.deleteByPaperIdAndExamSubjectId(paperId, examSubjectId);
            }
            this.saveBatch(questionList);
            sb.append("\n成功生成 ").append(questionList.size()).append(" 道变式题，总耗时：").append(System.currentTimeMillis() - beginTick).append(" ms");
        } catch (Exception e) {
            sb.append("\n异常：").append(ExceptionUtils.getStackTrace(e));
            log.error(sb.toString());
            throw e;
        } finally {
            log.info(sb.toString());
        }
    }

    /**
     * 检查权限
     */
    private void checkPermission(TokenUserVo userInfo, String paperId) {
        permissionService.checkEditorPermission(userInfo, paperId);
    }

    /**
     * 为试卷生成变式题
     * @param paperId 试卷Id
     * @param examSubjectId 考试科目Id
     * @param pushNum 推题数量
     * @param onlyQuestionId 如果存在，则只生成该题的变式题
     */
    @Override
    public List<ExamsubjectPushQues> generatePaperPushQues(String paperId, String examSubjectId, int pushNum, String onlyQuestionId) {
        // 查试卷
        PaperMain paperMain = paperMainMapper.selectById(paperId);
        if (paperMain == null) {
            throw new ApiException("找不到该试卷");
        }
        // 查试题
        List<String> questionIds = paperQuestionMapper.selectPaperQuestionIds(paperId);
        List<QuestionVo> srcQuestions = questionBuildService.selectQuestionVos(questionIds);

        // 搜出候选题
        Map<String, List<String>> srcQuestionIdCandiateQuestionIdsMap = findCandidates(srcQuestions, onlyQuestionId, pushNum, paperMain.getGradeId());
        if (srcQuestionIdCandiateQuestionIdsMap.isEmpty()) {
            return Collections.emptyList();
        }

        // 从候选题中挑出变式题
        Map<String, List<QuestionVo>> srcQuestionIdVariantsMap = chooseFromCandidates(
                paperId, examSubjectId, pushNum, onlyQuestionId, srcQuestionIdCandiateQuestionIdsMap, srcQuestions);

        // 构建推题结果
        return buildPushQuesList(srcQuestionIdVariantsMap, srcQuestions, examSubjectId, paperId);
    }

    // 查出候选题
    private Map<String, List<String>> findCandidates(List<QuestionVo> srcQuestions, String onlyQuestionId, int pushNum, Integer gradeId) {
        Map<String, List<String>> result = new HashMap<>();
        for (QuestionVo srcQuestion : srcQuestions) {
            // 跳过不推题的题目
            if (StringUtils.isNotEmpty(onlyQuestionId) && !StringUtils.equals(srcQuestion.getId(), onlyQuestionId)) {
                continue;
            }
            PushQuesParam param = buildPushQuesParam(srcQuestion, gradeId, pushNum);
            List<String> candidateIds = questionSearchService.findSrcQuestionVariants(param);
            if (!candidateIds.isEmpty()) {
                result.put(srcQuestion.getId(), candidateIds);
            }
        }
        return result;
    }

    // 构建搜索题目参数
    private PushQuesParam buildPushQuesParam(QuestionVo srcQuestion, Integer gradeId, int pushNum) {
        PushQuesParam param = new PushQuesParam();
        param.setQuestionId(srcQuestion.getId());
        param.setGradeLevel(srcQuestion.getStage());
        param.setSubjectId(srcQuestion.getSubjectId());
        // 查指定推题数量的2倍，至少10道题
        param.setQuestionCount(Math.max(pushNum * 2, 10));
        param.setKnowledgeIds(srcQuestion.getKnowledgeIds());
        param.setQuestionTypeId(srcQuestion.getType());
        param.setTryNoLimitQuestionType(true);
        param.setDifficulty(srcQuestion.getDifficulty());
        param.setScoreRate(srcQuestion.getScoreRate());
        param.setTryNoLimitDifficulty(true);
        param.setGradeId(gradeId);
        param.setTryNoLimitGrade(true);
        return param;
    }

    // 从候选题中挑选变式题
    private Map<String, List<QuestionVo>> chooseFromCandidates(String paperId, String examSubjectId, int pushNum, String onlyQuestionId,
                                                               Map<String, List<String>> srcQuestionIdCandiateQuestionIdsMap,
                                                               List<QuestionVo> srcQuestions) {
        Map<String, List<QuestionVo>> result = new HashMap<>();

        // 查出所有候选题目
        List<String> candidateQuestionIds = srcQuestionIdCandiateQuestionIdsMap.values().stream()
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<String, QuestionVo> candidateQuestionMap = questionBuildService.selectQuestionVos(candidateQuestionIds)
                .stream().collect(Collectors.toMap(QuestionVo::getId, Function.identity()));

        // 已添加的变式题。如果只推某道题的变式题，则加入该卷下其他题目已生成的变式题
        List<QuestionVo> addedVariants = new ArrayList<>();
        if (StringUtils.isNotEmpty(onlyQuestionId)) {
            List<String> otherQuestionPushQuestionIds = examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                            .eq(ExamsubjectPushQues::getPaperId, paperId)
                            .eq(ExamsubjectPushQues::getExamSubjectId, examSubjectId)
                            .ne(ExamsubjectPushQues::getQuesId, onlyQuestionId)
                            .select(ExamsubjectPushQues::getPushId))
                    .stream().map(ExamsubjectPushQues::getPushId).distinct().collect(Collectors.toList());
            if (!otherQuestionPushQuestionIds.isEmpty()) {
                addedVariants.addAll(questionBuildService.selectQuestionVos(otherQuestionPushQuestionIds));
            }
        }

        // 剔除与源题和已推题相似的题目
        srcQuestionIdCandiateQuestionIdsMap.forEach((srcQuestionId, thisQuestionCandidateIds) -> {
            List<QuestionVo> thisQuestionVariants = new ArrayList<>();
            for (String candidateId : thisQuestionCandidateIds) {
                QuestionVo candidate = candidateQuestionMap.get(candidateId);
                if (candidate == null) {
                    continue;
                }
                // 剔除与源题相似的
                if (srcQuestions.stream().anyMatch(questionVo -> SimilarUtil.isSameOrSimilar(questionVo, candidate))) {
                    continue;
                }
                // 剔除与已推题相似的
                if (addedVariants.stream().anyMatch(questionVo -> SimilarUtil.isSameOrSimilar(questionVo, candidate))) {
                    continue;
                }
                addedVariants.add(candidate);
                thisQuestionVariants.add(candidate);
                if (thisQuestionVariants.size() >= pushNum) {
                    break;
                }
            }
            if (!thisQuestionVariants.isEmpty()) {
                result.put(srcQuestionId, thisQuestionVariants);
            }
        });
        return result;
    }

    // 构建推题结果
    private List<ExamsubjectPushQues> buildPushQuesList(Map<String, List<QuestionVo>> srcQuestionIdVariantsMap, List<QuestionVo> srcQuestions, String examSubjectId, String paperId) {
        List<ExamsubjectPushQues> result = new ArrayList<>();

        Map<String, QuestionVo> srcQuestionMap = srcQuestions.stream().collect(Collectors.toMap(QuestionVo::getId, Function.identity()));
        Date createTime = new Date();

        srcQuestionIdVariantsMap.forEach((srcQuestionId, variants) -> {
            List<ExamsubjectPushQues> thisQuestionPushQuesList = new ArrayList<>();
            QuestionVo srcQuestion = srcQuestionMap.get(srcQuestionId);
            for (QuestionVo variant : variants) {
                ExamsubjectPushQues item = new ExamsubjectPushQues();
                item.setExamSubjectId(examSubjectId);
                item.setPaperId(paperId);
                item.setQuesId(srcQuestionId);
                item.setPushId(variant.getId());
                item.setDifficult(variant.getDifficulty());
                item.setScoreRate(variant.getScoreRate());
                item.setKnowMatchCount(getKnowledgeMatch(srcQuestion, variant));
                item.setCreateTime(createTime);
                thisQuestionPushQuesList.add(item);
            }
            thisQuestionPushQuesList.sort(Comparator.comparing(ExamsubjectPushQues::getKnowMatchCount).reversed());
            result.addAll(thisQuestionPushQuesList);
        });
        return result;
    }

    // 计算两个题目的知识点匹配个数
    private int getKnowledgeMatch(QuestionVo q1, QuestionVo q2) {
        List<Integer> knowledgeIds1 = q1.getKnowledgeIds();
        List<Integer> knowledgeIds2 = q2.getKnowledgeIds();
        Collection<Integer> intersection = CollUtil.intersection(knowledgeIds1, knowledgeIds2);
        return intersection.size();
    }

    /**
     * 查看试卷中一道题目的变式题列表
     */
    @Override
    public List<UserQuestionVo> listPushQuesByPaperIdAndQuestionId(String paperId, String examSubjectId, String questionId, TokenUserVo userVo) {
        checkPermission(userVo, paperId);
        List<String> pushIds = examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                        .eq(ExamsubjectPushQues::getPaperId, paperId)
                        .eq(ExamsubjectPushQues::getExamSubjectId, examSubjectId)
                        .eq(ExamsubjectPushQues::getQuesId, questionId)
                        .orderByDesc(ExamsubjectPushQues::getKnowMatchCount)
                        .select(ExamsubjectPushQues::getPushId))
                .stream().map(ExamsubjectPushQues::getPushId).distinct().collect(Collectors.toList());
        if (pushIds.isEmpty()) {
            return Collections.emptyList();
        }

        return questionBuildService.selectUserQuestionVos(pushIds, userVo.getUserId());
    }

    /**
     * 获取试卷变式题相关的考试科目列表
     */
    @Override
    public List<SimpleExamSubjectVo> listExamSubjectByPaperId(String paperId) {
        List<String> examSubjectIds = examSubjectPushQuesMapper.selectExamSubjectIdByPaperId(paperId);
        if (examSubjectIds.isEmpty()) {
            return Collections.emptyList();
        }
        examSubjectIds.removeIf(StringUtils.EMPTY::equals);
        List<SimpleExamSubjectVo> result = new LinkedList<>();
        result.add(SimpleExamSubjectVo.builder()
                .examId(StringUtils.EMPTY)
                .examSubjectId(StringUtils.EMPTY)
                .examName("本卷变式题")
                .build());
        if (!examSubjectIds.isEmpty()) {
            result.addAll(feignService.listExamSubjectByIds(examSubjectIds));
        }
        return result;
    }

    /**
     * 查询本卷或考卷是否已有变式题
     */
    @Override
    public boolean existsPushQues(String paperId, String examSubjectId) {
        ExamsubjectPushQues examsubjectPushQues = examSubjectPushQuesMapper.selectOne(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getPaperId, paperId)
                .eq(ExamsubjectPushQues::getExamSubjectId, examSubjectId)
                .select(ExamsubjectPushQues::getId)
                .last("LIMIT 1"));
        return examsubjectPushQues != null;
    }

    /**
     * 获取指定考试的变式题列表
     */
    @Override
    public List<ExamsubjectPushQues> getExamSubjectPushQuestionList(String examSubjectId) {
        return examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getExamSubjectId, examSubjectId)
                .select(ExamsubjectPushQues::getQuesId,
                        ExamsubjectPushQues::getPushId,
                        ExamsubjectPushQues::getDifficult,
                        ExamsubjectPushQues::getScoreRate,
                        ExamsubjectPushQues::getKnowMatchCount,
                        ExamsubjectPushQues::getIsStandard));
    }

    /**
     * 获取指定考试的变式题列表，无则返回本卷变式题
     */
    @Override
    public List<ExamsubjectPushQues> listPushQuestionListByExamSubjectIdOrPaperId(String examSubjectId, String paperId) {
        List<ExamsubjectPushQues> examSubjectPushQues = examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getPaperId, paperId)
                .in(ExamsubjectPushQues::getExamSubjectId, Arrays.asList(examSubjectId, StringUtils.EMPTY))
                .select(ExamsubjectPushQues::getExamSubjectId,
                        ExamsubjectPushQues::getQuesId,
                        ExamsubjectPushQues::getPushId,
                        ExamsubjectPushQues::getDifficult,
                        ExamsubjectPushQues::getScoreRate,
                        ExamsubjectPushQues::getKnowMatchCount,
                        ExamsubjectPushQues::getIsStandard));
        if (examSubjectPushQues.stream().anyMatch(x -> examSubjectId.equals(x.getExamSubjectId()))) {
            examSubjectPushQues.removeIf(x -> StringUtils.EMPTY.equals(x.getExamSubjectId()));
        }
        return examSubjectPushQues;
    }

    /**
     * 获取多份试卷的所有变式题，含考试变式题及本卷变式题
     */
    @Override
    public List<ExamsubjectPushQues> listPushQuestionListByPaperIds(List<String> paperIds) {
        return examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .in(ExamsubjectPushQues::getPaperId, paperIds)
                .select(ExamsubjectPushQues::getPaperId,
                        ExamsubjectPushQues::getQuesId,
                        ExamsubjectPushQues::getPushId,
                        ExamsubjectPushQues::getDifficult,
                        ExamsubjectPushQues::getScoreRate,
                        ExamsubjectPushQues::getKnowMatchCount,
                        ExamsubjectPushQues::getIsStandard));
    }

    /**
     * 查询试卷推题信息
     */
    @Override
    public PaperPushInfoVo paperPushInfo(String paperId) {
        // 从推题表中查询出所有的推题
        List<ExamsubjectPushQues> pushQues = examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getPaperId, paperId)
                .eq(ExamsubjectPushQues::getExamSubjectId, StringUtils.EMPTY)
                .select(ExamsubjectPushQues::getQuesId, ExamsubjectPushQues::getPushId, ExamsubjectPushQues::getIsStandard));
        Map<String, List<ExamsubjectPushQues>> quesIdPushQuesListMap = pushQues
                .stream().collect(Collectors.groupingBy(ExamsubjectPushQues::getQuesId));
        List<PushQuesCountVo> voList = quesIdPushQuesListMap.entrySet().stream().map(entry -> PushQuesCountVo.builder()
                .quesId(entry.getKey())
                .count(entry.getValue().size())
                .build()).collect(Collectors.toList());

        // 移除试卷中不再包含的题目
        List<String> questionIds = paperQuestionMapper.selectPaperQuestionIds(paperId);
        voList.removeIf(vo -> !questionIds.contains(vo.getQuesId()));

        return PaperPushInfoVo.builder()
                .isStandard(pushQues.stream().anyMatch(ExamsubjectPushQues::getIsStandard))
                .pushQuesCountList(voList)
                .build();
    }

    @Override
    public List<PushQuesCountVo> getQuesionIdAndPushCountList(String paperId) {
        List<String> questionIds = paperQuestionMapper.selectPaperQuestionIds(paperId);
        if (questionIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 从推题表中查询出所有的推题
        Map<String, Integer> questionIdPushCountMap = examSubjectPushQuesMapper.selectQuesionIdAndPushCountList(paperId, StringUtils.EMPTY).stream()
                .collect(Collectors.toMap(KeyValueInt::getKey, KeyValueInt::getValue));

        return questionIds.stream()
                .map(quesId ->
                        PushQuesCountVo.builder()
                                .quesId(quesId)
                                .count(questionIdPushCountMap.getOrDefault(quesId, 0))
                                .build()
                ).collect(Collectors.toList());
    }

    /**
     * 添加本卷变式题
     */
    @Override
    public void addPushQuesToPaper(String paperId, String quesId, List<String> pushQuestIds, TokenUserVo userInfo) {
        // 检查权限
        checkPermission(userInfo, paperId);

        PaperQuestion paperQuestion = paperQuestionMapper.selectOne(new LambdaQueryWrapper<PaperQuestion>()
                .eq(PaperQuestion::getPaperId, paperId)
                .eq(PaperQuestion::getQuestionId, quesId));
        if (paperQuestion == null) {
            throw new ApiException("题目不属于试卷");
        }

        // 查询已经添加的题目
        List<ExamsubjectPushQues> pushQues = examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getPaperId, paperId)
                .eq(ExamsubjectPushQues::getExamSubjectId, StringUtils.EMPTY)
                .eq(ExamsubjectPushQues::getQuesId, quesId)
                .select(ExamsubjectPushQues::getPushId, ExamsubjectPushQues::getIsStandard));
        // 如果有标准推题，则报错
        if (pushQues.stream().anyMatch(ExamsubjectPushQues::getIsStandard)) {
            throw new ApiException("当前试卷已是标准推题，若要执行此操作，请先取消标准推题");
        }

        List<String> existsPushQuestionIds = pushQues.stream().map(ExamsubjectPushQues::getPushId).collect(Collectors.toList());
        // 删除已经添加的题目
        pushQuestIds.removeAll(existsPushQuestionIds);
        if (pushQuestIds.isEmpty()) {
            // 没有需要添加的题目
            return;
        }

        // 查出源题及添加题的内容
        List<String> quesIds = new ArrayList<>(pushQuestIds);
        quesIds.add(quesId);
        Map<String, QuestionVo> questionMap = questionBuildService.selectQuestionVos(quesIds)
                .stream().collect(Collectors.toMap(QuestionVo::getId, Function.identity()));
        QuestionVo srcQuestion = questionMap.get(quesId);

        List<ExamsubjectPushQues> pushQuesList = new ArrayList<>(pushQuestIds.size());
        Date now = DateUtil.date();
        for (String pushQuestId : pushQuestIds) {
            QuestionVo pushQuestion = questionMap.get(pushQuestId);
            if (pushQuestion == null) {
                continue;
            }
            pushQuesList.add(ExamsubjectPushQues.builder()
                    .examSubjectId(StringUtils.EMPTY)
                    .paperId(paperId)
                    .quesId(quesId)
                    .pushId(pushQuestId)
                    .difficult(pushQuestion.getDifficulty())
                    .scoreRate(pushQuestion.getScoreRate())
                    .knowMatchCount(getKnowledgeMatch(srcQuestion, pushQuestion))
                    .createTime(now)
                    .build());
        }
        pushQuesList.sort(Comparator.comparing(ExamsubjectPushQues::getKnowMatchCount).reversed());
        if (!pushQuesList.isEmpty()) {
            this.saveBatch(pushQuesList);
        }
    }

    /**
     * 删除本卷单道题目的变式题
     */
    @Override
    public void deletePushQues(String paperId, String quesId, List<String> pushQuestIds, TokenUserVo userInfo) {
        // 检查权限
        checkPermission(userInfo, paperId);

        examSubjectPushQuesMapper.delete(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getPaperId, paperId)
                .eq(ExamsubjectPushQues::getExamSubjectId, StringUtils.EMPTY)
                .eq(ExamsubjectPushQues::getQuesId, quesId)
                .in(ExamsubjectPushQues::getPushId, pushQuestIds));
    }

    /**
     * 删除本卷全部变式题
     */
    @Override
    public void deletePaperPushQues(String paperId, TokenUserVo userInfo) {
        // 检查权限
        checkPermission(userInfo, paperId);

        examSubjectPushQuesMapper.delete(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getPaperId, paperId)
                .eq(ExamsubjectPushQues::getExamSubjectId, StringUtils.EMPTY));
    }

    /**
     * 复制本卷变式题到考试科目
     */
    @Override
    public void syncPaperPushQuesToExamSubject(String paperId, String examSubjectId) {
        List<ExamsubjectPushQues> examSubjectPushQues = examSubjectPushQuesMapper.selectList(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getPaperId, paperId)
                .eq(ExamsubjectPushQues::getExamSubjectId, StringUtils.EMPTY));
        if (examSubjectPushQues.isEmpty()) {
            return;
        }
        // 删除考试科目的变式题
        examSubjectPushQuesMapper.delete(new LambdaQueryWrapper<ExamsubjectPushQues>()
                .eq(ExamsubjectPushQues::getPaperId, paperId)
                .eq(ExamsubjectPushQues::getExamSubjectId, examSubjectId));
        // 添加考试科目的变式题
        examSubjectPushQues.forEach(x -> {
            x.setId(null);
            x.setIsStandard(Boolean.FALSE);
            x.setExamSubjectId(examSubjectId);
            x.setCreateTime(DateUtil.date());
            x.setUpdateTime(DateUtil.date());
        });
        this.saveBatch(examSubjectPushQues);
    }

    /**
     * 查询有标准推题的试卷ID列表
     */
    @Override
    public List<String> listPaperIdWithStandardPushQues(List<String> paperIds) {
        return examSubjectPushQuesMapper.selectStandardPushQuesPaperIds(paperIds);
    }

    /**
     * 更新是否标准推题标记
     */
    @Override
    public void updateIsStandardPushQues(String paperId, Boolean isStandardPushQues, TokenUserVo userInfo) {
        // 检查权限
        checkPermission(userInfo, paperId);

        examSubjectPushQuesMapper.update(null, new LambdaUpdateWrapper<ExamsubjectPushQues>()
                .set(ExamsubjectPushQues::getIsStandard, isStandardPushQues)
                .eq(ExamsubjectPushQues::getPaperId, paperId)
                .eq(ExamsubjectPushQues::getExamSubjectId, StringUtils.EMPTY));
    }
}
