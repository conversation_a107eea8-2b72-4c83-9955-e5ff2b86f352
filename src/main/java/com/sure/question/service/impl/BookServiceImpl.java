package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.Book;
import com.sure.question.entity.BookCategory;
import com.sure.question.entity.Chapter;
import com.sure.question.mapper.BookCategoryMapper;
import com.sure.question.mapper.BookMapper;
import com.sure.question.mapper.ChapterMapper;
import com.sure.question.service.BookService;
import com.sure.question.service.OssManager;
import com.sure.question.util.CacheUtil;
import com.sure.question.util.RedissonLockUtil;
import com.sure.question.vo.book.BookVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class BookServiceImpl extends ServiceImpl<BookMapper, Book> implements BookService {
    private final BookMapper bookMapper;
    private final BookCategoryMapper bookCategoryMapper;
    private final ChapterMapper chapterMapper;
    private final OssManager ossManager;

    /**
     * 查询教材
     */
    @Override
    public List<BookVo> listBooks(Integer gradeLevel, Integer subjectId) {
        List<Book> books = bookMapper.selectList(new LambdaQueryWrapper<Book>()
                .eq(gradeLevel != null, Book::getGradeLevel, gradeLevel)
                .eq(subjectId != null, Book::getSubjectId, subjectId));
        Map<Integer, BookCategory> categoryNameBookCategoryMap = bookCategoryMapper.selectList(new LambdaQueryWrapper<BookCategory>()
                .eq(gradeLevel != null, BookCategory::getGradeLevel, gradeLevel)
                .eq(subjectId != null, BookCategory::getSubjectId, subjectId))
                .stream().collect(Collectors.toMap(BookCategory::getCategoryId, Function.identity()));
        return books.stream().map(book -> {
                    BookCategory bookCategory = categoryNameBookCategoryMap.get(book.getCategoryId());
                    if (StringUtils.isNotEmpty(book.getCover())) {
                        book.setCover(ossManager.getPublicPathTK(book.getCover()));
                    }
                    return new BookVo(book, bookCategory);
                })
                .sorted(Comparator.comparing(BookVo::getGradeLevel)
                        .thenComparing(BookVo::getSubjectId)
                        .thenComparing(BookVo::getCategoryOrderId)
                        .thenComparing(BookVo::getOrderId))
                .collect(Collectors.toList());
    }

    /**
     * 查询教材版本
     */
    @Override
    public List<BookCategory> listBookCategories(Integer gradeLevel, Integer subjectId) {
        return bookCategoryMapper.selectList(new LambdaQueryWrapper<BookCategory>()
                .eq(gradeLevel != null, BookCategory::getGradeLevel, gradeLevel)
                .eq(subjectId != null, BookCategory::getSubjectId, subjectId)
                .orderByAsc(BookCategory::getGradeLevel)
                .orderByAsc(BookCategory::getSubjectId)
                .orderByAsc(BookCategory::getSortCode)
                .orderByAsc(BookCategory::getCategoryId));
    }

    /**
     * 添加教材
     */
    @Override
    public Integer addBook(int gradeLevelId, int subjectId, Integer gradeId, Integer term, String bookName, int categoryId, boolean enabled, String userId) {
        return RedissonLockUtil.executeAfterLocked("Ques:Lock:ChangeBookChapterKnowledge",
                () -> doAddBook(gradeLevelId, subjectId, gradeId, term, bookName, categoryId, enabled, userId));
    }

    public Integer doAddBook(int gradeLevelId, int subjectId, Integer gradeId, Integer term, String bookName, int categoryId, boolean enabled, String userId) {
        // 检查学段学科
        if (!CacheUtil.existsGradeLevelSubject(gradeLevelId, subjectId)) {
            throw new ApiException("学段学科错误");
        }
        // 检查学段年级学科
        if (gradeId != null && !CacheUtil.existsGradeLevelGradeSubject(gradeLevelId, gradeId, subjectId)) {
            throw new ApiException("不存在该年级或该年级未开设指定学科");
        }
        if (term != null) {
            if (gradeId == null) {
                throw new ApiException("指定学期则年级不能为空");
            }
            if (term < 1 || term > 3) {
                throw new ApiException("学期错误");
            }
        }
        // 检查教材名
        bookName = StringUtils.trim(bookName);
        if (StringUtils.isEmpty(bookName)) {
            throw new ApiException("教材名称不能为空");
        }
        // 检查版本
        BookCategory bookCategory = bookCategoryMapper.selectById(categoryId);
        if (bookCategory == null) {
            throw new ApiException("版本不存在");
        }
        if (!bookCategory.getGradeLevel().equals(gradeLevelId) || !bookCategory.getSubjectId().equals(subjectId)) {
            throw new ApiException("版本学段学科不匹配");
        }
        // 查学段学科现有教材
        List<Book> gradeLevelSubjectBooks = bookMapper.selectList(new LambdaQueryWrapper<Book>()
                .eq(Book::getGradeLevel, gradeLevelId)
                .eq(Book::getSubjectId, subjectId));
        // 同版本现有教材
        List<Book> sameCategoryBooks = gradeLevelSubjectBooks.stream().filter(b -> b.getCategoryId().equals(categoryId)).collect(Collectors.toList());
        // 检查同名教材
        String finalBookName = bookName;
        if (sameCategoryBooks.stream().anyMatch(book -> book.getBookName().equals(finalBookName))) {
            throw new ApiException("同名教材已存在");
        }

        // 创建新教材
        Book newBook = new Book();
        newBook.setGradeLevel(gradeLevelId);
        newBook.setSubjectId(subjectId);
        newBook.setBookName(bookName);
        newBook.setGradeId(gradeId == null ? 0 : gradeId);
        newBook.setTerm(term == null ? 0 : term);
        newBook.setEnabled(enabled);
        newBook.setCategoryId(categoryId);

        // 新教材Id、顺序
        Book maxIdBook = bookMapper.selectOne(new LambdaQueryWrapper<Book>().orderByDesc(Book::getId).last("limit 1"));
        newBook.setId((maxIdBook == null ? 0 : maxIdBook.getId()) + 1);
        int maxOrder = gradeLevelSubjectBooks.stream().filter(b -> b.getCategoryId().equals(categoryId))
                .mapToInt(Book::getOrderId).max().orElse(0);
        newBook.setOrderId(maxOrder + 1);

        bookMapper.insert(newBook);

        log.info("添加教材, userId = {}, newBook = {}", userId, newBook);

        return newBook.getId();
    }

    /**
     * 修改教材
     */
    @Override
    public void updateBook(int id, String bookName, Integer gradeId, Integer term, int orderId, boolean enabled, String userId) {
        String finalBookName = StringUtils.trim(bookName);
        if (StringUtils.isEmpty(finalBookName)) {
            throw new ApiException("教材名称不能为空");
        }
        RedissonLockUtil.executeAfterLocked("Ques:Lock:ChangeBookChapterKnowledge", () -> {
            Book book = bookMapper.selectById(id);
            if (book == null) {
                throw new ApiException("找不到教材");
            }
            List<Book> sameCategoryBooks = bookMapper.selectList(new LambdaQueryWrapper<Book>()
                    .eq(Book::getGradeLevel, book.getGradeLevel())
                    .eq(Book::getSubjectId, book.getSubjectId())
                    .eq(Book::getCategoryId, book.getCategoryId()));
            if (sameCategoryBooks.stream().anyMatch(b -> !b.getId().equals(id) && b.getBookName().equals(finalBookName))) {
                throw new ApiException("同名教材已存在");
            }
            if (gradeId != null && !CacheUtil.existsGradeLevelGradeSubject(book.getGradeLevel(), gradeId, book.getSubjectId())) {
                throw new ApiException("不存在该年级或该年级未开设指定学科");
            }
            if (term != null) {
                if (gradeId == null) {
                    throw new ApiException("指定学期则年级不能为空");
                }
                if (term < 1 || term > 3) {
                    throw new ApiException("学期错误");
                }
            }

            String oldBookName = book.getBookName();
            Integer oldGradeId = book.getGradeId();
            Integer oldTerm = book.getTerm();
            Integer oldOrderId = book.getOrderId();

            book.setBookName(finalBookName);
            book.setGradeId(gradeId == null ? 0 : gradeId);
            book.setTerm(term == null ? 0 : term);
            book.setOrderId(orderId);
            book.setEnabled(enabled);
            bookMapper.updateById(book);

            log.info("修改教材, userId = {}, bookId = {}, bookName({} -> {}), gradeId({} -> {})), term({} -> {}), orderId({} -> {})",
                    userId, id, oldBookName, book.getBookName(), oldGradeId, book.getGradeId(), oldTerm, book.getTerm(), oldOrderId, book.getOrderId());
        });
    }

    /**
     * 修改教材版本顺序
     */
    @Override
    public void updateBookCategoryOrder(int gradeLevel, int subjectId, int categoryId, int categoryOrderId, String userId) {
        RedissonLockUtil.executeAfterLocked("Ques:Lock:ChangeBookChapterKnowledge", () -> {
            BookCategory bookCategory = bookCategoryMapper.selectOne(new LambdaQueryWrapper<BookCategory>()
                    .eq(BookCategory::getGradeLevel, gradeLevel)
                    .eq(BookCategory::getSubjectId, subjectId)
                    .eq(BookCategory::getCategoryId, categoryId));
            if (bookCategory == null) {
                throw new ApiException("版本不存在");
            }
            bookCategoryMapper.update(null, new LambdaUpdateWrapper<BookCategory>()
                    .eq(BookCategory::getGradeLevel, gradeLevel)
                    .eq(BookCategory::getSubjectId, subjectId)
                    .eq(BookCategory::getCategoryId, categoryId)
                    .set(BookCategory::getSortCode, categoryOrderId));
            log.info("修改教材版本, userId = {}, gradeLevel = {}, subjectId = {}, categoryId = {}, sortCode({} -> {})",
                    userId, gradeLevel, subjectId, categoryId, bookCategory.getSortCode(), categoryOrderId);
        });
    }

    @Override
    public void deleteBook(int id, String userId) {
        RedissonLockUtil.executeAfterLocked("Ques:Lock:ChangeBookChapterKnowledge", () -> {
            Book book = bookMapper.selectById(id);
            if (book == null) {
                throw new ApiException("找不到教材");
            }
            if (chapterMapper.selectCount(new LambdaQueryWrapper<Chapter>().eq(Chapter::getBookId, id)) > 0) {
                throw new ApiException("该教材已有章节，不能删除");
            }
            bookMapper.deleteById(id);
            log.info("删除教材, userId = {}, book = {}", userId, book);
        });
    }
}
