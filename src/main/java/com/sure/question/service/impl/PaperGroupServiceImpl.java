package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sure.common.exception.ApiException;
import com.sure.question.entity.PaperGroup;
import com.sure.question.entity.PaperMain;
import com.sure.question.mapper.PaperGroupMapper;
import com.sure.question.mapper.PaperMainMapper;
import com.sure.question.service.PaperGroupService;
import com.sure.question.vo.papergroup.SortCodeVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
@Service("paperGroupService")
@RequiredArgsConstructor
public class PaperGroupServiceImpl extends ServiceImpl<PaperGroupMapper, PaperGroup> implements PaperGroupService {

    private final PaperGroupMapper paperGroupMapper;
    private final PaperMainMapper paperMainMapper;

    @Override
    public void addPaperGroup(Integer stage, Integer subjectId, String groupName, Integer sortCode, String userId) {
        groupName = groupName.trim();
        // 查看是否已经存在
        Integer count = paperGroupMapper.selectCount(new LambdaQueryWrapper<PaperGroup>()
                .eq(PaperGroup::getStage, stage)
                .eq(PaperGroup::getSubjectId, subjectId)
                .eq(PaperGroup::getGroupName, groupName));
        if (count > 0) {
            throw new ApiException("该分组已存在");
        }
        paperGroupMapper.insert(PaperGroup.builder()
                .stage(stage)
                .subjectId(subjectId)
                .groupName(groupName)
                .sortCode(sortCode)
                .createBy(userId)
                .createTime(new Date())
                .build());
    }

    @Override
    public List<PaperGroup> listPaperGroup(Integer stage, Integer subjectId) {
        return paperGroupMapper.selectList(new LambdaQueryWrapper<PaperGroup>()
                .eq(PaperGroup::getStage, stage)
                .eq(PaperGroup::getSubjectId, subjectId)
                .orderByAsc(PaperGroup::getSortCode)
                .orderByDesc(PaperGroup::getCreateTime));
    }

    @Override
    public void deletePaperGroup(Integer id) {
        // 检查分组下是否有未删除的试卷，如果有则不能删除
        Integer count = paperMainMapper.selectCount(new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getPaperGroupId, id)
                .eq(PaperMain::getStatus, 0));
        if (count > 0) {
            throw new ApiException("该分组下有未删除的试卷，不能删除");
        }
        paperGroupMapper.deleteById(id);
    }

    @Override
    public void updateSortCode(List<SortCodeVo> vos) {
        vos.forEach(vo -> paperGroupMapper.update(null, new LambdaUpdateWrapper<PaperGroup>()
                .eq(PaperGroup::getId, vo.getId())
                .set(PaperGroup::getSortCode, vo.getSortCode())));
    }

    @Override
    public void clearByGroupId(Integer groupId) {
        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .eq(PaperMain::getPaperGroupId, groupId)
                .set(PaperMain::getPaperGroupId, null));
    }

    @Override
    public void clearByPaperIds(List<String> ids) {
        paperMainMapper.update(null, new LambdaUpdateWrapper<PaperMain>()
                .in(PaperMain::getId, ids)
                .set(PaperMain::getPaperGroupId, null));
    }

}
