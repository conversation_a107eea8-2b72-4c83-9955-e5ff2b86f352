package com.sure.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.cache.Cache;
import com.sure.question.entity.*;
import com.sure.question.enums.BranchTypeEnum;
import com.sure.question.enums.DifficultyEnum;
import com.sure.question.enums.GradeLevelEnum;
import com.sure.question.enums.TermEnum;
import com.sure.question.mapper.*;
import com.sure.question.service.BaseService;
import com.sure.question.vo.baseVo.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class BaseServerImpl implements BaseService {
    private final RegionMapper regionMapper;
    private final GradeMapper gradeMapper;
    private final SubjectMapper subjectMapper;
    private final GradeSubjectMapper gradeSubjectMapper;
    private final BookMapper bookMapper;
    private final BookCategoryMapper bookCategoryMapper;
    private final PaperTypeMapper paperTypeMapper;
    private final QuestionTypeMainMapper questionTypeMainMapper;

    private final Cache cache;

    @Override
    public RegionVo getRegionWithCache() {
        RegionVo region = cache.getRegionCache();
        if (region == null) {
            region = buildRegion();
            cache.setRegionCache(region);
        }
        return region;
    }

    private RegionVo buildRegion() {
        List<Region> list = regionMapper.selectList(new LambdaQueryWrapper<Region>().select(Region::getId, Region::getName, Region::getPid));
        Map<Long, List<Region>> groupByPid = list.stream().collect(Collectors.groupingBy(Region::getPid));

        List<RegionProvinceVo> regionProvinces = new ArrayList<>();
        List<Region> provinceList = groupByPid.getOrDefault(0L, new ArrayList<>());
        provinceList.sort(Comparator.comparing(Region::getId));
        for (Region province : provinceList) {
            List<RegionCityVo> regionCities = new ArrayList<>();
            List<Region> cityList = groupByPid.getOrDefault(province.getId(), new ArrayList<>());
            cityList.sort(Comparator.comparing(Region::getId));
            for (Region city : cityList) {
                List<RegionCountyVo> regionCounties = new ArrayList<>();
                List<Region> countyList = groupByPid.getOrDefault(city.getId(), new ArrayList<>());
                countyList.sort(Comparator.comparing(Region::getId));
                for (Region county : countyList) {
                    regionCounties.add(new RegionCountyVo(county));
                }
                regionCities.add(new RegionCityVo(city, regionCounties));
            }
            regionProvinces.add(new RegionProvinceVo(province, regionCities));
        }
        return new RegionVo(regionProvinces);
    }

    @Override
    public void deleteRegionCache() {
        cache.delRegionCache();
    }


    @Override
    public MessageVo getMessageWithCache() {
        MessageVo message = cache.getMessageCache();
        if (message == null) {
            message = buildMessage();
            cache.setMessageCache(message);
        }
        return message;
    }

    private MessageVo buildMessage() {
        // 查年级
        Map<Integer, List<Grade>> gradeLevelGradesMap = gradeMapper.selectList(new LambdaQueryWrapper<>())
                .stream().collect(Collectors.groupingBy(Grade::getGradeLevel));
        // 查学科
        List<Subject> allSubjects = subjectMapper.selectList(new LambdaQueryWrapper<Subject>().orderByAsc(Subject::getOrderId));
        Map<Integer, Set<Integer>> gradeLevelSubjectIdsMap = gradeSubjectMapper.selectList(new LambdaQueryWrapper<>())
                .stream().collect(Collectors.groupingBy(GradeSubject::getGradeLevel, Collectors.mapping(GradeSubject::getSubjectId, Collectors.toSet())));
        // 查教材
        Map<Integer, Map<Integer, List<Book>>> gradeLevelSubjectBooksMap = bookMapper.selectList(new LambdaQueryWrapper<Book>().eq(Book::getEnabled, true))
                .stream().collect(Collectors.groupingBy(Book::getGradeLevel, Collectors.groupingBy(Book::getSubjectId)));
        // 查教材版本
        Map<Integer, BookCategory> gradeLevelSubjectBookCategoryMap = bookCategoryMapper.selectList(new LambdaQueryWrapper<>())
                .stream().collect(Collectors.toMap(BookCategory::getCategoryId, Function.identity()));
        // 查试卷类型
        Map<Integer, List<PaperType>> gradeLevelPaperTypeMap = paperTypeMapper.selectList(new LambdaQueryWrapper<>())
                .stream().collect(Collectors.groupingBy(PaperType::getGradeLevel));
        // 查题型
        Map<Integer, Map<Integer, List<QuestionTypeMain>>> gradeLevelSubjectQuestionTypesMap = questionTypeMainMapper.selectList(new LambdaQueryWrapper<>())
                .stream().collect(Collectors.groupingBy(QuestionTypeMain::getGradeLevel, Collectors.groupingBy(QuestionTypeMain::getSubjectId)));
        // 学期
        List<MessageTermVo> terms = Arrays.asList(new MessageTermVo(TermEnum.Fall), new MessageTermVo(TermEnum.Spring));

        // 学段
        List<MessageStageVo> stages = new ArrayList<>();
        for (GradeLevelEnum gradeLevel : GradeLevelEnum.values()) {
            // 学段内年级
            List<MessageGradeVo> grades = gradeLevelGradesMap.getOrDefault(gradeLevel.getId(), new ArrayList<>()).stream()
                    .map(MessageGradeVo::new).sorted(Comparator.comparing(MessageGradeVo::getId)).collect(Collectors.toList());
            // 学段内试卷类型
            List<MessagePaperTypeVo> paperTypes = gradeLevelPaperTypeMap.getOrDefault(gradeLevel.getId(), new ArrayList<>()).stream()
                    .sorted(Comparator.comparing(PaperType::getOrderId)).map(MessagePaperTypeVo::new).collect(Collectors.toList());
            // 学段内各科教材
            Map<Integer, List<Book>> subjectBooksMap = gradeLevelSubjectBooksMap.getOrDefault(gradeLevel.getId(), new HashMap<>());
            // 学段内各科题型
            Map<Integer, List<QuestionTypeMain>> subjectQuestionTypesMap = gradeLevelSubjectQuestionTypesMap.getOrDefault(gradeLevel.getId(), new HashMap<>());

            // 学科
            Set<Integer> subjectIds = gradeLevelSubjectIdsMap.getOrDefault(gradeLevel.getId(), new HashSet<>());
            List<MessageSubjectVo> subjects = new ArrayList<>();
            for (Subject subject : allSubjects) {
                if (!subjectIds.contains(subject.getId())) {
                    continue;
                }
                // 学段学科教材
                List<MessageBookVo> books = subjectBooksMap.getOrDefault(subject.getId(), new ArrayList<>()).stream()
                        .map(book -> {
                            BookCategory bookCategory = gradeLevelSubjectBookCategoryMap.get(book.getCategoryId());
                            return new MessageBookVo(book, bookCategory);
                        })
                        .sorted(Comparator.comparing(MessageBookVo::getCategoryOrderId).thenComparing(MessageBookVo::getOrderId))
                        .collect(Collectors.toList());
                // 学段学科题型
                List<MessageQuestionTypeVo> questionTypes = subjectQuestionTypesMap.getOrDefault(subject.getId(), new ArrayList<>()).stream()
                        .sorted(Comparator.comparing(QuestionTypeMain::getOrderId))
                        .map(MessageQuestionTypeVo::new).collect(Collectors.toList());
                subjects.add(new MessageSubjectVo(subject.getId(), subject.getSubjectName(), books, questionTypes));
            }

            stages.add(new MessageStageVo(gradeLevel.getId(), gradeLevel.getName(), grades, subjects, paperTypes, terms));
        }

        // 难易度
        List<MessageDifficultyVo> difficulties = Arrays.stream(DifficultyEnum.values())
                .map(MessageDifficultyVo::new).collect(Collectors.toList());

        // 小题题型
        List<MessageQuestionCategoryVo> quesCategories = Arrays.stream(BranchTypeEnum.values())
                .map(MessageQuestionCategoryVo::new).collect(Collectors.toList());

        return new MessageVo(stages, difficulties, quesCategories);
    }

    @Override
    public void deleteMessageCache() {
        cache.delMessageCache();
    }
}
