package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paperVo.SchoolPaperVo;
import com.sure.question.vo.question.FindQuestionPageParam;
import com.sure.question.vo.question.UserQuestionVo;

import java.util.List;

public interface SchoolBankService {
    IPage<SchoolPaperVo> getPapers(TokenUserVo userVo,
                                   Integer stage, Integer grade, Integer term, Integer subject,
                                   Integer type, Integer year, String region,
                                   Integer page, Integer size);

    /**
     * 校本题库教材选题或知识点选题
     */
    IPage<UserQuestionVo> findQuestionByChapterOrKnowledge(TokenUserVo userVo, FindQuestionPageParam param);

    void sharePaper(String paperId, String schoolId, String userId);

    IPage<SchoolPaperVo> getShareRecords(String userId, String schoolId, Integer gradeLevel, Integer gradeId, Integer term, Integer subjectId,
                                         Integer paperTypeId, Integer year, String regionId, String bookType, Integer flagCoachBookId,
                                         Integer paperGroupId, Integer page, Integer size);

    void withdrawPaper(String paperId, String schoolId, String userId);

    void deletePapers(List<String> paperIds, String schoolId, String userId);

    void recoverPapers(List<String> paperIds, String schoolId);

    void changePapersPublic(List<String> paperIds, String schoolId, boolean isPublic);

    void paperViewIncr(Integer paperId);

    void paperDownIncr(Integer paperShareId);

}
