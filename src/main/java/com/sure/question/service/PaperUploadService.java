package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sure.question.entity.PaperMain;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paper.FindPaperPageParam;
import com.sure.question.vo.paper.upload.DividePaperContentVo;
import com.sure.question.vo.paper.upload.UploadPaperParam;
import com.sure.question.vo.question.QuestionVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface PaperUploadService {
    /**
     * 查个人上传试卷
     */
    IPage<PaperMain> getSelfUpload(String userId, FindPaperPageParam param);

    /**
     * 上传试卷
     */
    String uploadPaper(MultipartFile file, UploadPaperParam param, boolean enablePdf, TokenUserVo userInfo);

    /**
     * 划题获取试卷内容
     */
    DividePaperContentVo getDividePaperContent(String paperId, boolean addRawHtml, String userId);

    /**
     * 划题添加题目
     */
    List<QuestionVo> addDividePaperQuestions(String paperId, String paperStructure, List<QuestionVo> questionVos, String userId);

    /**
     * 划题修改题目
     */
    List<QuestionVo> changeDividePaperQuestions(String paperId, String paperStructure, List<QuestionVo> questionVos, String userId);

    /**
     * 划题删除题目
     */
    void deleteDividePaperQuestions(String paperId, String paperStructure, List<String> questionIds, String userId);

    /**
     * 划题修改试卷结构
     */
    void changeDividePaperStructure(String paperId, String paperStructure, String userId);

    /**
     * 完成划题
     */
    void finishDivide(String paperId, boolean allowNoScore, TokenUserVo userVo);

    /**
     * 重启划题
     */
    void restartDivide(String paperId, String userId);
}
