package com.sure.question.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.question.vo.cleanTask.CheckQuestionVo;
import com.sure.question.vo.cleanTask.CleanTaskVo;
import com.sure.question.vo.question.QuestionVo;

import java.util.List;

public interface CleanTaskService {
    interface IGetPaperIdBatch {
        List<String> apply(int gradeLevel, int subjectId, String lastPaperId);
    }

    interface ISelectPaperQuestions {
        List<QuestionVo> select(String paperId);
    }

    interface IHandleQuestionHtml {
        String apply(String html, String questionId) throws Exception;
    }

    /**
     * 执行清理任务
     */
    void runPublicBankTask(int taskId, IHandleQuestionHtml handleQuestionHtml);

    void runCoachBookTask(int taskId, IHandleQuestionHtml handleQuestionHtml);

    void runCoachBookCustomTask(int taskId, IHandleQuestionHtml handleQuestionHtml);

    void runPushQuestionTask(int taskId, IHandleQuestionHtml handleQuestionHtml);

    void runTask(int taskId, IGetPaperIdBatch getPaperIdBatch, ISelectPaperQuestions selectPaperQuestions, IHandleQuestionHtml handleQuestionHtml);

    /**
     * 创建清理任务
     * @param name 任务名称
     * @param description 任务说明
     * @param gradeLevel 学段
     * @param subjectId 学科
     */
    void createCleanTask(String name, String description, int gradeLevel, int subjectId);

    /**
     * 为所有学段学科创建清理任务
     * @param name 任务名称
     * @param description 任务说明
     */
    void createCleanTaskForAllGradeLevelSubject(String name, String description);

    /**
     * 获取清理任务列表
     * @param gradeLevel 学段
     * @param subjectId 学科
     * @param name 任务名称
     * @param addStats 是否添加题目统计
     */
    List<CleanTaskVo> getCleanTaskList(Integer gradeLevel, Integer subjectId, String name, boolean addStats);

    /**
     * 获取清理任务信息
     */
    CleanTaskVo getCleanTaskInfo(int taskId);

    /**
     * 获取一页未确认题目
     */
    Page<CheckQuestionVo> getUnConfirmQuestionPage(int taskId, Integer page, Integer size);

    /**
     * 获取一页已忽略题目
     */
    Page<CheckQuestionVo> getIgnoredQuestionPage(int taskId, Integer page, Integer size);

    /**
     * 获取一页已确认题目
     */
    Page<CheckQuestionVo> getConfirmedQuestionPage(int taskId, Integer page, Integer size);

    /**
     * 获取一页已更新题目
     */
    Page<CheckQuestionVo> getUpdatedQuestionPage(int taskId, Integer page, Integer size);

    /**
     * 确认题目
     * @param taskId 任务Id
     * @param questionId 题目Id
     */
    void confirmTaskQuestion(int taskId, String questionId);

    /**
     * 忽略题目
     * @param taskId 任务Id
     * @param questionId 题目Id
     */
    void ignoreTaskQuestion(int taskId, String questionId);

    /**
     * 更新题目
     * @param taskId 任务Id
     * @param questionId 题目Id
     */
    void updateOneQuestion(int taskId, String questionId);

    /**
     * 确认并更新题目
     * @param taskId 任务Id
     * @param questionId 题目Id
     */
    void confirmAndUpdateQuestion(int taskId, String questionId);

    /**
     * 更新所有已确认题目
     * @param taskId 任务Id
     */
    void updateAllConfirmedQuestions(int taskId);
}
