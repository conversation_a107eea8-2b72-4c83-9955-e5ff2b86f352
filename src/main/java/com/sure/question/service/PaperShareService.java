package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paperVo.SharePaperVo;

import java.util.List;

/**
 * @data 2021/2/26 14:55
 * <AUTHOR>
 */
public interface PaperShareService {

    IPage<SharePaperVo> getMyShares(TokenUserVo userVo, Integer type, Integer grade, Integer year, String region, Integer subject,
                                    Integer stage, String bookType, Integer flagCoachBookId, Integer page, Integer size);

    List<SharePaperVo> getMyShareSpecifiedPaperId(String paperId, TokenUserVo userVo);

    IPage<SharePaperVo> getShareToMe(TokenUserVo userVo, Integer type, Integer grade, Integer year, String region, Integer subject,
                                     Integer stage, Integer page, Integer size, Integer bank, String keyword, String bookType,
                                     Integer flagCoachBookId, Integer paperGroupId);

    IPage<SharePaperVo> getAvailableCoachBookPapersShareToMe(TokenUserVo userVo, Integer type, Integer grade, Integer year, String region,
                                                             Integer subject, Integer stage, Integer page, Integer size, Integer bank,
                                                             String keyword, Integer coachBookId, Integer flagCoachBookId, Integer paperGroupId);

    void shareTo(TokenUserVo userVo, String targetId, String paperId);

    void recall(TokenUserVo userVo, Integer id);

    void hidePaper(TokenUserVo userVo, Integer paperId);
}
