package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.question.entity.PaperMain;
import com.sure.question.vo.Basket;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.CoachBookPaperMainVo;
import com.sure.question.vo.paper.FindPaperPageParam;

import java.io.OutputStream;

public interface PaperService {
    /**
     * 公共库试卷查询
     */
    IPage<PaperMain> findInPublicBank(FindPaperPageParam param, String userId);

    /**
     * 收藏试卷查询
     */
    IPage<PaperMain> getFavorite(FindPaperPageParam param, String userId);

    /**
     * 改变试卷收藏状态
     */
    void changeFav(Long paperId, String userId, String schoolId);

    /**
     * 获取试卷内容
     */
    Basket getPaperContent(String paperId);

    /**
     * 获取试卷内容（已渲染公式）
     */
    Basket getPaperContentRendered(String paperId);

    /**
     * 获取教辅试卷内容
     */
    Basket getCoachBookPaperContent(String paperId, String schoolId);

    /**
     * 获取教辅试卷内容（已渲染公式）
     */
    Basket getCoachBookPaperContentRendered(String paperId, String schoolId);

    /**
     * 家长查教辅试卷内容
     */
    Basket getCoachBookPaperContentParent(String paperId, String studentId, String schoolId, TokenUserVo userVo);

    /**
     * 查试卷名称
     */
    String getPaperNameByPaperId(String paperId);

    /**
     * 查试卷信息
     */
    PaperMain getPaperInfo(String paperId);

    /**
     * 查教辅试卷信息
     */
    CoachBookPaperMainVo getCoachBookPaperInfo(String paperId, String schoolId);

    /**
     * 修改试卷信息
     */
    void changeInfo(PaperMain paperMain, TokenUserVo userVo);

    /**
     * 修改试卷结构
     */
    void changePaperStructure(String paperId, String paperStructure, TokenUserVo userVo);

    /**
     * 修改试卷题目别名
     */
    void updateQuestionAlias(String paperId, String paperStructure, TokenUserVo userVo);

    /**
     * 增加答题卡或反馈卡下载次数
     */
    void addSheetDownloadCount(String paperId, String type);

    /**
     * 获取答题卡或反馈卡下载次数
     */
    Integer getSheetDownloadCount(String paperId, String type);

    /**
     * 试卷浏览次数自增1
     */
    void paperViewIncr(String paperId);

    /**
     * 试卷下载次数自增1
     */
    void paperDownIncr(String paperId);

    /**
     * 删除试卷（标记删除）
     */
    void delPaperById(String paperId, TokenUserVo userVo);

    /**
     * 下载试卷word
     */
    void downloadPaperWord(String paperId, String html, String size, Integer gradeId, Integer paperTypeId, TokenUserVo userVo, OutputStream outputStream);

    /**
     * 检查试卷及题目是否完整
     */
    void checkPaperAndQuestionsFinished(String paperId, boolean allowNoScore);



    CoachBookPaperMainVo getSchoolCoachPaper(String id, String schoolId, String studentId, String userId);

    Page<PaperMain> getAvailableCoachBookPapers(String userId, Integer page, Integer size, String keyword, Integer stage,
                                                Integer subjectId, Integer coachBookId, Integer flagCoachBookId, Integer paperGroupId);

    Page<PaperMain> getAvailableCoachBookPapersUpload(String userId, Integer page, Integer size, String keyword, Integer stage,
                                                      Integer subjectId, Integer coachBookId, Integer flagCoachBookId, Integer paperGroupId);
}
