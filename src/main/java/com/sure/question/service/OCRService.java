package com.sure.question.service;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

public interface OCRService {
    /**
     * 将pdf转为html
     */
    String convertPdfToHtml(InputStream pdfFileStream, String tempFolder, int maxPage, int gradeLevelId, int subjectId) throws Exception;

    /**
     * 解析ocr结果为html并保存
     */
    String parseOCRResponse(List<String> imgPathList, List<String> ocrResponsePathList, int subjectId) throws IOException;
}
