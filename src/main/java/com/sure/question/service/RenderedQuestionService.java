package com.sure.question.service;

import com.sure.question.entity.RenderedQuestion;
import com.sure.question.vo.question.QuestionVo;

import java.util.Collection;
import java.util.List;

public interface RenderedQuestionService {
    /**
     * 替换题目中的公式
     * @param questionVos 题目列表
     * @param renderImmediately 对于未渲染公式的题目，是否立即渲染
     */
    void replaceRenderedContent(List<? extends QuestionVo> questionVos, boolean renderImmediately);

    /**
     * 渲染题目公式
     * @param question 题目
     * @return 渲染后的内容
     */
    <T extends QuestionVo> List<RenderedQuestion> renderQuestion(T question);

    /**
     * 清除题目已渲染公式内容
     * @param questionId 题目Id
     */
    void deleteRenderedQuestion(String questionId);

    /**
     * 批量清除题目已渲染公式内容
     * @param questionIds 题目Ids
     */
    void deleteRenderedQuestions(Collection<String> questionIds);
}
