package com.sure.question.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.QuestionCorrect;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.questioncorrect.QuestionCorrectVo;

/**
 * <AUTHOR>
 * @date 2023/3/24 11:36
 */
public interface QuestionCorrectService extends IService<QuestionCorrect> {
    /**
     * 标记题目
     */
    void mark(QuestionCorrect params);

    /**
     * 取消标记
     */
    void unMark(String questionId, String userId);

    /**
     * 查一条报错记录
     */
    QuestionCorrect getQuestionCorrectItem(String questionId, String userId);

    /**
     * 查询题目纠错列表
     *
     * @param gradeLevel  学段
     * @param subjectId   科目id
     * @param status      状态
     * @param beginTime   开始时间
     * @param endTime     结束时间
     * @param currentPage 当前页
     * @param pageSize    页面大小
     * @return 纠错列表
     */
    Page<QuestionCorrectVo> listQuestion(Integer gradeLevel, Integer subjectId, int status, String beginTime, String endTime, Integer currentPage, Integer pageSize);

    /**
     * 更新状态为已处理
     *
     * @param questionId 题目Id
     * @param userInfo 处理员
     */
    void updateStatusToHandled(String questionId, TokenUserVo userInfo);

    /**
     * 纠正题目
     */
    QuestionVo correct(QuestionVo questionVo, String editorId);
}
