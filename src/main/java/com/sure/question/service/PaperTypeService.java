package com.sure.question.service;

import com.sure.question.entity.PaperType;

import java.util.List;

public interface PaperTypeService {
    List<PaperType> listPaperTypes(Integer gradeLevel);

    int addPaperType(int gradeLevel, String paperTypeName, String userId);

    void updatePaperType(int gradeLevel, int paperTypeId, String paperTypeName, int orderId, String userId);

    void deletePaperType(int gradeLevel, int paperTypeId, String userId);
}
