package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sure.question.document.QuestionDoc;
import com.sure.question.vo.es.QueryParam;

import java.util.Collection;
import java.util.Map;

public interface QuestionDocService {
    /**
     * 按条件搜索题目，返回题目Id和匹配到关键词的字段
     */
    IPage<QuestionDoc> selectQuestionDocIdHighlightsPage(QueryParam param);

    /**
     * 查学段学科题目总数
     */
    int selectGradeLevelSubjectQuestionCount(int gradeLevel, int subjectId);

    /**
     * 同步一批题目到ES
     * @param questionIdCollection 题目Id集合
     * @return 同步失败题目Id与原因映射
     */
    Map<String, String> sync(Collection<String> questionIdCollection);
}
