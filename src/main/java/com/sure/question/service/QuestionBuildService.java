package com.sure.question.service;

import com.sure.question.dto.question.SelectQuestionVoOption;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.UserQuestionVo;

import java.util.Collection;
import java.util.List;

/**
 * 查题相关
 */
public interface QuestionBuildService {
    /**
     * 查单个QuestionVo
     */
    QuestionVo selectQuestionVo(String questionId);

    /**
     * 查单个QuestionVo
     */
    QuestionVo selectQuestionVo(String questionId, SelectQuestionVoOption option);

    /**
     * 查单个UserQuestionVo
     */
    UserQuestionVo selectUserQuestionVo(String questionId, String userId);

    /**
     * 查单个UserQuestionVo
     */
    UserQuestionVo selectUserQuestionVo(String questionId, String userId, SelectQuestionVoOption option);

    /**
     * 查多个QuestionVos
     */
    List<QuestionVo> selectQuestionVos(Collection<String> questionIds);

    /**
     * 查多个QuestionVos，公式已渲染
     */
    List<QuestionVo> selectRenderedFormulaQuestionVos(List<String> questionIds);

    /**
     * 查多个QuestionVos
     */
    List<QuestionVo> selectQuestionVos(Collection<String> questionIds, SelectQuestionVoOption option);

    /**
     * 查多个UserQuestionVos
     */
    List<UserQuestionVo> selectUserQuestionVos(Collection<String> questionIds, String userId);

    /**
     * 查多个UserQuestionVos
     */
    List<UserQuestionVo> selectUserQuestionVos(Collection<String> questionIds, String userId, SelectQuestionVoOption option);

    /**
     * 查试卷题目QuestionVos，题目无序
     */
    List<QuestionVo> selectPaperQuestionVos(String paperId);

    /**
     * 查试卷题目QuestionVos，题目无序，公式已渲染
     */
    List<QuestionVo> selectRenderedFormulaPaperQuestionVos(String paperId);

    /**
     * 查试卷题目QuestionVos，题目无序
     */
    List<QuestionVo> selectPaperQuestionVos(String paperId, SelectQuestionVoOption option);

    /**
     * 查试卷题目UserQuestionVos，题目无序
     */
    List<UserQuestionVo> selectPaperUserQuestionVos(String paperId, String userId);

    /**
     * 查试卷题目UserQuestionVos，题目无序
     */
    List<UserQuestionVo> selectPaperUserQuestionVos(String paperId, String userId, SelectQuestionVoOption option);


    /**
     * 查出刚刚保存的题目（等待数据库主从同步）
     */
    List<QuestionVo> selectJustSavedQuestionVos(Collection<String> questionIds, SelectQuestionVoOption option);

    /**
     * 查出刚刚保存的题目（等待数据库主从同步）
     */
    List<QuestionVo> selectJustSavedQuestionVos(List<QuestionVo> srcQuestionVos, SelectQuestionVoOption option);
}
