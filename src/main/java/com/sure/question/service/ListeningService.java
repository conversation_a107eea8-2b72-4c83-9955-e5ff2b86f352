package com.sure.question.service;

import com.sure.question.vo.question.QuestionListeningVo;
import com.sure.question.vo.question.QuestionVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface ListeningService {
    /**
     * 查询题目听力（多道题）
     */
    Map<String, List<QuestionListeningVo>> getQuestionsListeningList(Collection<String> questionIds);

    /**
     * 保存题目听力
     */
    void saveQuestionListening(QuestionVo questionVo);

    /**
     * 从原题复制听力后再保存到新题上
     */
    void copyFromOldQuestionAndSaveNewQuestionListening(QuestionVo questionVo, String oldQuestionId);

    /**
     * 批量删除题目听力
     */
    void deleteQuestionsListening(Collection<String> questionIds);

    /**
     * 保存一条听力
     */
    void saveOneListening(QuestionListeningVo vo);
}
