package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sure.question.entity.SchoolCoachBook;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.coachBook.BookSchoolVo;
import com.sure.question.vo.paperVo.SchoolCoachPaperVo;
import com.sure.question.vo.question.UserQuestionVo;
import com.sure.question.vo.schoolCoachBook.*;

import java.util.List;
import java.util.Set;

public interface SchoolCoachService extends IService<SchoolCoachBook> {
    /**
     * 分页查询当前学校的定制教辅
     */
    Page<BookInfo> getBookPage(QueryBookVo queryVo, Integer page, Integer size);

    /**
     * 分页查询当前学校的定制试卷
     */
    Page<SchoolCoachPaperVo> getPapersPage(QueryPaperVo queryVo, Integer page, Integer size, String schoolId, String userId);

    /**
     * 定制教辅教材选题或知识点选题
     */
    IPage<UserQuestionVo> findQuestionByChapterOrKnowledge(TokenUserVo userVo, QueryQuestionVo queryVo);
    /**
     * 查询学校定制的特定试卷信息
     */
    SchoolCoachPaperVo getSimplePaper(String schoolId, Integer semesterId, String paperId);

    /**
     * 分页查询指定教辅定制的学校
     */
    Page<BookSchoolVo> getSchoolPage(Integer coachBookId, String keyword, Integer page, Integer size);

    /**
     * 添加一本教辅到指定的学校学年
     */
    String addBookToSchools(Integer coachBookId, Integer semesterId, Set<String> schoolIds, String userId);

    /**
     * 批量删除定制教辅的学校（按教辅）
     */
    int delSchoolByBook(Integer coachBookId, List<Integer> ids);


    void editSchoolBook(SchoolCoachBook vo, String userId);

    /**
     * 添加试卷到校本教辅
     *
     * @param gradeId
     * @param semesterId
     * @param term
     * @param paperId
     * @param schoolId
     * @param userId
     */
    void savePaperToSchoolCoach(String paperId, Integer gradeId, Integer semesterId, Integer term, String schoolId, String userId);

    /**
     * 查询学校开通的全部教辅
     *
     * @param schoolId 学校id
     * @return 教辅列表
     */
    List<SchoolCoachVo> listSchoolBooks(String schoolId);

    void setWrongQuesMark(List<WrongQuesMarkVo> vos, TokenUserVo tokenUserVo);

    String getLastPaperId(String schoolId, Integer semesterId, Integer term, String paperId);
}
