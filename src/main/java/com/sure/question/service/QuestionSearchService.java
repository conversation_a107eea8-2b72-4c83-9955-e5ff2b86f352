package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.question.vo.dataVo.IntStrVo;
import com.sure.question.vo.pushQuesVo.PushQuesParam;
import com.sure.question.vo.question.FindQuestionPageParam;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.UserQuestionVo;
import com.sure.question.vo.requestVo.intelligentVo.IntelligentRequest;
import com.sure.question.vo.requestVo.intelligentVo.WrongQuesTrain;

import java.util.List;

/**
 * 搜题相关
 */
public interface QuestionSearchService {
    /**
     * 规范化搜索题目请求参数
     * 设置地区前缀及后代，设置章节后代，设置知识点后代
     * @return 地区、章节、知识点找不到时返回false
     */
    boolean normalizeFindQuestionPageParam(FindQuestionPageParam param);

    /**
     * 构建QuesSearchVo分页
     */
    IPage<UserQuestionVo> buildQuesSearchVoPage(List<String> questionIds, int page, int size, String userId);

    /**
     * 教材选题或知识点选题
     */
    Page<UserQuestionVo> findByChapterOrKnowledge(FindQuestionPageParam param, String userId);

    /**
     * 添加变式题时选题
     */
    Page<UserQuestionVo> findByAddVariantsParam(FindQuestionPageParam param, String userId);

    /**
     * 指定题目Id查相似题
     * @param questionId 题目Id
     * @param size 相似题个数
     * @param userId 用户Id
     * @return 相似题
     */
    List<UserQuestionVo> findSimilarQuestions(String questionId, int size, String userId);

    /**
     * 根据题目内容查相似题
     */
    List<UserQuestionVo> findSimilarQuestions(QuestionVo questionVo, int size, String userId);

    /**
     * 指定题目Id查相似题知识点
     */
    List<IntStrVo> findSimilarQuestionsKnowledge(String questionId, int size);

    /**
     * 根据题目内容查相似题知识点
     */
    List<IntStrVo> findSimilarQuestionsKnowledge(QuestionVo questionVo, int size);

    /**
     * 智能组卷
     */
    List<QuestionVo> getIntelligentPaperQuestions(IntelligentRequest intelligent, String userId);

    /**
     * 学情组卷
     */
    List<QuestionVo> wrongQuesTrain(WrongQuesTrain wrongQuesTrain, String userId);

    /**
     * 搜原题的变式题
     */
    List<String> findSrcQuestionVariants(PushQuesParam param);
}
