package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sure.question.entity.PaperMain;
import com.sure.question.vo.paper.FindPaperPageParam;

public interface PaperMakeService {
    /**
     * 获取用户组卷记录
     */
    IPage<PaperMain> getSelfMake(FindPaperPageParam param, String userId);

    /**
     * 再次编辑已组试卷
     */
    void editMakePaper(String id, String userId);

    /**
     * 获取用户组卷数量
     */
    int getUserMakePaperCount(String userId);
}
