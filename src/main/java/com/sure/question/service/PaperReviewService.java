package com.sure.question.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sure.question.dto.paper.PaperContent;
import com.sure.question.entity.PaperMain;
import com.sure.question.vo.TokenUserVo;
import com.sure.question.vo.paper.FindPaperPageParam;
import com.sure.question.vo.paper.stat.EditorStatVo;
import com.sure.question.vo.question.QuestionVo;

import java.util.List;

public interface PaperReviewService {
    /**
     * 查待分配试卷
     */
    IPage<PaperMain> getUnassigned(FindPaperPageParam param);

    /**
     * 查已分配待提交试卷
     */
    IPage<PaperMain> getAssignedAndUnSubmitted(FindPaperPageParam param);

    /**
     * 查已提交待发布试卷
     */
    IPage<PaperMain> getSubmittedAndUnpublished(FindPaperPageParam param);

    /**
     * 查个人未提交试卷
     */
    IPage<PaperMain> getSelfUnsubmitted(FindPaperPageParam param, String userId);

    /**
     * 查个人已提交试卷
     */
    IPage<PaperMain> getSelfSubmitted(FindPaperPageParam param, String userId);

    /**
     * 分配审核任务
     */
    void assign(String editorId, List<String> paperIds);

    /**
     * 取消分配任务
     */
    void removeAssignment(List<String> paperIds);

    /**
     * 提交审核试卷
     */
    void submit(String paperId, boolean allowNoScore, TokenUserVo userVo);

    /**
     * 驳回提交
     */
    void rejectSubmission(List<String> paperIds);

    /**
     * 发布至公共库
     */
    void publish(String paperId, boolean allowNoScore);

    /**
     * 撤回发布至公共库
     */
    void unpublish(String paperId);


    /**
     * 审卷获取试卷内容
     */
    PaperContent getReviewPaperContent(String paperId, TokenUserVo userVo);

    /**
     * 审卷添加题目
     */
    List<QuestionVo> addReviewPaperQuestions(String paperId, String paperStructure, List<QuestionVo> questionVos, TokenUserVo userVo);

    /**
     * 审卷修改题目
     */
    List<QuestionVo> changeReviewPaperQuestions(String paperId, String paperStructure, List<QuestionVo> questionVos, TokenUserVo userVo);

    /**
     * 审卷删除题目
     */
    void deleteReviewPaperQuestions(String paperId, String paperStructure, List<String> questionIds, TokenUserVo userVo);

    /**
     * 审卷修改试卷结构
     */
    void changeReviewPaperStructure(String paperId, String paperStructure, TokenUserVo userVo);


    /**
     * 题库管理员查看编辑审核试卷统计
     */
    List<EditorStatVo> getEditorsReviewPaperStats(Integer gradeLevel, Integer subjectId);

    /**
     * 编辑员查看个人看题统计
     */
    EditorStatVo getEditorSelfReviewPaperStats(Integer gradeLevel, Integer subjectId, String userId);

    /**
     * 题库管理员查看编辑组卷统计
     */
    List<EditorStatVo> getEditorsMakePaperStats(Integer gradeLevel, Integer subjectId, String bookType);

    /**
     * 编辑员查看个人组卷统计
     */
    EditorStatVo getEditorSelfMakePaperStats(Integer gradeLevel, Integer subjectId, String bookType, String userId);

    /**
     * 题库管理员查看编辑上传统计
     */
    List<EditorStatVo> getEditorsUploadPaperStats(Integer gradeLevel, Integer subjectId, String bookType);

    /**
     * 编辑员查看个人上传统计
     */
    EditorStatVo getEditorSelfUploadPaperStats(Integer gradeLevel, Integer subjectId, String bookType, String userId);
}
