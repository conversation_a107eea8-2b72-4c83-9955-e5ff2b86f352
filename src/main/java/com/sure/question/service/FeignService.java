package com.sure.question.service;

import com.sure.question.entity.SysGrade;
import com.sure.question.vo.QuesUserVo;
import com.sure.question.vo.UserMsgVo;
import com.sure.question.vo.coachBook.AnswerSheetVo;
import com.sure.question.vo.coachBook.SheetVo;
import com.sure.question.vo.pushQuesVo.SimpleExamSubjectVo;
import com.sure.question.vo.sheet.SimpleSheetVo;

import java.util.List;
import java.util.Map;

public interface FeignService {
    List<UserMsgVo> lstTeacherInfoBySchoolIdAndKey(String schoolId, String key);

    List<UserMsgVo> lstTeacherInfoByIds(List<String> teacherIds);

    Map<String, String> getUserIdNameMobileMap(List<String> userIds);

    Map<String, String> getUserIdNameMap(List<String> userIds);

    Map<String, String> getEditorNameMap(List<String> editorIds);

    List<QuesUserVo> getEditorList(Integer gradeLevel, Integer subjectId);

    Map<String, String> getSchoolIdNameMap(List<String> schoolIds);

    Map<String, SimpleSheetVo> getPaperIdFeedbackSheetMap(List<String> paperIds);

    Map<String, SimpleSheetVo> getPaperIdAnswerSheetMap(List<String> paperIds);

    List<SheetVo> getFeedbackSheetsInfo(List<String> sheetIds);

    List<AnswerSheetVo> getAnswerSheetsInfo(List<String> sheetIds);

    SysGrade getSysGrade(int gradeId);

    List<SimpleExamSubjectVo> listExamSubjectByIds(List<String> examSubjectIds);

    List<String> checkoutSchoolIdsByRegionId(Long regionId, Integer schoolType, Boolean isEnabled);

    boolean isDisableFlagStuWrong(String schoolId);
}
