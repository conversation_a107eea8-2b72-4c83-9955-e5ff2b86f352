package com.sure.question.feign;

import com.sure.common.entity.Result;
import com.sure.question.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "PDF-SERVICE", configuration = FeignConfig.class)
public interface FeignPdfService {
    /**
     * 生成PDF
     */
    @GetMapping("/pdf/generate")
    Result generatePdf(@RequestParam("url") String url,
                       @RequestParam("watermark") Boolean watermark,
                       @RequestParam("headerContent") String headerContent,
                       @RequestParam("footerContent") String footerContent);

    /**
     * 生成PDF
     */
    @GetMapping("/pdf/generateWithLeftRightHeaderAndFooter")
    Result generateWithLeftRightHeaderAndFooter(@RequestParam("url") String url,
                                                @RequestParam("watermark") Boolean watermark,
                                                @RequestParam("headerLeft") String headerLeft,
                                                @RequestParam("headerRight") String headerRight,
                                                @RequestParam("footerContent") String footerContent);
}
