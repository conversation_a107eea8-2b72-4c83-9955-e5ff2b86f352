package com.sure.question.feign;

import com.sure.common.entity.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2024/1/27 15:03
 */
@FeignClient(value = "SURE-REPORT")
public interface ReportService {

    /**
     * 获取教师已经访问过的试卷id
     *
     * @param teacherId  教师id
     * @param semesterId 学期id
     * @param term       学期
     * @param classId    班级id
     * @return Result
     */
    @GetMapping("feedback/listTeacherAccessedFeedbackExamIds")
    Result listTeacherAccessedFeedbackExamIds(@RequestParam("teacherId") String teacherId, @RequestParam("semesterId") Integer semesterId,
                                              @RequestParam("term") Integer term, @RequestParam("classId") String classId);

}
