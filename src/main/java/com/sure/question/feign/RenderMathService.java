package com.sure.question.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "SURE-SIDECAR")
public interface RenderMathService {
    @PostMapping("renderMath")
    String renderHTMLMath(@RequestParam String questionId, @RequestBody String html);
}
