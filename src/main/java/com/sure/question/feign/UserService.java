package com.sure.question.feign;

import com.sure.common.entity.Result;
import com.sure.common.entity.UserInfo;
import com.sure.question.annotation.CheckFeignResult;
import com.sure.question.annotation.FeignResult;
import com.sure.question.entity.SysGrade;
import com.sure.question.vo.UserMsgVo;
import com.sure.question.vo.feign.StudentQueryVo;
import com.sure.question.vo.student.StudentInfoVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "SURE-USER")
public interface UserService {

    // 获取用户信息
    @GetMapping("user")
    UserInfo getUserInfo(@RequestParam("userId") String userId,
                         @RequestParam("key") String key);

    @GetMapping("feign/lstTeacherInfoBySchoolIdAndKey")
    List<UserMsgVo> lstTeacherInfoBySchoolIdAndKey(@RequestParam String schoolId, @RequestParam(required = false) String key);

    @PostMapping("feign/lstTeacherInfoByIds")
    List<UserMsgVo> lstTeacherInfoByIds(@RequestBody List<String> teacherIds);

    @PostMapping("feign/teacherInfo")
    Result getTeacherInfos(@RequestBody List<String> teacherIds);

    @GetMapping("feign/editorList")
    Result getEditorList(@RequestParam("gradeLevel") Integer gradeLevel, @RequestParam("subjectId") Integer subjectId);

    @PostMapping("feign/schoolInfo")
    Result getSchoolInfos(@RequestBody List<String> schoolIds);

    @GetMapping("feign/sysGradeInfo")
    @ApiOperation("获取系统年级信息")
    SysGrade getSysGradeInfo(@RequestParam int gradeId);

    @GetMapping("user/editorHasPermission")
    Boolean editorHasPermission(@RequestParam("editorId") String editorId, @RequestParam("gradeLevel") Integer gradeLevel, @RequestParam("subjectId") Integer subjectId);

    @GetMapping("student/getStudentInfo")
    StudentInfoVo getStudentInfo(@RequestParam("studentId") String studentId);

    @PostMapping("parent/lstParentInfo")
    Result listParentInfo(@RequestBody List<String> parentIds);

    /**
     * 是否是指定家长的小孩
     * @param parentId 家长Id
     * @param studentId 学生Id
     * @param checkActivated 是否检查已激活
     * @param checkInSchool 是否检查在读
     * @param schoolId 不为空则检查学生属于指定学校
     * @return Boolean
     */
    @GetMapping("parent/internal/isChildOfParent")
    Result isChildOfParent(@RequestParam("parentId") String parentId,
                           @RequestParam("studentId") String studentId,
                           @RequestParam("checkActivated") Boolean checkActivated,
                           @RequestParam("checkInSchool") Boolean checkInSchool,
                           @RequestParam("schoolId") String schoolId);

    /**
     * 查询指定机构或学校的下属学校ID（含本校）
     */
    @GetMapping("school/getSubordinateSchoolIds")
    Result getSubordinateSchoolIds(@RequestParam("schoolId") String schoolId);

    /**
     * 获取指定区域全体学校
     */
    @ApiOperation("获取指定区域全体学校")
    @GetMapping("school/getSchoolIdsByRegionId")
    Result getSchoolIdsByRegionId(@RequestParam long regionId, @RequestParam(required = false) Integer schoolType, @RequestParam(required = false) Boolean isEnabled);

    @PostMapping("student/listStudentIdsByName")
    Result listStudentIdsByName(@RequestBody StudentQueryVo vo);

    /**
     * 查询单校学生ID列表（按学生姓名模糊搜索）
     *
     * @param schoolId
     * @param studentNameKey 学生姓名关键词
     * @return 学生 id 列表
     */
    @GetMapping("student/listStudentIdsBySchoolIdAndStudentNameKey")
    Result listStudentIdsBySchoolIdAndStudentNameKey(@RequestParam String schoolId, @RequestParam String studentNameKey);

    @ApiOperation("查询学校是否禁用家长采集错题")
    @GetMapping("school/isSchoolDisableFlagStuWrong")
    Result isSchoolDisableFlagStuWrong(@RequestParam String schoolId);

    @ApiOperation("获取指定教师指定学年任课信息")
    @GetMapping("teacher/personTeachingByTeacherIdAndSemesterId")
    Result getPersonalTeachingByTeacherIdAndSemesterId(@RequestParam String teacherId, @RequestParam Integer semesterId);

    @GetMapping("student/getStudentCountByClassId")
    Result getStudentCountByClassId(@RequestParam String classId);

    /**
     * 检查用户是否具有下载教辅资料权限
     */
    @GetMapping("coachBookDownloadAssetConfig/checkUserHasPermission")
    @CheckFeignResult
    FeignResult<Boolean> checkUserHasDownloadCoachBookAssetPermission(@RequestParam String userId);
}
