package com.sure.question.annotation;

import com.sure.common.entity.Result;
import com.sure.question.util.SignUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Aspect
@Component
public class CheckSignAop {
    private final static String INTRANET_TOKEN = "HiShkVwLNgmJeRR7PRJONwvt4m9qKP2Y";

    /**
     * 拦截指定方法，判断签名是否通过
     *
     * @param pjp       切入点对象
     * @param checkSign 自定义的注解对象
     * @return
     * @throws Throwable
     */
    @Around("execution(* com.sure.question.controller.*Controller.*(..)) && @annotation(checkSign)")
    public Object doAround(ProceedingJoinPoint pjp, CheckSign checkSign) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        // 经过网关的内部请求
        if (INTRANET_TOKEN.equals(request.getHeader("authentication"))) {
            return pjp.proceed();
        }
        Enumeration<String> parameterNames = request.getParameterNames();
        Map<String, Object> map = new HashMap<>();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            map.put(paramName, request.getParameter(paramName));
        }
        String sign = request.getHeader("AuthSign");
        if (!SignUtil.sign(map).equals(sign)) {
            return Result.failed("签名校验不通过");
        }
        return pjp.proceed();
    }
}
