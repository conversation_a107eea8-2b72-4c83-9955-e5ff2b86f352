package com.sure.question.annotation;

import com.sure.common.entity.StatusCode;
import com.sure.common.exception.ApiException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class FeignResultAop {

    @Around("@annotation(CheckFeignResult)")
    public Object handleProcessResult(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed();
        if (result == null) {
            throw new IllegalStateException("接口无返回值");
        }
        if (!(result instanceof FeignResult)) {
            throw new IllegalStateException("返回值必须是 FeignResult 类型");
        }
        FeignResult<?> res = (FeignResult<?>) result;

        // 校验业务状态码
        if (!StatusCode.OK.equals(res.getCode())) {
            throw new ApiException(res.getMessage());
        }

        return res;
    }
}
