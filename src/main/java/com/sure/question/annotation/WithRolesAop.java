package com.sure.question.annotation;

import com.alibaba.fastjson.JSONArray;
import com.sure.common.exception.ApiException;
import com.sure.question.enums.RoleEnum;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Aspect
@Component
public class WithRolesAop {
    @Around("@annotation(com.sure.question.annotation.WithRoles)")
    public Object checkRoles(ProceedingJoinPoint pjp) throws Throwable {
        // 获取 request
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        String roleList = request.getHeader("roleList");
        List<Integer> roles = JSONArray.parseArray(roleList, Integer.class);

        // 获取注解的值
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        WithRoles annotation = signature.getMethod().getAnnotation(WithRoles.class);
        if (annotation.value().length == 0) { // 通过
            return pjp.proceed();
        } else { // 需要检验权限
            ArrayList<Integer> needs = new ArrayList<>(annotation.value().length);
            RoleEnum[] value = annotation.value();
            if (value.length > 0) {
                for (int i = 0; i < value.length; i++) {
                    needs.add(value[i].getCode());
                }
                if (!roles.removeAll(needs)) throw new ApiException("无此操作权限");
            }
            return pjp.proceed();
        }
    }

}
