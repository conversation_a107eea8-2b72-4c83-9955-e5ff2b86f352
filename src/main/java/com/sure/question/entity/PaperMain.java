package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sure.question.dto.paper.PaperInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PaperMain 实体类
 *
 * @version 1.0
 * @date 2019-04-23 08:54:02
 */
@TableName("paper_main")
@ApiModel
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaperMain implements java.io.Serializable, PaperInfo {
    /**
     * 试卷储存地址
     */
    @ApiModelProperty(value = "试卷储存地址")
    private String paperLocation;
    @ApiModelProperty(value = "试卷 id")
    @TableId(type = IdType.INPUT)
    private String id;
    @ApiModelProperty(value = "试卷名称")
    private String paperName;
    @ApiModelProperty(value = "学校 id")
    private String schoolId;
    @ApiModelProperty(value = "学段 id")
    private Integer gradeLevel;
    @ApiModelProperty(value = "学科 id")
    private Integer subjectId;
    @ApiModelProperty(value = "年级 id")
    private Integer gradeId;
    @ApiModelProperty(value = "年份")
    private Integer year;
    @ApiModelProperty(value = "试卷所属区划 id")
    private Long regionId;
    @ApiModelProperty(value = "标记试卷所属教辅，仅用于辅助筛选，真正确定试卷和教辅的关联记录保存在coach_book_paper表")
    private Integer flagCoachBookId;

    @ApiModelProperty(value = "试卷分组ID")
    private Integer paperGroupId;

    @ApiModelProperty(value = "试卷所属教辅名称")
    @TableField(exist = false)
    private String flagCoachBookName;

    @ApiModelProperty(value = "试卷类型")
    private Integer paperTypeId;
    @ApiModelProperty(value = "上传文件路径")
    private String uploadFile;

    /**
     * 上传类型：0-我的卷库；1-试卷库；2-组卷；3-制卡
     */
    @ApiModelProperty(value = "上传类型：0-我的卷库；1-试卷库；2-组卷；3-制卡")
    private String uploadType;

    /**
     * 组卷方式：0-默认；1-手动组卷；2-智能组卷
     */
    @ApiModelProperty(value = "组卷方式：0-默认；1-手动组卷；2-智能组卷")
    private String paperWay;

    /**
     * 试卷状态：0-正常；1-删除
     */
    @ApiModelProperty(value = "试卷状态：0-正常；1-删除")
    private String status;

    /**
     * 审核状态：0-未审核；1-审核未通过；2-审核通过
     */
    @ApiModelProperty(value = "审核状态：0-未审核；1-审核未通过；2-审核通过")
    private String checkStatus;

    /**
     * 划题状态：0-未完成划题标注；1-已完成制卷（可以编辑试题）
     */
    @ApiModelProperty(value = "划题状态：0-未完成划题标注；1-已完成制卷（可以编辑试题）")
    private String editStatus;

    /**
     * 分享状态：0-未分享；1-已分享
     */
    @ApiModelProperty(value = "分享状态：0-未分享；1-已分享")
    private String shareStatus;

    /**
     * 组分享状态：0-未审核分享；1-已审核分享
     */
    @ApiModelProperty(value = "组分享状态：0-未审核分享；1-已审核分享")
    private Integer groupShareStatus;

    /**
     * 浏览次数
     */
    @ApiModelProperty(value = "浏览次数")
    private Integer browseCount;

    /**
     * 卷库类型：1-个人库；2-校本库；3-公共库；4-定制资源
     */
    @ApiModelProperty(value = "卷库类型：1-个人库；2-校本库；3-公共库；4-定制资源")
    private String paperBank;

    /**
     * 分配状态: 0-不需要分配；1-待分配；2-已分配
     */
    @ApiModelProperty(value = "分配状态: 0-不需要分配；1-待分配；2-已分配")
    private String showStatus;

    @ApiModelProperty(value = "制卡ID")
    private String sheetId;

    /**
     * 是否精品：0-普通试卷；1-精品试卷
     */
    @ApiModelProperty(value = "是否精品：0-普通试卷；1-精品试卷")
    private String boutiqueStatus;
    @ApiModelProperty(value = "上传用户ID")
    private String userId;
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
    @ApiModelProperty(value = "更新时间")
    private Long updateTime;
    @ApiModelProperty(value = "组卷样式JSON")
    private String paperStyleJson;
    @ApiModelProperty(value = "下载次数")
    private Integer downloadCount;
    @ApiModelProperty(value = "答题卡下载次数")
    private Integer answerSheetDownloadCount;
    @ApiModelProperty(value = "错题反馈卡下载次数")
    private Integer feedbackSheetDownloadCount;
    @TableField(exist = false)
    private boolean ifFavorite;
    @TableField("editor_id")
    private String editorId;
    @TableField("format_id")
    private String formatId;
    @TableField("x_id")
    private String xId;
    private String term;

    /**
     * 同步状态：0-未同步；1-同步成功；2-同步失败。
     */
    @TableField("es_sync")
    private int esSync;

    @TableField(exist = false)
    private String shareUser;
    @TableField(exist = false)
    private Integer sharePaperId;
    @TableField("browse_count_add")
    private Integer browseCountAdd;
    @TableField("download_count_add")
    private Integer downloadCountAdd;
    @TableField("submit_time")
    private Long submitTime;

    /**
     * 错题反馈卡 id
     */
    @ApiModelProperty("错题反馈卡 id")
    @TableField(exist = false)
    private String feedbackSheetId;

    private static final long serialVersionUID = 1L;
}
