package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Subject 实体类
 *
 * @version 1.0
 * @date 2019-04-19 11:30:53
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Subject {
    @TableId(type = IdType.INPUT)
    private Integer id;
    private String subjectName;
    private String multiSubject;
    private Integer orderId;
}