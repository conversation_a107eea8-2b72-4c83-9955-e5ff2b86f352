package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <h1>首次查看学生试卷记录</h1>
 *
 * <AUTHOR>
 * @date 2023/07/26 10:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("paper_stu_access")
public class PaperStuAccess {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 学生ID
     */
    @TableField("student_id")
    private String studentId;
    /**
     * 家长ID
     */
    @TableField("parent_id")
    private String parentId;
    /**
     * 试卷ID
     */
    @TableField("paper_id")
    private Long paperId;
    /**
     * 教辅ID
     */
    @TableField("coach_book_id")
    private Integer coachBookId;
    /**
     * 类型，0-浏览；1-下载
     */
    private Integer type;
    /**
     * 第一次查看时间
     */
    @TableField("create_time")
    private Date createTime;
}
