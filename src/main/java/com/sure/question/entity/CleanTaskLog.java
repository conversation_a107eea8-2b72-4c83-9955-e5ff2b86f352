package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("clean_task_log")
public class CleanTaskLog {
    @TableId(type = IdType.INPUT)
    private Integer id;
    private Integer taskId;
    private String paperId;
    private String questionId;
    private String smallQuestionId;
    private String content;
    private Date createTime;
}
