package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("clean_task")
public class CleanTask {
    @TableId(type = IdType.INPUT)
    private Integer id;
    private String name;
    private String description;
    private Integer gradeLevel;
    private Integer subjectId;
    private String lastPaperId;
    private Integer lastPaperCount;
    private Date createTime;
    private Date updateTime;
}
