package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * QuestionBook 实体类
 *
 * @version 1.0
 * @date 2019-05-09 15:16:43
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("question_book")
public class QuestionBook {
    private Long id;
    private String questionId;
    private Integer questionNo;
    private Integer chapterId;
    private Long updateTime;
}