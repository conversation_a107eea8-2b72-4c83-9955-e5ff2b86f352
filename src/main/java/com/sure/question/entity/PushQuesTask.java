package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("push_ques_task")
public class PushQuesTask {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String examSubjectId;
    private String paperId;
    private Integer status;
    private String message;
    private String createBy;
    private Date createTime;
    private Date updateTime;
}
