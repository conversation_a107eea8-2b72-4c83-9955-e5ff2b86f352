package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <h1>试卷分组</h1>
 *
 * <AUTHOR>
 * @date 2023/7/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("paper_group")
public class PaperGroup {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 学段
     */
    private Integer stage;
    /**
     * 科目ID
     */
    private Integer subjectId;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 排序
     */
    private Integer sortCode;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
