package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 渲染公式后的题目内容
 */
@Data
@TableName(value = "rendered_question")
public class RenderedQuestion {
    /**
     * 题目Id
     */
    private String questionId;
    /**
     * 小题Id，若为材料，则存题目Id
     */
    private String smallQuestionId;
    /**
     * 题目材料或小题题干
     */
    private String contentHtml;
    /**
     * 选项
     */
    private String options;
    /**
     * 答案
     */
    private String answerHtml;
    /**
     * 解析
     */
    private String explanation;
    /**
     * 创建时间
     */
    private Date createTime;
}
