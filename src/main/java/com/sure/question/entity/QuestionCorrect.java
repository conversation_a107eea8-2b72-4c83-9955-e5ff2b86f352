package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/3/24 11:04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("question_correct")
public class QuestionCorrect {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 题目id
     */
    private String questionId;
    /**
     * 学段
     */
    private Integer gradeLevel;
    /**
     * 学科id
     */
    private Integer subjectId;
    /**
     * 试卷id
     */
    private String paperId;
    /**
     * 错误类型JSON
     */
    private String errorTypeJson;
    /**
     * 错误描述
     */
    private String description;
    /**
     * 报错者ID
     */
    private String userId;
    /**
     * 处理人ID
     */
    private String handlerId;
    /**
     * 状态：0-未处理；1-已处理
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 处理时间
     */
    private Date handleTime;
}
