package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 首页图片
 */
@TableName(value = "front_page_image")
@Data
@Builder
public class FrontPageImage {
    /**
     * Id
     */
    @TableId
    private String id;
    /**
     * 学段
     */
    private Integer gradeLevel;
    /**
     * 学科
     */
    private Integer subjectId;
    /**
     * 名称
     */
    private String name;
    /**
     * 说明
     */
    private String description;
    /**
     * 图片Url
     */
    private String imageUrl;
    /**
     * 跳转链接
     */
    private String link;
    /**
     * 顺序
     */
    private Integer sortCode;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
}
