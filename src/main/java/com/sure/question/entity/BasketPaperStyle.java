package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * BasketPaperStyle 实体类
 * @date 2019-05-09 15:16:43
 * @version 1.0
 */
@Data
@Builder
@ApiModel
@AllArgsConstructor
@TableName(value = "basket_paper_style")
public class BasketPaperStyle implements java.io.Serializable{

	@Tolerate
	public BasketPaperStyle(){}

	private static final long serialVersionUID = 1L;
	@TableId(type = IdType.INPUT)
	private String paperId;
	private String schoolId;
	private String userId;
	private String title;
	private String subTitle;
	private String paperInfo;
	private String secretTag;
	private String studentInfo;
	private String attentions;
	private String situation;
	private String template;
	private String volume1Title;
	private String volume2Title;
	private Integer dispTitle;
	private Integer dispSubtitle;
	private Integer dispGutter;
	private Integer dispSecretTag;
	private Integer dispPaperInfo;
	private Integer dispStudentInfo;
	private Integer dispScoreBox;
	private Integer dispAttentions;
	private Integer dispSituation;
	private Integer dispVolume;
	private Long createTime;
	private Long updateTime;
	private String questionCategoryJson;
	private Integer status;
	@TableField(exist = false)
	private Integer paper_type;
	@TableField(exist = false)
	private Integer grade_id;

}