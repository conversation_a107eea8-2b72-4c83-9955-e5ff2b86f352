package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 推相似题
 */
@Data
@ApiModel("推相似题")
@TableName("examsubject_push_ques")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExamsubjectPushQues {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 题目 id
     */
    @TableField("ques_id")
    private String quesId;
    /**
     * 变式题 id
     */
    @TableField("push_id")
    private String pushId;
    /**
     * 试卷 id
     */
    @TableField("paper_id")
    private String paperId;
    /**
     * 考试科目 id
     */
    @TableField("exam_subject_id")
    private String examSubjectId;
    /**
     * 难度
     */
    @TableField("difficult")
    private Integer difficult;
    /**
     * 得分率
     */
    @TableField("score_rate")
    private Float scoreRate;
    /**
     * 知识点匹配数目
     */
    @TableField("know_match_count")
    private Integer knowMatchCount;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 是否标准推题
     */
    @TableField("is_standard")
    private Boolean isStandard;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    @TableField(exist = false)
    private Integer distance;
}
