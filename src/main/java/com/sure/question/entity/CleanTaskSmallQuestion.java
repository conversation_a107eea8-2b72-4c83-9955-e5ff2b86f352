package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("clean_task_small_question")
public class CleanTaskSmallQuestion {
    private Integer taskId;
    private String smallQuestionId;
    private String questionId;
    private String contentHtml;
    private String oldContentHtml;
    private String options;
    private String oldOptions;
    private String answerHtml;
    private String oldAnswerHtml;
    private String explanation;
    private String oldExplanation;
    private Date createTime;
}
