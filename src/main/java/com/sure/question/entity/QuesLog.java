package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("queslog")
public class QuesLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("userId")
    private String userId;

    @TableField("logType")
    private Integer logType;

    @TableField("schoolId")
    private String schoolId;

    @TableField("paperId")
    private String paperId;

    @TableField("ip")
    private String ip;

    @TableField("createTime")
    private Date createTime;

}
