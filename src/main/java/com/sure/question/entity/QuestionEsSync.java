package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("question_es_sync")
public class QuestionEsSync {
    @TableId
    private Long id;
    private Long questionId;
    private String status;
    private Date createTime;
    private Date syncTime;
    private String failReason;
    private Integer failCount;
}
