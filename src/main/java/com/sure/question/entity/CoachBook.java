package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 教辅
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "coach_book")
public class CoachBook {
    /**
     * 教辅Id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 教辅合作商
     */
    private String partner;

    /**
     * 科目Id
     */
    private Integer subjectId;

    /**
     * 学段：1-小学，2-初中，4-高中
     */
    private Integer gradeLevel;

    /**
     * 年级ID，如果没有限定年级则为0
     */
    private Integer gradeId;
    /**
     * 学年ID
     */
    private Integer semesterId;

    /**
     * 上下学期 : 1-上学期;2-下学期
     */
    private Integer term;

    /**
     * 教辅年份
     */
    private Integer year;

    /**
     * 教辅代码
     */
    private String bookCode;

    /**
     * 教辅名称，如 单元测试
     */
    private String bookName;

    /**
     * 出版社名称
     */
    private String pressName;

    /**
     * 教辅类型：1-定制教辅；2-校本教辅
     */
    private Integer bookType;

    /**
     * 学校 id
     */
    private String schoolId;

    /**
     * 封面图片
     */
    private String cover;

    /**
     * 快捷标记
     */
    private Boolean wrongQuesQuickMark;

    /**
     * 试卷标记
     */
    private Boolean wrongQuesPaperMark;

    /**
     * 错题开通价格
     */
    private BigDecimal wrongQuesPrice;

    /**
     * 排序
     */
    private Integer orderId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 科目名称
     */
    @TableField(exist = false)
    private String subjectName;

    /**
     * 年级名称
     */
    @TableField(exist = false)
    private String gradeName;

    /**
     * 是否文轩教辅
     */
    public boolean isWinShare() {
        return "文轩".equals(partner);
    }
}
