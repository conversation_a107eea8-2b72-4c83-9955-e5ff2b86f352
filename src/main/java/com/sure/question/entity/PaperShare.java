package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @data 2021/3/3 9:40
 * <AUTHOR>
 */
@TableName("paper_share")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PaperShare {
    @TableId(type = IdType.INPUT)
    private Integer id;
    @TableField("paper_id")
    private String paperId;
    @TableField("user_id")
    private String userId;
    @TableField("share_id")
    private String shareId;
    @TableField("create_time")
    private Date createTime;
    @TableField("share_delete")
    private Boolean shareDelete;
}
