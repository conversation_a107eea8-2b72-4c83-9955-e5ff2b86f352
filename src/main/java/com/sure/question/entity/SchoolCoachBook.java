package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 学校开通的教辅
 */
@TableName(value = "school_coach_book")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SchoolCoachBook {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 学校 id
     */
    private String schoolId;

    /**
     * 教辅id
     */
    private Integer coachBookId;

    /**
     * 学年 id
     */
    private Integer semesterId;

    /**
     * 学期
     */
    private Integer term;

    /**
     * 学段 id
     */
    private Integer gradeLevel;
    /**
     * 年级 id
     */
    private Integer gradeId;

    /**
     * 学科 id
     */
    private Integer subjectId;

    /**
     * 快捷标记
     */
    private Boolean wrongQuesQuickMark;

    /**
     * 试卷标记
     */
    private Boolean wrongQuesPaperMark;

    /**
     * 是否编辑过标记方式
     */
    private Boolean isMarkWayEdited;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改者
     */
    private String updateBy;
    /**
     * 修改时间
     */
    private Date updateTime;

}
