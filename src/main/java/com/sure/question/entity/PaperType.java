package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PaperType 实体类
 *
 * @version 1.0
 * @date 2019-05-09 15:16:43
 */
@Data
@Builder
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
@TableName("paper_type")
public class PaperType {
    private Integer gradeLevel;
    private Integer paperTypeId;
    private String paperTypeName;
    private Boolean isSpecial;
    private Integer orderId;
}