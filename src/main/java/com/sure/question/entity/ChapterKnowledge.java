package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ChapterKnowledge 实体类
 *
 * @version 1.0
 * @date 2019-05-09 15:16:43
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("chapter_knowledge")
public class ChapterKnowledge {

    private Integer chapterId;
    private Integer knowledgeId;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChapterKnowledge that = (ChapterKnowledge) o;
        return (chapterId.equals(that.chapterId) &&
                knowledgeId.equals(that.knowledgeId));
    }

    @Override
    public int hashCode() {
        return chapterId.hashCode();
    }
}