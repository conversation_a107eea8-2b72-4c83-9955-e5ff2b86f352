package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * QuestionKnowledge 实体类
 *
 * @version 1.0
 * @date 2019-05-09 15:16:43
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("question_knowledge")
public class QuestionKnowledge {
    @TableId(type = IdType.INPUT)
    private Long id;
    private String questionId;
    private Integer questionNo;
    private Integer knowledgeId;
    private Long updateTime;


}