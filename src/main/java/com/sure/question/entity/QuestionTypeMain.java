package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * QuestionTypeMain 实体类
 *
 * @version 1.0
 * @date 2019-04-25 09:20:08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("ques_type_main")
public class QuestionTypeMain {
    @TableId(type = IdType.INPUT)
    private Integer id;
    private Integer subjectId;
    private Integer gradeLevel;
    private String questionTypeName;
    private Integer categoryId;
    private Integer orderId;
    private String groupName;
    private String extra;
}