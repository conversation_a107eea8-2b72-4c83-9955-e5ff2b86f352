package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * PaperQuestion 实体类
 *
 * @version 1.0
 * @date 2019-04-23 08:54:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("question")
public class Question {
    @TableId(type = IdType.INPUT)
    private String id;
    @TableField("school_id")
    private String schoolId;
    @TableField("paper_id")
    private String paperId;
    @TableField("grade_level")
    private Integer gradeLevel;
    @TableField("subject_id")
    private Integer subjectId;
    @TableField("grade_id")
    private Integer gradeId;

    /**
     * 题号
     */
    @TableField("start_num")
    private Integer startNum;
    @TableField("end_num")
    private Integer endNum;

    /**
     * 题型
     */
    @TableField("question_type_id")
    private Integer questionTypeId;
    private String selector;
    /**
     * 题目未编辑html
     */
    private String divideHtml;
    /**
     * 题干内容
     */
    private String contentHtml;
    private Integer optionCount;
    private String answerHtml;
    private String isAccurateAnswer;
    private String materialHtml;
    private Integer difficult;
    private Float score;
    private String showStatus;
    private Long updateTime;
    private String explanation;
    private String solution;
    private String comment;
    private Integer version;
    private String fromQuestionId;
    private Integer hasChild;
    private Integer year;
    @TableField(exist = false)
    private Float curScore;
    private String options;
    @TableField(exist = false)
    private Integer orderId;
    @TableField(exist = false)
    private Long createTime;
    @TableField(exist = false)
    private List<SmallQuestion> smallQuestions;
    private Integer status;
    private Integer bank;
    @TableField("know_count")
    private Integer knowCount;// 知识点总数
    @TableField("es_sync")
    private Integer esSync;
    @TableField("region_id")
    private String regionId;
    @TableField("region_check")
    private Boolean regionCheck;
    @TableField("use_count")
    private Integer useCount;
    @TableField("use_count_add")
    private Integer useCountAdd;
    @TableField("answer_count")
    private Integer answerCount;
    @TableField("score_rate")
    private Float scoreRate;
}