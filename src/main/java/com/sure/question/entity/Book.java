package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Book 实体类
 *
 * @version 1.0
 * @date 2019-04-22 17:45:14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class Book {

    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "主键ID")
    private Integer id;
    @ApiModelProperty(value = "学段ID", example = "2")
    private Integer gradeLevel;
    @ApiModelProperty(value = "学科ID", example = "7")
    private Integer subjectId;
    @ApiModelProperty(value = "教材版本ID", example = "10")
    private Integer categoryId;
    @ApiModelProperty(value = "教材名称", example = "七年级上")
    private String bookName;
    @ApiModelProperty(value = "年级ID", example = "7")
    private Integer gradeId;
    @ApiModelProperty(value = "学期", example = "7")
    private Integer term;
    @ApiModelProperty(value = "排序", example = "3")
    private Integer orderId;
    /**
     * 是否启用教材选题
     */
    private Boolean enabled;
    /**
     * 封面图片
     */
    private String cover;
}