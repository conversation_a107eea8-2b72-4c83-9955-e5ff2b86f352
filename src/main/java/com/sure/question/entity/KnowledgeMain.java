package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * KnowledgeMain 实体类
 *
 * @version 1.0
 * @date 2019-05-09 15:16:43
 */
@Data
@Builder
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
@TableName("knowledge_main")
public class KnowledgeMain {

    @TableId(type = IdType.INPUT)
    private Integer id;
    private Integer pid;
    private Integer gradeLevel;
    private Integer subjectId;
    private String knowledgeName;
    private Integer level;
    private Integer orderId;
    private String status;

}