package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("listening")
public class Listening {
    /**
     * 自增Id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 题目Id
     */
    private String questionId;

    /**
     * 小题Id
     */
    private String smallQuestionId;

    /**
     * 文本材料
     */
    private String material;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 重复次数
     */
    private Integer repeatCount;

    /**
     * 音频来源
     */
    private Integer audioSource;

    /**
     * 语速
     */
    private Integer speechRate;

    /**
     * 性别
     */
    private String voiceGender;

    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 创建时间
     */
    private Date createTime;
}
