package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("school_paper")
public class SchoolPaper {
    // 自增id
    @TableId(type = IdType.INPUT)
    private Integer id;

    // 学校id
    @TableField("school_id")
    private String schoolId;

    // 试卷id
    @TableField("paper_id")
    private String paperId;

    // 来源
    @TableField("source")
    private Integer source;

    // 是否公开
    @TableField("is_public")
    private Boolean isPublic;

    // 创建者
    @TableField("create_id")
    private String createId;

    // 创建时间
    @TableField("create_time")
    private Date createTime;

    // 是否删除
    @TableField("is_delete")
    private Boolean isDelete;

    // 删除者
    @TableField("delete_id")
    private String deleteId;

    // 删除时间
    @TableField("delete_time")
    private Date deleteTime;

    // 浏览次数
    @TableField("browse_count")
    private Integer browseCount;

    // 下载次数
    @TableField("download_count")
    private Integer downloadCount;
}
