package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SmallQuestion 实体类
 *
 * @version 1.0
 * @date 2019-05-09 15:16:43
 */
@Data
@Builder
@TableName("small_question")
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class SmallQuestion {
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 题 id
     */
    private String questionId;
    /**
     * 小题题号
     */
    private Integer questionNo;
    private Integer optionCount;
    /**
     * 题目内容
     */
    private String contentHtml;
    /**
     * 答案
     */
    private String answerHtml;
    private Float score;
    private Long updateTime;
    private String options;
    private String explanation;
    @TableField("question_type_id")
    private Integer questionTypeId;
    private Integer status;
    private String expla;
    private String knowledgeName;
}