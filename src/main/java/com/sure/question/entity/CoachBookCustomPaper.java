package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <h1>教辅定制试卷</h1>
 *
 * <AUTHOR>
 * @date 2023/6/8
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("coach_book_custom_paper")
public class CoachBookCustomPaper {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 试卷id
     */
    private String paperId;
    /**
     * 教辅id
     */
    private Integer coachBookId;
    /**
     * 排序码
     */
    private Integer sortCode;
    /**
     * 开放显示时间
     */
    private Date openTime;

    /**
     * PDF状态：0-待生成；1-生成中；2-生成成功；3-生成失败
     */
    private Integer pdfStatus;

    /**
     * PDF文件路径
     */
    private String pdfPath;

    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
