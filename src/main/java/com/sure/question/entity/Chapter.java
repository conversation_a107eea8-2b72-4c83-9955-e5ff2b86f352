package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Chapter 实体类
 *
 * @version 1.0
 * @date 2019-05-09 15:16:43
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class Chapter {

    @TableId(type = IdType.INPUT)
    private Integer id;
    @ApiModelProperty("书本ID")
    private Integer bookId;
    @ApiModelProperty("学段ID")
    private Integer gradeLevel;
    @ApiModelProperty("学科ID")
    private Integer subjectId;
    @ApiModelProperty("年级ID")
    private Integer gradeId;
    @ApiModelProperty("章节名称")
    private String chapterName;
    @ApiModelProperty("章节父节点ID")
    private Integer pid;
    @ApiModelProperty("目录等级")
    private Integer level;
    @ApiModelProperty("状态:启用/禁用")
    private String status;
    @ApiModelProperty("排序")
    private Integer orderId;

}