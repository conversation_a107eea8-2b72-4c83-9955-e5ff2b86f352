package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 首页试卷
 */
@TableName(value = "front_page_paper")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FrontPagePaper {
    /**
     * Id
     */
    @TableId
    private String id;
    /**
     * 学段
     */
    private Integer gradeLevel;
    /**
     * 学科
     */
    private Integer subjectId;
    /**
     * 合集名称
     */
    private String collectionName;
    /**
     * 合集排序
     */
    private Integer collectionSortCode;
    /**
     * 试卷Id
     */
    private String paperId;
    /**
     * 试卷排序
     */
    private Integer paperSortCode;
    /**
     * 创建者
     */
    private String createBy;
    /**
     * 创建时间
     */
    private Date createTime;
}
