package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("clean_task_question")
public class CleanTaskQuestion {
    private Integer taskId;
    private String questionId;
    private String paperId;
    private String contentHtml;
    private String oldContentHtml;
    private Date createTime;
    private Integer confirmStatus;
    private Date confirmTime;
    private Boolean isUpdated;
    private Date updateTime;
}
