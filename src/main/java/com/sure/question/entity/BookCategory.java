package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 教材版本
 */
@Data
@TableName("book_category")
public class BookCategory {
    /**
     * 版本（含修订版）Id
     */
    @TableId(type = IdType.INPUT)
    private Integer categoryId;
    /**
     * 版本（含修订版）名称
     */
    private String categoryName;
    /**
     * 学段Id
     */
    private Integer gradeLevel;
    /**
     * 学科Id
     */
    private Integer subjectId;
    /**
     * 出版社Id
     */
    private Integer publisherId;
    /**
     * 出版社名称
     */
    private String publisherName;
    /**
     * 版本Id
     */
    private Integer editionId;
    /**
     * 版本名称
     */
    private String editionName;
    /**
     * 修订年份
     */
    private Integer revisionYear;
    /**
     * 排序码
     */
    private Integer sortCode;
}
