package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * FavoritePaper 实体类
 *
 * @version 1.0
 * @date 2019-05-09 15:16:43
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("favorite_paper")
public class FavoritePaper {

    @TableId(type = IdType.INPUT)
    private Long id;
    private String schoolId;
    private String userId;
    private Long paperId;
    private Long createTime;

}