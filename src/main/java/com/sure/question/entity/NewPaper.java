package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("new_paper")
public class NewPaper {

    private String paperId;
    private String paperName;
    private String xId;
    private String json;
    private Date time;
    private Integer gradeLevel;
    private Integer subject;
}
