package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Region 实体类
 *
 * @version 1.0
 * @date 2019-04-19 11:30:53
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Region {
    @TableId(type = IdType.INPUT)
    private Long id;
    private Long pid;
    private String level;
    private Integer zipCode;
    private String cityCode;
    private String name;
    private String shortName;
    private String mergerName;
    private String pinyin;
}