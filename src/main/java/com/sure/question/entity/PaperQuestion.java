package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PaperQuestion 实体类
 *
 * @version 1.0
 * @date 2019-05-09 15:16:43
 */
@Data
@Builder
@TableName("paper_question")
@ApiModel
@AllArgsConstructor
@NoArgsConstructor
public class PaperQuestion {
    @TableId()
    private Long id;
    @ApiModelProperty(value = "试卷ID")
    private String paperId;
    @ApiModelProperty(value = "题目ID")
    private String questionId;
    @ApiModelProperty(value = "题目分数")
    private Float score;
    @ApiModelProperty(value = "题顺序")
    private Integer orderId;
    @ApiModelProperty(value = "创建时间")
    private Long createTime;
    private Integer status;
}