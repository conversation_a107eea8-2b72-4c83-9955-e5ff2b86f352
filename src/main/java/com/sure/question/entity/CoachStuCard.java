package com.sure.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@TableName(value = "coach_stu_card")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CoachStuCard implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 学科 id
     */
    private Integer subjectId;

    /**
     * 教辅 id
     */
    private Integer coachBookId;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 学生 id
     */
    private String studentId;

    /**
     * 家长 id
     */
    private String parentid;

    /**
     * 学校 id
     */
    private String schoolId;

    /**
     * 状态：0-初始，未激活；1-已激活；2-禁用。
     */
    private Integer status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 激活时间
     */
    private Date activeTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}