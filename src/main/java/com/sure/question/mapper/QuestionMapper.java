package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.Question;
import com.sure.question.vo.dataVo.StrStrVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Mapper
public interface QuestionMapper extends BaseMapper<Question> {

    void shareToBank(@Param("pid") String paperId);

    void updateEsSync(@Param("list") List<String> unSync, @Param("i") Integer i);

    void useCountIncr(@Param("questionIds") @NotEmpty List<String> questionIds);

    List<StrStrVo> listQuesTypeName(@Param("list") List<String> questionIds);

    /**
     * 查收藏题目
     */
    List<String> selectFavoriteQuestionIds(@Param("userId") String userId, @Param("stage") Integer stage, @Param("subject") Integer subject,
                                           @Param("diff") Integer difficulty, @Param("ques_type") Integer ques_type);

    /**
     * 查校本题库题目
     */
    List<String> selectSchoolPaperQuestionIds(@Param("schId") String schoolId, @Param("stage") Integer stage, @Param("subject") Integer subject,
                                              @Param("source") Integer source, @Param("region") String region, @Param("year") Integer year,
                                              @Param("diff") Integer difficulty, @Param("ques_type") Integer ques_type,
                                              @Param("chaps") List<Integer> chap, @Param("knows") List<Integer> know);

    /**
     * 查定制教辅题目
     */
    List<String> selectSchoolCoachBookQuestionIds(@Param("schId") String schoolId, @Param("stage") Integer stage, @Param("subject") Integer subject,
                                                  @Param("semesterId") Integer semesterId, @Param("term") Integer term, @Param("gradeId") Integer gradeId,
                                                  @Param("diff") Integer difficulty, @Param("ques_type") Integer ques_type,
                                                  @Param("chaps") List<Integer> chap, @Param("knows") List<Integer> know);
}
