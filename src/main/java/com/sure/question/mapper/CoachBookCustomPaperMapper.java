package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.CoachBookCustomPaper;
import com.sure.question.vo.dataVo.IntIntVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/8
 */
@Mapper
public interface CoachBookCustomPaperMapper extends BaseMapper<CoachBookCustomPaper> {

    void updateSortCodeBatch(@Param("batch") List<CoachBookCustomPaper> coachBookCustomPapers);

    void deleteBatch(@Param("batch") List<String> paperIds, @Param("coachBookId") int coachBookId);

    @Select("SELECT coach_book_id FROM coach_book_custom_paper WHERE paper_id = #{paperId}")
    List<Integer> selectCoachBookIdsByPaperId(@Param("paperId") String paperId);

    List<IntIntVo> selectBookIdAndPaperCount(@Param("coachBookIds") List<Integer> coachBookIds);
}
