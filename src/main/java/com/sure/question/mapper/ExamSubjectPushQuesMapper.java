package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.dto.coachBook.StandardQuestionCountDto;
import com.sure.question.entity.ExamsubjectPushQues;
import com.sure.question.vo.dataVo.KeyValueInt;
import com.sure.question.vo.dataVo.StrIntVo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

@Mapper
public interface ExamSubjectPushQuesMapper extends BaseMapper<ExamsubjectPushQues> {

    List<ExamsubjectPushQues> getPushQues(@Param("examSubjectId") String examSubjectId, @Param("quesIds") List<String> quesIds, @Param("pushNum") Integer pushNum);

    Integer getWrongQuesTrainCount(@Param("quesIds") List<String> quesIds, @Param("examSubjectId") String examSubjectId);

    @Delete("DELETE FROM examsubject_push_ques WHERE paper_id = #{paperId} AND exam_subject_id = #{examSubjectId}")
    Integer deleteByPaperIdAndExamSubjectId(@Param("paperId") String paperId, @Param("examSubjectId") String examSubjectId);

    @Select("SELECT DISTINCT exam_subject_id FROM examsubject_push_ques WHERE paper_id = #{paperId}")
    List<String> selectExamSubjectIdByPaperId(@Param("paperId") String paperId);

    List<StrIntVo> getPaperIdAndStandardCount(@Param("paperIds") List<String> paperIds);

    List<String> selectStandardPushQuesPaperIds(@Param("list") List<String> paperIds);

    /**
     * 查试卷各题的标准变式题数量
     */
    List<StandardQuestionCountDto> selectEachQuestionStandardQuestionCount(@Param("paperIds") Collection<String> paperIds);

    List<KeyValueInt> selectQuesionIdAndPushCountList(@Param("paperId")String paperId, @Param("examSubjectId") String examSubjectId);
}
