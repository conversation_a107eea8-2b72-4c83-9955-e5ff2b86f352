package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sure.question.entity.PaperMain;
import com.sure.question.vo.baseVo.GradeLevelSubjectVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

@Mapper
public interface PaperMainMapper extends BaseMapper<PaperMain> {
    /**
     * 查一页收藏的试卷
     */
    IPage<PaperMain> selectFavoritePaperPage(IPage<?> page, @Param("userId") String userId,
                                             @Param("gradeLevel") int gradeLevel, @Param("subjectId") int subjectId,
                                             @Param("paperTypeId") Integer paperTypeId, @Param("gradeId") Integer gradeId, @Param("term") Integer term);

    @Select("SELECT COUNT(*) FROM paper_main WHERE editor_id = #{id} AND subject_id = #{subject} AND grade_level =#{stage} AND `status` =0")
    Integer getEditorAllotPaperCount(@Param("id") String id, @Param("subject") Integer subject, @Param("stage") Integer stage);

    /**
     * 查个人可绑定考试科目的试卷
     */
    List<PaperMain> selectBangPaper(@Param("subjectId") Integer subjectId,
                                    @Param("stage") Integer stage,
                                    @Param("gradeId") Integer gradeId,
                                    @Param("term") Integer term,
                                    @Param("userId") String userId);

    /**
     * 更新试卷结构
     */
    int updatePaperStructure(@Param("paperId") String paperId, @Param("paperStructure") String paperStructure);

    @Select("SELECT COUNT(*) FROM (\n" +
            "SELECT id FROM paper_main WHERE id = #{pid} AND (paper_bank=3 or user_id in (#{uid}))\n" +
            "UNION\n" +
            "SELECT paper_id FROM paper_share WHERE paper_id = #{pid} AND (share_id = #{uid} OR user_id = #{uid})\n" +
            "UNION\n" +
            "SELECT paper_id FROM school_paper WHERE paper_id = #{pid} AND school_id = #{sch} )e")
    Boolean canSeePaper(@Param("uid") String userId, @Param("pid") String paperId, @Param("sch") String schoolId);

    @Update("UPDATE paper_main SET browse_count =browse_count+1 WHERE id = #{pid}")
    void paperViewIncr(@Param("pid") String paperId);

    @Update("UPDATE paper_main SET download_count =download_count+1 WHERE id = #{pid}")
    void paperDownIncr(@Param("pid") String paperId);

    Set<String> queryUsedQuesIds(@Param("userId") String userId, @Param("quesIds") List<String> quesIds);

    List<GradeLevelSubjectVo> selectPublicBankAllGradeLevelSubjects();

    void increaseFeedbackSheetDownloadCount(@Param("paperIds") List<String> paperIds);

    void increaseAnswerSheetDownloadCount(@Param("paperIds") List<String> paperIds);
}
