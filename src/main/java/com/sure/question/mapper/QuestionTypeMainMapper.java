package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.QuestionTypeMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QuestionTypeMainMapper extends BaseMapper<QuestionTypeMain> {
    /**
     * 查指定学段学科所有题型
     */
    List<QuestionTypeMain> selectGradeLevelSubjectQuestionTypes(@Param("gradeLevelId") int gradeLevelId, @Param("subjectId") int subjectId);

    /**
     * 查最大Id
     */
    Integer selectMaxId();
}
