package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.FavoriteQues;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Set;

@Mapper
public interface FavoriteQuesMapper extends BaseMapper<FavoriteQues> {
    /**
     * 查用户收藏试题Id
     * @param userId 用户Id
     * @param questionIds 非空则查指定题目中收藏题目，否则查所有收藏题目
     */
    Set<String> selectUserFavoriteQuestionIds(@Param("userId") String userId, @Param("questionIds") Collection<String> questionIds);
}
