package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.ChapterKnowledge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Mapper
public interface ChapterKnowledgeMapper extends BaseMapper<ChapterKnowledge> {
    /**
     * 查一批章节对应的知识点
     */
    List<Integer> selectChapterKnowledgeIds(@Param("chapterIds") Collection<Integer> chapterIds);

    /**
     * 删除一条章节知识点关联数据
     */
    int deleteByChapterIdAndKnowledgeId(@Param("chapterId") Integer chapterId, @Param("knowledgeId") Integer knowledgeId);

    /**
     * 批量插入
     */
    int insertList(@Param("list") Collection<ChapterKnowledge> list);

    /**
     * 插入chapter_knowledge表操作记录
     */
    int insertChapterKnowledgeOperationLog(@Param("operationId") long operationId,
                                           @Param("operationTime") Date operationTime,
                                           @Param("operationUser") String operationUser,
                                           @Param("operationType") String operationType,
                                           @Param("entities") List<ChapterKnowledge> entities);
}
