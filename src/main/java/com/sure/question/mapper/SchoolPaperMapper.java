package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.question.entity.SchoolPaper;
import com.sure.question.vo.paperVo.SchoolPaperVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface SchoolPaperMapper extends BaseMapper<SchoolPaper> {
    int updateToDeleted(@Param("paperIds") List<String> paperIds, @Param("schoolId") String schoolId, @Param("userId") String userId);

    int updateToUnDeleted(@Param("paperIds") List<String> paperIds, @Param("schoolId") String schoolId);

    int updateIsPublicBatch(@Param("paperIds") List<String> paperIds, @Param("schoolId") String schoolId, @Param("isPublic") boolean isPublic);

    IPage<SchoolPaperVo> getShareRecords(Page<SchoolPaperVo> mainPage,
                                         @Param("userId") String userId,
                                         @Param("schoolId") String schoolId,
                                         @Param("gradeLevel") Integer gradeLevel,
                                         @Param("gradeId") Integer gradeId,
                                         @Param("term") Integer term,
                                         @Param("subjectId") Integer subjectId,
                                         @Param("paperTypeId") Integer paperTypeId,
                                         @Param("year") Integer year,
                                         @Param("regionId") String regionId,
                                         @Param("bookType") String bookType,
                                         @Param("flagCoachBookId") Integer flagCoachBookId,
                                         @Param("paperGroupId") Integer paperGroupId);

    IPage<SchoolPaperVo> getSchoolPapers(Page<SchoolPaperVo> mainPage,
                                         @Param("schoolId") String schoolId,
                                         @Param("gradeLevel") Integer gradeLevel,
                                         @Param("gradeId") Integer gradeId,
                                         @Param("term") Integer term,
                                         @Param("subjectId") Integer subjectId,
                                         @Param("paperTypeId") Integer paperTypeId,
                                         @Param("year") Integer year,
                                         @Param("regionId") String regionId);

    @Update("UPDATE school_paper SET browse_count =browse_count+1 WHERE id = #{id}")
    void paperViewIncr(@Param("id") Integer paperShareId);

    @Update("UPDATE school_paper SET download_count =download_count+1 WHERE id = #{id}")
    void paperDownIncr(@Param("id") Integer paperShareId);

}
