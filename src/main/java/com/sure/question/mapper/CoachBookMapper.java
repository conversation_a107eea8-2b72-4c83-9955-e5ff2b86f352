package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.CoachBook;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CoachBookMapper extends BaseMapper<CoachBook> {

    List<CoachBook> querySchoolCoachBooks(@Param("schoolId") String schoolId, @Param("gradeId") Integer gradeId,
                                          @Param("semesterId") Integer semesterId, @Param("term") Integer term);

}
