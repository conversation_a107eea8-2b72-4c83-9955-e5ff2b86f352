package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.Region;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface RegionMapper extends BaseMapper<Region>{
    /**
     * 查一批区域Id及其所有后代区域Id
     */
    List<Long> selectSelfAndDescendantIds(@Param("regionIds") Collection<Long> regionIds);
}
