package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.QuestionEsSync;
import com.sure.question.vo.dataVo.StrStrVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface QuestionEsSyncMapper extends BaseMapper<QuestionEsSync> {
    /**
     * 批量插入待同步题目
     */
    int insertPendingQuestionsBatch(@Param("questionIds") Collection<String> questionIds);

    /**
     * 将同步超时的改为待同步
     */
    int updateTimeoutToPending(@Param("intervalMinutes") int intervalMinutes);

    /**
     * 查一批需要同步的记录
     */
    List<QuestionEsSync> selectNeedSyncBatch(@Param("batchSize") int batchSize);

    /**
     * 批量更新状态为同步中
     */
    int updateSyncInProgressBatch(@Param("ids") Collection<Long> ids);

    /**
     * 批量更新状态为失败，失败原因相同
     */
    int updateSyncFailedBatchSameReason(@Param("ids") Collection<Long> ids, @Param("reason") String reason);

    /**
     * 批量更新状态为失败，失败原因不同
     */
    int updateSyncFailedBatch(@Param("list") Collection<StrStrVo> list);

    /**
     * 批量更新状态为同步成功
     */
    int updateSyncSucceededBatch(@Param("ids") Collection<Long> ids);
}
