package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.CoachStuCard;
import com.sure.question.vo.coachstucard.*;
import com.sure.question.vo.dataVo.IntIntVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Mapper
public interface CoachStuCardMapper extends BaseMapper<CoachStuCard> {

    List<Integer> lstStudentActivedBookId(@Param("schoolId") String schoolId, @Param("studentId") String studentId, @Param("semesterId") Integer semesterId, @Param("term") Integer term);


    List<StudentSubjectVo> queryActiveStudentIds(@Param("schoolId") String schoolId, @Param("semesterId") Integer semesterId, @Param("term") Integer term,
                                                 @Param("subjectId") Integer subjectId, @Param("studentIds") List<String> studentIds);

    List<CardInfoVo> listCard(@Param("coachBookId") Integer coachBookId,
                              @Param("bookKeyword") String bookKeyword,
                              @Param("subjectId") Integer subjectId,
                              @Param("gradeLevel") Integer gradeLevel,
                              @Param("gradeId") Integer gradeId,
                              @Param("semesterId") Integer semesterId,
                              @Param("term") Integer term,
                              @Param("cardNo") String cardNo,
                              @Param("status") Integer status,
                              @Param("beginDate") Date beginDate,
                              @Param("endDate") Date endDate,
                              @Param("schoolIds") Collection<String> schoolIds,
                              @Param("studentIds") Set<String> studentIds);


    List<CoachStatVo> queryCoachStatInfo(@Param("bookIds") List<Integer> bookIds);

    /**
     * 激活码统计（按教辅）
     */
    List<ActiveStatVo> listCoachBookActiveStat(@Param("schoolIds") Collection<String> schoolIds,
                                                    @Param("semesterId") Integer semesterId,
                                                    @Param("term") Integer term,
                                                    @Param("gradeId") Integer gradeId,
                                                    @Param("subjectId") Integer subjectId,
                                                    @Param("bookKeyword") String bookKeyword);

    /**
     * 学情分析资源包激活数量统计
     */
    CountStatVo countStatCommon(@Param("schoolIds") Collection<String> schoolIds,
                                @Param("semesterId") Integer semesterId,
                                @Param("term") Integer term,
                                @Param("gradeId") Integer gradeId,
                                @Param("subjectId") Integer subjectId,
                                @Param("bookKeyword") String bookKeyword);

    List<IntIntVo> selectGroupByBookIdCount(@Param("bookIds") List<Integer> bookIds);

    void addCustomCardBatch(@Param("customCardVos") List<CustomCardVo> customCardVos);
}
