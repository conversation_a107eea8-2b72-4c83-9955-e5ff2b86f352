package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.question.entity.SchoolCoachBook;
import com.sure.question.vo.coachBook.BookSchoolVo;
import com.sure.question.vo.coachstucard.CoachSchoolStatVo;
import com.sure.question.vo.dataVo.IntIntVo;
import com.sure.question.vo.paperVo.SchoolCoachPaperVo;
import com.sure.question.vo.schoolCoachBook.BookInfo;
import com.sure.question.vo.schoolCoachBook.QueryBookVo;
import com.sure.question.vo.schoolCoachBook.QueryPaperVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

@Mapper
public interface SchoolCoachBookMapper extends BaseMapper<SchoolCoachBook> {
    /**
     * 判断是否存在指定的学校教辅
     *
     * @param schoolId
     * @param coachBookId
     * @return
     */
    @Select("SELECT EXISTS( SELECT 1 FROM school_coach_book WHERE school_id = #{schoolId} AND coach_book_id = #{coachBookId} LIMIT 1)")
    boolean existsBySchoolIdAndCoachBookId(@Param("schoolId") String schoolId, @Param("coachBookId") int coachBookId);

    /**
     * 查询学校的定制教辅ID
     *
     * @param schoolId     学校ID
     * @param coachBookIds 可能的教辅ID列表
     * @return 学校的定制教辅ID列表
     */
    List<Integer> querySchoolCoachBookIds(@Param("schoolId") String schoolId, @Param("coachBookIds") List<Integer> coachBookIds);

    /**
     * 分页查询学校的定制教辅
     */
    Page<BookInfo> getBookPage(@Param("page") IPage<BookInfo> page, @Param("req") QueryBookVo queryVo, @Param("bookType") Integer bookType);

    /**
     * 分页查询学校的定制试卷
     */
    Page<SchoolCoachPaperVo> getPaperPage(@Param("page") Page<SchoolCoachPaperVo> page, @Param("schoolId") String schoolId, @Param("req") QueryPaperVo queryVo, @Param("bookType") Integer bookType);

    SchoolCoachPaperVo getSimplePaper(@Param("schoolId") String schoolId, @Param("semesterId") Integer semesterId, @Param("paperId") String paperId);

    @Select("SELECT DISTINCT school_id FROM school_coach_book WHERE coach_book_id = #{bookId}")
    List<String> selectSchoolIdByBookId(@Param("bookId") Integer bookId);

    Page<BookSchoolVo> getSchoolPageByBookId(@Param("page") Page<BookSchoolVo> page, @Param("bookId") Integer bookId);

    Page<BookSchoolVo> getSchoolPageByBookIdAndSchoolIds(@Param("page") Page<BookSchoolVo> page, @Param("bookId") Integer bookId, @Param("schoolIds") Collection<String> schoolIds);

    List<IntIntVo> getBookIdAndSchoolCount(@Param("ids") List<Integer> coachBookIds);

    /**
     * 查询学生在指定学年学期激活的教辅ids
     */
    List<Integer> queryStudentCoachBookIds(@Param("schoolId") String schoolId, @Param("semesterId") Integer schoolCurrentSemester,
                                           @Param("term") Integer schoolCurrentTerm, @Param("subjectId") Integer subjectId,
                                           @Param("studentId") String studentId);

    /**
     * 激活码统计（按学校教辅）
     */
    List<CoachSchoolStatVo> listCoachSchoolActiveStat(@Param("schoolIds") Collection<String> schoolIds, @Param("semesterId") Integer semesterId, @Param("term") Integer term, @Param("gradeId") Integer gradeId, @Param("subjectId") Integer subjectId, @Param("bookKeyword") String bookKeyword);

    Integer queryLastPaperId(@Param("schoolId") String schoolId, @Param("semesterId") Integer semesterId, @Param("term") Integer term, @Param("paperId") String paperId);

    /**
     * 查询学校是否定制了指定教辅试卷
     */
    boolean selectExistsSchoolCoachBookPaper(@Param("schoolId") String schoolId, @Param("paperId") String paperId);

    /**
     * 查询学校是否定制了指定教辅练习卷
     */
    boolean selectExistsSchoolCoachBookCustomPaper(@Param("schoolId") String schoolId, @Param("paperId") String paperId);
}




