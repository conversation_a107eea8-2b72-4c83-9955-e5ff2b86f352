package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.KnowledgeMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Mapper
public interface KnowledgeMainMapper extends BaseMapper<KnowledgeMain> {
    /**
     * 批量插入
     */
    Integer insertList(@Param("os") List<KnowledgeMain> knowledgeMains);

    /**
     * 根据名称查Id，重名的只返回任意一个
     */
    Integer selectIdByName(@Param("stage") Integer stageId, @Param("subjectId") Integer subjectId, @Param("name") String name);

    /**
     * 查一批知识点Id及其所有后代知识点Id
     */
    List<Integer> selectSelfAndDescendantIds(@Param("knowledgeIds") Collection<Integer> knowledgeIds);

    /**
     * 插入knowledge_main表操作记录
     */
    int insertKnowledgeOperationLog(@Param("operationId") long operationId,
                                    @Param("operationTime") Date operationTime,
                                    @Param("operationUser") String operationUser,
                                    @Param("operationType") String operationType,
                                    @Param("entities") List<KnowledgeMain> entities);

}
