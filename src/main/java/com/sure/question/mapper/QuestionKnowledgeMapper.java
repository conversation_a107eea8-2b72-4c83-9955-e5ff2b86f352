package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.dto.question.SmallQuestionKnowledgeDto;
import com.sure.question.entity.QuestionKnowledge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Mapper
public interface QuestionKnowledgeMapper extends BaseMapper<QuestionKnowledge> {

    /**
     * 查一批小题的知识点
     */
    List<SmallQuestionKnowledgeDto> selectSmallQuestionsKnowledge(@Param("smallQuestionIds") Collection<String> smallQuestionIds);

    /**
     * 删除一批小题的知识点
     */
    int deleteBySmallQuestionIds(@Param("smallQuestionIds") Collection<String> smallQuestionIds);

    /**
     * 指定知识点和一批小题Id删除
     */
    int deleteByKnowledgeIdAndSmallQuestionIds(@Param("knowledgeId") Integer knowledgeId, @Param("smallQuestionIds") Collection<String> smallQuestionIds);

    /**
     * 批量插入
     */
    int insertList(@Param("entities") Collection<QuestionKnowledge> entities);

    /**
     * 插入question_knowledge表操作记录
     */
    int insertQuestionKnowledgeOperationLog(@Param("operationId") long operationId,
                                            @Param("operationTime") Date operationTime,
                                            @Param("operationUser") String operationUser,
                                            @Param("operationType") String operationType,
                                            @Param("entities") Collection<QuestionKnowledge> entities);
}
