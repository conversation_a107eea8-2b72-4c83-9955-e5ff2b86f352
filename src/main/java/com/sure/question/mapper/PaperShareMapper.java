package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sure.question.entity.PaperMain;
import com.sure.question.entity.PaperShare;
import com.sure.question.vo.paperVo.SharePaperVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PaperShareMapper extends BaseMapper<PaperShare> {

    IPage<SharePaperVo> getMyShares(Page<SharePaperVo> mainPage, @Param("u") String userId, @Param("t") Integer type,
                                    @Param("g") Integer grade, @Param("y") Integer year, @Param("r") String region,
                                    @Param("sub") Integer subject, @Param("st") Integer stage,
                                    @Param("bookType") String bookType, @Param("flagCoachBookId") Integer flagCoachBookId,
                                    @Param("page") Integer page, @Param("size") Integer size);

    List<SharePaperVo> getMyShareSpecifiedPaperId(@Param("u") String userId, @Param("paperId") String paperId);

    IPage<SharePaperVo> getShareToMe(Page<SharePaperVo> mainPage, @Param("u") String userId, @Param("t") Integer type, @Param("g") Integer grade, @Param("y") Integer year,
                                     @Param("r") String region, @Param("sub") Integer subject, @Param("st") Integer stage, @Param("page") Integer page, @Param("size") Integer size,
                                     @Param("bank") Integer bank, @Param("keyword") String keyword, @Param("bookType") String bookType,
                                     @Param("flagCoachBookId") Integer flagCoachBookId, @Param("paperGroupId") Integer paperGroupId);

    /**
     * 查分享给我的可绑定考试科目的试卷
     */
    List<PaperMain> selectBangPaper(@Param("subjectId") Integer subjectId,
                                    @Param("stage") Integer stage,
                                    @Param("gradeId") Integer gradeId,
                                    @Param("term") Integer term,
                                    @Param("uid") String userId);
}
