package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.PaperStuAccess;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/07/26 10:39
 */
@Mapper
public interface PaperStuAccessMapper extends BaseMapper<PaperStuAccess> {

    /**
     * 插入记录
     */
    void insertIgnore(@Param("studentId") String studentId, @Param("parentId") String parentId,
                      @Param("paperId") String paperId, @Param("coachBookId") Integer coachBookId,
                      @Param("type") Integer type);

    /**
     * 查询特定教辅，学生已读的试卷总数
     */
    Integer selectAccessPaperCount(@Param("studentId") String studentId, @Param("parentId") String parentId, @Param("coachBookIds") List<Integer> coachBookIds);
}
