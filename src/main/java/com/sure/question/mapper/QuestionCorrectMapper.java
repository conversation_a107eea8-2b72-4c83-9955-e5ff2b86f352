package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.QuestionCorrect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface QuestionCorrectMapper extends BaseMapper<QuestionCorrect> {
    /**
     * 查报错题目总数
     */
    int selectQuestionCount(@Param("gradeLevel") Integer gradeLevel,
                            @Param("subjectId") Integer subjectId,
                            @Param("status") int status,
                            @Param("beginTime") String beginTime,
                            @Param("endTime") String endTime);

    /**
     * 查报错题目Id
     */
    List<String> selectQuestionIds(@Param("gradeLevel") Integer gradeLevel,
                                   @Param("subjectId") Integer subjectId,
                                   @Param("status") int status,
                                   @Param("beginTime") String beginTime,
                                   @Param("endTime") String endTime,
                                   @Param("offset") int offset,
                                   @Param("rowCount") int rowCount);
}
