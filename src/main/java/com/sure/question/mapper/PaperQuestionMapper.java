package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.dto.question.QuestionPublicPaperDto;
import com.sure.question.entity.PaperQuestion;
import com.sure.question.vo.EntryVo;
import com.sure.question.vo.paperVo.EditorSubmitPaperQuestionStatsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface PaperQuestionMapper extends BaseMapper<PaperQuestion> {

    /**
     * 查一批题目关联的公共库试卷
     */
    List<QuestionPublicPaperDto> selectQuestionsPublicPaper(@Param("questionIds") Collection<String> questionIds);

    /**
     * 查试卷题目Id列表
     */
    List<String> selectPaperQuestionIds(@Param("paperId") String paperId);

    void insertList(@Param("os") List<PaperQuestion> paperQuestions);

    List<EntryVo<String, Long>> selectEditorAllotViewPaperTaskList(@Param("editorIds") List<String> editorIds, @Param("gradeLevelId") Integer gradeLevelId, @Param("subjectId") Integer subjectId);

    List<EditorSubmitPaperQuestionStatsVo> selectEditorViewPaperStats(@Param("editorIds") List<String> editorIds, @Param("gradeLevelId") Integer gradeLevelId, @Param("subjectId") Integer subjectId);

    List<EditorSubmitPaperQuestionStatsVo> selectEditorMakePaperStats(@Param("editorIds") List<String> editorIds, @Param("gradeLevelId") Integer gradeLevelId, @Param("subjectId") Integer subjectId, @Param("bookType") String bookType);

    List<EditorSubmitPaperQuestionStatsVo> selectEditorUploadPaperStats(@Param("editorIds") List<String> editorIds, @Param("gradeLevelId") Integer gradeLevelId, @Param("subjectId") Integer subjectId, @Param("bookType") String bookType);

}
