package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.CoachBookPaper;
import com.sure.question.vo.dataVo.IntIntVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CoachBookPaperMapper extends BaseMapper<CoachBookPaper> {

    @Select("SELECT coach_book_id FROM coach_book_paper WHERE paper_id = #{paperId}")
    Integer selectCoachBookIdByPaperId(@Param("paperId") String paperId);

    void insertBatch(@Param("batch") List<CoachBookPaper> batch);

    void updateSortCodeBatch(@Param("batch") List<CoachBookPaper> batch);

    void deleteBatch(@Param("batch") List<String> paperIds, @Param("coachBookId") int coachBookId);

    List<IntIntVo> selectBookIdAndPaperCount(@Param("coachBookIds") List<Integer> coachBookIds, @Param("onlyOpen") Boolean onlyOpen);
}
