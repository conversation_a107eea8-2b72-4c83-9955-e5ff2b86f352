package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.CleanTaskQuestion;
import com.sure.question.vo.cleanTask.StatsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CleanTaskQuestionMapper extends BaseMapper<CleanTaskQuestion> {
    void insertBatch(@Param("entities") List<CleanTaskQuestion> entities);

    List<StatsVo> selectTaskStats(@Param("taskIds") List<Integer> taskIds);

    void updateQuestionContent(@Param("taskId") int taskId, @Param("questionId") String questionId);
}
