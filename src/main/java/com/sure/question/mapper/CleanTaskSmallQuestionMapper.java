package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.CleanTaskSmallQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CleanTaskSmallQuestionMapper extends BaseMapper<CleanTaskSmallQuestion> {
    void insertBatch(@Param("entities") List<CleanTaskSmallQuestion> entities);

    void updateSmallQuestionContent(@Param("taskId") int taskId, @Param("questionId") String questionId);
}
