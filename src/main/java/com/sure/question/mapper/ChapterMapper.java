package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.Chapter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Mapper
public interface ChapterMapper extends BaseMapper<Chapter> {
    /**
     * 查一批章节Id及其所有后代章节Id
     */
    List<Integer> selectSelfAndDescendantIds(@Param("chapterIds") Collection<Integer> chapterIds);

    /**
     * 批量插入
     */
    int insertList(@Param("entities") Collection<Chapter> entities);

    /**
     * 插入knowledge_main表操作记录
     */
    int insertChapterOperationLog(@Param("operationId") long operationId,
                                  @Param("operationTime") Date operationTime,
                                  @Param("operationUser") String operationUser,
                                  @Param("operationType") String operationType,
                                  @Param("entities") List<Chapter> entities);
}
