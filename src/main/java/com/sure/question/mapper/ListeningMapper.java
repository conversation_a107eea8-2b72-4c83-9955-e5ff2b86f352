package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.Listening;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ListeningMapper extends BaseMapper<Listening> {
    List<Listening> selectQuestionListeningForceMaster(@Param("questionId") String questionId);
}
