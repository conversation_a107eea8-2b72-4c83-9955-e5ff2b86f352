package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.QuesLog;
import com.sure.question.vo.queslog.PaperStatVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface QuesLogMapper extends BaseMapper<QuesLog> {
    List<PaperStatVo> queryStatistic(@Param("beginDate") Date beginDate, @Param("endDate") Date endDate, @Param("gradeLevel") Integer gradeLevel,
                                     @Param("subjectId") Integer subjectId, @Param("paperName") String paperName);
}
