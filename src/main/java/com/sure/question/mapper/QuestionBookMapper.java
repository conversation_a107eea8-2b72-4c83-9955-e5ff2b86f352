package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.dto.question.QuestionChapterDto;
import com.sure.question.entity.QuestionBook;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

@Mapper
public interface QuestionBookMapper extends BaseMapper<QuestionBook> {
    /**
     * 查一批题目的章节
     */
    List<QuestionChapterDto> selectQuestionsChapter(@Param("questionIds") Collection<String> questionIds);

    /**
     * 按题目Id和章节Id删除
     */
    int deleteByQuestionIdChapterId(@Param("questionId") String questionId, @Param("chapterId") int chapterId);

    /**
     * 批量插入
     */
    int insertList(@Param("entities") Collection<QuestionBook> entities);

    /**
     * 插入question_book表操作记录
     */
    int insertQuestionBookOperationLog(@Param("operationId") long operationId,
                                       @Param("operationTime") Date operationTime,
                                       @Param("operationUser") String operationUser,
                                       @Param("operationType") String operationType,
                                       @Param("entities") List<QuestionBook> entities);
}
