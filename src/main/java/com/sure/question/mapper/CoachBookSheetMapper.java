package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.CoachBookSheet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CoachBookSheetMapper extends BaseMapper<CoachBookSheet> {
    void insertBatch(@Param("batch") List<CoachBookSheet> batch);

    Integer selectMaxSortCode(@Param("coachBookId") int coachBookId);

    void updateSortCodeBatch(@Param("batch") List<CoachBookSheet> batch);
}
