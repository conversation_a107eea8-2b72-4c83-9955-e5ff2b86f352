package com.sure.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sure.question.entity.FavoritePaper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface FavoritePaperMapper extends BaseMapper<FavoritePaper> {
    /**
     * 获取一批试卷中个人收藏的试卷 ids
     */
    List<String> selectPaperIdListByUserIdAndPaperIds(@Param("paperIds") Collection<String> paperIds, @Param("userId") String userId);
}
