package com.sure.question.cache;

import com.alibaba.fastjson.JSON;
import com.sure.question.vo.Basket;
import com.sure.question.vo.baseVo.MessageVo;
import com.sure.question.vo.baseVo.RegionVo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class Cache {

    @Value("${cacheKey.basketPre}")
    private String basketPre;
    @Value("${cacheKey.baseRegion}")
    private String regionKey;
    @Value("${cacheKey.baseMessage}")
    private String messageKey;
    @Value("${cacheKey.gradeLevelSubjectKnowledgeTreePre}")
    private String gradeLevelSubjectKnowledgeTreePre;
    @Value("${cacheKey.bookChapterTreePre}")
    private String bookChapterTreePre;

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 从缓存查数据
     */
    public String redisTemplateGet(String key) {
        return (String) redisTemplate.opsForValue().get(key);
    }

    /**
     * 设置缓存数据
     */
    public void redisTemplateSet(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 清除缓存
     */
    public void redisTemplateDelete(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 获取学段学科知识点缓存key
     */
    public String getGradeLevelSubjectKnowledgeTreeKey(int gradeLevel, int subjectId) {
        return gradeLevelSubjectKnowledgeTreePre + gradeLevel + "-" + subjectId;
    }

    /**
     * 获取教材章节缓存key
     */
    public String getBookChapterTreeKey(int bookId) {
        return bookChapterTreePre + bookId;
    }

    /**
     * 获取 basket redis key
     */
    public String getBasketKey(String userId) {
        return basketPre + userId;
    }

    /**
     * 根据userId获取Basket
     */
    public Basket getCacheBasket(String userId) {
        // 获取key
        String key = getBasketKey(userId);
        Object o = redisTemplate.opsForValue().get(key);
        if (o == null) {
            return null;
        }
        // 对象转化
        return JSON.parseObject(o.toString(), Basket.class);
    }

    /**
     * 设置basket
     */
    public void setCacheBasket(String userId, Basket basket) {
        // 获取key
        String basketKey = getBasketKey(userId);
        // 设置值
        redisTemplate.opsForValue().set(basketKey, JSON.toJSONString(basket));
    }

    /**
     * 获取region缓存
     */
    public RegionVo getRegionCache() {
        String o = (String) redisTemplate.opsForValue().get(regionKey);
        if (o == null) {
            return null;
        }
        return JSON.parseObject(o, RegionVo.class);
    }

    /**
     * 设置region缓存
     */
    public void setRegionCache(RegionVo regionCache) {
        redisTemplate.opsForValue().set(regionKey, JSON.toJSONString(regionCache));
    }

    /**
     * 删除region缓存
     */
    public void delRegionCache() {
        redisTemplate.delete(regionKey);
    }

    /**
     * 获取message缓存
     */
    public MessageVo getMessageCache() {
        String o = (String) redisTemplate.opsForValue().get(messageKey);
        if (o == null) {
            return null;
        }
        return JSON.parseObject(o, MessageVo.class);
    }

    /**
     * 设置message缓存
     */
    public void setMessageCache(MessageVo messageCache) {
        redisTemplate.opsForValue().set(messageKey, JSON.toJSONString(messageCache));
    }

    /**
     * 删除message缓存
     */
    public void delMessageCache() {
        redisTemplate.delete(messageKey);
    }

}
