package com.sure.question.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ThreadPoolConfig {
    /**
     * 用于上传PDF时并行调用OCR接口
     */
    @Bean(name = "ocrRequestExecutor")
    public ThreadPoolTaskExecutor ocrRequestExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("OcrRequest-");
        executor.setKeepAliveSeconds(60);
        return executor;
    }
}
