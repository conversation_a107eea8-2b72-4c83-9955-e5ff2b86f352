package com.sure.question.config;

import com.sure.question.common.IdWorker;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class IdWorkerConfig {
    @Value("${snowflake.datacenter-id}")
    private Integer dataCenterId;
    @Value("${snowflake.worker-id}")
    private Integer workerId;

    @Bean
    public IdWorker getIdWorker() {
        return new IdWorker(workerId, dataCenterId);
    }
}
