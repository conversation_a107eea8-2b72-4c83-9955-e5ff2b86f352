package com.sure.question.config;

import cn.hutool.core.net.URLDecoder;
import com.sure.question.util.SignUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.annotation.Configuration;

import java.nio.charset.Charset;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Configuration
public class FeignConfig implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        Map<String, Collection<String>> queries = template.queries();
        Map<String, Object> map = new HashMap<>(queries.size());
        for (Map.Entry<String, Collection<String>> entry : queries.entrySet()) {
            Iterator<String> iterator = entry.getValue().iterator();
            if (iterator.hasNext()) {
                map.put(entry.getKey(), URLDecoder.decode(iterator.next(), Charset.defaultCharset()));
            }
        }
        template.header("AuthSign", SignUtil.sign(map));
    }
}
