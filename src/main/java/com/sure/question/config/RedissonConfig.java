package com.sure.question.config;

import org.redisson.Redisson;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.redisson.api.RedissonClient;

@Configuration
public class RedissonConfig {
    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private Integer port;

    @Value("${spring.redis.password}")
    private String password;

    @Bean
    public RedissonClient redissonClient() {
        // 创建Redisson配置
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://" + host + ":" + port)
                .setPassword(password)
                .setConnectionPoolSize(10)
                .setIdleConnectionTimeout(10000)
                .setConnectionMinimumIdleSize(2)
                .setConnectTimeout(2000)
                .setTimeout(3000)
                .setRetryAttempts(3)
                .setRetryInterval(2000);

        // 返回Redisson客户端实例
        return Redisson.create(config);
    }
}
