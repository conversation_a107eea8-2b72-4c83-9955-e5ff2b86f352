package com.sure.question.vo.grapeVo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@Builder
@ApiModel
@NoArgsConstructor
public class GrapePaperVo {
    private List<GrapeBigQues> blocks;
    private String city;
    private String grade;
    private String name;
    private String period;
    private String province;
    private Integer score;
    private String subject;
    private String type;
    private List<GrapeGeneral> type_score;
    private Integer to_year;
}
