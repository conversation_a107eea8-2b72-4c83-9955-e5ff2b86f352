package com.sure.question.vo.grapeVo;


import com.sure.question.util.GrapeUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
@Data
public class GrapeWebSmallQuesVo {
    private List<String> answers;
    private List<String> explanations;
    private List<List<GrapeGeneral>> knowledges;
    private List<String> solutions;
    private List<GrapeGeneral> stems;
    private List<String> types;


    public GrapeWebSmallQuesVo(List<String> answers, List<String> explanations, List<List<GrapeGeneral>> knowledges, List<String> solutions, List<GrapeGeneral> stems, List<String> types) {
        this.answers = list2Base64(answers);
        this.explanations = list2Base64(explanations);
        this.knowledges = knowledges;
        this.solutions = list2Base64(solutions);
        this.stems = stems;
        this.types = types;
    }

    public GrapeWebSmallQuesVo() {
    }

    public List<String> getAnswers() {
        return answers;
    }

    public void setAnswers(List<String> answers) {
        this.answers = list2Base64(answers);
    }

    public List<String> getExplanations() {
        return explanations;
    }

    public void setExplanations(List<String> explanations) {
        this.explanations = list2Base64(explanations);
    }

    public List<List<GrapeGeneral>> getKnowledges() {
        return knowledges;
    }

    public void setKnowledges(List<List<GrapeGeneral>> knowledges) {
        this.knowledges = knowledges;
    }

    public List<String> getSolutions() {
        return solutions;
    }

    public void setSolutions(List<String> solutions) {
        this.solutions = list2Base64(solutions);
    }

    public List<GrapeGeneral> getStems() {
        return stems;
    }

    public void setStems(List<GrapeGeneral> stems) {
        this.stems = stems;
    }

    public List<String> getTypes() {
        return types;
    }

    public void setTypes(List<String> types) {
        this.types = types;
    }

    private ArrayList<String> list2Base64(List<String> list) {
        ArrayList<String> result = new ArrayList<>();
        for (String str : list) {
            String s = GrapeUtil.exchangImg(str);
            result.add(s);
        }
        return result;
    }
}
