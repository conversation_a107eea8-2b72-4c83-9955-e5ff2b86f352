package com.sure.question.vo.grapeVo;


import com.sure.question.util.GrapeUtil;
import lombok.Data;

/**
 * 题目爬取vo
 */
@Data
public class GrapeWebQuestionVo {
    private GrapeWebSmallQuesVo blocks;
    private String description;
    private Integer difficulty;
    private String period;
    private String subject;
    private String type;
    private Integer year;
    private Integer score;
    private String comment;

    public GrapeWebQuestionVo(GrapeWebSmallQuesVo blocks, String description, Integer difficulty,
                              String period, String subject, String type, Integer year,Integer score) {
        this.blocks = blocks;
        this.description = GrapeUtil.exchangImg(description);
        this.difficulty = difficulty;
        this.period = period;
        this.subject = subject;
        this.type = type;
        this.year = year;
        this.score = score;
    }

    public GrapeWebQuestionVo() {
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public GrapeWebSmallQuesVo getBlocks() {
        return blocks;
    }

    public void setBlocks(GrapeWebSmallQuesVo blocks) {
        this.blocks = blocks;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = GrapeUtil.exchangImg(description);
    }

    public Integer getDifficulty() {
        return difficulty;
    }

    public void setDifficulty(Integer difficulty) {
        this.difficulty = difficulty;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }
}
