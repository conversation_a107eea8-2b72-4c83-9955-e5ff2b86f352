package com.sure.question.vo.papergroup;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/7/19 11:37
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Validated
@ApiModel("试卷分组排序")
public class SortCodeVo {

    @NotNull
    @ApiModelProperty("分组ID")
    private Integer id;
    @NotNull
    @ApiModelProperty("排序")
    private Integer sortCode;

}
