package com.sure.question.vo.es;

import lombok.Data;

import java.util.List;

@Data
public class QueryParam {
    /**
     * 学段
     */
    private int gradeLevel;
    /**
     * 学科
     */
    private int subjectId;
    /**
     * 当前页
     */
    private int page;
    /**
     * 页大小
     */
    private int size;
    /**
     * 知识点
     */
    private List<Long> knowledgeIds;
    /**
     * 章节
     */
    private List<Long> chapterIds;
    /**
     * 题型
     */
    private Integer questionTypeId;
    /**
     * 难度
     */
    private Integer difficulty;
    /**
     * 试卷年份
     */
    private Integer year;
    /**
     * 试卷类型
     */
    private Integer paperTypeId;
    /**
     * 试卷年级
     */
    private Integer gradeId;
    /**
     * 试卷地区
     */
    private List<Long> regionIds;
    /**
     * 内容关键字
     */
    private String keyword;
    /**
     * 是否匹配答案解析
     */
    private boolean matchAnswerExplanation;
    /**
     * 最低匹配比例
     */
    private Integer keywordMinMatchPercent;
    /**
     * 关键词高亮
     */
    private boolean highlight;
    /**
     * 指定试卷范围
     */
    private List<Long> includePaperIds;
    /**
     * 剔除题目Id
     */
    private List<Long> excludeQuestionIds;
    /**
     * 排序方式: useCount-组卷次数; answerCount-作答次数; knowledgeMatch-知识点匹配度
     */
    private String sortBy;
    /**
     * 是否倒序
     */
    private Boolean isDesc;
}
