package com.sure.question.vo.coachBook;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InfoVo {
    /**
     * 教辅Id
     */
    private Integer id;
    /**
     * 教辅合作商
     */
    @NotEmpty
    private String partner;
    /**
     * 科目Id
     */
    @NotNull
    private Integer subjectId;
    /**
     * 学段Id
     */
    @NotNull
    private Integer gradeLevel;
    /**
     * 年级Id
     */
    private Integer gradeId;
    /**
     * 学期Id
     */
    private Integer semesterId;
    /**
     * 学期Id
     */
    private Integer term;
    /**
     * 年份
     */
    @NotNull
    private Integer year;
    /**
     * 教辅代码
     */
    private String bookCode;
    /**
     * 图书名称
     */
    @NotEmpty
    private String bookName;
    /**
     * 出版社名称
     */
    @NotEmpty
    private String pressName;

    private Boolean wrongQuesQuickMark;
    private Boolean wrongQuesPaperMark;

    /**
     * 试卷数量
     */
    private Integer paperCount;
    /**
     * 定制试卷数
     */
    private Integer customPaperCount;
    /**
     * 应用学校数量
     */
    private Integer schoolCount;

    /**
     * 排序ID
     */
    private Integer orderId;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 封面
     */
    private String cover;
}
