package com.sure.question.vo.coachBook;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Builder
public class PaperVo {
    @NotEmpty
    private String id;
    private String paperName;
    private String paperAlias;
    private Integer gradeLevel;
    private Integer subjectId;
    private Integer gradeId;
    private Integer term;
    private Integer year;
    private Long regionId;
    private Integer paperTypeId;
    private String uploadType;
    private String editStatus;

    /**
     * 学生错题数量，2.1 2.2算一道题
     */
    private Integer wrongQuesCount;

    private String examSubjectId;

    @NotNull
    private Integer coachBookId;
    private String coachBookName;
    private String coachBookCode;
    /**
     * 试卷分组ID
     */
    private Integer paperGroupId;
    /**
     * 标准变式题数量
     */
    private Integer standardQuestionCount;

    /**
     * 标准变式题是否添加完成
     */
    private Boolean standardQuestionFinished;

    @NotNull
    private Integer sortCode;
    private String feedbackSheetId;
    private String feedbackSheetName;
    private String feedbackSheetPdfUrl;
    private String feedbackSheetImageUrls;

    /**
     * 是否启用分值分析
     */
    private Boolean scoreAnalysis;

    /**
     * 是否已经查看过
     */
    private Boolean isAccessed;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date openTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 是否锁定
     */
    private Boolean locked;

    /**
     * 试卷正文pdf
     */
    private String contentPdfUrl;
    /**
     * 试卷正文pdf上传时间
     */
    private Date contentPdfUploadTime;

    /**
     * 试卷答案pdf
     */
    private String answerPdfUrl;
    /**
     * 试卷答案pdf上传时间
     */
    private Date answerPdfUploadTime;

    /**
     * 听力语音
     */
    private String listeningAudioUrl;
    /**
     * 听力语音上传时间
     */
    private Date listeningAudioUploadTime;
}
