package com.sure.question.vo.coachBook;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 教辅定制学校查询参数
 *
 * <AUTHOR>
 */
@ApiModel("教辅定制学校列表")
@Data
public class BookSchoolVo {
    /**
     * 记录 id
     */
    private Integer id;
    /**
     * 学校 id
     */
    @ApiModelProperty("学校 id")
    private String schoolId;
    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    private String schoolName;
    /**
     * 学段 id
     */
    @ApiModelProperty("学段 id")
    private Integer gradeLevel;
    /**
     * 年级 id
     */
    @ApiModelProperty("年级 id")
    private Integer gradeId;
    /**
     * 学年
     */
    @ApiModelProperty("学年")
    private Integer semesterId;
    /**
     * 学期
     */
    @ApiModelProperty("学期")
    private Integer term;
    /**
     * 快捷标记
     */
    @ApiModelProperty("快捷标记")
    private Boolean wrongQuesQuickMark;
    /**
     * 试卷标记
     */
    @ApiModelProperty("试卷标记")
    private Boolean wrongQuesPaperMark;
    /**
     * 创建者 id
     */
    @ApiModelProperty("创建者 id")
    private String createBy;
    /**
     * 创建者姓名
     */
    @ApiModelProperty("创建者姓名")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
