package com.sure.question.vo.coachBook;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SheetVo {
    private String id;
    private String name;
    private Integer sourceType;
    private String sourceId;
    private Integer bookType;
    private String schoolId;
    private String pdfUrl;
    private String imageUrls;
    private String createBy;
    private Date createTime;
    private Date updateTime;
    @NotNull
    private Integer coachBookId;
    @NotNull
    private Integer sortCode;
    private String paperId;
    private String paperName;
}
