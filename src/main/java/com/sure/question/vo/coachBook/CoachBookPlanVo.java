package com.sure.question.vo.coachBook;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/9 11:29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CoachBookPlanVo {

    private Integer coachBookId;
    private String coachBookName;
    private Integer subjectId;
    @JsonIgnore
    private Integer sortCode;
    private List<PkgVo> pkgs;
    private Boolean isSubscribed;

}
