package com.sure.question.vo.coachBook;

import com.sure.question.entity.PaperMain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CoachBookPaperMainVo {
    @ApiModelProperty(value = "试卷 id")
    private String id;

    @ApiModelProperty(value = "试卷名称")
    private String paperName;

    @ApiModelProperty(value = "学段 id")
    private Integer gradeLevel;

    @ApiModelProperty(value = "学科 id")
    private Integer subjectId;

    @ApiModelProperty(value = "年级 id")
    private Integer gradeId;

    @ApiModelProperty(value = "学期 id")
    private String term;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "试卷所属区划 id")
    private Long regionId;

    @ApiModelProperty(value = "试卷类型")
    private Integer paperTypeId;

    @ApiModelProperty(value = "组卷样式JSON")
    private String paperStyleJson;

    @ApiModelProperty(value = "标记试卷所属教辅，仅用于辅助筛选，真正确定试卷和教辅的关联记录保存在coach_book_paper表")
    private Integer flagCoachBookId;

    @ApiModelProperty(value = "试卷所属教辅名称")
    private String flagCoachBookName;

    @ApiModelProperty("错题反馈卡 id")
    private String feedbackSheetId;

    @ApiModelProperty("快捷标错")
    private Boolean wrongQuesQuickMark;

    @ApiModelProperty("试卷标错")
    private Boolean wrongQuesPaperMark;

    @ApiModelProperty("分值统计")
    private Boolean scoreAnalysis;

    public static CoachBookPaperMainVo createFromPaperMain(PaperMain paperMain) {
        CoachBookPaperMainVo vo = new CoachBookPaperMainVo();
        vo.setId(paperMain.getId());
        vo.setPaperName(paperMain.getPaperName());
        vo.setGradeLevel(paperMain.getGradeLevel());
        vo.setSubjectId(paperMain.getSubjectId());
        vo.setGradeId(paperMain.getGradeId());
        vo.setTerm(paperMain.getTerm());
        vo.setYear(paperMain.getYear());
        vo.setRegionId(paperMain.getRegionId());
        vo.setPaperTypeId(paperMain.getPaperTypeId());
        vo.setPaperStyleJson(paperMain.getPaperStyleJson());
        vo.setFlagCoachBookId(paperMain.getFlagCoachBookId());
        vo.setFlagCoachBookName(paperMain.getFlagCoachBookName());
        vo.setFeedbackSheetId(paperMain.getFeedbackSheetId());
        return vo;
    }
}
