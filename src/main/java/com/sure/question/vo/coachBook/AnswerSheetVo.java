package com.sure.question.vo.coachBook;

import lombok.Data;

import java.util.Date;

@Data
public class AnswerSheetVo {
    private String id;

    /**
     * 答题卡名称
     */
    private String name;

    private Integer gradeLevel;

    private Integer subjectId;

    /**
     * 类型 1个人 2学校 3公共
     */
    private Integer sheetType;

    /**
     * 来源：1-自主创建；2-题库组卷；3-试卷结构
     */
    private Integer sourceType;

    /**
     * 当来源为题库组卷时是试卷id，当来源为试卷结构时是考试科目id
     */
    private String sourceId;

    /**
     * 答题卡模式 1线上阅卷 2先阅后扫
     */
    private Integer mode;

    /**
     * 答题卡页数
     */
    private Integer pageCount;

    /**
     * 客观题数量
     */
    private Integer objectiveCount;

    /**
     * 主观题数量
     */
    private Integer subjectiveCount;

    /**
     * 客观题数据
     */
    private String objectivesJson;

    /**
     * 主观题题块数据
     */
    private String blocksJson;

    /**
     * 答题卡数据
     */
    private String sheetJson;

    /**
     * 模板数据
     */
    private String templateJson;

    /**
     * 页面内容
     */
    private String html;

    /**
     * PDF链接
     */
    private String pdfUrl;

    /**
     * 图片宽度
     */
    private Integer imageWidth;

    /**
     * 图片高度
     */
    private Integer imageHeight;

    /**
     * 图片链接
     */
    private String imageUrls;

    /**
     * 学校ID
     */
    private String schoolId;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 分享人
     */
    private String shareBy;

    /**
     * 分享的答题卡ID
     */
    private String shareId;

    /**
     * 状态：0-正常；1-放入回收站；2-已删除
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
