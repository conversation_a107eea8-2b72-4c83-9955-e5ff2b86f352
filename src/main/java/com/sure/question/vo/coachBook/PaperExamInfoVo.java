package com.sure.question.vo.coachBook;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/1/9 16:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaperExamInfoVo {

    private String examId;
    private String examSubjectId;
    private Integer subjectId;
    private String subjectName;
    private String paperId;
    private String paperName;
    private String paperAlias;
    private String feedbackSheetPdfUrl;
    private Integer total;
    private Integer submitted;
    /**
     * 状态：0-没有数据；1-已完成；2-扫描处理中；3-已有家长提交；4-已有教师提交
     */
    private Integer status;
    @JsonIgnore
    private Integer sortCode;
    /**
     * 是否已经查看过
     */
    private Boolean isAccessed;

}
