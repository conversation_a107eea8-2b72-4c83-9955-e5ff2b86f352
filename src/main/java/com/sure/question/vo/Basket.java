package com.sure.question.vo;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sure.question.dto.paper.structure.PaperStructure;
import com.sure.question.entity.BasketPaperStyle;
import com.sure.question.vo.question.QuestionVo;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@Builder
@ApiModel
public class Basket implements Serializable {
    private List<QuestionVo> questions;
    private BasketPaperStyle basketPaperStyle;
    private Map<String, Object> paperInfo;
    private String paperStructure;

    public Basket() {
        this.questions = new ArrayList<>();
        this.basketPaperStyle = new BasketPaperStyle();
        this.paperInfo = new HashMap<>();
    }

    /**
     * 获取试卷Id
     */
    @JsonIgnore
    @JSONField(serialize = false)
    public String getPaperId() {
        if (paperInfo == null) {
            return null;
        }
        return (String) paperInfo.get("id");
    }

    /**
     * 设置试卷Id
     */
    public void setPaperId(String paperId) {
        if (paperInfo == null) {
            paperInfo = new HashMap<>();
        }
        paperInfo.put("id", paperId);

        if (basketPaperStyle == null) {
            basketPaperStyle  = new BasketPaperStyle();
        }
        basketPaperStyle.setPaperId(paperId);
    }

    /**
     * 获取试卷结构
     */
    @JsonIgnore
    @JSONField(serialize = false)
    public String getPaperStructureJson() {
        String structureJson = paperStructure;
        if (StringUtils.isBlank(structureJson)) {
            // 兼容试卷结构存于样式中
            if (basketPaperStyle != null) {
                structureJson = basketPaperStyle.getQuestionCategoryJson();
            }
        }
        return structureJson;
    }

    /**
     * 从试卷结构中提取出题目ID
     */
    public List<String> extractQuestionIdsFromPaperStructure() {
        PaperStructure structure = extractStructure();
        return extractQuestionIdsFromPaperStructure(structure);
    }

    public PaperStructure extractStructure() {
        String structureJson = getPaperStructureJson();
        if (StringUtils.isBlank(structureJson)) {
            return null;
        }
        return JSONObject.parseObject(structureJson, PaperStructure.class);
    }

    private List<String> extractQuestionIdsFromPaperStructure(PaperStructure structure) {
        if (structure == null) {
            return new ArrayList<>();
        }
        return new ArrayList<>(structure.extractQuestionIdBranchIdsMap().keySet());
    }
}
