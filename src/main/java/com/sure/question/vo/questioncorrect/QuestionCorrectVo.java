package com.sure.question.vo.questioncorrect;

import com.sure.question.vo.question.QuestionVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/24 14:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuestionCorrectVo {

    private String id;
    private Integer gradeLevel;
    private Integer subjectId;
    private QuestionVo question;

    /**
     * 报错记录
     */
    private List<QuestionCorrectRecordVo> records = new ArrayList<>();
}
