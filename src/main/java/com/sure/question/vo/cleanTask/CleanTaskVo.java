package com.sure.question.vo.cleanTask;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CleanTaskVo {
    private Integer id;
    private String name;
    private String description;
    private Integer gradeLevel;
    private Integer subjectId;
    private String lastPaperId;
    private Integer lastPaperCount;
    private Date createTime;
    private Date updateTime;
    private Integer checkedQuestionCount;
    private Integer confirmedQuestionCount;
    private Integer ignoredQuestionCount;
    private Integer updatedQuestionCount;
}
