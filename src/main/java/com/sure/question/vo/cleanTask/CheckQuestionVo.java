package com.sure.question.vo.cleanTask;

import com.sure.question.entity.CleanTaskQuestion;
import com.sure.question.entity.CleanTaskSmallQuestion;
import com.sure.question.vo.question.QuestionVo;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CheckQuestionVo {
    private Integer taskId;
    private String questionId;
    private String paperId;
    private String paperName;
    private QuestionVo questionVo;
    private CleanTaskQuestion taskQuestion;
    private List<CleanTaskSmallQuestion> taskSmallQuestion = new ArrayList<>();
}
