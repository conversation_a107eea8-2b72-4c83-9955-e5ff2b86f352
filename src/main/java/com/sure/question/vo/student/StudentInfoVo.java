package com.sure.question.vo.student;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/16 17:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StudentInfoVo {
    /**
     * 学生ID
     */
    private String studentId;
    /**
     * 学生所在学校ID
     */
    private String schoolId;
    /**
     * 学生所在学校名称
     */
    private String schoolName;
    /**
     * 班级ID
     */
    private String classId;
    /**
     * 班级名称
     */
    private String className;
    /**
     * 学段
     */
    private Integer gradeLevel;
    /**
     * 年级ID
     */
    private Integer gradeId;
    /**
     * 姓名
     */
    private String realName;
    /**
     * 性别
     */
    private String sex;
    /**
     * 校内学号
     */
    private String studentNo;
    /**
     * 学生所在学校当前学年
     */
    private Integer schoolCurrentSemester;
    /**
     * 学生所在学校当前学期
     */
    private Integer schoolCurrentTerm;
    /**
     * 学生所关联的家长ID列表
     */
    private List<String> parentIds;
    /**
     * 是否在读
     */
    private Boolean isInSchool;
}
