package com.sure.question.vo.schoolCoachBook;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@ApiModel("教辅信息")
@Data
public class BookInfo {
    /**
     * 教辅Id
     */
    @ApiModelProperty("教辅Id")
    private Integer id;
    /**
     * 科目Id
     */
    @NotNull
    @ApiModelProperty("科目Id")
    private Integer subjectId;
    /**
     * 学年Id
     */
    @ApiModelProperty("学年Id")
    private Integer semesterId;
    /**
     * 学期Id
     */
    @ApiModelProperty("学期Id")
    private Integer term;
    /**
     * 学段Id
     */
    @NotNull
    @ApiModelProperty("学段Id")
    private Integer gradeLevel;
    /**
     * 年级Id
     */
    @ApiModelProperty("年级Id")
    private Integer gradeId;
    /**
     * 年份
     */
    @NotNull
    @ApiModelProperty("年份")
    private Integer year;

    private String bookCode;
    /**
     * 图书名称
     */
    @NotEmpty
    @ApiModelProperty("图书名称")
    private String bookName;
    /**
     * 出版社名称
     */
    @NotEmpty
    @ApiModelProperty("出版社名称")
    private String pressName;
    /**
     * 试卷数量
     */
    @ApiModelProperty("试卷数量")
    private Integer paperCount;
    /**
     * 定制卷数目
     */
    private Integer customPaperCount;

    private Boolean wrongQuesQuickMark;
    private Boolean wrongQuesPaperMark;

    private String cover;

//    /**
//     * 反馈卡数量
//     */
//    @ApiModelProperty("反馈卡数量")
//    private Integer feedbackSheetCount;
}
