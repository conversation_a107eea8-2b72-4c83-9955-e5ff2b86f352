package com.sure.question.vo.schoolCoachBook;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 试卷查询参数
 *
 * <AUTHOR>
 */
@ApiModel("试卷查询参数")
@Data
public class QueryPaperVo {
    /**
     * 科目Id
     */
    @ApiModelProperty("科目Id")
    private Integer subjectId;
    /**
     * 试卷类型
     */
    @ApiModelProperty("试卷类型")
    private Integer paperTypeId;
    /**
     * 学段Id
     */
    @ApiModelProperty("学段Id")
    private Integer gradeLevel;
    /**
     * 年级Id
     */
    @ApiModelProperty("年级Id")
    private Integer gradeId;
    /**
     * 学年
     */
    @ApiModelProperty("学年")
    private Integer semesterId;
    /**
     * 学期Id
     */
    @ApiModelProperty("学期Id")
    private Integer term;
    /**
     * 年份
     */
    @ApiModelProperty("年份")
    private Integer year;
    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keyword;
    /**
     * 排序规则：time-按时间排序；browseCount-按浏览次数排序
     */
    @ApiModelProperty("排序规则：time-按时间排序；browseCount-按浏览次数排序")
    private String orderBy;
}
