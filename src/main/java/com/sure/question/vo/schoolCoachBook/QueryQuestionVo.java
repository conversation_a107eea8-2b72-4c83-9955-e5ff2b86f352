package com.sure.question.vo.schoolCoachBook;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 试题查询参数
 *
 * <AUTHOR>
 */
@ApiModel("试题查询参数")
@Data
public class QueryQuestionVo {
    @NotNull
    @Min(1)
    private Integer page;
    @NotNull
    @Min(1)
    private Integer size;
    /**
     * 学段 id
     */
    @ApiModelProperty("学段 id")
    @NotNull(message = "不能为空")
    private Integer gradeLevel;
    /**
     * 学科 id
     */
    @ApiModelProperty("学科 id")
    @NotNull(message = "不能为空")
    private Integer subjectId;
    /**
     * 知识点 id
     */
    @ApiModelProperty("知识点 id")
    private Integer knowledge;
    /**
     * 教材Id
     */
    @ApiModelProperty("教材Id")
    private Integer bookId;
    /**
     * 章节 id
     */
    @ApiModelProperty("章节 id")
    private Integer chapter;
    /**
     * 难度 id
     */
    @ApiModelProperty("难度 id")
    private Integer difficulty;
    /**
     * 试题类型 id
     */
    @ApiModelProperty("试题类型 id")
    private Integer quesType;
    /**
     * 教辅应用学年
     */
    @ApiModelProperty("教辅应用学年")
    private Integer semesterId;
    /**
     * 教辅应用学期
     */
    @ApiModelProperty("教辅应用学期")
    private Integer term;
    /**
     * 教辅应用年级
     */
    @ApiModelProperty("教辅应用年级")
    private Integer gradeId;
}
