package com.sure.question.vo.schoolCoachBook;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 教辅查询参数
 *
 * <AUTHOR>
 */
@ApiModel("教辅查询参数")
@Data
public class QueryBookVo {
    private String schoolId;
    /**
     * 科目Id
     */
    private Integer subjectId;
    /**
     * 学段Id
     */
    private Integer gradeLevel;
    /**
     * 年级Id
     */
    private Integer gradeId;
    /**
     * 学年
     */
    private Integer semesterId;
    /**
     * 学期Id
     */
    private Integer term;
    /**
     * 图书名称
     */
    private String bookName;
    /**
     * 出版社名称
     */
    private String pressName;
    /**
     * 是否只查询到开放时间的
     */
    private Boolean onlyOpen;
}
