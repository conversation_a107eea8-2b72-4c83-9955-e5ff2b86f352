package com.sure.question.vo.schoolCoachBook;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/12/1 18:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("错题标记")
@Validated
public class WrongQuesMarkVo {

    @ApiModelProperty("教辅ID")
    @NotNull(message = "教辅ID不能为空")
    private Integer id;
    @ApiModelProperty("快捷标记")
    @NotNull(message = "快捷标记不能为空")
    private Boolean wrongQuesQuickMark;
    @ApiModelProperty("试卷标记")
    @NotNull(message = "试卷标记不能为空")
    private Boolean wrongQuesPaperMark;

}