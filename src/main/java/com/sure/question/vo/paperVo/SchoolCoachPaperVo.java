package com.sure.question.vo.paperVo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel("学校教辅试卷")
@Data
public class SchoolCoachPaperVo {
    /**
     * 试卷 id
     */
    @ApiModelProperty("试卷 id")
    private String id;
    /**
     * 试卷名称
     */
    @ApiModelProperty("试卷名称")
    private String paperName;
    /**
     * 学段：1-小学；2-初中；4-高中
     */
    @ApiModelProperty("学段：1-小学；2-初中；4-高中")
    private Integer gradeLevel;
    /**
     * 学科 id
     */
    @ApiModelProperty("学科 id")
    private Integer subjectId;
    /**
     * 年级 id
     */
    @ApiModelProperty("年级 id")
    private Integer gradeId;
    /**
     * 学年 id
     */
    @ApiModelProperty("学年 id")
    private Integer semesterId;
    /**
     * 学期 : 1-上学期；2-下学期
     */
    @ApiModelProperty("学期 : 1-上学期；2-下学期")
    private String term;
    /**
     * 试卷年份
     */
    @ApiModelProperty("试卷年份")
    private Integer year;
    /**
     * 试卷所属区划 id
     */
    @ApiModelProperty("试卷所属区划 id")
    private Long regionId;
    /**
     * 试卷类型 id
     */
    @ApiModelProperty("试卷类型 id")
    private Integer paperTypeId;
    /**
     * 卷库类型：1-个人库；2-校本库；3-公共库；4-定制资源
     */
    @ApiModelProperty("卷库类型：1-个人库；2-校本库；3-公共库；4-定制资源")
    private String paperBank;
    /**
     * 浏览次数
     */
    @ApiModelProperty("浏览次数")
    private Integer browseCount;
    /**
     * 下载次数
     */
    @ApiModelProperty("下载次数")
    private Integer downloadCount;


    /**
     * 教辅 id
     */
    @ApiModelProperty("教辅 id")
    private Integer bookId;
    /**
     * 教辅名称
     */
    @ApiModelProperty("教辅名称")
    private String bookName;
    /**
     * 出版社名称
     */
    @ApiModelProperty("出版社名称")
    private String pressName;
    /**
     * 是否已收藏该卷
     */
    @ApiModelProperty("是否已收藏该卷")
    @TableField(exist = false)
    private Boolean ifFavorite;
}
