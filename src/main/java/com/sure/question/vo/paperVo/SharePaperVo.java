package com.sure.question.vo.paperVo;

import lombok.Data;

import java.util.Date;

@Data
public class SharePaperVo {
    private String id;
    private String paperName;
    private Integer gradeLevel;
    private Integer subjectId;
    private Integer gradeId;
    private String term;
    private Integer year;
    private Long regionId;
    private Integer flagCoachBookId;
    private String flagCoachBookName;
    private Integer paperGroupId;
    private Integer paperTypeId;
    private String paperBank;
    private String sharePaperId;
    private Date createTime;
    private String srcUserId;
    private String srcUserName;
    private String destUserId;
    private String destUserName;
    private Boolean isDelete;
}
