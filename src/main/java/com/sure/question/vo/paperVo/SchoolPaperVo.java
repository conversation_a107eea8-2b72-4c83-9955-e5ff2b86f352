package com.sure.question.vo.paperVo;

import lombok.Data;

import java.util.Date;

@Data
public class SchoolPaperVo {
    private String id;
    private String paperName;
    private Integer gradeLevel;
    private Integer subjectId;
    private Integer gradeId;
    private String term;
    private Integer year;
    private Long regionId;
    private Integer flagCoachBookId;
    private String flagCoachBookName;
    private Integer paperGroupId;
    private Integer paperTypeId;
    private String paperBank;
    private String schoolPaperId;
    private String schoolId;
    private String schoolName;
    private String createUserId;
    private String createUserName;
    private Date createTime;
    private Integer browseCount;
    private Integer downloadCount;
    private Integer source;
    private Boolean isPublic;
    private Boolean isDelete;
}
