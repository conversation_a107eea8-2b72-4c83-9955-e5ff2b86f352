package com.sure.question.vo.paper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.entity.PaperMain;
import com.sure.question.enums.PaperRecordCategoryEnum;
import com.sure.question.enums.PaperStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 试卷审核查询参数
 */
@Data
public class FindPaperPageParam {
    /**
     * 学段
     */
    @NotNull
    private Integer stage;
    /**
     * 学科
     */
    @NotNull
    private Integer subject;
    /**
     * 试卷类型
     */
    private Integer type;
    /**
     * 年份
     */
    private Integer year;
    /**
     * 地区
     */
    private String region;
    /**
     * 年级
     */
    private Integer gradeId;
    /**
     * 学期
     */
    private Integer term;
    /**
     * 关键字
     */
    private String keyword;
    /**
     * 试卷类别：常规、教辅
     */
    private String bookType;
    /**
     * 教辅Id
     */
    private Integer flagCoachBookId;
    /**
     * 试卷组Id
     */
    private Integer paperGroupId;
    /**
     * 排序规则：year-按年份排序;browseCount-按浏览次数排序
     */
    private String orderBy;
    @NotNull
    @Min(1)
    private Integer page;
    @NotNull
    @Min(1)
    private Integer size;

    /**
     * 构建查询条件
     */
    public LambdaQueryWrapper<PaperMain> buildPaperQueryWrapper() {
        LambdaQueryWrapper<PaperMain> queryWrapper = new LambdaQueryWrapper<PaperMain>()
                .eq(PaperMain::getGradeLevel, stage)
                .eq(PaperMain::getSubjectId, subject)
                .eq(PaperMain::getStatus, PaperStatusEnum.Status0.getCode())
                .eq(type != null && type != 0, PaperMain::getPaperTypeId, type)
                .eq(year != null && year != 0, PaperMain::getYear, year)
                .likeRight(StringUtils.isNotBlank(region), PaperMain::getRegionId, StringUtils.stripEnd(StringUtils.trim(region), "0"))
                .eq(gradeId != null && gradeId != 0, PaperMain::getGradeId, gradeId)
                .eq(term != null && term != 0, PaperMain::getTerm, term)
                .like(StringUtils.isNotBlank(keyword), PaperMain::getPaperName, StringUtils.trim(keyword))
                .eq(paperGroupId != null && paperGroupId != 0, PaperMain::getPaperGroupId, paperGroupId);
        // 常规试卷
        if (PaperRecordCategoryEnum.NORMAL.getId().equals(bookType)) {
            queryWrapper.isNull(PaperMain::getFlagCoachBookId);
        }
        // 教辅试卷
        if (PaperRecordCategoryEnum.COACH.getId().equals(bookType)) {
            // 不指定教辅
            if (flagCoachBookId == null) {
                queryWrapper.isNotNull(PaperMain::getFlagCoachBookId);
            }
            // 指定教辅
            else {
                queryWrapper.eq(PaperMain::getFlagCoachBookId, flagCoachBookId);
            }
        }
        return queryWrapper;
    }
}
