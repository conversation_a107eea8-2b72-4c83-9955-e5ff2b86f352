package com.sure.question.vo.paper.bind;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 阅卷服务需要的试卷结构数据
 */

@Data
public class BindPaperStructure {
    /**
     * 试卷Id
     */
    private String paperId;

    /**
     * 试卷名称
     */
    private String paperName;

    /**
     * 客观题
     */
    private List<ObjectiveQuestion> objectiveQuestions = new ArrayList<>();

    /**
     * 主观题
     */
    private List<SubjectiveQuestion> subjectiveQuestions = new ArrayList<>();
}
