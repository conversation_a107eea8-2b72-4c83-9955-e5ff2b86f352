package com.sure.question.vo.paper.bind;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Question {
    /**
     * 题号
     */
    private int questionCode;

    /**
     * 题名
     */
    private String questionName;

    /**
     * 大题号
     */
    private int topicCode;

    /**
     * 大题名
     */
    private String topicName;

    /**
     * 满分
     */
    private Double fullScore;

    /**
     * 是否附加题
     */
    private boolean isAdditional;

    /**
     * 题库题目Id
     */
    private String questionId;

    /**
     * 题库小题Id
     */
    private String branchId;

    /**
     * 题库题型Id
     */
    private Integer quesTypeId;

    /**
     * 题库题型名称
     */
    private String quesTypeName;

    /**
     * 知识点
     */
    private List<Knowledge> knowledgeList = new ArrayList<>();
}
