package com.sure.question.vo;

import com.sure.question.enums.RoleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;

/**
 * @data 2021/3/2 17:30
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class TokenUserVo {
    private String userId;
    private String schoolId;
    private List<Integer> roles = new LinkedList<>();

    /**
     * 是不是题库管理员
     *
     * @return 是不是题库管理员
     */
    public boolean isSysManager() {
        return roles.contains(RoleEnum.SysManager.getCode());
    }

    /**
     * 是不是题库编辑员
     *
     * @return 是不是题库编辑员
     */
    public boolean isSysEditor() {
        return roles.contains(RoleEnum.SysEditor.getCode());
    }

    /**
     * 判断是不是超级管理员
     *
     * @return 是不是超级管理员
     */
    public boolean isSuperManager() {
        return roles.contains(RoleEnum.SuperManager.getCode());
    }

    /**
     * 是不是学校管理员
     *
     * @return 是不是学校管理员
     */
    public boolean isSchoolManager() {
        return roles.contains(RoleEnum.SchoolManager.getCode());
    }
}
