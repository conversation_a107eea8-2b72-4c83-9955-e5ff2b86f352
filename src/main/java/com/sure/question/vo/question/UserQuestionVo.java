package com.sure.question.vo.question;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 包含用户相关数据的题目
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class UserQuestionVo extends QuestionVo {
    /**
     * 是否收藏
     */
    private boolean ifFavorite;
    /**
     * 是否报错
     */
    private boolean ifCorrect;
    /**
     * 是否已组卷
     */
    private boolean used;
}
