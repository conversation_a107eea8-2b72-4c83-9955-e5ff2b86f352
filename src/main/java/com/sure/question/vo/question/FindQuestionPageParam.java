package com.sure.question.vo.question;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class FindQuestionPageParam {
    @NotNull
    @Min(1)
    private Integer page;
    @NotNull
    @Min(1)
    private Integer size;
    @NotNull
    private Integer stage;
    @NotNull
    private Integer subject;
    /**
     * 单个知识点
     */
    private Integer knowledge;
    /**
     * 多个知识点
     */
    private List<Integer> knowledgeIds;
    /**
     * 教材
     */
    private Integer bookId;
    /**
     * 单个章节
     */
    private Integer chapter;
    /**
     * 多个章节
     */
    private List<Integer> chapterIds;
    private Integer difficulty;
    private Integer ques_type;
    private Integer source;
    private Integer year;
    private Integer gradeId;
    private String region;
    private String keyword;
    private String sort_by;
    private Boolean isDesc;
    /**
     * 忽略试卷Id
     */
    private String ignorePaperId;
    /**
     * 搜索试卷范围
     */
    private List<String> paperIds;

    /**
     * 地区前缀，数据中按前缀匹配地区
     */
    private String regionPrefix;
    /**
     * 自身及后代地区Id
     */
    private List<Long> selfAndDescendantRegionIds;

    /**
     * 自身及后代章节Id
     */
    private List<Integer> selfAndDescendantChapterIds;

    /**
     * 自身及后代知识点Id
     */
    private List<Integer> selfAndDescendantKnowledgeIds;
}
