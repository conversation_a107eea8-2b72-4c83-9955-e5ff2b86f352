package com.sure.question.vo.question;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 修改题目知识点传参
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
public class ChangeChapterParam {
    @NotBlank
    private String questionId;
    @NotEmpty
    private List<Integer> chapterIds;
}
