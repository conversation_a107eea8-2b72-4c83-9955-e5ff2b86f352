package com.sure.question.vo.question;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.question.CheckQuestionOption;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.enums.BaseEnum;
import com.sure.question.enums.BranchTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class SmallQuestionVo implements Serializable {
    private String id;
    private Integer code;
    private Integer type;
    private String stem;
    private List<SmallQuestionOptionVo> options = new ArrayList<>();
    private String solution;
    private String explanation;
    private List<SmallQuestionKnowledgeVo> knowledges = new ArrayList<>();
    private Integer questionTypeId;//
    private Float score;
    private String expla;
    private String knowledgeName;

    public SmallQuestionVo(SmallQuestion sq) {
        id = sq.getId();
        code = sq.getQuestionNo();
        type = sq.getQuestionTypeId();
        stem = sq.getContentHtml();
        solution = sq.getAnswerHtml();
        explanation = sq.getExplanation();
        knowledges = new ArrayList<>();
        questionTypeId = sq.getQuestionTypeId();
        score = sq.getScore();
        setOptionsJson(sq.getOptions());
    }

    @JSONField(serialize = false)
    @JsonIgnore
    public String getOptionsJson() {
        if (options == null || options.isEmpty()) {
            return null;
        }
        return JSON.toJSONString(options);
    }

    public void setOptionsJson(String optionsJson) {
        options = StringUtils.isBlank(optionsJson) ? new ArrayList<>() : JSON.parseArray(optionsJson, SmallQuestionOptionVo.class);
        options.sort(Comparator.comparing(opt -> ObjectUtils.defaultIfNull(opt.getLabel(), StringUtils.EMPTY)));
    }

    public void check(CheckQuestionOption option) {
        if (StringUtils.isBlank(id)) {
            throw new ApiException("小题Id不能为空");
        }

        if (code == null) {
            throw new ApiException("小题号不能为空");
        } else if (code <= 0) {
            throw new ApiException("小题号必须大于0");
        }

        if (score == null) {
            if (!option.isAllowScoreNull()) {
                throw new ApiException("小题分数不能为空");
            }
        } else if (score < 0) {
            throw new ApiException("小题分数不能小于0");
        } else if (score == 0) {
            if (!option.isAllowScoreNull()) {
                throw new ApiException("小题分数不能为0");
            }
        }

        // 小题题型
        if (type == null) {
            throw new ApiException("小题题型不能为空");
        }
        BranchTypeEnum branchType = BaseEnum.getById(BranchTypeEnum.class, type);
        if (branchType == null) {
            throw new ApiException("小题题型错误");
        }

        // 选项
        if (branchType.getIsChoice()) {
            if (CollUtil.isEmpty(options)) {
                if (!branchType.equals(BranchTypeEnum.TrueOrFalse)) {
                    throw new ApiException("选择题选项不能为空");
                }
            }
            Set<String> labelSet = new HashSet<>();
            for (SmallQuestionOptionVo opt : options) {
                String label = opt.getLabel();
                if (StringUtils.isBlank(label)) {
                    throw new ApiException("选项标签不能为空");
                }
                if (!(label.length() == 1 && label.compareTo("A") >= 0 && label.compareTo("Z") <= 0)) {
                    throw new ApiException("选项标签必须是大写字母");
                }
                if (labelSet.contains(label)) {
                    throw new ApiException("选项标签不能重复");
                } else {
                    labelSet.add(label);
                }
            }
        } else if (CollUtil.isNotEmpty(options)) {
            throw new ApiException("非选择题不应存在选项");
        }

        // 答案
        if (StringUtils.isBlank(solution)) {
            if (!option.isAllowAnswerNull()) {
                throw new ApiException("答案不能为空");
            }
        } else if (branchType.getIsObjective()) {
            if (branchType.equals(BranchTypeEnum.SingleChoice) || branchType.equals(BranchTypeEnum.TrueOrFalse)) {
                if (solution.length() != 1) {
                    throw new ApiException("客观题答案格式错误");
                }
            }
            String[] answers = solution.split("");
            Set<String> answerSet = new HashSet<>(answers.length);
            Set<String> optionSet;
            if (branchType.equals(BranchTypeEnum.TrueOrFalse)) {
                optionSet = new HashSet<>();
                optionSet.add("T");
                optionSet.add("F");
            } else {
                optionSet = options.stream().map(SmallQuestionOptionVo::getLabel).collect(Collectors.toSet());
            }
            for (String s : answers) {
                if (!optionSet.contains(s) || answerSet.contains(s)) {
                    throw new ApiException("客观题答案格式错误");
                }
                answerSet.add(s);
            }
        } else if (branchType == BranchTypeEnum.FillBlank) {
            List<String> blankAnswers;
            try {
                blankAnswers = JSON.parseArray(solution, String.class);
            } catch (Exception e) {
                throw new ApiException("填空题答案格式错误");
            }
            if (blankAnswers.isEmpty() || blankAnswers.stream().anyMatch(StringUtils::isBlank)) {
                if (!option.isAllowAnswerNull()) {
                    throw new ApiException("填空题答案不能为空");
                }
            }
        }

        // 知识点
        if (CollUtil.isEmpty(knowledges)) {
            if (!option.isAllowKnowledgeEmpty()) {
                throw new ApiException("知识点不能为空");
            }
        } else if (knowledges.stream().map(SmallQuestionKnowledgeVo::getId).collect(Collectors.toSet()).size() != knowledges.size()) {
            throw new ApiException("小题知识点不能重复");
        }
    }
}
