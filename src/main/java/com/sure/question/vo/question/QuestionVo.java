package com.sure.question.vo.question;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sure.common.exception.ApiException;
import com.sure.question.dto.question.CheckQuestionOption;
import com.sure.question.entity.Question;
import com.sure.question.entity.QuestionBook;
import com.sure.question.entity.QuestionKnowledge;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.enums.BaseEnum;
import com.sure.question.enums.DifficultyEnum;
import com.sure.question.util.GrapeUtil;
import com.sure.question.util.IdUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class QuestionVo {
    /**
     * 题目Id
     */
    private String id;
    /**
     * 题号
     */
    private Integer code;
    /**
     * 学段
     */
    private Integer stage;
    /**
     * 学科
     */
    private Integer subjectId;
    /**
     * 题型
     */
    private Integer type;
    /**
     * 难度
     */
    private Integer difficulty;
    /**
     * 平均得分率
     */
    private Float scoreRate;
    /**
     * 组卷次数
     */
    private Integer useCount;
    /**
     * 作答次数
     */
    private Integer answerCount;
    /**
     * 年份
     */
    private Integer year;
    /**
     * 分数
     */
    private Float score;
    /**
     * 划题内容
     */
    private String divide;
    /**
     * 材料
     */
    private String trunk;
    /**
     * 点评
     */
    private String comment;
    /**
     * 小题
     */
    private List<SmallQuestionVo> branches = new ArrayList<>();
    /**
     * 章节
     */
    private List<QuestionChapterVo> chapters = new ArrayList<>();
    /**
     * 听力
     */
    private List<QuestionListeningVo> listening = new ArrayList<>();
    /**
     * 试卷来源
     */
    private List<QuestionReferPaperVo> referPaper = new ArrayList<>();


    public void setQuestion(Question q) {
        id = q.getId();
        stage = q.getGradeLevel();
        subjectId = q.getSubjectId();
        type = q.getQuestionTypeId();
        difficulty = q.getDifficult();
        scoreRate = q.getScoreRate();
        useCount = q.getUseCount();
        answerCount = q.getAnswerCount();
        year = q.getYear();
        score = q.getScore();
        divide = q.getDivideHtml();
        trunk = q.getContentHtml();
        comment = q.getComment();
        branches = new ArrayList<>();
        chapters = new ArrayList<>();
        listening = new ArrayList<>();
        referPaper = new ArrayList<>();
    }


    /**
     * 题目所有知识点
     */
    @JSONField(serialize = false)
    @JsonIgnore
    public List<Integer> getKnowledgeIds() {
        if (CollUtil.isEmpty(branches)) {
            return Collections.emptyList();
        }
        return branches.stream().flatMap(x -> ObjectUtils.defaultIfNull(x.getKnowledges(), new ArrayList<SmallQuestionKnowledgeVo>()).stream())
                .map(SmallQuestionKnowledgeVo::getId).distinct().collect(Collectors.toList());
    }

    /**
     * 检查
     */
    public void check(CheckQuestionOption option) {
        if (StringUtils.isBlank(id)) {
            throw new ApiException("题目Id不能为空");
        }

        if (CollUtil.isEmpty(branches)) {
            throw new ApiException("没有小题");
        }
        for (int idx = 0; idx < branches.size(); idx++) {
            SmallQuestionVo branch = branches.get(idx);
            if (!Objects.equals(idx + 1, branch.getCode())) {
                throw new ApiException("小题未按顺序");
            }
            branch.check(option);
        }

        if (StringUtils.isBlank(trunk) && branches.stream().allMatch(branch -> StringUtils.isBlank(branch.getStem()))) {
            throw new ApiException("没有材料或题干");
        }

        Set<String> ids = new HashSet<>();
        ids.add(id);
        for (SmallQuestionVo branch : branches) {
            if (ids.contains(branch.getId())) {
                throw new ApiException("小题Id重复");
            }
            ids.add(branch.getId());
        }
        Pattern pattern = Pattern.compile("^[0-9]{19}$");
        if (!pattern.matcher(id).matches()) {
            throw new ApiException("题目Id错误");
        }
        if (!ids.stream().allMatch(x -> pattern.matcher(x).matches())) {
            throw new ApiException("小题Id错误");
        }

        if ((type == null || type == 0) && !option.isAllowQuestionTypeNull()) {
            throw new ApiException("题型不能为空");
        }

        if (difficulty == null || difficulty == 0) {
            if (!option.isAllowDifficultyNull()) {
                throw new ApiException("难度不能为空");
            }
        } else if (BaseEnum.getById(DifficultyEnum.class, difficulty) == null) {
            throw new ApiException("难度错误");
        }

        if (score == null) {
            if (!option.isAllowScoreNull()) {
                throw new ApiException("分数不能为空");
            }
        } else if (score < 0) {
            throw new ApiException("分数不能小于0");
        } else if (score == 0) {
            if (!option.isAllowScoreNull()) {
                throw new ApiException("分数不能为0");
            }
        } else {
            float sumScore = branches.stream().map(SmallQuestionVo::getScore).filter(Objects::nonNull).reduce(0F, Float::sum);
            if (sumScore != score) {
                throw new ApiException("小题分数和题目分数不一致");
            }
        }

        if (!CollUtil.isEmpty(chapters)) {
            if (chapters.stream().map(QuestionChapterVo::getId).collect(Collectors.toSet()).size() != chapters.size()) {
                throw new ApiException("章节重复");
            }
        }

        if (!CollUtil.isNotEmpty(listening)) {
            Set<String> smallQuestionIds = branches.stream().map(SmallQuestionVo::getId).collect(Collectors.toSet());
            // 有多个小题的，题目本身可以有听力语音
            if (branches.size() > 1) {
                smallQuestionIds.add("0");
            }
            for (QuestionListeningVo listeningVo : listening) {
                if (!StringUtils.equals(id, listeningVo.getQuestionId())) {
                    throw new ApiException("听力语音题目Id错误");
                }
                if (!smallQuestionIds.contains(listeningVo.getSmallQuestionId())) {
                    throw new ApiException("听力语音小题Id错误");
                } else {
                    smallQuestionIds.remove(listeningVo.getSmallQuestionId());
                }
            }
        }
    }

    /**
     * 转为实体
     */
    public Question toEntity(List<SmallQuestion> smallQuestionList, List<QuestionKnowledge> questionKnowledgeList, List<QuestionBook> questionBookList) {
        long updateTime = System.currentTimeMillis();

        Question question = Question.builder()
                .id(id)
                .questionTypeId(type == null ? 0 : type)
                .divideHtml(divide)
                .contentHtml(trunk)
                .comment(comment)
                .difficult(difficulty == null ? 0 : difficulty)
                .score(score)
                .updateTime(updateTime)
                .build();
        // 处理图片
        GrapeUtil.quesReplaceImage(question, "image");

        if (CollUtil.isNotEmpty(branches)) {
            branches.sort(Comparator.comparing(SmallQuestionVo::getCode));
            for (SmallQuestionVo branch : branches) {
                SmallQuestion sq = SmallQuestion.builder()
                        .id(branch.getId())
                        .questionId(question.getId())
                        .questionNo(branch.getCode())
                        .questionTypeId(branch.getType())
                        .contentHtml(branch.getStem())
                        .options(branch.getOptionsJson())
                        .answerHtml(branch.getSolution())
                        .explanation(branch.getExplanation())
                        .score(branch.getScore())
                        .updateTime(updateTime)
                        .build();
                // 处理图片
                GrapeUtil.sqReplaceImage(sq, "image");
                smallQuestionList.add(sq);

                if (CollUtil.isNotEmpty(branch.getKnowledges())) {
                    branch.getKnowledges().sort(Comparator.comparing(SmallQuestionKnowledgeVo::getId));
                    for (SmallQuestionKnowledgeVo knowledgeVo : branch.getKnowledges()) {
                        QuestionKnowledge qk = new QuestionKnowledge();
                        qk.setId(IdUtil.longId());
                        qk.setQuestionId(sq.getId());
                        qk.setKnowledgeId(knowledgeVo.getId());
                        questionKnowledgeList.add(qk);
                    }
                }
            }
        }

        if (CollUtil.isNotEmpty(chapters)) {
            chapters.sort(Comparator.comparing(QuestionChapterVo::getBookId).thenComparing(QuestionChapterVo::getId));
            for (QuestionChapterVo chapterVo : chapters) {
                QuestionBook qb = new QuestionBook();
                qb.setId(IdUtil.longId());
                qb.setChapterId(chapterVo.getId());
                qb.setQuestionId(question.getId());
                questionBookList.add(qb);
            }
        }

        return question;
    }
}
