package com.sure.question.vo.question;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Valid
public class ChangeKnowledgeBranchParam {
    @NotBlank
    private String branchId;
    @NotEmpty
    private List<Integer> knowledgeIds;
}
