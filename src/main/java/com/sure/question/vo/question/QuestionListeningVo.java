package com.sure.question.vo.question;

import com.sure.common.exception.ApiException;
import com.sure.question.enums.ListeningAudioSourceEnum;
import com.sure.question.enums.ListeningVoiceGenderEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

@Data
public class QuestionListeningVo {
    /**
     * 自增Id
     */
    private String id;

    /**
     * 题目Id
     */
    @NotEmpty
    private String questionId;

    /**
     * 小题Id
     */
    @NotEmpty
    private String smallQuestionId;

    /**
     * 文本材料
     */
    @NotEmpty
    private String material;

    /**
     * 音频链接
     */
    private String audioUrl;

    /**
     * 重复次数
     */
    @NotNull
    private Integer repeatCount;

    /**
     * 音频来源
     */
    @NotNull
    private Integer audioSource;

    /**
     * 语速
     */
    private Integer speechRate;

    /**
     * 性别
     */
    private String voiceGender;

    /**
     * 音频文件base64
     */
    private String audioFileBase64;

    public void checkThrowEx() {
        if (StringUtils.isAnyEmpty(questionId, smallQuestionId)) {
            throw new ApiException("未指定听力对应题目");
        }
        if (StringUtils.isEmpty(material)) {
            throw new ApiException("无听力材料");
        }
        if (this.getRepeatCount() == null || this.getRepeatCount() < 1 || this.getRepeatCount() > 5) {
            throw new ApiException("听力重复次数应在1至5次之间");
        }
        if (ListeningAudioSourceEnum.TTS.getCode().equals(this.getAudioSource())) {
            if (this.getSpeechRate() == null || this.getSpeechRate() < 100 || this.getSpeechRate() > 300) {
                throw new ApiException("听力语速应在每分钟100至300词之间");
            }
            if (Arrays.stream(ListeningVoiceGenderEnum.values()).noneMatch(x -> x.getCode().equals(this.getVoiceGender()))) {
                throw new ApiException("听力语音合成声音性别错误");
            }
        } else if (ListeningAudioSourceEnum.UPLOAD.getCode().equals(this.getAudioSource())) {
            if (StringUtils.isEmpty(audioFileBase64)) {
                throw new ApiException("听力未上传音频文件");
            }
        } else if (!ListeningAudioSourceEnum.NONE.getCode().equals(this.getAudioSource())) {
            throw new ApiException("听力语音来源错误");
        }
    }
}
