package com.sure.question.vo.baseVo;

import com.sure.question.enums.BranchTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MessageQuestionCategoryVo {
    private int id;
    private String categoryName;
    private boolean isSubjective;
    private int orderId;

    public MessageQuestionCategoryVo (BranchTypeEnum branchType) {
        id = branchType.getId();
        categoryName = branchType.getName();
        isSubjective = !branchType.getIsObjective();
        orderId = branchType.ordinal() + 1;
    }
}
