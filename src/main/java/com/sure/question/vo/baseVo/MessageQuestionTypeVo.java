package com.sure.question.vo.baseVo;

import com.sure.question.entity.QuestionTypeMain;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MessageQuestionTypeVo {
    private int id;
    private String name;
    private int categoryId;
    private int orderId;
    private String groupName;
    private String extra;

    public MessageQuestionTypeVo(QuestionTypeMain type) {
        id = type.getId();
        name = type.getQuestionTypeName();
        categoryId = type.getCategoryId();
        orderId = type.getOrderId();
        groupName = type.getGroupName();
        extra = type.getExtra();
    }
}
