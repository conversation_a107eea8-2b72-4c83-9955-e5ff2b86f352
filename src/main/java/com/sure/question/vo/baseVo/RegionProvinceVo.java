package com.sure.question.vo.baseVo;

import com.sure.question.entity.Region;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class RegionProvinceVo {
    private long id;
    private String name;
    private List<RegionCityVo> cities;

    public RegionProvinceVo(Region r, List<RegionCityVo> cities) {
        id = r.getId();
        name = r.getName();
        this.cities = cities;
    }
}
