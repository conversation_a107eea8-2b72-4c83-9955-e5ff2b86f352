package com.sure.question.vo.baseVo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sure.question.entity.Book;
import com.sure.question.entity.BookCategory;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class MessageBookVo {
    private int id;
    private String name;
    @JsonIgnore
    private int orderId;
    private String press;
    private String pressId;
    private int editionId;
    private String editionName;
    @JsonIgnore
    private int categoryOrderId;

    public MessageBookVo(Book book, BookCategory bookCategory) {
        id = book.getId();
        name = book.getBookName();
        orderId = book.getOrderId() == null ? 0 : book.getOrderId();
        pressId = book.getCategoryId().toString();
        press = bookCategory == null ? "" : bookCategory.getCategoryName();
        editionId = bookCategory == null ? 0 : bookCategory.getEditionId();
        editionName = bookCategory == null ? "" : bookCategory.getEditionName();
        categoryOrderId = bookCategory == null || bookCategory.getSortCode() == null ? 0 : bookCategory.getSortCode();
    }
}
