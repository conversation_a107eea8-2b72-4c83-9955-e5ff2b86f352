package com.sure.question.vo.baseVo;

import com.sure.question.entity.Region;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class RegionCityVo {
    private long id;
    private String name;
    private List<RegionCountyVo> counties;

    public RegionCityVo(Region r, List<RegionCountyVo> counties) {
        id = r.getId();
        name = r.getName();
        this.counties = counties;
    }
}
