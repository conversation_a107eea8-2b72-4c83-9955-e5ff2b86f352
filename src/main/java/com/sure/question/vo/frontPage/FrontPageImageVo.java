package com.sure.question.vo.frontPage;

import com.sure.question.entity.FrontPageImage;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FrontPageImageVo {
    /**
     * Id
     */
    private String id;
    /**
     * 学段
     */
    private Integer gradeLevel;
    /**
     * 学科
     */
    private Integer subjectId;
    /**
     * 名称
     */
    private String name;
    /**
     * 说明
     */
    private String description;
    /**
     * 图片Url
     */
    private String imageUrl;
    /**
     * 跳转链接
     */
    private String link;
    /**
     * 顺序
     */
    private Integer sortCode;

    public FrontPageImageVo(FrontPageImage entity) {
        this.id = entity.getId();
        this.gradeLevel = entity.getGradeLevel();
        this.subjectId = entity.getSubjectId();
        this.name = entity.getName();
        this.description = entity.getDescription();
        this.imageUrl = entity.getImageUrl();
        this.link = entity.getLink();
        this.sortCode = entity.getSortCode();
    }
}
