package com.sure.question.vo.frontPage;

import com.sure.question.entity.FrontPagePaper;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FrontPagePaperVo {
    /**
     * Id
     */
    private String id;
    /**
     * 学段
     */
    private Integer gradeLevel;
    /**
     * 学科
     */
    private Integer subjectId;
    /**
     * 合集名称
     */
    private String collectionName;
    /**
     * 合集顺序
     */
    private Integer collectionSortCode;
    /**
     * 试卷Id
     */
    private String paperId;
    /**
     * 顺序
     */
    private Integer paperSortCode;
    /**
     * 试卷名称
     */
    private String paperName;
    /**
     * 浏览次数
     */
    private Integer browseCount;

    public FrontPagePaperVo (FrontPagePaper entity) {
        this.id = entity.getId();
        this.gradeLevel = entity.getGradeLevel();
        this.subjectId = entity.getSubjectId();
        this.collectionName = entity.getCollectionName();
        this.collectionSortCode = entity.getCollectionSortCode();
        this.paperId = entity.getPaperId();
        this.paperSortCode = entity.getPaperSortCode();
    }
}
