package com.sure.question.vo.book;

import com.sure.question.entity.Book;
import com.sure.question.entity.BookCategory;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BookVo {
    @ApiModelProperty(value = "主键ID")
    private Integer id;
    @ApiModelProperty(value = "学段ID", example = "2")
    private Integer gradeLevel;
    @ApiModelProperty(value = "学科ID", example = "7")
    private Integer subjectId;
    @ApiModelProperty(value = "版本（含修订版）ID", example = "10")
    private Integer categoryId;
    @ApiModelProperty(value = "版本（含修订版）名称", example = "10")
    private String categoryName;
    @ApiModelProperty(value = "版本ID", example = "10")
    private Integer editionId;
    @ApiModelProperty(value = "版本名称", example = "10")
    private String editionName;
    private Integer revisionYear;
    private Integer publisherId;
    private String publisherName;
    private int categoryOrderId;
    @ApiModelProperty(value = "教材名称", example = "七年级上")
    private String bookName;
    @ApiModelProperty(value = "年级ID", example = "7")
    private Integer gradeId;
    @ApiModelProperty(value = "学期", example = "1")
    private Integer term;
    @ApiModelProperty(value = "排序", example = "3")
    private int orderId;
    @ApiModelProperty(value = "封面")
    private String cover;
    @ApiModelProperty(value = "是否启用教材选题")
    private Boolean enabled;

    public BookVo(Book book, BookCategory bookCategory) {
        id = book.getId();
        gradeLevel = book.getGradeLevel();
        subjectId = book.getSubjectId();
        categoryId = book.getCategoryId();
        bookName = book.getBookName();
        gradeId = book.getGradeId();
        term = book.getTerm();
        orderId = book.getOrderId() == null ? 0 : book.getOrderId();
        enabled = book.getEnabled();
        cover = book.getCover();
        if (bookCategory != null) {
            categoryName = bookCategory.getCategoryName();
            editionId = bookCategory.getEditionId();
            editionName = bookCategory.getEditionName();
            revisionYear = bookCategory.getRevisionYear();
            publisherId = bookCategory.getPublisherId();
            publisherName = bookCategory.getPublisherName();
            categoryOrderId = bookCategory.getSortCode() == null ? 0 : bookCategory.getSortCode();
        }
    }
}
