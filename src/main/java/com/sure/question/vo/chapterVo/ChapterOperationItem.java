package com.sure.question.vo.chapterVo;

import com.sure.question.vo.excelImport.IOperationItem;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ChapterOperationItem implements IOperationItem {
    private Integer id;
    private String name;
    private Integer categoryId;
    private Integer bookId;
    private String fullName;
    private String operationName = "";
    private String operationValue = "";
    private List<KnowledgeOperation> knowledgeOperations = new ArrayList<>();
    private List<ChapterImportVo> rows = new ArrayList<>();

    public ChapterOperationItem(String name, Integer categoryId, Integer bookId, String fullName) {
        this.name = name;
        this.categoryId = categoryId;
        this.bookId = bookId;
        this.fullName = fullName;
    }

    @Data
    public static class KnowledgeOperation {
        private Integer knowledgeId;
        private String knowledgeName;
        private String knowledgeOperation;

        public KnowledgeOperation(String knowledgeName, String knowledgeOperation) {
            this.knowledgeName = knowledgeName;
            this.knowledgeOperation = knowledgeOperation;
        }

        public KnowledgeOperation(Integer knowledgeId, String knowledgeName, String knowledgeOperation) {
            this.knowledgeId = knowledgeId;
            this.knowledgeName = knowledgeName;
            this.knowledgeOperation = knowledgeOperation;
        }
    }
}
