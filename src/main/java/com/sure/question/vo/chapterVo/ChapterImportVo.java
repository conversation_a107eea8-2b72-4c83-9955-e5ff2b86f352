package com.sure.question.vo.chapterVo;

import com.sure.question.vo.excelImport.IImportOperation;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Data
public class ChapterImportVo implements IImportOperation {
    private Integer rowNo;
    private String gradeLevelName;
    private String subjectName;
    private Integer categoryId;
    private String categoryName;
    private Integer bookId;
    private String bookName;
    private String chapter1Name;
    private String chapter2Name;
    private String chapter3Name;
    private String operation;
    private String operationName;
    private String operationValue;
    private String knowledgeName;
    private String knowledgeOperation;

    private static List<Function<ChapterImportVo, String>> getters = Arrays.asList(
            ChapterImportVo::getGradeLevelName,
            ChapterImportVo::getSubjectName,
            ChapterImportVo::getCategoryName,
            ChapterImportVo::getBookName,
            ChapterImportVo::getChapter1Name,
            ChapterImportVo::getChapter2Name,
            ChapterImportVo::getChapter3Name,
            ChapterImportVo::getOperation,
            ChapterImportVo::getKnowledgeName,
            ChapterImportVo::getKnowledgeOperation
            );
    private static List<BiConsumer<ChapterImportVo, String>> setters = Arrays.asList(
            ChapterImportVo::setGradeLevelName,
            ChapterImportVo::setSubjectName,
            ChapterImportVo::setCategoryName,
            ChapterImportVo::setBookName,
            ChapterImportVo::setChapter1Name,
            ChapterImportVo::setChapter2Name,
            ChapterImportVo::setChapter3Name,
            ChapterImportVo::setOperation,
            ChapterImportVo::setKnowledgeName,
            ChapterImportVo::setKnowledgeOperation
            );

    public void clean() {
        IImportOperation.trim(this, getters, setters);
        splitOperationNameValue();
        if (StringUtils.equalsAny(operationName, "移入", "移出") && StringUtils.isNotEmpty(operationValue)) {
            if (!operationValue.startsWith(categoryName)) {
                operationValue = categoryName + operationValue;
            }
        }
    }

    public List<ChapterOperationItem> getOperationItems() {
        List<ChapterOperationItem> list = new ArrayList<>();
        String fullName = categoryName + bookName;
        list.add(new ChapterOperationItem(fullName, categoryId, bookId, fullName));
        fullName += "\\" + chapter1Name;
        list.add(new ChapterOperationItem(chapter1Name, categoryId, bookId, fullName));
        if (StringUtils.isNotBlank(chapter2Name)) {
            fullName += "\\" + chapter2Name;
            list.add(new ChapterOperationItem(chapter2Name, categoryId, bookId, fullName));
        }
        if (StringUtils.isNotBlank(chapter3Name)) {
            fullName += "\\" + chapter3Name;
            list.add(new ChapterOperationItem(chapter3Name, categoryId, bookId, fullName));
        }
        ChapterOperationItem last = list.get(list.size() - 1);
        last.getRows().add(this);
        if (StringUtils.isNotBlank(operationName)) {
            last.setOperationName(operationName);
            last.setOperationValue(operationValue);
        }
        if (StringUtils.isNotBlank(knowledgeName)) {
            last.getKnowledgeOperations().add(new ChapterOperationItem.KnowledgeOperation(knowledgeName, knowledgeOperation));
        }
        return list;
    }
}
