package com.sure.question.vo.chapterVo;

import com.sure.question.vo.excelImport.IImportGradeLevelSubject;
import com.sure.question.vo.excelImport.IImportRow;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Data
public class CopyChapterQuestionImportVo implements IImportGradeLevelSubject, IImportRow {
    private Integer rowNo;
    private String gradeLevelName;
    private String subjectName;
    private Integer srcCategoryId;
    private String srcCategoryName;
    private Integer srcBookId;
    private String srcBookName;
    private String srcChapter1Name;
    private String srcChapter2Name;
    private String srcChapter3Name;
    private Integer srcChapterId;
    private Integer srcQuestionCount;
    private Integer dstCategoryId;
    private String dstCategoryName;
    private Integer dstBookId;
    private String dstBookName;
    private String dstChapter1Name;
    private String dstChapter2Name;
    private String dstChapter3Name;
    private Integer dstChapterId;
    private Integer insertQuestionCount;

    private static List<Function<CopyChapterQuestionImportVo, String>> getters = Arrays.asList(
            CopyChapterQuestionImportVo::getGradeLevelName,
            CopyChapterQuestionImportVo::getSubjectName,
            CopyChapterQuestionImportVo::getSrcCategoryName,
            CopyChapterQuestionImportVo::getSrcBookName,
            CopyChapterQuestionImportVo::getSrcChapter1Name,
            CopyChapterQuestionImportVo::getSrcChapter2Name,
            CopyChapterQuestionImportVo::getSrcChapter3Name,
            CopyChapterQuestionImportVo::getDstCategoryName,
            CopyChapterQuestionImportVo::getDstBookName,
            CopyChapterQuestionImportVo::getDstChapter1Name,
            CopyChapterQuestionImportVo::getDstChapter2Name,
            CopyChapterQuestionImportVo::getDstChapter3Name);
    private static List<BiConsumer<CopyChapterQuestionImportVo, String>> setters = Arrays.asList(
            CopyChapterQuestionImportVo::setGradeLevelName,
            CopyChapterQuestionImportVo::setSubjectName,
            CopyChapterQuestionImportVo::setSrcCategoryName,
            CopyChapterQuestionImportVo::setSrcBookName,
            CopyChapterQuestionImportVo::setSrcChapter1Name,
            CopyChapterQuestionImportVo::setSrcChapter2Name,
            CopyChapterQuestionImportVo::setSrcChapter3Name,
            CopyChapterQuestionImportVo::setDstCategoryName,
            CopyChapterQuestionImportVo::setDstBookName,
            CopyChapterQuestionImportVo::setDstChapter1Name,
            CopyChapterQuestionImportVo::setDstChapter2Name,
            CopyChapterQuestionImportVo::setDstChapter3Name);

    public void clean() {
        for (int i = 0; i < getters.size(); i++) {
            Function<CopyChapterQuestionImportVo, String> getter = getters.get(i);
            BiConsumer<CopyChapterQuestionImportVo, String> setter = setters.get(i);
            String value = getter.apply(this);
            if (StringUtils.isBlank(value)) {
                value = "";
            } else {
                value = StringUtils.trim(value);
            }
            setter.accept(this, value);
        }
    }

    public String getSrcFullChapterName() {
        return getFullName(srcChapter1Name, srcChapter2Name, srcChapter3Name);
    }

    public String getDstFullChapterName() {
        return getFullName(dstChapter1Name, dstChapter2Name, dstChapter3Name);
    }

    private String getFullName(String chapter1Name, String chapter2Name, String chapter3Name) {
        String fullName = chapter1Name;
        if (StringUtils.isNotEmpty(chapter2Name)) {
            fullName += "\\" + chapter2Name;
        }
        if (StringUtils.isNotEmpty(chapter3Name)) {
            fullName += "\\" + chapter3Name;
        }
        return fullName;
    }
}
