package com.sure.question.vo.pushQuesVo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 试卷变式请求参数
 */
@ApiModel("试卷变式请求参数")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaperPushRequestVo {
    /**
     * 试卷 id
     */
    @ApiModelProperty("试卷 id")
    @NotBlank
    private String paperId;
    /**
     * 变式题数量
     */
    @ApiModelProperty("变式题数量")
    @NotNull
    @Min(3)
    @Max(100)
    private Integer pushNum;
    /**
     * 题目 id，存在则只生成该题的变式题
     */
    @ApiModelProperty("题目 id，存在则只生成该题的变式题")
    private String questionId;
}
