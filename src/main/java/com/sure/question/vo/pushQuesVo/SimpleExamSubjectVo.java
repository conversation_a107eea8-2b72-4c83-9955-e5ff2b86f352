package com.sure.question.vo.pushQuesVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 考试科目信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SimpleExamSubjectVo {
    /**
     * 考试 id
     */
    @ApiModelProperty("考试 id")
    private String examId;
    /**
     * 考试名称
     */
    @ApiModelProperty("考试名称")
    private String examName;
    /**
     * 考试范围
     */
    @ApiModelProperty("考试范围")
    private Integer examScope;
    /**
     * 学校 id
     */
    @ApiModelProperty("学校 id")
    private String schoolId;
    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    private String schoolName;
    /**
     * 考试年级
     */
    @ApiModelProperty("考试年级")
    private Integer gradeId;
    /**
     * 考试科目 id
     */
    @ApiModelProperty("考试科目 id")
    private String examSubjectId;
    /**
     * 学科 id
     */
    @ApiModelProperty("学科 id")
    private Integer subjectId;
    /**
     * 学科名称
     */
    @ApiModelProperty("学科名称")
    private String subjectName;
    /**
     * 考试开始时间
     */
    @ApiModelProperty("考试开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;
    /**
     * 考试结束时间
     */
    @ApiModelProperty("考试结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
