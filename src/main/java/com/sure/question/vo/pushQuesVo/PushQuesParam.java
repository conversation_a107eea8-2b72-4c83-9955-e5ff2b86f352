package com.sure.question.vo.pushQuesVo;

import lombok.Data;

import java.util.List;

/**
 * 推题参数
 */
@Data
public class PushQuesParam {
    /**
     * 原题Id
     */
    private String questionId;
    /**
     * 学段
     */
    private int gradeLevel;
    /**
     * 学科
     */
    private int subjectId;
    /**
     * 推题数目
     */
    private int questionCount;
    /**
     * 题目知识点
     */
    private List<Integer> knowledgeIds;
    /**
     * 题型
     */
    private Integer questionTypeId;
    /**
     * 不满足推题数量时是否尝试不限题型
     */
    private boolean tryNoLimitQuestionType;
    /**
     * 难度
     */
    private Integer difficulty;
    /**
     * 得分率
     */
    private Float scoreRate;
    /**
     * 不满足推题数量时是否尝试不限难度
     */
    private boolean tryNoLimitDifficulty;
    /**
     * 年级
     */
    private Integer gradeId;
    /**
     * 不满足推题数量时是否尝试不限年级
     */
    private boolean tryNoLimitGrade;
}
