package com.sure.question.vo.knowledgeMainVo;

import com.sure.question.vo.excelImport.IImportOperation;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Data
public class KnowledgeImportVo implements IImportOperation {
    private Integer rowNo;
    private String gradeLevelName;
    private String subjectName;
    private String knowledge1Name;
    private String knowledge2Name;
    private String knowledge3Name;
    private String operation;
    private String operationName;
    private String operationValue;

    private static List<Function<KnowledgeImportVo, String>> getters = Arrays.asList(
            KnowledgeImportVo::getGradeLevelName,
            KnowledgeImportVo::getSubjectName,
            KnowledgeImportVo::getKnowledge1Name,
            KnowledgeImportVo::getKnowledge2Name,
            KnowledgeImportVo::getKnowledge3Name,
            KnowledgeImportVo::getOperation);
    private static List<BiConsumer<KnowledgeImportVo, String>> setters = Arrays.asList(
            KnowledgeImportVo::setGradeLevelName,
            KnowledgeImportVo::setSubjectName,
            KnowledgeImportVo::setKnowledge1Name,
            KnowledgeImportVo::setKnowledge2Name,
            KnowledgeImportVo::setKnowledge3Name,
            KnowledgeImportVo::setOperation);
    
    public void clean() {
        IImportOperation.trim(this, getters, setters);
        splitOperationNameValue();
    }

    public String getFullKnowledgeName() {
        String name = knowledge1Name;
        if (StringUtils.isNotEmpty(knowledge2Name)) {
            name += "\\" + knowledge2Name;
            if (StringUtils.isNotEmpty(knowledge3Name)) {
                name += "\\" + knowledge3Name;
            }
        }
        return name;
    }

    public List<KnowledgeOperationItem> getOperationItems() {
        List<KnowledgeOperationItem> list = new ArrayList<>();
        String fullName = knowledge1Name;
        list.add(new KnowledgeOperationItem(knowledge1Name, fullName));
        if (StringUtils.isNotBlank(knowledge2Name)) {
            fullName += "\\" + knowledge2Name;
            list.add(new KnowledgeOperationItem(knowledge2Name, fullName));
        }
        if (StringUtils.isNotBlank(knowledge3Name)) {
            fullName += "\\" + knowledge3Name;
            list.add(new KnowledgeOperationItem(knowledge3Name, fullName));
        }
        if (StringUtils.isNotBlank(operationName)) {
            KnowledgeOperationItem last = list.get(list.size() - 1);
            last.setOperationName(operationName);
            last.setOperationValue(operationValue);
        }
        return list;
    }
}
