package com.sure.question.vo.coachstucard;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分析资源包激活统计
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CountStatVo {
    /**
     * 教辅数量
     */
    @ApiModelProperty("教辅数量")
    private Integer bookCount;

    /**
     * 已激活的卡数量
     */
    @ApiModelProperty("已激活的卡数量")
    private Integer cardCount;

    /**
     * 已激活的学校数量
     */
    @ApiModelProperty("已激活的学校数量")
    private Integer schoolCount;
}
