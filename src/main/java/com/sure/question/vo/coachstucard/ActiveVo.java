package com.sure.question.vo.coachstucard;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/24 9:18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("激活状态")
public class ActiveVo {

    @ApiModelProperty("激活状态：0-未激活；1-已激活；2-已过期")
    private Integer status;
    @ApiModelProperty("科目列表")
    private List<String> subjects;

}
