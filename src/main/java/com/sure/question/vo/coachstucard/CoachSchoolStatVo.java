package com.sure.question.vo.coachstucard;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sure.question.entity.CoachBook;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/31 17:03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CoachSchoolStatVo {

    private CoachBook coachBook;
    private String schoolId;
    private String schoolName;
    private Integer activeCount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date latestActiveTime;

}
