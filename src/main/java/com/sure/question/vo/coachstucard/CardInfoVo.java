package com.sure.question.vo.coachstucard;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sure.question.entity.CoachBook;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/15 13:56
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CardInfoVo {

    private Integer id;
    @JsonIgnore
    private Integer coachBookId;
    private CoachBook coachBook;
    private String cardNo;
    private String studentId;
    private String studentName;
    private String schoolId;
    private String schoolName;
    private String parentId;
    private String parentName;
    private String parentMobile;
    private Integer status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date activeTime;

}
