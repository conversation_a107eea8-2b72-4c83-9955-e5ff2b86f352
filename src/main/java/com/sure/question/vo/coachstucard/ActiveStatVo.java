package com.sure.question.vo.coachstucard;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sure.question.entity.CoachBook;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/15 16:06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActiveStatVo {
    private CoachBook coachBook;
    private Integer bookId;
    private Integer totalCount;
    private Integer activeCount;
    private Integer activeSchoolCount;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date latestActiveTime;
}
