package com.sure.question.vo.queslog;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/5/18 9:55
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("试卷日志详情")
public class QuesLogDetailVo {

    private Long id;
    private String userId;
    private String userName;
    @ApiModelProperty("日志类型：1-浏览试卷；2-下载试卷")
    private Integer logType;
    private String schoolId;
    private String schoolName;
    private String paperId;
    private String paperName;
    private String ip;
    private String address;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
