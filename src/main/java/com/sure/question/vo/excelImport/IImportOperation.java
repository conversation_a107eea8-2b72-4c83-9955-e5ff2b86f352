package com.sure.question.vo.excelImport;

import com.sure.common.exception.ApiException;
import com.sure.question.enums.BaseEnum;
import com.sure.question.enums.GradeLevelEnum;
import com.sure.question.util.CacheUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 导入行接口
 */
public interface IImportOperation extends IImportGradeLevelSubject, IImportRow {
    String getOperation();
    String getOperationName();
    void setOperationName(String operationName);
    String getOperationValue();
    void setOperationValue(String operationValue);

    <T extends IOperationItem> List<T> getOperationItems();

    /**
     * 去除导入字符串字段收尾空格
     */
    static <T extends IImportOperation> void trim(T vo, List<Function<T, String>> getters, List<BiConsumer<T, String>> setters) {
        for (int i = 0; i < getters.size(); i++) {
            Function<T, String> getter = getters.get(i);
            BiConsumer<T, String> setter = setters.get(i);
            String value = getter.apply(vo);
            if (value == null) {
                value = "";
            } else {
                value = value.trim();
            }
            setter.accept(vo, value);
        }
    }

    /**
     * 拆分操作名称和操作值
     */
    default void splitOperationNameValue() {
        if (StringUtils.isBlank(getOperation())) {
            setOperationName("");
            setOperationValue("");
            return;
        }
        String[] split = getOperation().split("[:：]", 2);
        setOperationName(split[0].trim());
        setOperationValue(split.length > 1 ? split[1].trim() : "");
    }

    /**
     * 检查学段
     */
    static <T extends IImportGradeLevelSubject & IImportRow> void checkGradeLevel(List<T> vos, int gradeLevelId) {
        checkRuleThrowEx("学段不能为空", vos, vo -> StringUtils.isEmpty(vo.getGradeLevelName()));
        String gradeLevelName = BaseEnum.getNameById(GradeLevelEnum.class, gradeLevelId);
        checkRuleThrowEx("学段与指定学段不一致", vos, vo -> !StringUtils.equals(vo.getGradeLevelName(), gradeLevelName));
    }

    /**
     * 检查学科
     */
    static <T extends IImportGradeLevelSubject & IImportRow> void checkSubject(List<T> vos, int subjectId) {
        checkRuleThrowEx("学科不能为空", vos, vo -> StringUtils.isEmpty(vo.getSubjectName()));
        String subjectName = CacheUtil.getSubjectName(subjectId);
        checkRuleThrowEx("学科与指定学科不一致", vos, vo -> !StringUtils.equals(vo.getSubjectName(), subjectName));
    }

    /**
     * 检查操作
     */
    static <T extends IImportOperation> void checkOperation(List<T> vos, List<String> validOperationNames, List<String> noValueOperationNames) {
        checkRuleThrowEx("未知操作", vos, vo -> StringUtils.isNotEmpty(vo.getOperationName()) && !validOperationNames.contains(vo.getOperationName()));
        checkRuleThrowEx("操作需要值", vos, vo -> StringUtils.isNotEmpty(vo.getOperationName()) && !noValueOperationNames.contains(vo.getOperationName()) && StringUtils.isEmpty(vo.getOperationValue()));
        checkRuleThrowEx("无操作名称", vos, vo -> StringUtils.isEmpty(vo.getOperationName()) && StringUtils.isNotEmpty(vo.getOperationValue()));
    }

    /**
     * 检查是否满足规则，不满足的报错
     */
    static <T extends IImportRow> void checkRuleThrowEx(String error, List<T> vos, Predicate<T> predicate) {
        IOperationItem.throwLongEx(error, vos, vo -> "第 " + vo.getRowNo() + " 行", 100, predicate);
    }

    /**
     * 构建操作树
     * @param importVos 导入行
     * @param mergeFunc 处理同一节点的多个导入行的合并函数
     */
    static <T extends IImportOperation, R extends IOperationItem> List<TreeNode<R>> buildOperationItemTree(Collection<T> importVos, BiConsumer<R, R> mergeFunc) {
        List<TreeNode<R>> topNodes = new ArrayList<>();

        for (T vo : importVos) {
            List<R> items = vo.getOperationItems();

            TreeNode<R> parent = null;
            List<TreeNode<R>> nodes = topNodes;
            int idx = 0;
            for (R item : items) {
                idx++;
                TreeNode<R> node = nodes.stream().filter(x -> StringUtils.equals(x.getItem().getName(), item.getName()))
                        .findFirst().orElse(null);
                if (node == null) {
                    node = new TreeNode<>(item);
                    node.setParent(parent);
                    nodes.add(node);
                } else if (idx == items.size()) {
                    if (!node.getChildren().isEmpty()) {
                        node.setItem(item);
                    } else if (mergeFunc != null) {
                        mergeFunc.accept(node.getItem(), item);
                    } else {
                        throw new ApiException("重复：" + item.getFullName());
                    }
                }

                parent = node;
                nodes = node.getChildren();
            }
        }

        return topNodes;
    }
}
