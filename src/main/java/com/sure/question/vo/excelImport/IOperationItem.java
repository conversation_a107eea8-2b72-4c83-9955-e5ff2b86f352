package com.sure.question.vo.excelImport;

import com.sure.common.exception.ApiException;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 操作树节点元素接口
 */
public interface IOperationItem {
    Integer getId();
    void setId(Integer id);
    String getName();
    String getFullName();
    String getOperationName();
    void setOperationName(String operationName);
    String getOperationValue();

    /**
     * 根据名称设置操作树节点元素Id
     */
    static <T extends IOperationItem, R> void addOperationItemTreeIdThrowEx(List<TreeNode<T>> operationItemNodes,
                                                                            List<TreeNode<R>> dbNodes,
                                                                            Function<R, Integer> idGetter,
                                                                            Function<R, String> nameGetter,
                                                                            String type) {
        List<TreeNode<R>> notInExcelDBNodes = new ArrayList<>();
        addOperationItemTreeId(operationItemNodes, dbNodes, idGetter, nameGetter, notInExcelDBNodes);
        if (!notInExcelDBNodes.isEmpty()) {
            throwLongEx("excel中未包含所有系统" + type, notInExcelDBNodes, node -> node.getFullName(nameGetter), 100);
        }
    }
    static <T extends IOperationItem, R> void addOperationItemTreeId(List<TreeNode<T>> operationItemNodes,
                                                                     List<TreeNode<R>> dbNodes,
                                                                     Function<R, Integer> idGetter,
                                                                     Function<R, String> nameGetter,
                                                                     List<TreeNode<R>> notInExcelDbNodes) {
        // 分层递归添加
        for (TreeNode<T> operationItemNode : operationItemNodes) {
            String name = operationItemNode.getItem().getName();
            TreeNode<R> dbNode = dbNodes.stream().filter(x -> nameGetter.apply(x.getItem()).equals(name))
                    .findFirst().orElse(null);
            if (dbNode != null) {
                operationItemNode.getItem().setId(idGetter.apply(dbNode.getItem()));
                addOperationItemTreeId(operationItemNode.getChildren(), dbNode.getChildren(), idGetter, nameGetter, notInExcelDbNodes);
                dbNodes.remove(dbNode);
            }
        }
        notInExcelDbNodes.addAll(dbNodes);
    }

    /**
     * 检查操作
     */
    static <T extends IOperationItem> void checkOperation(List<TreeNode<T>> topNodes, String type) {
        fillOperationAdd(topNodes);
        List<TreeNode<T>> flatNodes = topNodes.stream()
                .flatMap(importNode -> importNode.getFlatNodes().stream()).collect(Collectors.toList());
        checkNoIdNodeOperation(flatNodes, type);
        checkHasIdNodeOperation(flatNodes, type);
        checkHasIdNodeUnknownOperation(flatNodes, type);
        checkAdd(flatNodes, type);
        checkDelete(flatNodes, type);
        checkRename(flatNodes);
        checkMoveOut(flatNodes, topNodes, type);
        checkMoveIn(flatNodes, topNodes, type);
        if (type.equals("知识点")) {
            checkSplit(flatNodes, topNodes, type);
            checkMerge(flatNodes, topNodes, type);
        }
    }

    /**
     * 所有子节点为新增、移入、合并的，父节点操作设为新增
     */
    static <T extends IOperationItem> void fillOperationAdd(List<TreeNode<T>> nodes) {
        List<String> childOperationNames = Arrays.asList("新增", "移入", "合并");
        for (TreeNode<T> node : nodes) {
            fillOperationAdd(node.getChildren());
            if (node.getItem().getId() == null && StringUtils.isBlank(node.getItem().getOperationName()) && !node.getChildren().isEmpty()) {
                if (node.getChildren().stream().allMatch(child -> childOperationNames.contains(child.getItem().getOperationName()))) {
                    node.getItem().setOperationName("新增");
                }
            }
        }
    }

    /**
     * 检查不存在的元素的操作
     */
    static <T extends IOperationItem> void checkNoIdNodeOperation(List<TreeNode<T>> nodes, String type) {
        List<String> validOperationNames = new ArrayList<>(Arrays.asList("新增", "移入"));
        if (type.equals("知识点")) {
            validOperationNames.add("合并");
        }
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getId() == null && !validOperationNames.contains(node.getItem().getOperationName());
        checkNodesThrowEx(nodes, predicate, "系统不存在的" + type + "对应操作只能是" + StringUtils.join(validOperationNames, "或"));
    }

    /**
     * 检查存在的元素不允许的操作
     */
    static <T extends IOperationItem> void checkHasIdNodeOperation(List<TreeNode<T>> nodes, String type) {
        List<String> invalidOperationNames = Arrays.asList("新增", "移入");
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getId() != null && invalidOperationNames.contains(node.getItem().getOperationName());
        checkNodesThrowEx(nodes, predicate, "系统已存在的" + type + "对应操作不能是" + StringUtils.join(invalidOperationNames, "或"));
    }

    /**
     * 检查存在的元素的未知操作
     */
    static <T extends IOperationItem> void checkHasIdNodeUnknownOperation(List<TreeNode<T>> nodes, String type) {
        List<String> validOperationNames = new ArrayList<>(Arrays.asList("删除", "改名称", "移出"));
        if (type.equals("知识点")) {
            validOperationNames.add("合并");
            validOperationNames.add("拆分");
        }
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getId() != null
                && StringUtils.isNotEmpty(node.getItem().getOperationName())
                && !validOperationNames.contains(node.getItem().getOperationName());
        checkNodesThrowEx(nodes, predicate, "未知操作");
    }

    /**
     * 检查新增操作
     */
    static <T extends IOperationItem> void checkAdd(List<TreeNode<T>> nodes, String type) {
        List<String> validOperationNames = new ArrayList<>(Arrays.asList("新增", "移入"));
        if (type.equals("知识点")) {
            validOperationNames.add("合并");
        }
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getOperationName().equals("新增")
                && !node.getChildren().stream().allMatch(child -> validOperationNames.contains(child.getItem().getOperationName()));
        checkNodesThrowEx(nodes, predicate, "新增上级" + type + "，其所有下级" + type + "操作必须是" + StringUtils.join(validOperationNames, "或"));
    }

    /**
     * 检查删除操作
     */
    static <T extends IOperationItem> void checkDelete(List<TreeNode<T>> nodes, String type) {
        List<String> validOperationNames = new ArrayList<>(Arrays.asList("删除", "移出"));
        if (type.equals("知识点")) {
            validOperationNames.add("拆分");
        }
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getOperationName().equals("删除") && !node.getChildren().stream().allMatch(child -> {
            // 拆分不包含自身
            if (child.getItem().getOperationName().equals("拆分")) {
                String fullName = child.getItem().getFullName();
                return !splitOperationValueFullNames(child.getItem().getOperationValue()).contains(fullName);
            }
            return validOperationNames.contains(child.getItem().getOperationName());
        });
        checkNodesThrowEx(nodes, predicate, "删除上级" + type + "，其所有下级" + type + "操作必须是" + StringUtils.join(validOperationNames, "或"));
    }

    /**
     * 检查重命名操作
     */
    static <T extends IOperationItem> void checkRename(List<TreeNode<T>> nodes) {
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getOperationName().equals("改名称") &&
                StringUtils.equals(node.getItem().getName(), node.getItem().getOperationValue());
        checkNodesThrowEx(nodes, predicate, "改名称操作新名称不能与旧名称相同");
    }

    /**
     * 检查移出操作
     */
    static <T extends IOperationItem> void checkMoveOut(List<TreeNode<T>> nodes, List<TreeNode<T>> topNodes, String type) {
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getOperationName().equals("移出") && checkMoveInOutNotInPair(node, topNodes);
        checkNodesThrowEx(nodes, predicate, "移出的" + type + "必须对应移入");

        List<String> validOperationNames = Arrays.asList("删除", "移出");
        predicate = node -> node.getItem().getOperationName().equals("移出")
                && !node.getChildren().stream().allMatch(child -> validOperationNames.contains(child.getItem().getOperationName()));
        checkNodesThrowEx(nodes, predicate, "移出上级" + type + "，其所有下级" + type + "操作必须是移出或删除");
    }

    /**
     * 检查移入操作
     */
    static <T extends IOperationItem> void checkMoveIn(List<TreeNode<T>> nodes, List<TreeNode<T>> topNodes, String type) {
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getOperationName().equals("移入") && checkMoveInOutNotInPair(node, topNodes);
        checkNodesThrowEx(nodes, predicate, "移入的" + type + "必须对应移出");
    }

    /**
     * 检查移入移出是否配对
     */
    static <T extends IOperationItem> boolean checkMoveInOutNotInPair(TreeNode<T> node, List<TreeNode<T>> topNodes) {
        String thisOperationName = node.getItem().getOperationName();
        String thisOperationValue = node.getItem().getOperationValue();
        TreeNode<T> another = findNodeByFullName(thisOperationValue, topNodes);
        if (another == null) {
            return true;
        }
        String anotherOperationName = another.getItem().getOperationName();
        String anotherOperationValue = another.getItem().getOperationValue();
        if (thisOperationName.equals("移入") && !anotherOperationName.equals("移出")) {
            return true;
        }
        if (thisOperationName.equals("移出") && !anotherOperationName.equals("移入")) {
            return true;
        }
        String thisFullName = node.getItem().getFullName();
        return !StringUtils.equals(thisFullName, anotherOperationValue);
    }

    /**
     * 检查拆分操作
     */
    static <T extends IOperationItem> void checkSplit(List<TreeNode<T>> nodes, List<TreeNode<T>> topNodes, String type) {
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getOperationName().equals("拆分") && !node.getChildren().isEmpty();
        checkNodesThrowEx(nodes, predicate, "含有下级的" + type + "不能拆分");

        predicate = node -> node.getItem().getOperationName().equals("拆分") && checkOperationValueFullNamesDuplicate(node);
        checkNodesThrowEx(nodes, predicate, "拆分的" + type + "不能重复");

        predicate = node -> node.getItem().getOperationName().equals("拆分") && checkSplitMergeNotInPair(node, topNodes);
        checkNodesThrowEx(nodes, predicate, "拆分的" + type + "必须对应合并");
    }

    /**
     * 检查合并操作
     */
    static <T extends IOperationItem> void checkMerge(List<TreeNode<T>> nodes, List<TreeNode<T>> topNodes, String type) {
        Predicate<TreeNode<T>> predicate = node -> node.getItem().getOperationName().equals("合并") && !node.getChildren().isEmpty();
        checkNodesThrowEx(nodes, predicate, "含有下级的" + type + "不能合并");

        predicate = node -> node.getItem().getOperationName().equals("合并") && checkOperationValueFullNamesDuplicate(node);
        checkNodesThrowEx(nodes, predicate, "合并的" + type + "不能重复");

        predicate = node -> node.getItem().getOperationName().equals("合并") && checkSplitMergeNotInPair(node, topNodes);
        checkNodesThrowEx(nodes, predicate, "合并的" + type + "必须对应拆分");
    }

    /**
     * 将操作值拆分为若干个完整名称
     */
    static List<String> splitOperationValueFullNames(String operationValue) {
        return Arrays.stream(operationValue.split("[;；]")).map(StringUtils::trim).collect(Collectors.toList());
    }

    /**
     * 检查拆分、合并操作的值不能重复
     */
    static <T extends IOperationItem> boolean checkOperationValueFullNamesDuplicate(TreeNode<T> node) {
        List<String> fullNames = splitOperationValueFullNames(node.getItem().getOperationValue());
        return fullNames.size() != new HashSet<>(fullNames).size();
    }

    /**
     * 检查合并拆分是否配对
     */
    static <T extends IOperationItem> boolean checkSplitMergeNotInPair(TreeNode<T> node, List<TreeNode<T>> topNodes) {
        String thisOperationName = node.getItem().getOperationName();
        String thisOperationValue = node.getItem().getOperationValue();
        String thisFullName = node.getItem().getFullName();
        List<String> thisOperationValueFullNames = splitOperationValueFullNames(thisOperationValue);
        for (String fullName : thisOperationValueFullNames) {
            if (StringUtils.equals(thisFullName, fullName)) {
                if (thisOperationName.equals("拆分")) {
                    continue;
                } else {
                    return true;
                }
            }
            TreeNode<T> another = findNodeByFullName(fullName, topNodes);
            if (another == null) {
                return true;
            }
            String anotherOperationName = another.getItem().getOperationName();
            String anotherOperationValue = another.getItem().getOperationValue();
            if (thisOperationName.equals("拆分") && !anotherOperationName.equals("合并")) {
                return true;
            }
            if (thisOperationName.equals("合并") && !anotherOperationName.equals("拆分")) {
                return true;
            }
            List<String> anotherOperationValueFullNames = splitOperationValueFullNames(anotherOperationValue);
            if (!anotherOperationValueFullNames.contains(thisFullName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查节点并以完整名称抛出异常
     */
    static <T extends IOperationItem> void checkNodesThrowEx(List<TreeNode<T>> nodes, Predicate<TreeNode<T>> predicate, String error) {
        nodes = nodes.stream().filter(predicate).collect(Collectors.toList());
        if (nodes.isEmpty()) {
            return;
        }
        throwLongEx(error, nodes, node -> node.getItem().getFullName(), 10);
    }

    /**
     * 检查列表抛出异常，限制异常消息最大行数
     */
    static <T> void throwLongEx(String error, List<T> list, Function<T, String> nameGetter, int maxSize, Predicate<T> predicate) {
        list = list.stream().filter(predicate).collect(Collectors.toList());
        if (list.isEmpty()) {
            return;
        }
        throwLongEx(error, list, nameGetter, maxSize);
    }

    static <T> void throwLongEx(String error, List<T> list, Function<T, String> nameGetter, int maxSize) {
        String names;
        if (list.size() <= maxSize) {
            names = list.stream().map(nameGetter).collect(Collectors.joining("\n"));
        } else {
            names = list.subList(0, maxSize).stream().map(nameGetter).collect(Collectors.joining("\n"));
            names += "\n......";
        }
        throw new ApiException(error + "：（共" + list.size() + "个）\n" + names);
    }

    /**
     * 根据完整名称在树中找节点
     */
    static <T extends IOperationItem> TreeNode<T> findNodeByFullName(String fullName, List<TreeNode<T>> topNodes) {
        if (StringUtils.isBlank(fullName)) {
            return null;
        }
        String[] names = fullName.split("\\\\");
        List<TreeNode<T>> nodes = topNodes;
        for (int i = 0; i < names.length; i++) {
            String name = names[i];
            TreeNode<T> node = nodes.stream().filter(x -> name.equals(x.getItem().getName())).findFirst().orElse(null);
            if (node == null) {
                return null;
            }
            if (i == names.length - 1) {
                return node;
            } else {
                nodes = node.getChildren();
            }
        }
        return null;
    }
}
