package com.sure.question.vo.excelImport;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 树节点
 */

@Data
@NoArgsConstructor
public class TreeNode<T> {
    private T item;
    private TreeNode<T> parent;
    private List<TreeNode<T>> children = new ArrayList<>();

    public TreeNode(T item) {
        this.item = item;
    }

    /**
     * 自身及下属节点平铺列表
     */
    public List<TreeNode<T>> getFlatNodes() {
        List<TreeNode<T>> result = new ArrayList<>();
        getFlatNodes(this, result);
        return result;
    }
    private void getFlatNodes(TreeNode<T> node, List<TreeNode<T>> list) {
        list.add(node);
        node.children.forEach(child -> getFlatNodes(child, list));
    }

    /**
     * 所有树节点元素平铺列表
     */
    public static <T> List<T> getFlatElements(List<TreeNode<T>> tree) {
        return tree.stream().flatMap(node -> node.getFlatNodes().stream()).map(TreeNode::getItem).collect(Collectors.toList());
    }

    /**
     * 由自身Id和父Id构建树
     */
    public static <T> List<TreeNode<T>> buildTree(Collection<T> records, Function<T, Object> idGetter, Function<T, Object> parentIdGetter) {
        Map<Object, TreeNode<T>> nodeMap = records.stream().collect(Collectors.toMap(idGetter, TreeNode::new));

        nodeMap.forEach((id, node) -> {
            Object parentId = parentIdGetter.apply(node.getItem());
            if (parentId != null) {
                TreeNode<T> parent = nodeMap.get(parentId);
                if (parent != null) {
                    node.setParent(parent);
                    parent.getChildren().add(node);
                }
            }
        });

        return nodeMap.values().stream().filter(node -> node.getParent() == null).collect(Collectors.toList());
    }

    /**
     * 由自身Id和父Id构建树, 并排序
     */
    public static <T> List<TreeNode<T>> buildTree(Collection<T> records, Function<T, Object> idGetter, Function<T, Object> parentIdGetter, Comparator<T> comparator) {
        List<TreeNode<T>> topNodes = buildTree(records, idGetter, parentIdGetter);
        topNodes.forEach(node -> node.sortChildren(comparator));
        topNodes.sort((a, b) -> comparator.compare(a.getItem(), b.getItem()));
        return topNodes;
    }

    private void sortChildren(Comparator<T> comparator) {
        children.sort((a, b) -> comparator.compare(a.getItem(), b.getItem()));
        children.forEach(child -> child.sortChildren(comparator));
    }

    /**
     * 给定节点名称getter，返回从根节点到该节点的完整名称，以反斜杠间隔
     */
    public String getFullName(Function<T, String> nameGetter) {
        StringBuilder sb = new StringBuilder();
        TreeNode<T> node = this;
        while (node != null) {
            sb.insert(0, "\\" + nameGetter.apply(node.item));
            node = node.parent;
        }
        return sb.substring(1);
    }

    /**
     * 创建变更后的新列表及包含删除元素的完整列表
     */
    public static <T> void buildNewList(List<T> addList, List<T> updateList, List<T> deleteList, List<T> fullList, List<T> newList,
                                        Collection<T> dbList, Function<T, Object> idGetter, Function<T, Object> pIdGetter, Comparator<T> comparator) {
        // 无序
        List<T> noOrderFullList = new ArrayList<>(addList);
        noOrderFullList.addAll(updateList);
        Set<Object> updateIds = updateList.stream().map(idGetter).collect(Collectors.toSet());
        for (T t : dbList) {
            if (!updateIds.contains(idGetter.apply(t))) {
                noOrderFullList.add(t);
            }
        }

        // 排序
        List<TreeNode<T>> tree = TreeNode.buildTree(noOrderFullList, idGetter, pIdGetter, comparator);
        Set<Object> deleteIds = deleteList.stream().map(idGetter).collect(Collectors.toSet());
        for (TreeNode<T> topNode : tree) {
            topNode.getFlatNodes().stream().map(TreeNode::getItem).forEach(t -> {
                fullList.add(t);
                if (!deleteIds.contains(idGetter.apply(t))) {
                    newList.add(t);
                }
            });
        }
    }
}
