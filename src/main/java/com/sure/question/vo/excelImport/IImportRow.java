package com.sure.question.vo.excelImport;

import java.util.List;

/**
 * 行号接口
 */
public interface IImportRow {
    Integer getRowNo();
    void setRowNo(Integer rowNo);

    /**
     * 设置连续行号
     */
    static <T extends IImportRow>  void setSequentialRowNo(List<T> rows, int startRowNo) {
        int rowNo = startRowNo;
        for (T row : rows) {
            row.setRowNo(rowNo++);
        }
    }
}
