package com.sure.question.vo.ques;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/24 9:29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Validated
@ApiModel("根据知识点查询试题")
public class QuesByKnowVo {

    @NotEmpty(message = "知识点ID列表不能为空")
    @ApiModelProperty(value = "知识点ID列表", required = true)
    private List<Integer> knowIds = new ArrayList<>();
    @ApiModelProperty(value = "试卷ID列表")
    private List<String> paperIds = new ArrayList<>();

}
