package com.sure.question.vo.ingestion;

import com.sure.question.entity.IngestionPageOcr;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 题目划分识别结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IngestionQuestionOcrVo {
    private Integer ingestionId;
    private String pageImageUrl;
    private String questionOcrStatus;
    private String questionOcrResult;

    public IngestionQuestionOcrVo(IngestionPageOcr pageOcr) {
        ingestionId = pageOcr.getIngestionId();
        pageImageUrl = pageOcr.getPageImageUrl();
        questionOcrStatus = pageOcr.getQuestionOcrStatus();
        questionOcrResult = pageOcr.getQuestionOcrResult();
    }
}
