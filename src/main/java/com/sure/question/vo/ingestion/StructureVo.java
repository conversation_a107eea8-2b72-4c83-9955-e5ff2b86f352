package com.sure.question.vo.ingestion;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StructureVo {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 结构类型
     */
    private String structureType;

    /**
     * 结构名称
     */
    private String structureName;

    /**
     * 结构页面序号
     */
    private List<Integer> pageNumbers;

    /**
     * 结构页面图片Url
     */
    private List<String> pageImageUrls;

    /**
     * 父结构Id
     */
    @JsonIgnore
    private Long structureParentId;

    /**
     * 子结构
     */
    private List<StructureVo> children = new ArrayList<>();
}
