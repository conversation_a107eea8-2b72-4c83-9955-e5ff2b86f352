package com.sure.question.vo.ingestion;

import com.sure.question.dto.coachBook.ingestion.PageImage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IngestionVo {
    private Integer id;
    private Integer coachBookId;
    private Integer gradeLevel;
    private Integer subjectId;
    private String fileType;
    private List<String> filePath;
    private List<PageImage> pageImages;
    private Integer pdfPageCount;
    private String pdfToImageStatus;
}
