package com.sure.question.vo.ingestion;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sure.question.dto.coachBook.ingestion.PageArea;
import com.sure.question.entity.IngestionPaper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class IngestionPaperVo {
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paperId;
    private Integer ingestionId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long mainBodyStructureId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long answerStructureId;
    private List<PageArea> mainBodyAreas = new ArrayList<>();
    private List<PageArea> answerAreas = new ArrayList<>();
    private Boolean confirmed;

    public IngestionPaperVo(IngestionPaper paper) {
        paperId = paper.getPaperId();
        ingestionId = paper.getIngestionId();
        mainBodyStructureId = paper.getMainBodyStructureId();
        answerStructureId = paper.getAnswerStructureId();
        if (StringUtils.isNotEmpty(paper.getMainBodyAreas())) {
            mainBodyAreas = JSON.parseArray(paper.getMainBodyAreas(), PageArea.class);
        }
        if (StringUtils.isNotEmpty(paper.getAnswerAreas())) {
            answerAreas = JSON.parseArray(paper.getAnswerAreas(), PageArea.class);
        }
        confirmed = paper.getConfirmed();
    }
}
