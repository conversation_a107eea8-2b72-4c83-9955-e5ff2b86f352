package com.sure.question.vo.ingestion;

import com.sure.question.entity.IngestionPageOcr;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 整页识别结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IngestionPageOcrVo {
    private Integer ingestionId;
    private String pageImageUrl;
    private String pageOcrStatus;
    private String pageOcrResult;

    public IngestionPageOcrVo(IngestionPageOcr pageOcr) {
        ingestionId = pageOcr.getIngestionId();
        pageImageUrl = pageOcr.getPageImageUrl();
        pageOcrStatus = pageOcr.getPageOcrStatus();
        pageOcrResult = pageOcr.getPageOcrResult();
    }
}
