package com.sure.question.vo.ingestion;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sure.question.entity.CoachBookIngestionTocLlm;
import com.sure.question.enums.CommonProgressStatusEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@NoArgsConstructor
public class TocRecognizeVo {
    private Integer ingestionId;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long structureId;
    private String status;
    private List<TocRecognizeResultVo> result;
    private String errorMessage;

    public TocRecognizeVo(CoachBookIngestionTocLlm tocLlm) {
        ingestionId = tocLlm.getIngestionId();
        structureId = tocLlm.getStructureId();
        status = tocLlm.getLlmStatus();
        if (StringUtils.isNotEmpty(tocLlm.getLlmResult())) {
            result = JSON.parseArray(tocLlm.getLlmResult(), TocRecognizeResultVo.class);
        }
        if (CommonProgressStatusEnum.FAILED.getId().equals(status)) {
            errorMessage = tocLlm.getErrorMessage();
        }
    }
}
