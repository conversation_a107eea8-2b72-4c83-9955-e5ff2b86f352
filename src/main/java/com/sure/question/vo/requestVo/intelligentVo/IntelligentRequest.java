package com.sure.question.vo.requestVo.intelligentVo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IntelligentRequest {
    private Integer difficulty;
    private Long region;
    private Integer stage;
    private Integer subject;
    private List<Integer> chapters = new ArrayList<>();
    private List<Integer> knowledges = new ArrayList<>();
    private List<ReqTopic> topics = new ArrayList<>();
}
