package com.sure.question.vo.requestVo.intelligentVo;

import com.sure.common.exception.ApiException;
import com.sure.question.common.Constants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@ApiModel("错题训练")
public class WrongQuesTrain {
    /**
     * 错题列表
     */
    @ApiModelProperty("错题列表")
    @NotEmpty
    private List<QuestionScoreRateVo> questions;

    /**
     * 训练模式：错题重做(redo)，错题拓展(expand)
     */
    @ApiModelProperty("训练模式：redo-错题重做; expand-错题拓展")
    @NotBlank
    private String trainType;

    /**
     * 规范化参数
     */
    public void normalize() {
        trainType = trainType.toLowerCase();
        if (!StringUtils.equalsAny(trainType, Constants.WRONGQUESTRAIN_TYPE_REDO, Constants.WRONGQUESTRAIN_TYPE_EXPAND)) {
            throw new ApiException("训练模式参数错误");
        }
        Set<String> questionIds = new HashSet<>();
        questions.forEach(q -> {
            if (questionIds.contains(q.getQuestionId())) {
                throw new ApiException("题目不能重复");
            } else {
                questionIds.add(q.getQuestionId());
            }
        });
    }
}
