package com.sure.question.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> teng
 * @date 2021/9/25 12:41
 */
@Slf4j
@Component
public class RedisUtil {

    private RedisTemplate<String, Object> redisTemplate;
    private static RedisUtil redisUtil;

    public RedisUtil(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @PostConstruct
    public void init() {
        redisUtil = this;
        redisUtil.redisTemplate = redisTemplate;
    }

    // =============================common============================

    /**
     * 指定缓存失效时间
     *
     * @param key     键 不能为null
     * @param seconds 过期时间
     */
    public static Boolean expire(String key, long seconds) {
        try {
            return redisUtil.redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error(String.format("RedisUtil expire error. Param, key: %s, seconds: %d", key, seconds), e);
            throw new RuntimeException(String.format("RedisUtil expire error. Param, key: %s, seconds: %d", key, seconds));
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回 0 代表永久有效
     */
    public static Long getExpire(String key) {
        try {
            return redisUtil.redisTemplate.getExpire(key, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error(String.format("RedisUtil getExpire error. Param, key: %s", key), e);
            throw new RuntimeException(String.format("RedisUtil getExpire error. Param, key: %s", key));
        }
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public static Boolean hasKey(String key) {
        try {
            return redisUtil.redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hasKey error. Param, key: %s", key), e);
            throw new RuntimeException(String.format("RedisUtil hasKey error. Param, key: %s", key));
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    public static Long delete(String... key) {
        try {
            return redisUtil.redisTemplate.delete(Arrays.asList(key));
        } catch (Exception e) {
            log.error(String.format("RedisUtil delete error. Param, key: %s", Arrays.asList(key)), e);
            throw new RuntimeException(String.format("RedisUtil delete error. Param, key: %s", Arrays.asList(key)));
        }
    }

    // ============================String=============================

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public static Object get(String key) {
        try {
            return redisUtil.redisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtil get error. Param, key: %s", key), e);
            throw new RuntimeException(String.format("RedisUtil get error. Param, key: %s", key));
        }
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     */
    public static void set(String key, Object value) {
        try {
            redisUtil.redisTemplate.opsForValue().set(key, value);
        } catch (Exception e) {
            log.error(String.format("RedisUtil set error. Param, key: %s, value: %s", key, value), e);
            throw new RuntimeException(String.format("RedisUtil set error. Param, key: %s, value: %s", key, value));
        }
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key     键
     * @param value   值
     * @param seconds 时间(秒) time要大于0 如果time小于等于0 将设置无限期
     */
    public static void set(String key, Object value, long seconds) {
        try {
            redisUtil.redisTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error(String.format("RedisUtil set error. Param, key: %s, value: %s, seconds: %d", key, value, seconds), e);
            throw new RuntimeException(String.format("RedisUtil set error. Param, key: %s, value: %s, seconds: %d", key, value, seconds));
        }
    }

    /**
     * 如果不存在就设值
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static Boolean setIfAbsent(String key, Object value) {
        try {
            return redisUtil.redisTemplate.opsForValue().setIfAbsent(key, value);
        } catch (Exception e) {
            log.error(String.format("RedisUtil setIfAbsent error. Param, key: %s, value: %s", key, value), e);
            throw new RuntimeException(String.format("RedisUtil setIfAbsent error. Param, key: %s, value: %s", key, value));
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 要增加几
     * @return 增加后的值
     */
    public static Long increment(String key, long delta) {
        try {
            return redisUtil.redisTemplate.opsForValue().increment(key, delta);
        } catch (Exception e) {
            log.error(String.format("RedisUtil incr error. Param, key: %s, delta: %d", key, delta), e);
            throw new RuntimeException(String.format("RedisUtil incr error. Param, key: %s, delta: %d", key, delta));
        }
    }

    // ================================Hash=================================

    /**
     * Hash Fields
     *
     * @param key 键 不能为null
     * @return hash的key集合
     */
    public static Set<Object> hKeys(String key) {
        try {
            return redisUtil.redisTemplate.opsForHash().keys(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hKeys error. Param, key: %s", key), e);
            throw new RuntimeException(String.format("RedisUtil hKeys error. Param, key: %s", key));
        }
    }

    /**
     * HashGet
     *
     * @param key     键 不能为null
     * @param hashKey 项 不能为null
     * @return 值
     */
    public static Object hGet(String key, Object hashKey) {
        try {
            return redisUtil.redisTemplate.opsForHash().get(key, hashKey);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hGet error. Param, key: %s, hashKey: %s", key, hashKey), e);
            throw new RuntimeException(String.format("RedisUtil hGet error. Param, key: %s, hashKey: %s", key, hashKey));
        }
    }

    /**
     * 获取hash 指定 hashKey集合对应的值的列表
     *
     * @param key      key
     * @param hashKeys hashKeys
     * @return 值列表
     */
    public static List<Object> hMultiGet(String key, Collection<Object> hashKeys) {
        try {
            return redisUtil.redisTemplate.opsForHash().multiGet(key, hashKeys);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hMultiGet error. Param, key: %s, hashKeys: %s", key, hashKeys), e);
            throw new RuntimeException(String.format("RedisUtil hMultiGet error. Param, key: %s, hashKeys: %s", key, hashKeys));
        }
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public static Map<Object, Object> hEntries(String key) {
        try {
            return redisUtil.redisTemplate.opsForHash().entries(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hEntries error. Param, key: %s", key), e);
            throw new RuntimeException(String.format("RedisUtil hEntries error. Param, key: %s", key));
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key     键
     * @param hashKey 项
     * @param value   值
     */
    public static void hPut(String key, Object hashKey, Object value) {
        try {
            redisUtil.redisTemplate.opsForHash().put(key, hashKey, value);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hSet error. Param, key: %s, hashKey: %s, value: %s", key, hashKey, value), e);
            throw new RuntimeException(String.format("RedisUtil hSet error. Param, key: %s, hashKey: %s, value: %s", key, hashKey, value));
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key     键
     * @param hashKey 项
     * @param value   值
     * @param seconds 时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     */
    public static void hPut(String key, Object hashKey, Object value, long seconds) {
        try {
            redisUtil.redisTemplate.opsForHash().put(key, hashKey, value);
            expire(key, seconds);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hSet error. Param, key: %s, hashKey: %s, value: %s, seconds: %d", key, hashKey, value, seconds), e);
            throw new RuntimeException(String.format("RedisUtil hSet error. Param, key: %s, hashKey: %s, value: %s, seconds: %d", key, hashKey, value, seconds));
        }
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     */
    public static void hPutAll(String key, Map<Object, Object> map) {
        try {
            redisUtil.redisTemplate.opsForHash().putAll(key, map);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hPutAll error. Param, key: %s, map: %s", key, map.toString()), e);
            throw new RuntimeException(String.format("RedisUtil hPutAll error. Param, key: %s, map: %s", key, map));
        }
    }

    /**
     * HashSet 并设置时间
     *
     * @param key     键
     * @param map     对应多个键值
     * @param seconds 时间(秒)
     */
    public static void hPutAll(String key, Map<Object, Object> map, long seconds) {
        try {
            redisUtil.redisTemplate.opsForHash().putAll(key, map);
            expire(key, seconds);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hPutAll error. Param, key: %s, map: %s, seconds: %d", key, map.toString(), seconds), e);
            throw new RuntimeException(String.format("RedisUtil hPutAll error. Param, key: %s, map: %s, seconds: %d", key, map, seconds));
        }
    }

    /**
     * 删除hash表中的值
     *
     * @param key      键 不能为null
     * @param hashKeys 项 可以使多个 不能为null
     */
    public static long hDelete(String key, Object... hashKeys) {
        try {
            return redisUtil.redisTemplate.opsForHash().delete(key, hashKeys);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hDel error. Param, key: %s, hashKeys: %s", key, Arrays.asList(hashKeys)), e);
            throw new RuntimeException(String.format("RedisUtil hDel error. Param, key: %s, hashKeys: %s", key, Arrays.asList(hashKeys)));
        }
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key     键 不能为null
     * @param hashKey 项 不能为null
     * @return true 存在 false不存在
     */
    public static boolean hHasKey(String key, Object hashKey) {
        try {
            return redisUtil.redisTemplate.opsForHash().hasKey(key, hashKey);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hHasKey error. Param, key: %s, hashKey: %s", key, hashKey), e);
            throw new RuntimeException(String.format("RedisUtil hHasKey error. Param, key: %s, hashKey: %s", key, hashKey));
        }
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key     键
     * @param hashKey 项
     * @param delta   要增加几(大于0)
     * @return 增加后的值
     */
    public static double hIncrement(String key, Object hashKey, double delta) {
        try {
            return redisUtil.redisTemplate.opsForHash().increment(key, hashKey, delta);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hIncr error. Param, key: %s, hashKey: %s, delta: %s", key, hashKey, delta), e);
            throw new RuntimeException(String.format("RedisUtil hIncr error. Param, key: %s, hashKey: %s, delta: %s", key, hashKey, delta));
        }
    }

    /**
     * hash递减
     *
     * @param key     键
     * @param hashKey 项
     * @param delta   要减少记(小于0)
     * @return 减少后的值
     */
    public static double hDecrement(String key, Object hashKey, double delta) {
        try {
            return redisUtil.redisTemplate.opsForHash().increment(key, hashKey, -delta);
        } catch (Exception e) {
            log.error(String.format("RedisUtil hDecr error. Param, key: %s, hashKey: %s, delta: %s", key, hashKey, delta), e);
            throw new RuntimeException(String.format("RedisUtil hDecr error. Param, key: %s, hashKey: %s, delta: %s", key, hashKey, delta));
        }
    }
    // ============================set=============================

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     * @return 集合
     */
    public static Set<Object> sMembers(String key) {
        try {
            return redisUtil.redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtil sMembers error. Param, key: %s", key), e);
            throw new RuntimeException(String.format("RedisUtil sMembers error. Param, key: %s", key));
        }
    }

    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true 存在 false不存在
     */
    public static Boolean sIsMember(String key, Object value) {
        try {
            return redisUtil.redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            log.error(String.format("RedisUtil sIsMember error. Param, key: %s, value: %s", key, value), e);
            throw new RuntimeException(String.format("RedisUtil sIsMember error. Param, key: %s, value: %s", key, value));
        }
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public static Long sAdd(String key, Object... values) {
        try {
            return redisUtil.redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            log.error(String.format("RedisUtil sAdd error. Param, key: %s, values: %s", key, Arrays.asList(values)), e);
            throw new RuntimeException(String.format("RedisUtil sAdd error. Param, key: %s, values: %s", key, Arrays.asList(values)));
        }
    }

    /**
     * 将set数据放入缓存
     *
     * @param key     键
     * @param seconds 时间(秒)
     * @param values  值 可以是多个
     * @return 成功个数
     */
    public static Long sAddEx(String key, long seconds, Object... values) {
        try {
            Long count = redisUtil.redisTemplate.opsForSet().add(key, values);
            expire(key, seconds);
            return count;
        } catch (Exception e) {
            log.error(String.format("RedisUtil sAdd error. Param, key: %s, seconds: %d,values: %s", key, seconds, Arrays.asList(values)), e);
            throw new RuntimeException(String.format("RedisUtil sAdd error. Param, key: %s, seconds: %d,values: %s", key, seconds, Arrays.asList(values)));
        }
    }

    /**
     * 获取set缓存的长度
     *
     * @param key 键
     * @return 个数
     */
    public static Long sSize(String key) {
        try {
            return redisUtil.redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtil sSize error. Param, key: %s", key), e);
            throw new RuntimeException(String.format("RedisUtil sSize error. Param, key: %s", key));
        }
    }

    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     */
    public static Long sRemove(String key, Object... values) {
        try {
            return redisUtil.redisTemplate.opsForSet().remove(key, values);
        } catch (Exception e) {
            log.error(String.format("RedisUtil sRemove error. Param, key: %s, values: %s", key, Arrays.asList(values)), e);
            throw new RuntimeException(String.format("RedisUtil sRemove error. Param, key: %s, values: %s", key, Arrays.asList(values)));
        }
    }
    // ===============================list=================================

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @return 值的列表
     */
    public static List<Object> lRange(String key, long start, long end) {
        try {
            return redisUtil.redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            log.error(String.format("RedisUtil lRange error. Param, key: %s, start: %d, end: %d", key, start, end), e);
            throw new RuntimeException(String.format("RedisUtil lRange error. Param, key: %s, start: %d, end: %d", key, start, end));
        }
    }

    /**
     * 获取list缓存的长度
     *
     * @param key 键
     * @return 列表中元素个数
     */
    public static Long lSize(String key) {
        try {
            return redisUtil.redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtil lSize error. Param, key: %s", key), e);
            throw new RuntimeException(String.format("RedisUtil lSize error. Param, key: %s", key));
        }
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引
     * @return 值
     */
    public static Object lIndex(String key, long index) {
        try {
            return redisUtil.redisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            log.error(String.format("RedisUtil lIndex error. Param, key: %s, index: %d", key, index), e);
            throw new RuntimeException(String.format("RedisUtil lIndex error. Param, key: %s, index: %d", key, index));
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     */
    public static Long lRightPush(String key, Object value) {
        try {
            return redisUtil.redisTemplate.opsForList().rightPush(key, value);
        } catch (Exception e) {
            log.error(String.format("RedisUtil lRightPush error. Param, key: %s, value: %s", key, value), e);
            throw new RuntimeException(String.format("RedisUtil lRightPush error. Param, key: %s, value: %s", key, value));
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key     键
     * @param value   值
     * @param seconds 时间(秒)
     */
    public static Long lRightPush(String key, Object value, long seconds) {
        try {
            Long count = redisUtil.redisTemplate.opsForList().rightPush(key, value);
            expire(key, seconds);
            return count;
        } catch (Exception e) {
            log.error(String.format("RedisUtil lRightPush error. Param, key: %s, value: %s, seconds: %d", key, value, seconds), e);
            throw new RuntimeException(String.format("RedisUtil lRightPush error. Param, key: %s, value: %s, seconds: %d", key, value, seconds));
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key    键
     * @param values 值
     */
    public static Long lRightPushAll(String key, List<Object> values) {
        try {
            return redisUtil.redisTemplate.opsForList().rightPushAll(key, values);
        } catch (Exception e) {
            log.error(String.format("RedisUtil lRightPushAll error. Param, key: %s, values: %s", key, values), e);
            throw new RuntimeException(String.format("RedisUtil lRightPushAll error. Param, key: %s, values: %s", key, values));
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key     键
     * @param values  值
     * @param seconds 时间(秒)
     */
    public static Long lRightPushAll(String key, List<Object> values, long seconds) {
        try {
            Long count = redisUtil.redisTemplate.opsForList().rightPushAll(key, values);
            expire(key, seconds);
            return count;
        } catch (Exception e) {
            log.error(String.format("RedisUtil lRightPushAll error. Param, key: %s, values: %s, seconds: %d", key, values, seconds), e);
            throw new RuntimeException(String.format("RedisUtil lRightPushAll error. Param, key: %s, values: %s, seconds: %d", key, values, seconds));
        }
    }

    /**
     * 将list中的元素取出来
     */
    public static Object lLeftPop(String key) {
        try {
            return redisUtil.redisTemplate.opsForList().leftPop(key);
        } catch (Exception e) {
            log.error(String.format("RedisUtil lLeftPop error. Param, key: %s", key), e);
            throw new RuntimeException(String.format("RedisUtil lLeftPop error. Param, key: %s", key));
        }
    }

    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     */
    public static void lSet(String key, long index, Object value) {
        try {
            redisUtil.redisTemplate.opsForList().set(key, index, value);
        } catch (Exception e) {
            log.error(String.format("RedisUtil lSet error. Param, key: %s, index: %d, value: %s", key, index, value), e);
            throw new RuntimeException(String.format("RedisUtil lSet error. Param, key: %s, index: %d, value: %s", key, index, value));
        }
    }

    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     */
    public static void lRemove(String key, long count, Object value) {
        try {
            redisUtil.redisTemplate.opsForList().remove(key, count, value);
        } catch (Exception e) {
            log.error(String.format("RedisUtil lRemove error. Param, key: %s, count: %d, value: %s", key, count, value), e);
            throw new RuntimeException(String.format("RedisUtil lRemove error. Param, key: %s, count: %d, value: %s", key, count, value));
        }
    }

}
