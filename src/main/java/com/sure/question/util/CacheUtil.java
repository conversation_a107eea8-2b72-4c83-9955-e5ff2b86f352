package com.sure.question.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sure.question.entity.Grade;
import com.sure.question.entity.GradeSubject;
import com.sure.question.entity.Subject;
import com.sure.question.mapper.GradeMapper;
import com.sure.question.mapper.GradeSubjectMapper;
import com.sure.question.mapper.SubjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 缓存工具类，缓存系统中常用数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CacheUtil {
    private final SubjectMapper subjectMapper;
    private final GradeMapper gradeMapper;
    private final GradeSubjectMapper gradeSubjectMapper;

    private static CacheUtil instance;

    private static List<Subject> subjects = new ArrayList<>();
    private static List<Grade> grades = new ArrayList<>();
    private static List<GradeSubject> gradeSubjects = new ArrayList<>();

    @PostConstruct
    public void init() {
        instance = this;
    }

    /**
     * 查科目
     */
    public static List<Subject> getSubjects() {
        if (subjects.isEmpty()) {
            subjects = instance.subjectMapper.selectList(new LambdaQueryWrapper<Subject>()
                    .orderByAsc(Subject::getOrderId));
        }
        return subjects;
    }

    /**
     * 根据科目id获取科目名
     */
    public static String getSubjectName(Integer subjectId) {
        for (Subject s : getSubjects()) {
            if (s.getId().equals(subjectId)) {
                return s.getSubjectName();
            }
        }
        return null;
    }

    /**
     * 根据科目名获取科目id
     */
    public static Integer getSubjectId(String subjectName) {
        for (Subject s : getSubjects()) {
            if (s.getSubjectName().equals(subjectName)) {
                return s.getId();
            }
        }
        return null;
    }

    /**
     * 根据科目id获取科目排序码
     */
    public static Integer getSubjectSortCode(Integer subjectId) {
        for (Subject s : getSubjects()) {
            if (s.getId().equals(subjectId)) {
                return s.getOrderId();
            }
        }
        return null;
    }

    /**
     * 查年级
     */
    public static List<Grade> getGrades() {
        if (grades.isEmpty()) {
            grades = instance.gradeMapper.selectList(new LambdaQueryWrapper<Grade>()
                    .orderByAsc(Grade::getId));
        }
        return grades;
    }

    /**
     * 根据年级id获取年级名
     */
    public static String getGradeName(Integer gradeId) {
        for (Grade grade : getGrades()) {
            if (grade.getId().equals(gradeId)) {
                return grade.getGradeName();
            }
        }
        return null;
    }

    /**
     * 根据年级名称获取年级Id
     */
    public static Integer getGradeId(String gradeName) {
        for (Grade grade : getGrades()) {
            if (grade.getGradeName().equals(gradeName)) {
                return grade.getId();
            }
        }
        return null;
    }

    /**
     * 根据年级id获取学段
     */
    public static Integer getGradeLevel(Integer gradeId) {
        for (Grade grade : getGrades()) {
            if (grade.getId().equals(gradeId)) {
                return grade.getGradeLevel();
            }
        }
        return null;
    }

    /**
     * 查年级学科
     */
    public static List<GradeSubject> getGradeSubjects() {
        if (gradeSubjects.isEmpty()) {
            gradeSubjects = instance.gradeSubjectMapper.selectList(new LambdaQueryWrapper<>());
        }
        return gradeSubjects;
    }

    /**
     * 查是否存在学段学科
     */
    public static boolean existsGradeLevelSubject(int gradeLevel, int subjectId) {
        return getGradeSubjects().stream().anyMatch(item -> item.getGradeLevel().equals(gradeLevel) && item.getSubjectId().equals(subjectId));
    }

    /**
     * 查是否存在学段年级学科
     */
    public static boolean existsGradeLevelGradeSubject(int gradeLevel, int gradeId, int subjectId) {
        return getGradeSubjects().stream().anyMatch(item -> item.getGradeLevel().equals(gradeLevel)
                && item.getGradeId().equals(gradeId)
                && item.getSubjectId().equals(subjectId));
    }
}
