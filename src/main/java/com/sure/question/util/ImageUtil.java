package com.sure.question.util;

import com.sure.common.exception.ApiException;
import org.apache.commons.lang3.StringUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

public class ImageUtil {
    public static byte[] toByteArray(BufferedImage img, String format) throws IOException {
        if (!StringUtils.equalsAny(format, "jpg", "png", "gif")) {
            throw new ApiException("指定图片格式错误");
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(img, format, os);
        os.flush();
        byte[] bytes = os.toByteArray();
        os.close();
        return bytes;
    }

    public static String toBase64DataUrl(BufferedImage img, String format) throws IOException {
        byte[] bytes = toByteArray(img, format);
        String base64String = Base64.getEncoder().encodeToString(bytes);
        String mime = StringUtils.equals(format, "jpg") ? "jpeg" : format;
        return "data:image/" + mime + ";base64," + base64String;
    }
}
