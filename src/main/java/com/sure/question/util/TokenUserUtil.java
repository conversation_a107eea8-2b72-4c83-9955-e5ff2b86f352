package com.sure.question.util;

import com.alibaba.fastjson.JSONArray;
import com.sure.common.entity.StatusCode;
import com.sure.common.entity.UserInfo;
import com.sure.common.exception.ApiException;
import com.sure.question.feign.UserService;
import com.sure.question.vo.TokenUserVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 请求用户信息
 */
@Component
public class TokenUserUtil {
    @Resource
    UserService userService;

    private static TokenUserUtil instance;

    @PostConstruct
    public void init() {
        instance = this;
    }

    /**
     * 从header中获取当前用户信息
     */
    public static TokenUserVo getTokenUserVo(HttpServletRequest request) {
        String userId = request.getHeader("userId");
        String schoolId = request.getHeader("schoolId");
        String roleList = request.getHeader("roleList");
        List<Integer> roles = JSONArray.parseArray(roleList, Integer.class);
        return new TokenUserVo(userId, schoolId, roles);
    }

    /**
     * 获取当前用户id
     */
    public static String getUserId(HttpServletRequest request) {
        String userId = request.getHeader("userId");
        if (StringUtils.isEmpty(userId)) {
            throw new ApiException("未登录", StatusCode.Unauthorized);
        }
        return userId;
    }

    /**
     * 获取当前用户所在的学校id
     */
    public static String getSchoolId(HttpServletRequest request) {
        return request.getHeader("schoolId");
    }

    /**
     * 从缓存中获取当前用户信息
     */
    public static UserInfo getUserInfo(HttpServletRequest request) {
        String userId = request.getHeader("userId");
        String key = request.getHeader ("key");
        return instance.userService.getUserInfo(userId, key);
    }
}
