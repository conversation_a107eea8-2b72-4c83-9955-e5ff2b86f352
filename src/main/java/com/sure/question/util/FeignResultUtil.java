package com.sure.question.util;

import com.alibaba.fastjson.JSON;
import com.sure.common.entity.Result;
import com.sure.common.entity.StatusCode;
import com.sure.common.exception.ApiException;

import java.util.Collections;
import java.util.List;

/**
 * Feign调用结果转换
 */
public class FeignResultUtil {
    public static <T> T getObjectThrowMessage(Result result, Class<T> clazz) {
        String data = getDataStringThrowMessage(result);
        if (data == null) {
            return null;
        }
        return JSON.parseObject(data, clazz);
    }

    public static <T> List<T> getListThrowMessage(Result result, Class<T> clazz) {
        String data = getDataStringThrowMessage(result);
        if (data == null) {
            return Collections.emptyList();
        }
        return JSON.parseArray(data, clazz);
    }

    public static String getDataStringThrowMessage(Result result) {
        if (!StatusCode.OK.equals(result.getCode())) {
            throw new ApiException(result.getMessage());
        }
        if (result.getData() == null) {
            return null;
        }
        return JSON.toJSONString(result.getData());
    }

    public static void checkThrowMessage(Result result) {
        if (!StatusCode.OK.equals(result.getCode())) {
            throw new ApiException(result.getMessage());
        }
    }
}
