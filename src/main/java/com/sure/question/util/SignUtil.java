package com.sure.question.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.SecureUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class SignUtil {
    private final static String SALT = "jYR4tpA@gU(rYg*S";

    public static String sign(Map<String, Object> map) {
        StringBuilder sb = new StringBuilder();
        sb.append(SALT);
        if (!map.isEmpty()) {
            List<String> keyList = new ArrayList<>(map.keySet());
            Collections.sort(keyList);
            for (String key : keyList) {
                if (map.get(key) != null) {
                    sb.append("&").append(key).append("=").append(map.get(key));
                }
            }
        }
        return SecureUtil.md5(sb.toString()).toUpperCase();
    }

    public static String sign(Object bean) {
        Map<String, Object> map = BeanUtil.beanToMap(bean);
        return sign(map);
    }
}
