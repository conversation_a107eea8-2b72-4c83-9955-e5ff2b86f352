package com.sure.question.util;

import lombok.Builder;

import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

public class TreeUtil {
    /**
     * 深度优先遍历树结构（多个根节点）
     * @param <T>       节点类型
     * @param nodes     要遍历的根节点集合
     * @param options   遍历选项，通过 TraverseOptions.builder() 构建
     * @return 如果遍历完成则返回 true，如果被回调中断则返回 false
     */
    public static <T> boolean traverse(Collection<T> nodes, TraverseOptions<T> options) {
        if (nodes == null || nodes.isEmpty()) {
            return true;
        }

        // 检查回调是否需要上下文
        boolean needsContext = options.preOrderWithContext != null || options.postOrderWithContext != null;
        List<T> path = needsContext ? new ArrayList<>() : null;

        int index = 0;
        int siblingCount = nodes.size();
        for (T node : nodes) {
            if (!_traverse(node, index, siblingCount, path, options)) {
                return false; // 中断
            }
            index++;
        }
        return true; // 完成
    }

    /**
     * 深度优先遍历树结构（单个根节点）
     */
    public static <T> boolean traverse(T node, TraverseOptions<T> options) {
        if (node == null) {
            return true;
        }
        return traverse(Collections.singletonList(node), options);
    }

    /**
     * 遍历选项配置类
     */
    @Builder
    public static class TraverseOptions<T> {
        private Predicate<T> preOrder;
        private BiPredicate<T, TraverseContext<T>> preOrderWithContext;
        private Predicate<T> postOrder;
        private BiPredicate<T, TraverseContext<T>> postOrderWithContext;
        private Function<T, Collection<T>> childrenGetter;
    }

    /**
     * 遍历上下文对象
     */
    public static class TraverseContext<T> {
        /**
         * 父节点, 根节点的父节点为 null
         */
        public final T parent;
        /**
         * 当前节点的深度，根节点深度为 0
         */
        public final int depth;
        /**
         * 从根节点到当前节点的路径，包括当前节点
         */
        public final List<T> path;
        /**
         * 当前节点在兄弟节点中的索引
         */
        public final int index;
        /**
         * 当前节点是否是第一个兄弟节点
         */
        public final boolean isFirst;
        /**
         * 当前节点是否是最后一个兄弟节点
         */
        public final boolean isLast;
        /**
         * 当前节点的子节点
         */
        public final Collection<T> children;
        /**
         * 当前节点是否是叶子节点
         */
        public final boolean isLeaf;

        private TraverseContext(List<T> path, int index, int siblingCount, Collection<T> children) {
            this.parent = path.size() > 1 ? path.get(path.size() - 2) : null;
            this.depth = path.size() - 1;
            this.path = Collections.unmodifiableList(new ArrayList<>(path)); // 创建路径快照
            this.index = index;
            this.isFirst = index == 0;
            this.isLast = index == siblingCount - 1;
            this.children = children;
            this.isLeaf = children == null || children.isEmpty();
        }
    }

    /**
     * 内部递归遍历辅助函数
     */
    private static <T> boolean _traverse(T node, int index, int siblingCount, List<T> path, TraverseOptions<T> options) {
        Collection<T> children = options.childrenGetter.apply(node);
        if (children == null) {
            children = Collections.emptyList();
        }

        TraverseContext<T> context = null;
        if (path != null) {
            path.add(node);
            context = new TraverseContext<>(path, index, siblingCount, children);
        }

        try {
            // 1. 前序操作 (Pre-order)
            if (options.preOrderWithContext != null) {
                if (!options.preOrderWithContext.test(node, context)) {
                    return false;
                }
            }
            if (options.preOrder != null) {
                if (!options.preOrder.test(node)) {
                    return false;
                }
            }

            // 2. 遍历子节点
            int i = 0;
            for (T child : children) {
                if (!_traverse(child, i, children.size(), path, options)) {
                    return false; // 从子节点传来的中断信号
                }
                i++;
            }

            // 3. 后序操作 (Post-order)
            if (options.postOrderWithContext != null) {
                if (!options.postOrderWithContext.test(node, context)) {
                    return false;
                }
            }
            if (options.postOrder != null) {
                if (!options.postOrder.test(node)) {
                    return false;
                }
            }

        } finally {
            // 确保回溯逻辑总是执行
            if (path != null) {
                path.remove(path.size() - 1);
            }
        }
        return true; // 继续
    }



    /**
     * 平铺树节点
     */
    public static <T> List<T> getFlattenNodes(Collection<T> tree, Function<T, Collection<T>> childrenGetter) {
        List<T> result = new ArrayList<>();
        TraverseOptions<T> options = TraverseOptions.<T>builder().childrenGetter(childrenGetter).preOrder(node -> {
            result.add(node);
            return true;
        }).build();
        traverse(tree, options);
        return result;
    }

    /**
     * 平铺树节点
     */
    public static <T> List<T> getFlattenNodes(T rootNode, Function<T, Collection<T>> childrenGetter) {
        return getFlattenNodes(Collections.singletonList(rootNode), childrenGetter);
    }

    /**
     * 构建树
     */
    public static <T> List<T> buildTree(Collection<T> records, Function<T, Object> idGetter, Function<T, Object> parentIdGetter, Function<T, Collection<T>> childrenGetter) {
        List<T> result = new ArrayList<>();

        Map<Object, T> nodeMap = records.stream().collect(Collectors.toMap(idGetter, Function.identity()));
        records.forEach(node -> {
            Object parentId = parentIdGetter.apply(node);
            if (parentId != null) {
                T parent = nodeMap.get(parentId);
                if (parent != null) {
                    childrenGetter.apply(parent).add(node);
                    return;
                }
            }
            result.add(node);
        });

        return result;
    }
}
