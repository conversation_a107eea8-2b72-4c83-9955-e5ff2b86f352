package com.sure.question.util;

import com.sure.common.exception.ApiException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@RequiredArgsConstructor
@Component
public class RedissonLockUtil {
    private final RedissonClient redissonClient;
    private static RedissonLockUtil instance;

    @PostConstruct
    private void setInstance() {
        instance = this;
    }

    public static <T> T executeAfterLocked(String key, Supplier<T> supplier) {
        RLock lock = tryLock(key);
        try {
            return supplier.get();
        } finally {
            lock.unlock();
        }
    }

    public static void executeAfterLocked(String key, Runnable runnable) {
        RLock lock = tryLock(key);
        try {
            runnable.run();
        } finally {
            lock.unlock();
        }
    }

    private static RLock tryLock(String key) {
        RLock lock = instance.redissonClient.getLock(key);
        boolean locked;
        try {
            locked = lock.tryLock(10, 300, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            log.error("redisson获取锁失败, key = {}, ex = \n", key, ex);
            throw new ApiException("获取锁失败，请稍后再试");
        }
        if (!locked) {
            throw new ApiException("锁已被占用，请稍后再试");
        }
        return lock;
    }
}
