package com.sure.question.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

public class MultipartFileUtil {
    public static Map<String, String> imageMIMETypeMap = new HashMap<String, String>() {{
        put("image/jpeg", ".jpg");
        put("image/png", ".png");
    }};

    public static String getImageExtension(MultipartFile file) {
        if (StringUtils.isEmpty(file.getContentType())) {
            return null;
        }
        String contentType = file.getContentType().toLowerCase();
        return imageMIMETypeMap.get(contentType);
    }

    public static boolean isPdf(MultipartFile file) {
        return StringUtils.equalsIgnoreCase(file.getContentType(), "application/pdf");
    }

    public static boolean isWord(MultipartFile file) {
        return StringUtils.equalsAnyIgnoreCase(file.getContentType(),
                "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
    }

    public static boolean isAudio(MultipartFile file) {
        return StringUtils.equalsAnyIgnoreCase(file.getContentType(), "audio/mpeg");
    }

    public static String getName(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            return StringUtils.EMPTY;
        }
        int idx = fileName.lastIndexOf(".");
        if (idx > 0) {
            return fileName.substring(0, idx);
        } else {
            return fileName;
        }
    }

    public static String getSuffix(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            return StringUtils.EMPTY;
        }
        int idx = fileName.lastIndexOf(".");
        if (idx > 0) {
            return fileName.substring(idx);
        }
        return "";
    }
}
