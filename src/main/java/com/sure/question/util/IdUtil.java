package com.sure.question.util;

import com.sure.question.common.IdWorker;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
@RequiredArgsConstructor
public class IdUtil {
    private final IdWorker idWorker;
    private static IdUtil instance;

    @PostConstruct
    public void init() {
        instance = this;
    }

    public static long longId() {
        return instance.idWorker.nextId();
    }

    public static String StringId() {
        return String.valueOf(instance.idWorker.nextId());
    }
}
