package com.sure.question.util;

import com.aspose.words.*;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.*;
import java.nio.charset.StandardCharsets;

public class WordConvertUtil {
    /**
     * word文档转为html
     * @param inputStream word文档输入流
     * @return html字符串
     */
    public static String word2html(InputStream inputStream) {
        String html = convertWordToHtml(inputStream);
        return alignImgElementsVerticallyMiddle(html);
    }

    /**
     * 将word文档转为html
     */
    private static String convertWordToHtml(InputStream inputStream) {
        HtmlSaveOptions options = new HtmlSaveOptions();
        // 图片转换为base64
        options.setExportImagesAsBase64(true);
        // 公式转为mathml
        options.setOfficeMathOutputMode(HtmlOfficeMathOutputMode.MATH_ML);
        // 保留隐藏的书签、原始形状等
        options.setExportRoundtripInformation(true);
        // 去掉页头页脚
        options.setExportHeadersFootersMode(ExportHeadersFootersMode.NONE);

        // 执行转换
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            Document doc = new Document(inputStream);
            doc.save(byteArrayOutputStream, options);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        byte[] bytes = byteArrayOutputStream.toByteArray();
        return new String(bytes, StandardCharsets.UTF_8);
    }

    /**
     * html中图片竖直居中
     */
    private static String alignImgElementsVerticallyMiddle(String html) {
        org.jsoup.nodes.Document doc = Jsoup.parse(html);
        Elements imgElements = doc.getElementsByTag("img");
        if (imgElements.isEmpty()) {
            return html;
        }
        for (Element img : imgElements) {
            String style = img.attr("style");
            if (StringUtils.isNotEmpty(style)) {
                style += ";";
            }
            style += "vertical-align: middle;";
            img.attr("style", style);
        }
        return doc.body().html();
    }

    /**
     * html转为word文档
     * @param html html字符串
     * @param size 文档尺寸
     * @param outputStream word文档输出流
     */
    public static void html2word(String html, String size, OutputStream outputStream) {
        try {
            Document doc = new Document();
            DocumentBuilder docBuilder = new DocumentBuilder(doc);
            PageSetup pageSetup = docBuilder.getPageSetup();

            // 设置页面尺寸
            double topBottomMargin = ConvertUtil.millimeterToPoint(20);
            double leftRightMargin = ConvertUtil.inchToPoint(0.5);
            double columnSpacing = 0.75d;
            if ("A3".equalsIgnoreCase(size)) {
                pageSetup.setPaperSize(PaperSize.A3);
                pageSetup.setOrientation(Orientation.LANDSCAPE);
                pageSetup.getTextColumns().setCount(2);
                columnSpacing = ConvertUtil.inchToPoint(0.75d);
                pageSetup.getTextColumns().setSpacing(columnSpacing);
            } else if ("K8".equalsIgnoreCase(size)) {
                pageSetup.setPaperSize(PaperSize.CUSTOM);
                pageSetup.setPageWidth(ConvertUtil.millimeterToPoint(260d));
                pageSetup.setPageHeight(ConvertUtil.millimeterToPoint(370d));
                pageSetup.setOrientation(Orientation.LANDSCAPE);
                pageSetup.getTextColumns().setCount(2);
                columnSpacing = ConvertUtil.inchToPoint(0.75d);
                pageSetup.getTextColumns().setSpacing(columnSpacing);
            } else {
                pageSetup.setPaperSize(PaperSize.A4);
                pageSetup.setOrientation(Orientation.PORTRAIT);
            }
            pageSetup.setTopMargin(topBottomMargin);
            pageSetup.setBottomMargin(topBottomMargin);
            pageSetup.setLeftMargin(leftRightMargin);
            pageSetup.setRightMargin(leftRightMargin);

            // 设置制表位
            double contentWidth = (pageSetup.getPageWidth() - pageSetup.getLeftMargin() - pageSetup.getRightMargin() - columnSpacing) / pageSetup.getTextColumns().getCount();
            double tabStopWidth = contentWidth / 4;
            TabStopCollection tabStops = docBuilder.getParagraphFormat().getTabStops();
            tabStops.clear();
            tabStops.add(tabStopWidth, TabAlignment.LEFT, TabLeader.NONE);
            tabStops.add(tabStopWidth * 2, TabAlignment.LEFT, TabLeader.NONE);
            tabStops.add(tabStopWidth * 3, TabAlignment.LEFT, TabLeader.NONE);

            // 插入文档
            docBuilder.getFont().setName("Times New Roman");
            docBuilder.getFont().setNameFarEast("SimSun");
            docBuilder.getFont().setSize(10.5d);
            docBuilder.insertHtml(html, true);

            // 处理选择题选项
            doc.getRange().replace("@@tab-stop@@", "\t", new FindReplaceOptions());

            // 处理分页
            doc.getRange().replace("@@page-break@@", "\f", new FindReplaceOptions());

            doc.save(outputStream, SaveFormat.DOCX);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
