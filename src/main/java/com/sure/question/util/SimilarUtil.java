package com.sure.question.util;

import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.SmallQuestionVo;
import org.apache.commons.lang3.StringUtils;
import org.xm.Similarity;

import java.util.List;
import java.util.stream.Collectors;

public class SimilarUtil {

    /**
     * 情况
     * 1.推题有相似或相同的原题
     * 2.同道推题重复出现在一道题目或多道题目的推题
     * 3.相似推题重复出现在一道题目或多道题目的推题
     * 解决方案
     * 找出候选的推题，然后对原来的题目进行过滤，且保证每个推题当且仅当使用一次
     */
    public static boolean isSameOrSimilar(QuestionVo sourceQues, QuestionVo targetQues) {
        boolean isSame = false;
        boolean isSimilar = false;
        if (sourceQues == null && targetQues == null) {
            return isSame = true;
        } else if (sourceQues == null || targetQues == null) {
            return isSame = false;
        } else {
            if (sourceQues.getId().equals(targetQues.getId())) {
                return isSame = true;
            }
            if (sourceQues.getSubjectId() != null && !sourceQues.getSubjectId().equals(targetQues.getSubjectId())) {
                return isSame = false;
            }
            if (sourceQues.getStage() != null && !sourceQues.getStage().equals(targetQues.getStage())) {
                return isSame = false;
            }
            if (sourceQues.getType() != null && !sourceQues.getType().equals(targetQues.getType())) {
                return isSame = false;
            }
            List<String> sourceIds = sourceQues.getBranches().stream().map(SmallQuestionVo::getId).collect(Collectors.toList());
            List<String> targetIds = targetQues.getBranches().stream().map(SmallQuestionVo::getId).collect(Collectors.toList());
            for (String sourceId : sourceIds) {
                for (String targetId : targetIds) {
                    if (sourceId.equals(targetId)) {
                        return isSame = true;
                    }
                }
            }
            //英语完形填空，stem为空
            List<String> sourceStems = sourceQues.getBranches().stream().filter(smallQuestionVo -> StringUtils.isNotBlank(smallQuestionVo.getStem())).map(smallQuestionVo -> DistinctSentenceUtil.delHTMLTag(smallQuestionVo.getStem())).collect(Collectors.toList());
            List<String> targetStems = targetQues.getBranches().stream().filter(smallQuestionVo -> StringUtils.isNotBlank(smallQuestionVo.getStem())).map(smallQuestionVo -> DistinctSentenceUtil.delHTMLTag(smallQuestionVo.getStem())).collect(Collectors.toList());
            if (sourceStems.isEmpty() || targetStems.isEmpty()) {
                //如果没有小题内容，比较大题的内容信息
                if (StringUtils.isNotBlank(sourceQues.getTrunk()) && StringUtils.isNotBlank(targetQues.getTrunk())) {
                    //如果是英语
                    String sourceTrunk = DistinctSentenceUtil.delHTMLTag(sourceQues.getTrunk());
                    String targetTrunk = DistinctSentenceUtil.delHTMLTag(targetQues.getTrunk());
                    // 截取前300字符
                    if (sourceTrunk.length() > 300) {
                        sourceTrunk = sourceTrunk.substring(0, 300);
                    }
                    if (targetTrunk.length() > 300) {
                        targetTrunk = targetTrunk.substring(0, 300);
                    }
                    if (sourceQues.getSubjectId() == 3) {
                        double similarity = Similarity.morphoSimilarity(sourceTrunk, targetTrunk);
                        if (similarity >= 0.80 && similarity <= 1.0) {
                            return isSimilar = true;
                        }
                    } else {
                        double similarity = Similarity.phraseSimilarity(sourceTrunk, targetTrunk);
                        if (similarity >= 0.90 && similarity <= 1.0) {
                            return isSimilar = true;
                        }
                    }
                }
            }
            for (String sourceStem : sourceStems) {
                for (String targetStem : targetStems) {
                    double similarity = Similarity.phraseSimilarity(sourceStem, targetStem);
                    if (similarity >= 0.90 && similarity <= 1.0) {
                        return isSimilar = true;
                    }
                }
            }
        }
        return isSame || isSimilar;
    }

}
