package com.sure.question.util.excelExport;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.io.OutputStream;
import java.security.InvalidParameterException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class Workbook {
    @Getter
    private final XSSFWorkbook xssfWorkbook;

    public Workbook() {
        xssfWorkbook = new XSSFWorkbook();
    }

    public <T> Workbook addSheet(Sheet<T> sheet, List<T> rows) {
        String sheetName = sheet.getSheetName();
        // 设置默认名称
        int sheetCount = xssfWorkbook.getNumberOfSheets();
        if (StringUtils.isEmpty(sheetName)) {
            sheetName = "Sheet".concat(String.valueOf(sheetCount + 1));
        }
        // 检查名称重复
        Set<String> sheetNames = new HashSet<>();
        for (int i = 0; i < sheetCount; i++) {
            sheetNames.add(xssfWorkbook.getSheetName(i));
        }
        while (sheetNames.contains(sheetName)) {
            sheetName += "(1)";
        }
        // 检查列
        if (sheet.getColumns() == null || sheet.getColumns().isEmpty()) {
            throw new InvalidParameterException("sheet中无列");
        }
        // 创建sheet
        XSSFSheet xssfSheet = xssfWorkbook.createSheet(sheetName);
        // 填充sheet
        sheet.writeSheet(xssfWorkbook, xssfSheet, rows);
        return this;
    }

    public void write(OutputStream outputStream) throws IOException {
        xssfWorkbook.write(outputStream);
    }
}
