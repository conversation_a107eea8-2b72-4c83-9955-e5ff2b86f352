package com.sure.question.util.excelExport;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Column<T> {
    /**
     * 标题
     */
    private String title;
    /**
     * 是否序号
     */
    private boolean isSequence;
    /**
     * 值
     */
    private Function<T, Object> valueGetter;
    /**
     * 合并行依据的值，相邻行相同值合并
     */
    private Function<T, Object> mergeRowKeyGetter;
    /**
     * 样式
     */
    private Style style;
    /**
     * 条件样式
     */
    private Function<T, Style> conditionalCellStyleGetter;
    /**
     * 是否冻结
     */
    private boolean freeze;
    /**
     * 宽度（字符数），null则为自动宽度
     */
    private Integer width = 9;
    /**
     * 子列
     */
    private List<Column<T>> children = new ArrayList<>();

    public static <T> Column<T> sequenceColumn(String title, boolean freeze) {
        Column<T> col = new Column<>();
        col.setTitle(title);
        col.setSequence(true);
        col.setFreeze(freeze);
        return col;
    }

    public Column(String title, Function<T, Object> valueGetter) {
        this(title, valueGetter, null, false, 9);
    }

    public Column(String title, Function<T, Object> valueGetter, String dataFormat) {
        this(title, valueGetter, dataFormat, false, 9);
    }

    public Column(String title, Function<T, Object> valueGetter, boolean freeze) {
        this(title, valueGetter, null, freeze, 9);
    }

    public Column(String title, Function<T, Object> valueGetter, Integer width) {
        this(title, valueGetter, null, false, width);
    }

    public Column(String title, Function<T, Object> valueGetter, String dataFormat, boolean freeze) {
        this(title, valueGetter, dataFormat, freeze, 9);
    }

    public Column(String title, Function<T, Object> valueGetter, String dataFormat, Integer width) {
        this(title, valueGetter, dataFormat, false, width);
    }

    public Column(String title, Function<T, Object> valueGetter, boolean freeze, Integer width) {
        this(title, valueGetter, null, freeze, width);
    }

    public Column(String title, Function<T, Object> valueGetter, String dataFormat, boolean freeze, Integer width) {
        this.title = title;
        this.valueGetter = valueGetter;
        this.freeze = freeze;
        this.width = width;
        if (StringUtils.isNotEmpty(dataFormat)) {
            Style style = new Style();
            style.setDataFormat(dataFormat);
            this.style = style;
        }
    }

    public void setColumnAutoWidth() {
        width = null;
    }
    
    public static <T, R> Function<T, Object> childValueGetter(Function<T, R> parentValueGetter, Function<R, Object> thisValueGetter) {
        return x -> {
            R parentValue = parentValueGetter.apply(x);
            return parentValue == null ? null : thisValueGetter.apply(parentValue);
        };
    }
}
