package com.sure.question.util.excelExport;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Style {
    /**
     * 字体
     */
    private String fontName;
    /**
     * 字号
     */
    private Float fontSize;
    /**
     * 颜色
     */
    private String fontColor;
    /**
     * 加粗
     */
    private Boolean fontBold;
    /**
     * 背景色
     */
    private String backgroundColor;
    /**
     * 显示边框
     */
    private Boolean showBorder;
    /**
     * 边框颜色
     */
    private String borderColor;
    /**
     * 横向排列: center, left, right
     */
    private String horizontalAlign;
    /**
     * 纵向排列: center, top, bottom
     */
    private String verticalAlign;
    /**
     * 数据格式
     */
    private String dataFormat;

    /**
     * 合并样式
     */
    public Style merge(Style anotherStyle) {
        Style newStyle = new Style();
        newStyle.setFontName(ObjectUtils.defaultIfNull(anotherStyle.getFontName(), fontName));
        newStyle.setFontSize(ObjectUtils.defaultIfNull(anotherStyle.getFontSize(), fontSize));
        newStyle.setFontColor(ObjectUtils.defaultIfNull(anotherStyle.getFontColor(), fontColor));
        newStyle.setFontBold(ObjectUtils.defaultIfNull(anotherStyle.getFontBold(), fontBold));
        newStyle.setBackgroundColor(ObjectUtils.defaultIfNull(anotherStyle.getBackgroundColor(), backgroundColor));
        newStyle.setShowBorder(ObjectUtils.defaultIfNull(anotherStyle.getShowBorder(), showBorder));
        newStyle.setBorderColor(ObjectUtils.defaultIfNull(anotherStyle.getBorderColor(), borderColor));
        newStyle.setHorizontalAlign(ObjectUtils.defaultIfNull(anotherStyle.getHorizontalAlign(), horizontalAlign));
        newStyle.setVerticalAlign(ObjectUtils.defaultIfNull(anotherStyle.getVerticalAlign(), verticalAlign));
        newStyle.setDataFormat(ObjectUtils.defaultIfNull(anotherStyle.getDataFormat(), dataFormat));
        return newStyle;
    }

    /**
     * 仅设置数据格式
     */
    public static Style dataFormatStyle(String dataFormat) {
        Style style = new Style();
        style.setDataFormat(dataFormat);
        return style;
    }
}
