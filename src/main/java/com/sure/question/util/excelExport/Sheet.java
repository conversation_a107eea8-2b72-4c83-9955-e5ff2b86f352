package com.sure.question.util.excelExport;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import java.util.*;
import java.util.function.Function;

@Data
public class Sheet<T> {
    /**
     * 名称
     */
    private String sheetName;
    /**
     * 标题
     */
    private String caption;
    /**
     * 是否固定表头
     */
    private boolean freezeHeader;
    /**
     * 列
     */
    private List<Column<T>> columns;
    /**
     * 表尾
     */
    private String footer;
    /**
     * 标题行高
     */
    private float captionRowHeight = 30;
    /**
     * 标题样式
     */
    private Style captionStyle = new Style("微软雅黑", 14F, null, false, null, false, null, "center", "center", "");
    /**
     * 表头行高
     */
    private float headerRowHeight = 18;
    /**
     * 表头样式
     */
    private Style headerStyle = new Style("微软雅黑", 10F, null, false, "F0F0F0", true, "A0A0A0", "center", "center", "");
    /**
     * 表体行高
     */
    private float bodyRowHeight = 18;
    /**
     * 表体样式
     */
    private Style bodyStyle = new Style("微软雅黑", 10F, null, false, null, true, "A0A0A0", "center", "center", "");
    /**
     * 条件行样式
     */
    private Function<T, Style> conditionalRowStyleGetter;
    /**
     * 表尾行高
     */
    private float footerRowHeight = 18;
    /**
     * 表尾样式
     */
    private Style footerStyle = new Style("微软雅黑", 8F, "808080", false, null, false, null, "left", "center", "");
    /**
     * 表尾与表体间隔
     */
    private int footerBodyGap = 1;


    @Setter(AccessLevel.NONE)
    private XSSFWorkbook xssfWorkbook;

    @Setter(AccessLevel.NONE)
    private XSSFSheet xssfSheet;

    public void writeSheet(XSSFWorkbook xssfWorkbook, XSSFSheet xssfSheet, List<T> rows) {
        this.xssfWorkbook = xssfWorkbook;
        this.xssfSheet = xssfSheet;

        // 展开列
        List<Column<T>> expandColumns = getExpandColumnsRecursive(columns);

        // 标题
        int headerStartRowIndex = writeCaption(expandColumns);

        // 表头
        int bodyStartRowIndex = writeHeader(expandColumns, headerStartRowIndex);

        // 表体
        int footerStartRowIndex = writeBody(expandColumns, bodyStartRowIndex, rows);

        // 表尾
        writeFooter(expandColumns, footerStartRowIndex);

        // 设置列宽
        setColumnsWidth(expandColumns);

        // 冻结视图
        freeze(expandColumns, bodyStartRowIndex);
    }

    /**
     * 展开列
     */
    private List<Column<T>> getExpandColumnsRecursive(List<Column<T>> columns) {
        List<Column<T>> expandColumns = new ArrayList<>();
        for (Column<T> column : columns) {
            if (column.getChildren() != null && !column.getChildren().isEmpty()) {
                expandColumns.addAll(getExpandColumnsRecursive(column.getChildren()));
            }
            else {
                expandColumns.add(column);
            }
        }
        return expandColumns;
    }

    /**
     * 写标题
     */
    private int writeCaption(List<Column<T>> expandColumns) {
        if (StringUtils.isEmpty(caption)) {
            return 0;
        }
        CellStyle captionCellStyle = createXssfCellStyle(captionStyle);
        XSSFRow xssfRow = createXssfRow(0, captionRowHeight);
        for (int i = 0; i < expandColumns.size(); i++) {
            XSSFCell cell = xssfRow.createCell(i);
            cell.setCellStyle(captionCellStyle);
            if (i == 0) {
                cell.setCellValue(caption);
            }
        }
        addMergeArea(0, 0, 1, expandColumns.size());
        return 1;
    }

    /**
     * 写表头
     */
    private int writeHeader(List<Column<T>> expandColumns, int startRowIndex) {
        List<String[]> rows = new ArrayList<>();
        traverseHeaderColumns(rows, expandColumns.size(), columns, 0, 0, null);

        XSSFCellStyle cellStyle = createXssfCellStyle(headerStyle);
        int rowIndex = 0;
        for (String[] row : rows) {
            XSSFRow xssfRow = createXssfRow(rowIndex + startRowIndex, headerRowHeight);
            for (int colIndex = 0; colIndex < expandColumns.size(); colIndex++) {
                XSSFCell cell = xssfRow.createCell(colIndex);
                cell.setCellStyle(cellStyle);
                if (StringUtils.isEmpty(row[colIndex])) {
                    continue;
                }
                cell.setCellValue(row[colIndex]);

                // 合并下边和右边的空单元格
                int rowSpan = 1;
                int colSpan = 1;
                int nextRow, nextCol;
                while ((nextRow = rowIndex + rowSpan) < rows.size()) {
                    if (rows.get(nextRow)[colIndex] != null) {
                        break;
                    }
                    rows.get(nextRow)[colIndex] = "";
                    rowSpan++;
                }
                while ((nextCol = colIndex + colSpan) < expandColumns.size()) {
                    if (row[nextCol] != null) {
                        break;
                    }
                    row[nextCol] = "";
                    colSpan++;
                }
                if (rowSpan > 1 || colSpan > 1) {
                    addMergeArea(rowIndex + startRowIndex, colIndex, rowSpan, colSpan);
                }
            }
            rowIndex++;
        }

        return rowIndex + startRowIndex;
    }

    /**
     * 遍历设置表头文本
     */
    private int traverseHeaderColumns(List<String[]> rows, int columnCount, List<Column<T>> columns, int rowIndex, int colIndex, Column<T> parent) {
        String[] row;

        // 当前层级行，不存在则创建
        if (rows.size() > rowIndex) {
            row = rows.get(rowIndex);
        }
        else {
            row = new String[columnCount];
            rows.add(row);
        }

        for (Column<T> col : columns) {
            row[colIndex] = col.getTitle();

            // 子列是否冻结与父列相同
            if (parent != null) {
                col.setFreeze(parent.isFreeze());
            }

            // 子列在下一行，colIndex始终是下一列的序号
            if (col.getChildren() != null && !col.getChildren().isEmpty()) {
                colIndex = traverseHeaderColumns(rows, columnCount, col.getChildren(), rowIndex + 1, colIndex, col);
            }
            else {
                colIndex++;
            }
        }
        return colIndex;
    }

    /**
     * 写表体
     */
    private int writeBody(List<Column<T>> expandColumns, int startRowIndex, List<T> rows) {
        // 各列样式
        Style[] columnRawStyles = new Style[expandColumns.size()];
        XSSFCellStyle[] columnStyles = new XSSFCellStyle[expandColumns.size()];
        getColumnStyles(expandColumns, columnRawStyles, columnStyles);

        // 条件样式
        Map<Style, XSSFCellStyle> styleMap = new HashMap<>();

        int rowIndex = 0;
        for (T data : rows) {
            XSSFRow xssfRow = createXssfRow(rowIndex + startRowIndex, bodyRowHeight);
            Style conditionalRowStyle = null;
            if (conditionalRowStyleGetter != null) {
                conditionalRowStyle = conditionalRowStyleGetter.apply(data);
            }
            int colIndex = 0;
            for (Column<T> col : expandColumns) {
                XSSFCell xssfCell = xssfRow.createCell(colIndex);
                setBodyCellValue(xssfCell, col, data, rowIndex);
                Style columnRawStyle = columnRawStyles[colIndex];
                XSSFCellStyle columnStyle = columnStyles[colIndex];
                setBodyCellStyle(xssfCell, col, data, columnStyle, columnRawStyle, conditionalRowStyle, styleMap);
                mergeBodyCell(col, data, rowIndex, startRowIndex, colIndex, rows);
                colIndex++;
            }
            rowIndex++;
        }
        return rowIndex + startRowIndex;
    }

    /**
     * 表体各列样式
     */
    private void getColumnStyles(List<Column<T>> expandColumns, Style[] columnRawStyles, XSSFCellStyle[] columnStyles) {
        Map<Style, XSSFCellStyle> styleMap = new HashMap<>();
        int colIndex = 0;
        for (Column<T> col : expandColumns) {
            Style rawStyle = bodyStyle;
            if (col.getStyle() != null) {
                rawStyle = rawStyle.merge(col.getStyle());
            }
            columnRawStyles[colIndex] = rawStyle;

            XSSFCellStyle style = styleMap.get(rawStyle);
            if (style == null) {
                style = createXssfCellStyle(rawStyle);
                styleMap.put(rawStyle, style);
            }
            columnStyles[colIndex] = style;
            colIndex++;
        }
    }

    /**
     * 设置表体单元格值
     */
    private void setBodyCellValue(XSSFCell cell, Column<T> col, T data, int sequence) {
        if (col.isSequence()) {
            cell.setCellValue(sequence + 1);
            return;
        }
        Object value = col.getValueGetter().apply(data);
        if (value == null) {
            return;
        }
        if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * 设置表体单元格样式
     */
    private void setBodyCellStyle(XSSFCell xssfCell, Column<T> col, T data, XSSFCellStyle columnStyle, Style columnRawStyle, Style conditionalRowStyle, Map<Style, XSSFCellStyle> styleMap) {
        // 应用样式：先列，再行，最后单元格，后者覆盖前者
        Style style = columnRawStyle;

        if (conditionalRowStyle != null) {
            style = style.merge(conditionalRowStyle);
        }

        // 单元格条件样式
        Style conditionalCellStyle = null;
        if (col.getConditionalCellStyleGetter() != null) {
            conditionalCellStyle = col.getConditionalCellStyleGetter().apply(data);
        }
        if (conditionalCellStyle != null) {
            style = style.merge(conditionalCellStyle);
        }

        // 不存在条件样式则设置列样式
        if (conditionalRowStyle == null && conditionalCellStyle == null) {
            xssfCell.setCellStyle(columnStyle);
            return;
        }

        // 相同的条件样式不再创建
        XSSFCellStyle cellStyle = styleMap.get(style);
        if (cellStyle == null) {
            cellStyle = createXssfCellStyle(style);
            styleMap.put(style, cellStyle);
        }
        xssfCell.setCellStyle(cellStyle);
    }

    /**
     * 合并表体单元格
     */
    private void mergeBodyCell(Column<T> col, T data, int rowIndex, int startRowIndex, int colIndex, List<T> rows) {
        if (col.getMergeRowKeyGetter() == null) {
            return;
        }
        Object key = col.getMergeRowKeyGetter().apply(data);
        if (key == null) {
            return;
        }
        // 与上一行相同不合并
        if (rowIndex > 0) {
            T preRowData = rows.get(rowIndex - 1);
            if (Objects.equals(key, col.getMergeRowKeyGetter().apply(preRowData))) {
                return;
            }
        }
        // 合并相邻相同键值行
        int rowSpan = 1;
        while (rowIndex + rowSpan < rows.size()) {
            T nextRowData = rows.get(rowIndex + rowSpan);
            if (!Objects.equals(key, col.getMergeRowKeyGetter().apply(nextRowData))) {
                break;
            }
            rowSpan++;
        }
        if (rowSpan > 1) {
            addMergeArea(rowIndex + startRowIndex, colIndex, rowSpan, 1);
        }
    }

    /**
     * 写表尾
     */
    private void writeFooter(List<Column<T>> expandColumns, int startRowIndex) {
        if (StringUtils.isEmpty(footer)) {
            return;
        }
        CellStyle footerCellStyle = createXssfCellStyle(footerStyle);
        XSSFRow xssfRow = createXssfRow(startRowIndex + footerBodyGap, footerRowHeight);
        for (int i = 0; i < expandColumns.size(); i++) {
            XSSFCell cell = xssfRow.createCell(i);
            cell.setCellStyle(footerCellStyle);
            if (i == 0) {
                cell.setCellValue(footer);
            }
        }
        addMergeArea(startRowIndex + footerBodyGap, 0, 1, expandColumns.size());
    }

    /**
     * 设置列宽
     */
    private void setColumnsWidth(List<Column<T>> expandColumns) {
        int colIndex = 0;
        for (Column<T> col : expandColumns) {
            if (col.getWidth() != null) {
                xssfSheet.setColumnWidth(colIndex, col.getWidth() * 256);
            }
            else {
                xssfSheet.autoSizeColumn(colIndex, true);
            }
            colIndex++;
        }
    }

    /**
     * 冻结视图
     */
    private void freeze(List<Column<T>> expandColumns, int bodyStartRowIndex) {
        int colSplit = 0;
        int colIndex = 0;
        for (Column<T> col : expandColumns) {
            colIndex++;
            if (col.isFreeze()) {
                colSplit = colIndex;
            }
        }
        int rowSplit = 0;
        if (freezeHeader) {
            rowSplit = bodyStartRowIndex;
        }
        if (colSplit > 0 || rowSplit > 0) {
            xssfSheet.createFreezePane(colSplit, rowSplit);
        }
    }


    /**
     * 合并单元格
     */
    private void addMergeArea(int rowIndex, int colIndex, int rowSpan, int colSpan) {
        CellRangeAddress range = new CellRangeAddress(rowIndex, rowIndex + rowSpan - 1, colIndex, colIndex + colSpan - 1);
        xssfSheet.addMergedRegion(range);
    }

    /**
     * 创建行
     */
    private XSSFRow createXssfRow(int rowIndex, float rowHeight) {
        XSSFRow xssfRow = xssfSheet.createRow(rowIndex);
        xssfRow.setHeightInPoints(rowHeight);
        return xssfRow;
    }

    /**
     * 创建样式
     */
    private XSSFCellStyle createXssfCellStyle(Style style) {
        XSSFCellStyle cellStyle = xssfWorkbook.createCellStyle();
        if (style == null) {
            return cellStyle;
        }

        if (StringUtils.isNotEmpty(style.getFontName())) {
            XSSFFont font = xssfWorkbook.createFont();
            font.setFontName(style.getFontName());
            if (style.getFontSize() != null) {
                font.setFontHeight(style.getFontSize());
            }
            if (Boolean.TRUE.equals(style.getFontBold())) {
                font.setBold(true);
            }
            if (StringUtils.isNotEmpty(style.getFontColor())) {
                XSSFColor color = getColor(style.getFontColor());
                font.setColor(color);
            }
            cellStyle.setFont(font);
        }

        if (StringUtils.isNotEmpty(style.getBackgroundColor())) {
            XSSFColor color = getColor(style.getBackgroundColor());
            cellStyle.setFillForegroundColor(color);
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        }

        if (Boolean.TRUE.equals(style.getShowBorder())) {
            cellStyle.setBorderTop(BorderStyle.THIN);
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setBorderBottom(BorderStyle.THIN);
            cellStyle.setBorderLeft(BorderStyle.THIN);
            if (StringUtils.isNotEmpty(style.getBorderColor())) {
                XSSFColor color = getColor(style.getBorderColor());
                cellStyle.setTopBorderColor(color);
                cellStyle.setRightBorderColor(color);
                cellStyle.setBottomBorderColor(color);
                cellStyle.setLeftBorderColor(color);
            }
        }

        if ("center".equalsIgnoreCase(style.getHorizontalAlign())) {
            cellStyle.setAlignment(HorizontalAlignment.CENTER);
        } else if ("left".equalsIgnoreCase(style.getHorizontalAlign())) {
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
        } else if ("right".equalsIgnoreCase(style.getHorizontalAlign())) {
            cellStyle.setAlignment(HorizontalAlignment.RIGHT);
        }
        if ("center".equalsIgnoreCase(style.getVerticalAlign())) {
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        } else if ("top".equalsIgnoreCase(style.getVerticalAlign())) {
            cellStyle.setVerticalAlignment(VerticalAlignment.TOP);
        } else if ("bottom".equalsIgnoreCase(style.getVerticalAlign())) {
            cellStyle.setVerticalAlignment(VerticalAlignment.BOTTOM);
        }

        if (StringUtils.isNotEmpty(style.getDataFormat())) {
            DataFormat dataFormat = xssfWorkbook.createDataFormat();
            cellStyle.setDataFormat(dataFormat.getFormat(style.getDataFormat()));
        }

        return cellStyle;
    }

    /**
     * 创建颜色
     */
    private XSSFColor getColor(String hex) {
        hex = hex.toUpperCase();
        if (!hex.matches("^[0-9A-F]{6}$")) {
            hex = "000000";
        }
        int r = Integer.valueOf(hex.substring(0, 2), 16);
        int g = Integer.valueOf(hex.substring(2, 4), 16);
        int b = Integer.valueOf(hex.substring(4, 6), 16);
        byte[] colorBytes = {(byte) r, (byte) g, (byte) b};
        return new XSSFColor(colorBytes, null);
    }
}
