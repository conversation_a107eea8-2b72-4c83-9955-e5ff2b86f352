package com.sure.question.util;

import cn.hutool.core.util.StrUtil;

/**
 * 脱敏工具类
 */
public class DesensitizedUtils {
    /**
     * 对字符串进行脱敏操作
     *
     * @param str     原始字符串
     * @param front   左侧需要保留几位明文字段
     * @param end     右侧需要保留几位明文字段
     * @param maskStr 用于遮罩的字符串, 如'*'
     * @return 脱敏后结果
     */
    public static String desValue(String str, int front, int end, String maskStr) {
        if (StrUtil.isEmpty(str)) {
            return str;
        }

        StringBuilder sb = new StringBuilder();
        int n = str.length();
        if (front + end == n) {
            end = 0;
        }
        for (int i = 0; i < n; i++) {
            if (i < front || i > (n - end - 1)) {
                sb.append(str.charAt(i));
            } else {
                sb.append(maskStr);
            }
        }

        return sb.toString();
    }

    /**
     * 姓名脱敏（两个字符内的只显示第一个，两个以上的只显示第一个字符和一个字符，其他隐藏为星号，比如：张*，李*民
     *
     * @param fullName 姓名
     * @return 结果
     */
    public static String chineseName(String fullName) {
        return desValue(fullName, 1, 1, "*");
    }
}
