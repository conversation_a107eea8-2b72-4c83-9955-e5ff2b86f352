package com.sure.question.util;

import com.sure.common.exception.ApiException;
import org.apache.commons.io.IOUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.cos.COSDictionary;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

public class PdfUtil {
    public static List<String> pdfToImage(InputStream pdfFileStream, float dpi, int maxPage, Function<Integer, String> imagePathGetter) throws IOException {
        List<String> imgPathList = new ArrayList<>();
        byte[] bytes = IOUtils.toByteArray(pdfFileStream);
        try (PDDocument document = Loader.loadPDF(bytes)) {
            if (document.getNumberOfPages() > maxPage) {
                throw new ApiException("pdf文件最多支持" + maxPage + "页");
            }
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            for (int page = 0; page < document.getNumberOfPages(); page++) {
                BufferedImage image = pdfRenderer.renderImageWithDPI(page, dpi);
                String imagePath = imagePathGetter.apply(page);
                ImageIO.write(image, "png", new File(imagePath));
                imgPathList.add(imagePath);
            }
        }
        return imgPathList;
    }

    /**
     * 清除pdf文件的元数据，如标题、作者、备注等
     */
    public static byte[] removeMetaData(MultipartFile file) throws IOException {
        return removeMetaData(file.getInputStream());
    }

    public static byte[] removeMetaData(InputStream pdfFileStream) throws IOException {
        byte[] bytes = IOUtils.toByteArray(pdfFileStream);
        try (PDDocument document = Loader.loadPDF(bytes)) {
            // 清空 Info Dictionary
            COSDictionary infoDict = document.getDocumentInformation().getCOSObject();
            infoDict.clear();

            // 删除 XMP 元数据流
            document.getDocumentCatalog().getCOSObject().removeItem(COSName.METADATA);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            document.save(out);
            return out.toByteArray();
        }
    }
}
