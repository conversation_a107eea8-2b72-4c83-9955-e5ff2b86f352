package com.sure.question.util;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.sure.question.common.IdWorker;
import com.sure.question.entity.Question;
import com.sure.question.entity.QuestionKnowledge;
import com.sure.question.entity.SmallQuestion;
import com.sure.question.service.OssManager;
import com.sure.question.service.WaterMarkManager;
import com.sure.question.vo.question.QuestionVo;
import com.sure.question.vo.question.SmallQuestionKnowledgeVo;
import com.sure.question.vo.question.SmallQuestionVo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class GrapeUtil {
    private final WaterMarkManager finalWaterMarkManager;
    private final OssManager finalOssManager;
    private final IdWorker finalIdWorker;

    private static WaterMarkManager waterMarkManager;
    private static OssManager ossManager;
    private static IdWorker idWorker;
    private static final String jpeg = "data:image/jpeg;base64,";
    private static final String jpg = "data:image/jpg;base64,";
    private static final String png = "data:image/png;base64,";
    private static final String gif = "data:image/gif;base64,";

    @PostConstruct
    private void init() {
        waterMarkManager = finalWaterMarkManager;
        ossManager = finalOssManager;
        idWorker = finalIdWorker;
    }

    // 将 img 标签里的 src 连接的图片转成base64
    public static String exchangImg(String html) {
        if (html.contains("http:") || html.contains("https:")) {

            Document parse = Jsoup.parse(html);
            Elements img = parse.getElementsByTag("img");
            String url = null;
            for (Element e : img) {
                // 获取http连接
                url = e.attr("src");
                try {
                    String s = jpg + img2Base64(url);
                    e.attr("src", s);
                    e.removeAttr("alt");
                } catch (Exception e1) {
                    e1.printStackTrace();
                }
            }
            html = parse.body().html();
        }
        return html;
    }

    public static String img2Base64(String url) {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient httpClient = httpClientBuilder.build();

        int k = url.indexOf("http");
        if (k > 0) {
//            url = url.substring(k, url.length() - k);
            url = url.substring(k);
        }
        if (url.endsWith("\\")) {
            url = url.substring(0, url.length() - 1);
        }

        HttpGet httpGet = new HttpGet(url);

        String result = StringUtils.EMPTY;
        try {
            CloseableHttpResponse response = httpClient.execute(httpGet);
            InputStream content = response.getEntity().getContent();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = (content.read(buffer))) != -1) {
                outputStream.write(buffer, 0, len);
            }
            result = Base64.encode(outputStream.toByteArray());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    // 替换 Question 里面的内容
    public static void quesReplaceImage(Question question, String type) {
        try {
            String pathPre = question.getId();
            question.setContentHtml(solution2Img(question.getContentHtml(), pathPre, "image"));// 题目内容
            question.setDivideHtml(solution2Img(question.getDivideHtml(), pathPre, "image"));// 划题内容
            question.setAnswerHtml(solution2Img(question.getAnswerHtml(), pathPre, "image"));// 答案
            question.setExplanation(solution2Img(question.getExplanation(), pathPre, "image"));// 解析
            question.setSolution(solution2Img(question.getSolution(), pathPre, "image"));// 解答
            question.setComment(solution2Img(question.getComment(), pathPre, "image"));// 点评
            question.setOptions(Option2Img(question.getOptions(), pathPre, "image"));// 选项
            question.setMaterialHtml(solution2Img(question.getMaterialHtml(), pathPre, "image"));
        } catch (Exception e) {
        }
    }

    public static void quesReplaceImage(QuestionVo question) {
        try {
            String pathPre = question.getId();
            question.setTrunk(solution2Img(question.getTrunk(), pathPre, "base64"));// 题目内容
            question.setDivide(solution2Img(question.getDivide(), pathPre, "base64"));// 划题内容
            question.setComment(solution2Img(question.getComment(), pathPre, "base64"));// 点评
            List<SmallQuestionVo> branches = question.getBranches();
            for (SmallQuestionVo smallQuestionVo : branches) {
                // 封装小题
                SmallQuestion smallQuestion = new SmallQuestion();
                smallQuestion.setId(smallQuestionVo.getId());// 封装ID
                smallQuestion.setUpdateTime(System.currentTimeMillis());// 创建时间
                smallQuestion.setQuestionId(question.getId());// 大题id
                smallQuestion.setAnswerHtml(smallQuestionVo.getSolution());// 题目答案
                smallQuestion.setContentHtml(smallQuestionVo.getStem());// 题目内容
                smallQuestion.setQuestionNo(smallQuestionVo.getCode());// 小题题号
                // 设置选项内容  miko
                smallQuestion.setOptions(smallQuestionVo.getOptionsJson());

                smallQuestion.setQuestionTypeId(smallQuestionVo.getType());// 小题类型
                smallQuestion.setExplanation(smallQuestionVo.getExplanation());// 小题解析
                // TODO: 设置分数
                smallQuestion.setScore(smallQuestionVo.getScore());
                // 设置解析2
                smallQuestion.setExpla(smallQuestionVo.getExpla());
                // 原知识点题目
                smallQuestion.setKnowledgeName(smallQuestionVo.getKnowledgeName());
                // 设置点评
                // 知识点
                List<SmallQuestionKnowledgeVo> knowledges = smallQuestionVo.getKnowledges();

                for (int k = 0; k < knowledges.size(); k++) {
                    SmallQuestionKnowledgeVo next = knowledges.get(k);
                    QuestionKnowledge questionKnowledge = new QuestionKnowledge();
                    questionKnowledge.setKnowledgeId(next.getId());
                    questionKnowledge.setQuestionId(smallQuestionVo.getId());
                    questionKnowledge.setQuestionNo(k);
                    questionKnowledge.setUpdateTime(System.currentTimeMillis());
                }
                GrapeUtil.sqReplaceImage(smallQuestion, "base64");
                smallQuestionVo.setSolution(smallQuestion.getAnswerHtml());// 题目答案
                smallQuestionVo.setStem(smallQuestion.getContentHtml());// 题目内容
                smallQuestionVo.setCode(smallQuestion.getQuestionNo());// 小题题号
                smallQuestionVo.setOptionsJson(smallQuestion.getOptions());
            }
        } catch (Exception e) {
        }
    }

    // 替换 SmallQuestion 里面的内容
    public static void sqReplaceImage(SmallQuestion sq, String type) {
        try {
            String pathPre = sq.getQuestionId();
            sq.setContentHtml(solution2Img(sq.getContentHtml(), pathPre, type));// 小题内容
            sq.setAnswerHtml(solution2Img(sq.getAnswerHtml(), pathPre, type));// 答案
            sq.setExplanation(solution2Img(sq.getExplanation(), pathPre, type));// 小题解析
            sq.setExpla(solution2Img(sq.getExpla(), pathPre, type));// 解析2
            sq.setOptions(Option2Img(sq.getOptions(), pathPre, type));// 选项
        } catch (Exception e) {
        }
    }


    //
    public static String replaceImage(String html, String prePath, String type) throws IOException {
        if (StringUtils.isBlank(html) || html.equals("null")) {
            return null;
        }
        String s = "<html><body>" + html + "</body></html>";
        Document document = Jsoup.parse(s);

        Elements img = document.getElementsByTag("img");
        for (Element e : img) {
            // 获取http连接
            String src = e.attr("src");
            String newPath = "";
//            if (src.startsWith(jpg)) newPath = prePath + "_" + "a.jpg";
//            if (src.startsWith(png)) newPath = prePath + "_" + "a.png";
//            if (src.startsWith(gif)) newPath = prePath + "_" + "a.gif";
//            if (src.startsWith(jpeg)) newPath = prePath + "_" + "a.jpeg";
            if (src.startsWith(jpg)) newPath = prePath + "_" + idWorker.nextId() + ".jpg";
            if (src.startsWith(png)) newPath = prePath + "_" + idWorker.nextId() + ".png";
            if (src.startsWith(gif)) newPath = prePath + "_" + idWorker.nextId() + ".gif";
            if (src.startsWith(jpeg)) newPath = prePath + "_" + idWorker.nextId() + ".jpeg";
            if (StringUtils.isNotBlank(newPath)) {
                int i = newPath.lastIndexOf("/");
                String name = newPath.substring(i + 1, newPath.length());
//                base64ToImage(src, newPath);
                if ("image".equals(type)) {
                    base64ToImage2(src, name);
                    e.attr("src", ossManager.getPublicPathTK(name));
                } else if ("base64".equals(type)) {
                    if (src.startsWith("data")) {
                        String baseStr = waterMarkManager.baseStrToUrlRemoveAndWaterMark(src, true, prePath);
                        e.attr("src", baseStr);
                    } else if (src.startsWith("https://tkimgs")) {
                        base64ToImage2(src, name);
                        e.attr("src", ossManager.getPublicPathTK(name));
                    }
                }
            }
//            e.removeAttr("alt");
        }
        Document.OutputSettings outputSettings = document.outputSettings();
        outputSettings.prettyPrint(false);
        document.outputSettings(outputSettings);

        //        String text = document.body().html().replaceAll("\n\\s*", "");
        return document.body().html();
    }

    // 存到 oos
    public static void base64ToImage2(String baseStr, String name) {
        if (StringUtils.isNotBlank(baseStr)) {
            String content = baseStr.replaceFirst(jpg, "");
            content = content.replaceFirst(png, "");
            content = content.replaceFirst(gif, "");
            content = content.replaceFirst(jpeg, "");

            byte[] bytes = cn.hutool.core.codec.Base64.decode(content);
            ossManager.uploadTK(name, bytes);
        }
    }


    // 替换选项中的base64
    public static String Option2Img(String html, String prePath, String type) throws IOException {
        if (StringUtils.isBlank(html) || html.equals("null")) return null;
        JSONArray opsJson = JSONArray.parseArray(html);
        ArrayList<Map<String, String>> opsList = new ArrayList<>();
        if (opsJson != null && !opsJson.isEmpty()) {
            for (Object obj : opsJson) {
                JSONObject jsonObject = JSON.parseObject(obj.toString());
                HashMap<String, String> opsMap = new HashMap<>();
                Object label = jsonObject.get("label");
                Object content = jsonObject.get("content");
                if (label != null) {
                    opsMap.put("label", label.toString());// 选项
                }
                if (content != null) {
                    String contentStr = content.toString();
                    contentStr = GrapeUtil.replaceImage(contentStr, prePath, type);
                    opsMap.put("content", contentStr);// 选项内容
                }
                opsList.add(opsMap);
            }
        }
        return JSON.toJSONString(opsList);
    }

    // 替换答案中的base64
    public static String solution2Img(String html, String prePath, String type) throws IOException {
        if (StringUtils.isBlank(html) || html.equals("null")) return null;
        try {
            JSONArray opsJson = JSONArray.parseArray(html);
            ArrayList<String> list = new ArrayList<>();
            for (Object next : opsJson) {
                if (next != null) {
                    String s = next.toString();
                    s = GrapeUtil.replaceImage(s, prePath, type);
                    list.add(s);
                }
            }
            return JSON.toJSONString(list);
        } catch (JSONException e) {
            return GrapeUtil.replaceImage(html, prePath, type);
        }
    }
}
