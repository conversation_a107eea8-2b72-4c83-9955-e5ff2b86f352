package com.sure.question.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

/**
 * 教辅类型枚举
 */
@Getter
public enum CoachBookTypeEnum {
    /**
     * 定制教辅
     */
    SYS(1, "定制教辅"),

    /**
     * 初中校本教辅
     */
    SCHOOL(2, "校本教辅");


    @EnumValue
    private final Integer code;
    private final String name;

    CoachBookTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CoachBookTypeEnum getValue(Integer code) {
        for (CoachBookTypeEnum r : values()) {
            if (r.getCode().equals(code)) {
                return r;
            }
        }
        return null;
    }
}
