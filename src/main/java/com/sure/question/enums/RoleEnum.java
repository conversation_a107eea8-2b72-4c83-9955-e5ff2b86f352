package com.sure.question.enums;

import lombok.Getter;

/**
 * 角色枚举类
 */
@Getter
public enum RoleEnum {
    SchoolManager(1, "学校管理员"),
    Principal(2, "校长"),
    Teacher(3, "老师"),
    GradeHead(4, "年级主任"),
    SubjectHead(5, "学科组长"),
    ClassPrepareHead(6, "备课组长"),
    Head<PERSON>eacher(7, "班主任"),
    Parents(8, "家长"),
    Student(9, "学生"),
    SysManager(101, "题库管理员"),
    SysEditor(102, "题库编辑员"),
    SuperManager(200, "超级管理员");

    private final Integer code;
    private final String roleName;

    RoleEnum(Integer code, String roleName) {
        this.code = code;
        this.roleName = roleName;
    }

    public static RoleEnum getValue(Integer code) {
        for (RoleEnum r : values()) {
            if (r.getCode().equals(code)) {
                return r;
            }
        }
        return null;
    }


}
