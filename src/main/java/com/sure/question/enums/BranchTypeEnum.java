package com.sure.question.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BranchTypeEnum implements BaseEnum<Integer, String> {
    SingleChoice(11, "单选题", true, true),
    MultipleChoice(12, "多选题", true, true),
    TrueOrFalse(13, "判断题", true, false),
    FillBlank(21, "填空题", false, false),
    Essay(22, "解答题", false, false),
    ChineseWriting(23, "语文作文", false, false),
    EnglishWriting(24, "英语作文", false, false);

    private final Integer id;
    private final String name;
    /**
     * 是否客观题
     */
    private final Boolean isObjective;
    /**
     * 是否选择题
     */
    private final Boolean isChoice;
}
