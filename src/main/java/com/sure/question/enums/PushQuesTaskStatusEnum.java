package com.sure.question.enums;

import lombok.Getter;

/**
 * 推题任务状态枚举
 *
 * <AUTHOR>
 * @date 2023/2/17 9:41
 */
@Getter
public enum PushQuesTaskStatusEnum {

    /**
     * 待处理
     */
    PENDING(0, "待处理"),
    /**
     * 处理中
     */
    HANDLING(1, "处理中"),
    /**
     * 处理成功
     */
    SUCCESS(2, "处理成功"),
    /**
     * 处理失败
     */
    FAIL(3, "处理失败"),
    /**
     * 已清空
     */
    CLEANED(4, "已清空");

    private final int code;
    private final String desc;

    PushQuesTaskStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
