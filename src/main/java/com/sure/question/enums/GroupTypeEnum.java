package com.sure.question.enums;

import lombok.Getter;

/**
 * 角色枚举类
 */
@Getter
public enum GroupTypeEnum {
    School(1, "校本库"),
    Personal(2, "自定义");

    private final Integer code;
    private final String roleName;

    GroupTypeEnum(Integer code, String roleName) {
        this.code = code;
        this.roleName = roleName;
    }

    public static GroupTypeEnum getValue(Integer code) {
        for (GroupTypeEnum r : values()) {
            if (r.getCode().equals(code)) {
                return r;
            }
        }
        return null;
    }


}
