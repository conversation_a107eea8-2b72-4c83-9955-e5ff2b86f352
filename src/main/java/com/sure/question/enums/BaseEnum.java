package com.sure.question.enums;

import java.util.Objects;

public interface BaseEnum <U, V> {
    U getId();
    V getName();

    /**
     * 由Id或Code获取枚举
     */
    static <E extends Enum<E> & BaseEnum<U, V>, U, V> E getById(Class<E> enumClass, U id) {
        for (E enumConstant : enumClass.getEnumConstants()) {
            if (Objects.equals(enumConstant.getId(), id)) {
                return enumConstant;
            }
        }
        return null;
    }

    /**
     * 由名称或说明获取枚举
     */
    static <E extends Enum<E> & BaseEnum<U, V>, U, V> E getByName(Class<E> enumClass, V name) {
        for (E enumConstant : enumClass.getEnumConstants()) {
            if (Objects.equals(enumConstant.getName(), name)) {
                return enumConstant;
            }
        }
        return null;
    }

    /**
     * 由Id或Code获取名称或说明
     */
    static <E extends Enum<E> & BaseEnum<U, V>, U, V> V getNameById(Class<E> enumClass, U id) {
        E enumConstant = BaseEnum.getById(enumClass, id);
        return enumConstant == null ? null : enumConstant.getName();
    }

    /**
     * 由名称或说明获取Id或Code
     */
    static <E extends Enum<E> & BaseEnum<U, V>, U, V> U getIdByName(Class<E> enumClass, V name) {
        E enumConstant = BaseEnum.getByName(enumClass, name);
        return enumConstant == null ? null : enumConstant.getId();
    }
}
