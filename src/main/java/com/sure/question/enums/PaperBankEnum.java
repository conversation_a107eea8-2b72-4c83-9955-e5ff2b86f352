package com.sure.question.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

@Getter
public enum PaperBankEnum {
    /**
     * 个人库
     */
    Personal(1, "个人库"),

    /**
     * 校本库
     */
    School(2, "校本库"),

    /**
     * 公共库
     */
    Public(3, "公共库"),

    /**
     * 订制资源
     */
    Custom(4, "订制资源"),

    /**
     * 分享库
     */
    Share(5, "分享库");

    @EnumValue
    private final Integer code;
    private final String bankName;

    PaperBankEnum(Integer code, String bankName) {
        this.code = code;
        this.bankName = bankName;
    }
}
