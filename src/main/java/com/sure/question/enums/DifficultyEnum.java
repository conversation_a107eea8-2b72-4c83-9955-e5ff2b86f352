package com.sure.question.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DifficultyEnum implements BaseEnum<Integer, String> {
    Easy(1, "容易"),
    Light(2, "较易"),
    Medium(3, "中等"),
    Challenging(4, "较难"),
    Hard(5, "困难");

    private final Integer id;
    private final String name;

    /**
     * 得分率转为难度
     */
    public static DifficultyEnum fromScoreRate(float scoreRate) {
        if (scoreRate < 0 || scoreRate > 1) {
            return null;
        }
        if (scoreRate < 0.2F) {
            return Hard;
        } else if (scoreRate < 0.4F) {
            return Challenging;
        } else if (scoreRate < 0.6F) {
            return Medium;
        } else if (scoreRate < 0.8F) {
            return Light;
        } else {
            return Easy;
        }
    }
}
