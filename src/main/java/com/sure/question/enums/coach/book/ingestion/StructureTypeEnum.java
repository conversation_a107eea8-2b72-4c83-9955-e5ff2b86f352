package com.sure.question.enums.coach.book.ingestion;

import com.sure.question.enums.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum StructureTypeEnum implements BaseEnum<String, String> {
    COVER("cover", "封面"),
    TOC("toc", "目录"),
    MAIN_BODY("main_body", "正文"),
    ANSWER("answer", "答案解析");

    private final String id;
    private final String name;
}
