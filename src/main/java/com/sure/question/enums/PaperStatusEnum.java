package com.sure.question.enums;

import lombok.Getter;

@Getter
public enum PaperStatusEnum {

    // 显示状态
    Status0(0, "正常"),
    Status1(1, "删除"),
    // 编辑状态
    EditStatus0(0, "未完成划题标注"),
    EditStatus1(1, "完成制卷"),
    // 分配状态
    AllotStatus0(0,"不需要分配"),
    AllotStatus1(1,"待分配"),
    AllotStatus2(2, "已分配"),
    // 分享状态
    ShareStatus0(0, "未分享"),
    ShareStatus1(1, "已分享"),
    // 审核状态
    CheckStatus0(0, "未审核"),
    CheckStatus1(1, "审核未通过"),
    CheckStatus2(2, "审核通过"),
    ;

    private final Integer code;
    private final String desc;

    PaperStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
