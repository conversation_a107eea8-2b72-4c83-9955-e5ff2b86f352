<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.SchoolPaperMapper">
    <update id="updateToDeleted">
        update school_paper
        set is_delete = true,
        delete_id = #{userId},
        delete_time = now()
        where school_id = #{schoolId}
        and is_delete = false
        and paper_id in
        <foreach collection="paperIds" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>

    <update id="updateToUnDeleted">
        update school_paper
        set is_delete = false,
        delete_id = null,
        delete_time = null
        where school_id = #{schoolId}
        and is_delete = true
        and paper_id in
        <foreach collection="paperIds" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>

    <update id="updateIsPublicBatch">
        update school_paper
        set is_public = #{isPublic}
        where school_id = #{schoolId}
        and is_delete = false
        and paper_id in
        <foreach collection="paperIds" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>

    <select id="getShareRecords" resultType="com.sure.question.vo.paperVo.SchoolPaperVo">
        SELECT t2.`id`,
        t2.`paper_name` AS paperName,
        t2.`grade_level` AS gradeLevel,
        t2.`subject_id` AS subjectId,
        t2.`grade_id` AS gradeId,
        t2.`term`,
        t2.`year`,
        t2.`region_id` AS regionId,
        t2.flag_coach_book_id as flagCoachBookId,
        t2.paper_group_id as paperGroupId,
        t2.`paper_type_id` AS paperTypeId,
        t2.`paper_bank` AS paperBank,
        t1.`id` schoolPaperId,
        t1.`school_id` AS schoolId,
        t1.`create_id` createUserId,
        t1.`create_time` AS createTime,
        t1.`browse_count` AS browseCount,
        t1.`download_count` AS downloadCount,
        t1.`source`,
        t1.`is_public` AS isPublic,
        t1.`is_delete` AS isDelete
        FROM school_paper t1, paper_main t2
        WHERE t1.`paper_id` = t2.`id`
        AND t1.`create_id` = #{userId}
        <if test="schoolId != null and schoolId != ''">AND t1.`school_id` = #{schoolId}</if>
        <if test="gradeLevel != null">AND t2.`grade_level` = #{gradeLevel}</if>
        <if test="gradeId != null">AND t2.`grade_id` = #{gradeId}</if>
        <if test="term != null">AND t2.`term` = #{term}</if>
        <if test="subjectId != null">AND t2.`subject_id` = #{subjectId}</if>
        <if test="paperTypeId != null">AND t2.`paper_type_id` = #{paperTypeId}</if>
        <if test="year != null">AND t2.`year` = #{year}</if>
        <if test="regionId != null and regionId != ''">AND t2.`region_id` like concat(#{regionId}, '%')</if>
        <if test="bookType == 'coach' and flagCoachBookId != null">AND t2.flag_coach_book_id = #{flagCoachBookId}</if>
        <if test="bookType == 'coach' and flagCoachBookId == null">AND t2.flag_coach_book_id IS NOT NULL</if>
        <if test="bookType == 'normal'">AND t2.flag_coach_book_id IS NULL</if>
        ORDER BY t1.`create_time` DESC
    </select>

    <select id="getSchoolPapers" resultType="com.sure.question.vo.paperVo.SchoolPaperVo">
        SELECT t2.`id`,
        t2.`paper_name`,
        t2.`grade_level`,
        t2.`subject_id`,
        t2.`grade_id`,
        t2.`term`,
        t2.`year`,
        t2.`region_id`,
        t2.`paper_type_id`,
        t2.`paper_bank`,
        t1.`id` schoolPaperId,
        t1.`school_id`,
        t1.`create_id` createUserId,
        t1.`create_time`,
        t1.`browse_count`,
        t1.`download_count`,
        t1.`source`,
        t1.`is_public`,
        t1.`is_delete`
        FROM school_paper t1, paper_main t2
        WHERE t1.`paper_id` = t2.`id`
        AND t1.`school_id` = #{schoolId}
        AND t1.`is_delete` = FALSE
        AND t1.`is_public` = TRUE
        <if test="gradeLevel != null">AND t2.`grade_level` = #{gradeLevel}</if>
        <if test="gradeId != null">AND t2.`grade_id` = #{gradeId}</if>
        <if test="term != null">AND t2.`term` = #{term}</if>
        <if test="subjectId != null">AND t2.`subject_id` = #{subjectId}</if>
        <if test="paperTypeId != null">AND t2.`paper_type_id` = #{paperTypeId}</if>
        <if test="year != null">AND t2.`year` = #{year}</if>
        <if test="regionId != null and regionId != ''">AND t2.`region_id` like concat(#{regionId}, '%')</if>
        ORDER BY t1.`create_time` DESC
    </select>
</mapper>