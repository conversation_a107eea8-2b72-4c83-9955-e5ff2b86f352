<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.CoachBookSheetMapper">
    <insert id="insertBatch">
        INSERT INTO coach_book_sheet (coach_book_id, sheet_id, sort_code, create_by, create_time, is_delete) VALUES
        <foreach collection="batch" item="item" separator=",">
            (#{item.coachBookId}, #{item.sheetId}, #{item.sortCode}, #{item.createBy}, #{item.createTime}, #{item.isDelete})
        </foreach>
    </insert>

    <select id="selectMaxSortCode" resultType="integer">
        SELECT MAX(sort_code) FROM coach_book_sheet WHERE coach_book_id = #{coachBookId}
    </select>

    <update id="updateSortCodeBatch">
        <foreach collection="batch" item="item" separator=";">
            UPDATE coach_book_sheet
            SET sort_code = #{item.sortCode}
            WHERE coach_book_id = #{item.coachBookId}
            AND sheet_id = #{item.sheetId}
        </foreach>
    </update>
</mapper>
