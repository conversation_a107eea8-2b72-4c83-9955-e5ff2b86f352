<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.CleanTaskSmallQuestionMapper">
    <insert id="insertBatch">
        INSERT INTO clean_task_small_question
        VALUES
        <foreach collection="entities" item="item" separator=",">
            (#{item.taskId},
             #{item.smallQuestionId},
             #{item.questionId},
             #{item.contentHtml},
             #{item.oldContentHtml},
             #{item.options},
             #{item.oldOptions},
             #{item.answerHtml},
             #{item.oldAnswerHtml},
             #{item.explanation},
             #{item.oldExplanation},
             #{item.createTime})
        </foreach>
    </insert>

    <update id="updateSmallQuestionContent">
        UPDATE small_question q, clean_task_small_question c
        SET q.content_html = CASE WHEN c.content_html IS NULL OR c.content_html = '' THEN q.content_html ELSE c.content_html END,
            q.options = CASE WHEN c.options IS NULL OR c.options = '' THEN q.options ELSE c.options END,
            q.answer_html = CASE WHEN c.answer_html IS NULL OR c.answer_html = '' THEN q.answer_html ELSE c.answer_html END,
            q.explanation = CASE WHEN c.explanation IS NULL OR c.explanation = '' THEN q.explanation ELSE c.explanation END
        WHERE c.task_id = #{taskId}
            AND c.question_id = #{questionId}
            AND q.id = c.small_question_id
            AND q.question_id = c.question_id
    </update>
</mapper>
