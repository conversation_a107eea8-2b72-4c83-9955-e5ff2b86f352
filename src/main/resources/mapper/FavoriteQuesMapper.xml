<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.FavoriteQuesMapper">
    <select id="selectUserFavoriteQuestionIds" resultType="java.lang.String">
        SELECT question_id
        FROM favorite_ques
        WHERE user_id = #{userId}
        <if test="questionIds != null and !questionIds.isEmpty()">
            AND question_id IN <foreach collection="questionIds" item="o" separator="," open="(" close=")">#{o}</foreach>
        </if>
    </select>
</mapper>
