<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.CleanTaskQuestionMapper">
    <insert id="insertBatch">
        INSERT INTO clean_task_question
        VALUES
        <foreach collection="entities" item="item" separator=",">
            (#{item.taskId}, #{item.questionId}, #{item.paperId}, #{item.contentHtml}, #{item.oldContentHtml}, #{item.createTime}, #{item.confirmStatus}, #{item.confirmTime}, #{item.isUpdated}, #{item.updateTime})
        </foreach>
    </insert>

    <select id="selectTaskStats" resultType="com.sure.question.vo.cleanTask.StatsVo">
        SELECT task_id taskId,
               COUNT(*) checkedQuestionCount,
               COUNT(IF(confirm_status = 1 AND is_updated = 0, 1, NULL)) confirmedQuestionCount,
               COUNT(IF(confirm_status = 2 AND is_updated = 0, 1, NULL)) ignoredQuestionCount,
               COUNT(IF(is_updated = 1, 1, NULL)) updatedQuestionCount
        FROM clean_task_question
        WHERE task_id IN <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">#{taskId}</foreach>
        GROUP BY task_id
    </select>

    <update id="updateQuestionContent">
        UPDATE question q, clean_task_question c
        SET q.content_html = c.content_html
        WHERE c.task_id = #{taskId}
            AND c.question_id = #{questionId}
            AND q.id = c.question_id
            AND c.content_html IS NOT NULL
            AND c.content_html != ''
    </update>
</mapper>
