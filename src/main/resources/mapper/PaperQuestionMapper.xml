<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.PaperQuestionMapper">

    <select id="selectQuestionsPublicPaper" resultType="com.sure.question.dto.question.QuestionPublicPaperDto">
        SELECT pq.question_id, pq.paper_id, p.paper_name, p.year, p.paper_type_id
        FROM paper_question pq, paper_main p
        WHERE pq.question_id IN <foreach collection="questionIds" item="qId" open="(" close=")" separator=",">#{qId}</foreach>
        AND p.id = pq.paper_Id
        AND p.`status` = 0
        AND p.paper_bank = 3
    </select>

    <select id="selectPaperQuestionIds" resultType="java.lang.String">
        SELECT question_id
        FROM paper_question
        WHERE paper_id = #{paperId}
    </select>

    <insert id="insertList">
        INSERT INTO `paper_question` (
        `id`,
        `paper_id`,
        `question_id`,
        `score`,
        `order_id`,
        `create_time`,
        `status`
        )
        VALUES
        <foreach collection="os" item="o" separator=",">
            (
            #{o.id},
            #{o.paperId},
            #{o.questionId},
            #{o.score},
            #{o.orderId},
            #{o.createTime},
            #{o.status}
            )
        </foreach>
    </insert>

    <select id="selectEditorAllotViewPaperTaskList" resultType="com.sure.question.vo.EntryVo">
        SELECT editor_id as `key`, COUNT(*) as `value`
        FROM paper_main
        WHERE grade_level = #{gradeLevelId}
          AND subject_id = #{subjectId}
          AND `status` = 0
          AND editor_id in <foreach collection="editorIds" item="id" open="(" close=")" separator=",">#{id}</foreach>
        GROUP BY editor_id
    </select>

    <select id="selectEditorViewPaperStats" resultType="com.sure.question.vo.paperVo.EditorSubmitPaperQuestionStatsVo">
        SELECT p.id as paperId,
               p.editor_id as editorId,
               p.submit_time as submitTime,
               COUNT(DISTINCT pq.question_id) as questionCount,
               COUNT(*) as smallQuestionCount
        FROM paper_main as p,
             paper_question as pq,
             small_question as sq
        WHERE p.editor_id in <foreach collection="editorIds" item="id" open="(" close=")" separator=",">#{id}</foreach>
          AND p.grade_level = #{gradeLevelId}
          AND p.subject_id = #{subjectId}
          AND p.share_status = 1
          AND p.status = 0
          AND pq.paper_id = p.id
          AND sq.question_id = pq.question_id
        GROUP BY p.id, p.editor_id, p.submit_time
    </select>

    <select id="selectEditorMakePaperStats" resultType="com.sure.question.vo.paperVo.EditorSubmitPaperQuestionStatsVo">
        SELECT p.id as paperId,
               p.user_id as editorId,
               p.create_time as submitTime,
               COUNT(DISTINCT pq.question_id) as questionCount,
               COUNT(*) as smallQuestionCount
        FROM paper_main as p,
             paper_question as pq,
             small_question as sq
        WHERE p.user_id in <foreach collection="editorIds" item="id" open="(" close=")" separator=",">#{id}</foreach>
          AND p.grade_level = #{gradeLevelId}
          AND p.subject_id = #{subjectId}
          AND p.status = 0
          AND p.upload_type = 2
            <if test="bookType != null">
              <if test="bookType == 'normal'">
                AND p.flag_coach_book_id IS NULL
              </if>
              <if test="bookType == 'coach'">
                AND p.flag_coach_book_id IS NOT NULL
              </if>
            </if>
          AND pq.paper_id = p.id
          AND sq.question_id = pq.question_id
        GROUP BY p.id, p.user_id, p.create_time
    </select>

    <select id="selectEditorUploadPaperStats" resultType="com.sure.question.vo.paperVo.EditorSubmitPaperQuestionStatsVo">
        SELECT p.id as paperId,
               p.user_id as editorId,
               p.create_time as submitTime,
               COUNT(DISTINCT pq.question_id) as questionCount,
               COUNT(*) as smallQuestionCount
        FROM paper_main as p,
             paper_question as pq,
             small_question as sq
        WHERE p.user_id in <foreach collection="editorIds" item="id" open="(" close=")" separator=",">#{id}</foreach>
          AND p.grade_level = #{gradeLevelId}
          AND p.subject_id = #{subjectId}
          AND p.status = 0
          AND p.upload_type = 0
          AND p.edit_status = 1
            <if test="bookType != null">
                <if test="bookType == 'normal'">
                    AND p.flag_coach_book_id IS NULL
                </if>
                <if test="bookType == 'coach'">
                    AND p.flag_coach_book_id IS NOT NULL
                </if>
            </if>
          AND pq.paper_id = p.id
          AND sq.question_id = pq.question_id
        GROUP BY p.id, p.user_id, p.create_time
    </select>
</mapper>
















