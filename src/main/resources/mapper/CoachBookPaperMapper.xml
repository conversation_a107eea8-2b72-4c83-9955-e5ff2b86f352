<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.CoachBookPaperMapper">
    <insert id="insertBatch">
        INSERT INTO coach_book_paper (paper_id, coach_book_id, sort_code, create_by, create_time, open_time) VALUES
        <foreach collection="batch" item="item" separator=",">
            (#{item.paperId}, #{item.coachBookId}, #{item.sortCode}, #{item.createBy}, #{item.createTime}, #{item.openTime})
        </foreach>
    </insert>

    <update id="updateSortCodeBatch">
        <foreach collection="batch" item="item" separator=";">
            UPDATE coach_book_paper
            SET sort_code = #{item.sortCode}
            WHERE coach_book_id = #{item.coachBookId}
            AND paper_id = #{item.paperId}
        </foreach>
    </update>

    <delete id="deleteBatch">
        DELETE FROM coach_book_paper
        WHERE coach_book_id = #{coachBookId}
        AND paper_id IN
        <foreach collection="batch" item="id" separator="," open="(" close=")">#{id}</foreach>
    </delete>

    <select id="selectBookIdAndPaperCount" resultType="com.sure.question.vo.dataVo.IntIntVo">
        SELECT coach_book_id AS `id`, COUNT(*) AS `value`
        FROM coach_book_paper
        WHERE coach_book_id IN
        <foreach collection="coachBookIds" item="coachBookId" separator="," open="(" close=")">#{coachBookId}</foreach>
        <if test="onlyOpen">
            AND open_time &lt;= now()
        </if>
        GROUP BY coach_book_id
    </select>
</mapper>
