<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.QuestionEsSyncMapper">
	<insert id="insertPendingQuestionsBatch">
		INSERT INTO question_es_sync (question_id, status, create_time) VALUES
		<foreach collection="questionIds" separator="," item="qId">
			(#{qId}, 'pending', NOW())
		</foreach>
	</insert>

	<update id="updateTimeoutToPending">
		UPDATE question_es_sync
		SET status = 'pending'
		WHERE status = 'in_progress'
		AND sync_time &lt; DATE_SUB(NOW(), INTERVAL #{intervalMinutes} MINUTE)
	</update>

	<select id="selectNeedSyncBatch" resultType="com.sure.question.entity.QuestionEsSync">
		SELECT id, question_id, status, create_time, sync_time
		FROM question_es_sync
		WHERE status = 'pending'
		OR (status = 'failed' AND fail_count &lt; 3)
		ORDER BY id
		LIMIT 0, #{batchSize}
	</select>

	<update id="updateSyncInProgressBatch">
		UPDATE question_es_sync
		SET status = 'in_progress', sync_time = NOW()
		WHERE id IN <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
		AND status IN ('pending', 'failed')
	</update>

	<update id="updateSyncFailedBatchSameReason">
		UPDATE question_es_sync
		SET status = 'failed', fail_reason = #{reason}, fail_count = fail_count + 1
		WHERE id IN <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
		AND status = 'in_progress'
	</update>

	<update id="updateSyncFailedBatch">
		<foreach collection="list" item="item" separator=";">
			UPDATE question_es_sync
			SET status = 'failed', fail_reason = #{item.value}, fail_count = fail_count + 1
			WHERE id = #{item.key}
			AND status = 'in_progress'
		</foreach>
	</update>

	<update id="updateSyncSucceededBatch">
		UPDATE question_es_sync
		SET status = 'succeeded'
		WHERE id IN <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
		AND status = 'in_progress'
	</update>
</mapper>