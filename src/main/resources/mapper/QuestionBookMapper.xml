<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.QuestionBookMapper">

	<select id="selectQuestionsChapter" resultType="com.sure.question.dto.question.QuestionChapterDto">
		SELECT qb.question_id, qb.chapter_id, c.chapter_name, c.book_id
		FROM question_book qb, chapter c
		WHERE qb.question_id IN <foreach collection="questionIds" item="qId" open="(" close=")" separator=",">#{qId}</foreach>
		AND qb.chapter_id = c.id
	</select>

	<delete id="deleteByQuestionIdChapterId">
		DELETE FROM question_book
		WHERE question_id = #{questionId} AND chapter_id = #{chapterId}
	</delete>

    <insert id="insertList">
		INSERT INTO question_book (id, question_id, question_no, chapter_id, update_time) VALUES
		<foreach collection="entities" separator="," item="entity">
			(#{entity.id}, #{entity.questionId}, #{entity.questionNo}, #{entity.chapterId}, #{entity.updateTime})
		</foreach>
	</insert>

	<insert id="insertQuestionBookOperationLog">
		INSERT INTO log_question_book (operation_id, operation_time, operation_user, operation_type,
		id, question_id, question_no, chapter_id, update_time)
		VALUES <foreach collection="entities" item="entity" separator=",">
		(#{operationId}, #{operationTime}, #{operationUser}, #{operationType},
		#{entity.id}, #{entity.questionId}, #{entity.questionNo}, #{entity.chapterId}, #{entity.updateTime})
	</foreach>
	</insert>
</mapper>