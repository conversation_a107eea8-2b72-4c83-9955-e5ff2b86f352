<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.QuestionKnowledgeMapper">

    <select id="selectSmallQuestionsKnowledge" resultType="com.sure.question.dto.question.SmallQuestionKnowledgeDto">
        SELECT qk.question_id AS smallQuestionId, qk.knowledge_id, k.knowledge_name
        FROM question_knowledge qk, knowledge_main k
        WHERE qk.question_id IN <foreach collection="smallQuestionIds" item="sqId" open="(" close=")" separator=",">#{sqId}</foreach>
        AND qk.knowledge_id = k.id
    </select>

    <delete id="deleteBySmallQuestionIds">
        DELETE FROM question_knowledge
        WHERE question_id IN <foreach collection="smallQuestionIds" separator="," open="(" close=")" item="id">#{id}</foreach>
    </delete>

    <delete id="deleteByKnowledgeIdAndSmallQuestionIds">
        DELETE
        FROM question_knowledge
        WHERE knowledge_id = #{knowledgeId}
        AND question_id IN <foreach collection="smallQuestionIds" separator="," open="(" close=")" item="qId">#{qId}</foreach>
    </delete>

    <insert id="insertList">
        INSERT INTO question_knowledge (id, question_id, question_no, knowledge_id, update_time) VALUES
        <foreach collection="entities" separator="," item="item">
            (#{item.id}, #{item.questionId}, #{item.questionNo}, #{item.knowledgeId}, #{item.updateTime})
        </foreach>
    </insert>

    <insert id="insertQuestionKnowledgeOperationLog">
        INSERT INTO log_question_knowledge (operation_id, operation_time, operation_user, operation_type,
        id, question_id, question_no, knowledge_id, update_time)
        VALUES <foreach collection="entities" item="entity" separator=",">
        (#{operationId}, #{operationTime}, #{operationUser}, #{operationType},
        #{entity.id}, #{entity.questionId}, #{entity.questionNo}, #{entity.knowledgeId}, #{entity.updateTime})
        </foreach>
    </insert>

</mapper>
