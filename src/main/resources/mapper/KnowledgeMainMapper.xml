<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.KnowledgeMainMapper">

    <insert id="insertList">
        INSERT INTO `knowledge_main` (
        `id`,
        `pid`,
        `grade_level`,
        `subject_id`,
        `knowledge_name`,
        `level`,
        `order_id`,
        `status`
        )
        VALUES
        <foreach collection="os" item="o" separator=",">
            (
            #{o.id},
            #{o.pid},
            #{o.gradeLevel},
            #{o.subjectId},
            #{o.knowledgeName},
            #{o.level},
            #{o.orderId},
            #{o.status}
            )
        </foreach>
    </insert>

    <select id="selectIdByName" resultType="java.lang.Integer">
        select id
        from knowledge_main
        where subject_id = #{subjectId}
          and grade_level = #{stage}
          and knowledge_name = #{name}
        limit 1
    </select>

    <select id="selectSelfAndDescendantIds" resultType="java.lang.Integer">
        WITH RECURSIVE CTE AS (
            SELECT id FROM knowledge_main
            WHERE id IN <foreach collection="knowledgeIds" item="kId" open="(" close=")" separator=",">#{kId}</foreach>
            UNION
            SELECT k.id
            FROM knowledge_main k
            INNER JOIN CTE c ON k.pid = c.id
        )
        SELECT id FROM CTE;
    </select>

    <insert id="insertKnowledgeOperationLog">
        INSERT INTO log_knowledge (operation_id, operation_time, operation_user, operation_type,
                                   id, pid, grade_level, subject_id, knowledge_name, level, order_id, status)
        VALUES <foreach collection="entities" item="entity" separator=",">
        (#{operationId}, #{operationTime}, #{operationUser}, #{operationType},
        #{entity.id}, #{entity.pid}, #{entity.gradeLevel}, #{entity.subjectId}, #{entity.knowledgeName}, #{entity.level}, #{entity.orderId}, #{entity.status})
        </foreach>
    </insert>

</mapper>