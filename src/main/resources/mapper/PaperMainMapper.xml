<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.PaperMainMapper">
    <update id="increaseFeedbackSheetDownloadCount" parameterType="map">
        UPDATE paper_main
        SET feedback_sheet_download_count = feedback_sheet_download_count + 1
        WHERE id IN
            <foreach collection="paperIds" separator="," open="(" close=")" item="paperId">
                #{paperId}
            </foreach>
    </update>

    <update id="increaseAnswerSheetDownloadCount" parameterType="map">
        UPDATE paper_main
        SET answer_sheet_download_count = answer_sheet_download_count + 1
        WHERE id IN
        <foreach collection="paperIds" separator="," open="(" close=")" item="paperId">
            #{paperId}
        </foreach>
    </update>

    <select id="selectFavoritePaperPage" resultType="com.sure.question.entity.PaperMain">
        SELECT p.*
        FROM paper_main p, favorite_paper f
        WHERE p.grade_level = #{gradeLevel}
        AND p.subject_id = #{subjectId}
        AND p.status = 0
        <if test="paperTypeId != null and paperTypeId != 0">
            AND p.paper_type_id = #{paperTypeId}
        </if>
        <if test="gradeId != null and gradeId != 0">
            AND p.grade_id = #{gradeId}
        </if>
        <if test="term != null and term != 0">
            AND p.term = #{term}
        </if>
        AND f.paper_id = p.id
        AND f.user_id = #{userId}
        ORDER BY f.create_time DESC
    </select>

    <select id="queryUsedQuesIds" resultType="string">
        SELECT
            pq.question_id
        FROM
            paper_main pm
                JOIN paper_question pq ON pm.id = pq.paper_id
        WHERE
            pm.user_id = #{userId}
          AND upload_type = 2
          AND pq.question_id IN
        <foreach collection="quesIds" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectPublicBankAllGradeLevelSubjects" resultType="com.sure.question.vo.baseVo.GradeLevelSubjectVo">
        SELECT DISTINCT grade_level gradeLevel, subject_id subjectId
        FROM paper_main
        WHERE paper_bank = 3 AND `status` = 0
        ORDER BY grade_level, subject_id
    </select>

    <select id="selectBangPaper" resultType="com.sure.question.entity.PaperMain">
        SELECT id, paper_name, update_time, grade_level, subject_id, grade_id, term, region_id, year
        FROM paper_main
        WHERE subject_id = #{subjectId}
        AND grade_level = #{stage}
        <if test="gradeId != null and gradeId != 0">AND grade_id = #{gradeId}</if>
        <if test="term != null and term != 0">AND term = #{term}</if>
        AND user_id = #{userId}
        AND `status` = 0
        AND (upload_type = 2 OR (upload_type = 0 AND edit_status = 1))
        ORDER BY update_time DESC
        LIMIT 30
    </select>

    <update id="updatePaperStructure">
        UPDATE paper_main
        SET paper_style_json = #{paperStructure}, update_time = UNIX_TIMESTAMP() * 1000
        WHERE id = #{paperId}
    </update>
</mapper>
