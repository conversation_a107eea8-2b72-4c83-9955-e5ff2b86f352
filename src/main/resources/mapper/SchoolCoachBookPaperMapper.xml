<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.SchoolCoachBookMapper">
    <select id="getBookPage" resultType="com.sure.question.vo.schoolCoachBook.BookInfo">
        SELECT
            sb.semester_id,
            sb.term,
            sb.grade_level,
            sb.grade_id,
            sb.subject_id,
            b.id,
            b.book_code,
            b.book_name,
            b.press_name,
            b.year,
            b.cover
        FROM school_coach_book sb
        INNER JOIN coach_book b ON b.id = sb.coach_book_id AND b.book_type = #{bookType}
        <where>
            <if test="req.schoolId != null and req.schoolId != ''">
                sb.school_id = #{req.schoolId}
            </if>
            <if test="req.subjectId != null">
                AND sb.subject_id = #{req.subjectId}
            </if>
            <if test="req.semesterId != null">
                AND sb.semester_id = #{req.semesterId}
            </if>
            <if test="req.gradeLevel != null">
                AND sb.grade_level = #{req.gradeLevel}
            </if>
            <if test="req.gradeId != null">
                AND sb.grade_id = #{req.gradeId}
            </if>
            <if test="req.term != null">
                AND sb.term = #{req.term}
            </if>
            <if test="req.bookName != null and req.bookName != ''">
                AND b.book_name LIKE CONCAT('%', #{req.bookName}, '%')
            </if>
            <if test="req.pressName != null and req.pressName !=''">
                AND b.press_name LIKE CONCAT('%', #{req.pressName}, '%')
            </if>
        </where>
    </select>

    <select id="getPaperPage" resultType="com.sure.question.vo.paperVo.SchoolCoachPaperVo">
        SELECT b.*,
               p.`id`,
               p.`paper_name`,
               p.`paper_type_id`,
               p.`browse_count`,
               p.`download_count`
        FROM (
                SELECT DISTINCT b.id AS book_id, b.book_name, b.press_name, b.term, b.`year`, b.grade_level, b.grade_id, b.subject_id
                  FROM school_coach_book sb
                 INNER JOIN coach_book b ON sb.coach_book_id = sb.coach_book_id AND b.book_type = #{bookType}
                 WHERE sb.school_id = #{schoolId}
        <if test="req.semesterId != null">
            AND sb.semester_id = #{req.semesterId}
        </if>
        <if test="req.gradeLevel != null">
            AND b.grade_level = #{req.gradeLevel}</if>
        <if test="req.subjectId != null">
            AND b.subject_id = #{req.subjectId}
        </if>
        <if test="req.gradeId != null">
            AND b.grade_id = #{req.gradeId}
        </if>
        <if test="req.term != null">
            AND b.term = #{req.term}
        </if>
        <if test="req.year != null">
            AND b.`year` = #{req.year}
        </if>
        <if test="req.keyword != null">
            AND p.paper_name LIKE CONCAT('%', #{req.keyword}, '%')
        </if>
        ) b
        INNER JOIN coach_book_paper bp ON bp.coach_book_id = b.book_id
        INNER JOIN paper_main p ON p.id = bp.paper_id
        <where>
            <if test="req.paperTypeId != null">AND p.`paper_type_id` = #{req.paperTypeId}</if>
        </where>
        ORDER BY b.`year` DESC, b.term, bp.sort_code
    </select>

    <select id="getSimplePaper" resultType="com.sure.question.vo.paperVo.SchoolCoachPaperVo">
        SELECT sb.semester_id,
               sb.term,
               sb.grade_level,
               sb.grade_id,
               sb.subject_id,
               p.paper_name,
               sb.coach_book_id AS 'bookId'
        FROM school_coach_book sb
                 INNER JOIN coach_book_paper bp ON bp.coach_book_id = sb.coach_book_id
                 LEFT JOIN paper_main p ON p.id = bp.paper_id
        WHERE sb.school_id = #{schoolId}
          AND sb.semester_id = #{semesterId}
          AND bp.paper_id = #{paperId}
        ORDER BY sb.id DESC
        LIMIT 1
    </select>

    <select id="getSchoolPageByBookId" resultType="com.sure.question.vo.coachBook.BookSchoolVo">
        SELECT *
        FROM school_coach_book
        WHERE coach_book_id = #{bookId}
    </select>

    <select id="getSchoolPageByBookIdAndSchoolIds" resultType="com.sure.question.vo.coachBook.BookSchoolVo">
        SELECT *
        FROM school_coach_book
        WHERE coach_book_id = #{bookId}
        AND school_id IN
        <foreach collection="schoolIds" item="o" separator="," open="(" close=")">#{o}</foreach>
    </select>

    <select id="getBookIdAndSchoolCount" resultType="com.sure.question.vo.dataVo.IntIntVo">
        SELECT coach_book_id AS `id`, COUNT(DISTINCT school_id) AS `value`
        FROM school_coach_book
        WHERE coach_book_id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">#{id}</foreach>
        GROUP BY coach_book_id
    </select>

    <select id="queryStudentCoachBookIds" parameterType="map" resultType="integer">
        SELECT c.coach_book_id
        FROM school_coach_book b
            JOIN coach_stu_card c ON b.coach_book_id = c.coach_book_id
        WHERE b.school_id = #{schoolId}
          AND b.semester_id = #{semesterId}
          AND b.term = #{term}
          AND b.subject_id = #{subjectId}
          AND c.student_id = #{studentId}
    </select>

    <resultMap id="coachSchoolStatResult" type="com.sure.question.vo.coachstucard.CoachSchoolStatVo">
        <result property="schoolId" column="schoolId" />
        <result property="activeCount" column="activeCount" />
        <result property="latestActiveTime" column="latestActiveTime" />
        <association property="coachBook" javaType="com.sure.question.entity.CoachBook" autoMapping="true" />
    </resultMap>
    <select id="listCoachSchoolActiveStat" resultMap="coachSchoolStatResult">
        SELECT
        stat.schoolId,
        stat.activeCount,
        stat.latestActiveTime,
        book.*
        FROM
        (
        SELECT
            sb.school_id AS schoolId,
            sb.coach_book_id,
            COUNT(*) AS activeCount,
            MAX(sc.active_time) AS latestActiveTime
        FROM school_coach_book sb
                 INNER JOIN coach_stu_card sc ON sc.coach_book_id = sb.coach_book_id AND sc.school_id = sb.school_id
        <where>
            <if test="semesterId != null">
                sb.semester_id = #{semesterId}
            </if>
            <if test="term != null">
                AND sb.term = #{term}
            </if>
            <if test="gradeId != null">
                AND sb.grade_id = #{gradeId}
            </if>
            <if test="subjectId != null">
                AND sb.subject_id = #{subjectId}
            </if>
            <if test="schoolIds != null and schoolIds.size > 0">
                AND sb.school_id IN <foreach collection="schoolIds" item="schoolId" open="(" close=")" separator=",">#{schoolId}</foreach>
            </if>
          AND sc.`status` = 1
        </where>
        GROUP BY sb.school_id, sb.coach_book_id
        ) stat
        INNER JOIN coach_book book ON book.id = stat.coach_book_id
        <if test="bookKeyword != null and bookKeyword != ''">
            WHERE book.book_name LIKE CONCAT('%', #{bookKeyword}, '%')
            OR book.book_code LIKE CONCAT('%', #{bookKeyword}, '%')
        </if>
    </select>

    <select id="querySchoolCoachBookIds" parameterType="map" resultType="java.lang.Integer">
        SELECT DISTINCT coach_book_id FROM school_coach_book
        WHERE school_id = #{schoolId}
        AND coach_book_id IN
        <foreach collection="coachBookIds" item="bookId" open="(" close=")" separator=",">
            #{bookId}
        </foreach>
    </select>
    <select id="queryLastPaperId" resultType="java.lang.Integer">
        SELECT scb.coach_book_id
        FROM school_coach_book scb
            JOIN coach_book_paper p ON scb.coach_book_id = p.coach_book_id
        WHERE scb.school_id = #{schoolId}
            AND scb.semester_id = #{semesterId}
            AND scb.term = #{term}
            AND p.paper_id = #{paperId}
    </select>

    <select id="selectExistsSchoolCoachBookPaper" resultType="java.lang.Boolean">
        SELECT EXISTS(
            SELECT *
            FROM school_coach_book scb, coach_book_paper p
            WHERE scb.school_id = #{schoolId}
                AND p.paper_id = #{paperId}
                AND p.coach_book_id = scb.coach_book_id
                AND p.open_time &lt; NOW()
        )
    </select>

    <select id="selectExistsSchoolCoachBookCustomPaper" resultType="java.lang.Boolean">
        SELECT EXISTS(
            SELECT *
            FROM school_coach_book scb, coach_book_custom_paper p
            WHERE scb.school_id = #{schoolId}
              AND p.paper_id = #{paperId}
              AND p.coach_book_id = scb.coach_book_id
              AND p.open_time &lt; NOW()
        )
    </select>

</mapper>
