<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.CoachStuCardMapper">
    <select id="lstStudentActivedBookId" resultType="java.lang.Integer">
        SELECT card.coach_book_id
        FROM coach_stu_card card
        INNER JOIN school_coach_book scb ON scb.coach_book_id = card.coach_book_id
        WHERE card.student_id = #{studentId}
        AND scb.school_id = #{schoolId}
        <if test="semesterId != null">AND scb.semester_id = #{semesterId}</if>
        <if test="term != null">AND scb.term = #{term}</if>
    </select>

    <select id="queryActiveStudentIds" parameterType="map" resultType="com.sure.question.vo.coachstucard.StudentSubjectVo">
        SELECT DISTINCT c.student_id AS studentId,c.subject_id AS subjectId
        FROM school_coach_book b
            JOIN coach_stu_card c ON b.coach_book_id = c.coach_book_id
        WHERE b.school_id = #{schoolId}
            <if test="semesterId != null">AND b.semester_id = #{semesterId}</if>
            <if test="term != null">AND b.term = #{term}</if>
            <if test="subjectId != null">AND b.subject_id = #{subjectId}</if>
            AND c.student_id IN
            <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
                #{studentId}
            </foreach>
    </select>

    <select id="listCard" parameterType="map" resultType="com.sure.question.vo.coachstucard.CardInfoVo">
        SELECT
            c.id,
            c.coach_book_id as coachBookId,
            c.card_no as cardNo,
            c.student_id as studentId,
            c.school_id as schoolId,
            c.parentId,
            c.status,
            c.active_time as activeTime
        FROM coach_stu_card c
            JOIN coach_book b ON c.coach_book_id = b.id
        <where>
            <if test="coachBookId != null">
                AND c.coach_book_id = #{coachBookId}
            </if>
            <if test="bookKeyword != null and bookKeyword != ''">
                AND (b.book_name LIKE CONCAT('%', #{bookKeyword}, '%') OR b.book_code LIKE CONCAT('%', #{bookKeyword}, '%'))
            </if>
            <if test="subjectId != null">
                AND b.subject_id = #{subjectId}
            </if>
            <if test="gradeLevel != null">
                AND b.grade_level = #{gradeLevel}
            </if>
            <if test="gradeId != null">
                AND b.grade_id = #{gradeId}
            </if>
            <if test="semesterId != null">
                AND b.semester_id = #{semesterId}
            </if>
            <if test="term != null">
                AND b.term = #{term}
            </if>
            <if test="cardNo != null and cardNo != ''">
                AND c.card_no Like CONCAT('%', #{cardNo}, '%')
            </if>
            <if test="status != null">
                AND c.status = #{status}
            </if>
            <if test="beginDate != null">
                AND c.active_time &gt;= #{beginDate}
            </if>
            <if test="endDate != null">
                AND c.active_time &lt;= #{endDate}
            </if>
            <if test="schoolIds != null and schoolIds.size > 0">
                AND c.school_id IN
                <foreach collection="schoolIds" item="schoolId" open="(" close=")" separator=",">
                    #{schoolId}
                </foreach>
            </if>
            <if test="studentIds != null and studentIds.size > 0">
                AND c.student_id IN
                <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
                    #{studentId}
                </foreach>
            </if>
        </where>
        ORDER BY CASE WHEN c.active_time IS NULL THEN 1 ELSE 0 END,
        c.active_time DESC
    </select>

    <select id="queryCoachStatInfo" parameterType="map" resultType="com.sure.question.vo.coachstucard.CoachStatVo">
        SELECT
            coach_book_id AS coachBookId,
            COUNT(DISTINCT school_id) AS activeSchoolCount,
            MAX(active_time) AS latestActiveTime
        FROM coach_stu_card
        WHERE coach_book_id IN
            <foreach collection="bookIds" item="coachBookId" open="(" close=")" separator=",">
                #{coachBookId}
            </foreach>
            AND status = 1
        GROUP BY coach_book_id
    </select>

    <resultMap id="bookStatResult" type="com.sure.question.vo.coachstucard.ActiveStatVo">
        <id property="bookId" column="id" />
        <result property="activeCount" column="activeCount" />
        <result property="activeSchoolCount" column="activeSchoolCount" />
        <result property="latestActiveTime" column="latestActiveTime" />
        <association property="coachBook" javaType="com.sure.question.entity.CoachBook" autoMapping="true" />
    </resultMap>
    <select id="listCoachBookActiveStat" resultMap="bookStatResult">
        SELECT
        stat.coach_book_id AS bookId,
        stat.activeCount,
        stat.activeSchoolCount,
        stat.latestActiveTime,
        book.*
        FROM
        (
        SELECT
        sb.coach_book_id,
        SUM(CASE sc.`status` WHEN 1 THEN 1 ELSE 0 END) AS activeCount,
        COUNT(DISTINCT sc.school_id) AS activeSchoolCount,
        MAX(active_time) AS latestActiveTime
        FROM school_coach_book sb
        LEFT JOIN coach_stu_card sc ON sc.coach_book_id = sb.coach_book_id AND sc.school_id = sb.school_id
        <where>
            <if test="semesterId != null">
                sb.semester_id = #{semesterId}
            </if>
            <if test="term != null">
                AND sb.term = #{term}
            </if>
            <if test="gradeId != null">
                AND sb.grade_id = #{gradeId}
            </if>
            <if test="subjectId != null">
                AND sb.subject_id = #{subjectId}
            </if>
            <if test="schoolIds != null and schoolIds.size > 0">
                AND sb.school_id IN <foreach collection="schoolIds" item="schoolId" open="(" close=")" separator=",">#{schoolId}</foreach>
            </if>
        </where>
        GROUP BY sb.coach_book_id
        ) stat
        INNER JOIN coach_book book ON book.id = stat.coach_book_id
        <if test="bookKeyword != null and bookKeyword != ''">
            WHERE book.book_name LIKE CONCAT('%', #{bookKeyword}, '%')
            OR book.book_code LIKE CONCAT('%', #{bookKeyword}, '%')
        </if>
    </select>

    <select id="countStatCommon" resultType="com.sure.question.vo.coachstucard.CountStatVo">
        SELECT
        COUNT( DISTINCT sb.coach_book_id ) AS bookCount,
        SUM( CASE sc.`status` WHEN 1 THEN 1 ELSE 0 END ) AS cardCount,
        COUNT( DISTINCT sc.school_id ) AS schoolCount
        FROM school_coach_book sb
        <if test="bookKeyword != null and bookKeyword != ''">
            INNER JOIN coach_book book ON book.id = sb.coach_book_id
            AND ( book.book_name LIKE CONCAT('%', #{bookKeyword}, '%') OR book.book_code LIKE CONCAT('%', #{bookKeyword}, '%') )
        </if>
        LEFT JOIN coach_stu_card sc ON sc.coach_book_id = sb.coach_book_id AND sc.school_id = sb.school_id AND sc.`status` = 1
        <where>
            <if test="semesterId != null">
                sb.semester_id = #{semesterId}
            </if>
            <if test="term != null">
                AND sb.term = #{term}
            </if>
            <if test="gradeId != null">
                AND sb.grade_id = #{gradeId}
            </if>
            <if test="subjectId != null">
                AND sb.subject_id = #{subjectId}
            </if>
            <if test="schoolIds != null and schoolIds.size > 0">
                AND sb.school_id IN <foreach collection="schoolIds" item="schoolId" open="(" close=")" separator=",">#{schoolId}</foreach>
            </if>
        </where>
    </select>

    <select id="selectGroupByBookIdCount" resultType="com.sure.question.vo.dataVo.IntIntVo">
        SELECT coach_book_id AS `id`, COUNT(*) AS `value` FROM coach_stu_card
        WHERE coach_book_id IN <foreach collection="bookIds" item="bookId" open="(" close=")" separator=",">#{bookId}</foreach>
        GROUP BY coach_book_id
    </select>

    <insert id="addCustomCardBatch">
        <foreach collection="customCardVos" item="o" separator=";">
            INSERT IGNORE INTO `coach_stu_card` (`subject_id`, `coach_book_id`, `card_no`, `school_id`, `student_id`, `parentId`, `status`, `create_time`, `active_time`)
            SELECT #{o.subjectId}, #{o.coachBookId}, #{o.cardNo}, #{o.schoolId}, #{o.studentId}, #{o.parentId}, 1, NOW(), NOW()
            FROM DUAL
            WHERE NOT EXISTS (SELECT 1 FROM `coach_stu_card` WHERE `school_id` = #{o.schoolId} AND `coach_book_id` = #{o.coachBookId} AND `student_id` = #{o.studentId} )
        </foreach>
    </insert>

</mapper>
