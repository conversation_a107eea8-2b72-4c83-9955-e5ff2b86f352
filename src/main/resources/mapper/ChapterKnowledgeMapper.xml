<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.ChapterKnowledgeMapper">
    <select id="selectChapterKnowledgeIds" resultType="java.lang.Integer">
        SELECT DISTINCT knowledge_id
        FROM chapter_knowledge
        WHERE chapter_id IN <foreach collection="chapterIds" item="cId" open="(" close=")" separator=",">#{cId}</foreach>
    </select>

    <delete id="deleteByChapterIdAndKnowledgeId">
        DELETE FROM chapter_knowledge where chapter_id = #{chapterId} and knowledge_id = #{knowledgeId}
    </delete>

    <insert id="insertList">
        INSERT INTO chapter_knowledge (chapter_id, knowledge_id) VALUES
        <foreach collection="list" item="item" separator=",">(#{item.chapterId}, #{item.knowledgeId})</foreach>
    </insert>

    <insert id="insertChapterKnowledgeOperationLog">
        INSERT INTO log_chapter_knowledge (operation_id, operation_time, operation_user, operation_type,
        chapter_id, knowledge_id)
        VALUES <foreach collection="entities" item="entity" separator=",">
        (#{operationId}, #{operationTime}, #{operationUser}, #{operationType},
        #{entity.chapterId}, #{entity.knowledgeId})
        </foreach>
    </insert>
</mapper>