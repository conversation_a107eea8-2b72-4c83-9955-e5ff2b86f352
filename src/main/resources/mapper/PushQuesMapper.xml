<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.ExamSubjectPushQuesMapper">
    <select id="getPushQues" resultType="com.sure.question.entity.ExamsubjectPushQues">
        SELECT ques_id as quesId, push_id as pushId
        FROM (
        SELECT ques_id,
        push_id,
        ROW_NUMBER() OVER (PARTITION BY ques_id ORDER BY know_match_count DESC ) AS rownum
        FROM push_ques
        WHERE exam_subject_id = #{examSubjectId}
        and ques_id in
        <foreach collection="quesIds" item="o" separator="," open="(" close=")">
            #{o}
        </foreach>
        ) AS s
        WHERE s.rownum
        <![CDATA[ <= ]]> ${pushNum}
    </select>

    <select id="getWrongQuesTrainCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM push_ques
        WHERE exam_subject_Id = #{examSubjectId}
        AND ques_id in
        <foreach collection="quesIds" item="o" separator="," open="(" close=")">
            #{o}
        </foreach>
    </select>

    <select id="getPaperIdAndStandardCount" resultType="com.sure.question.vo.dataVo.StrIntVo">
        SELECT push.paper_id AS `key`, COUNT(*) AS `value`
        FROM examsubject_push_ques push, paper_question pq
        WHERE push.paper_id IN <foreach collection="paperIds" item="o" separator="," open="(" close=")">#{o}</foreach>
         AND push.exam_subject_id = ''
         AND push.is_standard = 1
         AND push.paper_id = pq.paper_id
         AND push.ques_id = pq.question_id
        GROUP BY push.paper_id
    </select>

    <select id="selectStandardPushQuesPaperIds" parameterType="map" resultType="java.lang.String">
        SELECT DISTINCT paper_id FROM examsubject_push_ques WHERE is_standard = 1 AND paper_id IN
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectEachQuestionStandardQuestionCount" resultType="com.sure.question.dto.coachBook.StandardQuestionCountDto">
        SELECT paper_id paperId, ques_id questionId, COUNT(DISTINCT push_id) standardQuestionCount
        FROM examsubject_push_ques
        WHERE paper_id IN <foreach collection="paperIds" item="o" separator="," open="(" close=")">#{o}</foreach>
        AND exam_subject_id = ''
        AND is_standard = 1
        GROUP BY paper_id, ques_id
    </select>

    <select id="selectQuesionIdAndPushCountList" resultType="com.sure.question.vo.dataVo.KeyValueInt">
        SELECT ques_id AS `key`, COUNT(*) AS `value`
        FROM examsubject_push_ques
        WHERE paper_id = #{paperId}
          AND exam_subject_id = #{examSubjectId}
        GROUP BY ques_id
    </select>
</mapper>
