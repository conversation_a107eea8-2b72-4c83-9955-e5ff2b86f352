<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.QuesLogMapper">

	<select id="queryStatistic" parameterType="map" resultType="com.sure.question.vo.queslog.PaperStatVo">
		SELECT
			pm.id AS paperId,
			pm.paper_name AS paperName,
			pm.grade_level AS gradeLevel,
			pm.grade_id AS gradeId,
			pm.subject_id AS subjectId,
			pm.`year`,
			pm.paper_type_id AS paperTypeId,
			pm.region_id AS regionId,
			pm.flag_coach_book_id AS flagCoachBookId,
			COALESCE ( sum( CASE WHEN ql.logType = 1 THEN 1 ELSE 0 END ), 0 ) AS browseCount,
			COALESCE ( sum( CASE WHEN ql.logType = 2 THEN 1 ELSE 0 END ), 0 ) AS downloadCount
		FROM paper_main pm
			JOIN queslog ql ON pm.id = ql.paperId
		<where>
			<if test="beginDate != null">
				AND ql.createTime &gt;= #{beginDate}
			</if>
			<if test="endDate != null">
				AND ql.createTime &lt;= #{endDate}
			</if>
			<if test="gradeLevel != null">
				AND pm.grade_level = #{gradeLevel}
			</if>
			<if test="subjectId != null">
				AND pm.subject_id = #{subjectId}
			</if>
			<if test="paperName != null and paperName != ''">
				AND pm.paper_name LIKE CONCAT( '%', #{paperName}, '%' )
			</if>
		</where>
		GROUP BY pm.id
		ORDER BY downloadCount DESC,pm.id DESC
	</select>

</mapper>