<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.ChapterMapper">

	<select id="selectSelfAndDescendantIds" resultType="java.lang.Integer">
		WITH RECURSIVE CTE AS (
		SELECT id FROM chapter
		WHERE id IN <foreach collection="chapterIds" item="cId" open="(" close=")" separator=",">#{cId}</foreach>
		UNION
		SELECT c.id
		FROM chapter c
		INNER JOIN CTE cte ON c.pid = cte.id
		)
		SELECT id FROM CTE;
	</select>

	<insert id="insertList">
		INSERT INTO chapter (id, book_id, grade_level, subject_id, grade_id, chapter_name, pid, level, status, order_id)
		VALUES <foreach collection="entities" item="entity" separator=",">
		(#{entity.id}, #{entity.bookId}, #{entity.gradeLevel}, #{entity.subjectId}, #{entity.gradeId}, #{entity.chapterName}, #{entity.pid}, #{entity.level}, #{entity.status}, #{entity.orderId})
	</foreach>
	</insert>

	<insert id="insertChapterOperationLog">
		INSERT INTO log_chapter (operation_id, operation_time, operation_user, operation_type,
		id, book_id, grade_level, subject_id, grade_id, chapter_name, pid, level, status, order_id)
		VALUES <foreach collection="entities" item="entity" separator=",">
		(#{operationId}, #{operationTime}, #{operationUser}, #{operationType},
		#{entity.id}, #{entity.bookId}, #{entity.gradeLevel}, #{entity.subjectId}, #{entity.gradeId}, #{entity.chapterName}, #{entity.pid}, #{entity.level}, #{entity.status}, #{entity.orderId})
	</foreach>
	</insert>
</mapper>