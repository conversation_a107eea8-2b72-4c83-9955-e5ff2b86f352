<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.RegionMapper">
	<select id="selectSelfAndDescendantIds" resultType="java.lang.Long">
		WITH RECURSIVE CTE AS (
			SELECT id FROM region WHERE id IN <foreach collection="regionIds" item="rId" open="(" close=")" separator=",">#{rId}</foreach>
			UNION
			SELECT r.id
			FROM region r
			INNER JOIN CTE c ON r.pid = c.id
		)
		SELECT id FROM CTE;
	</select>
</mapper>