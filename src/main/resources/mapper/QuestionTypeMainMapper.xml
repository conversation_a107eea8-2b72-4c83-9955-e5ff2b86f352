<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.QuestionTypeMainMapper">
	<select id="selectGradeLevelSubjectQuestionTypes" resultType="com.sure.question.entity.QuestionTypeMain">
		SELECT * FROM ques_type_main
		WHERE grade_level = #{gradeLevelId}
		AND subject_id = #{subjectId}
		ORDER BY order_id
	</select>

	<select id="selectMaxId" resultType="java.lang.Integer">
		SELECT id FROM ques_type_main
		ORDER BY id DESC
		LIMIT 1
	</select>
</mapper>