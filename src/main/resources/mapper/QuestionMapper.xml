<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.QuestionMapper">

    <update id="updateEsSync">
        UPDATE question set es_sync = #{i} where id in
        <foreach collection="list" separator="," open="(" close=")" item="o">
            #{o}
        </foreach>
    </update>

    <update id="useCountIncr">
        UPDATE question
        SET `use_count` = `use_count` + 1
        WHERE id IN <foreach collection="questionIds" separator="," open="(" close=")" item="o">#{o}</foreach>
    </update>

    <update id="shareToBank" parameterType="java.lang.String">
        update question
        set bank = 3
        where paper_id = #{pid}
    </update>

    <select id="listQuesTypeName" resultType="com.sure.question.vo.dataVo.StrStrVo">
        SELECT e.id AS `key`, q.question_type_name AS `value` FROM
        (SELECT `id` ,`question_type_id` FROM question
          WHERE `id` IN <foreach collection="list" item="i" separator="," open="(" close=")">#{i}</foreach>
        ) e
        LEFT JOIN ques_type_main q ON q.`id` = e.`question_type_id`
    </select>

    <select id="selectFavoriteQuestionIds" resultType="java.lang.String">
        SELECT q.id
        FROM favorite_ques f, question q
        WHERE f.user_id = #{userId}
        AND q.id = f.question_id
        AND q.grade_level = #{stage}
        AND q.subject_id = #{subject}
        <if test="diff != null and diff != 0">
            AND q.difficult = #{diff}
        </if>
        <if test="ques_type != null and ques_type != 0">
            AND q.question_type_id = #{ques_type}
        </if>
    </select>

    <sql id="questionConditions">
        <if test="diff != null and diff != 0">
            AND q.difficult = #{diff}
        </if>
        <if test="ques_type != null and ques_type != 0">
            AND q.question_type_id = #{ques_type}
        </if>
        <if test="chaps != null and !chaps.isEmpty()">
            AND EXISTS (
            SELECT *
            FROM question_book qb
            WHERE qb.question_id = q.id
            AND qb.chapter_id IN <foreach collection="chaps" item="cId" open="(" close=")" separator=",">#{cId}</foreach>
            )
        </if>
        <if test="knows != null and !knows.isEmpty()">
            AND EXISTS (
            SELECT *
            FROM small_question sq, question_knowledge qk
            WHERE sq.question_id = q.id
            AND qk.question_id = sq.id
            AND qk.knowledge_id IN <foreach collection="knows" item="kId" open="(" close=")" separator=",">#{kId}</foreach>
            )
        </if>
    </sql>

    <select id="selectSchoolPaperQuestionIds" resultType="java.lang.String">
        SELECT id
        FROM question q
        WHERE id IN (
        SELECT DISTINCT pq.question_id
        FROM school_paper sp, paper_main p, paper_question pq
        WHERE sp.school_id = #{schId}
        AND sp.is_delete = FALSE
        AND sp.is_public = TRUE
        AND p.id = sp.paper_id
        AND p.grade_level = #{stage}
        AND p.subject_id = #{subject}
        AND p.`status` = 0
        <if test="year != null and year != 0">
            AND p.year= #{year}
        </if>
        <if test="source != null and source != 0">
            AND p.paper_type_id = #{source}
        </if>
        <if test="region != null and region != '' and region != '0'">
            AND p.region_id LIKE CONCAT(#{region}, '%')
        </if>
        AND pq.paper_id = p.id
        )
        <include refid="questionConditions"/>
        ORDER BY id LIMIT 1000
    </select>

    <select id="selectSchoolCoachBookQuestionIds" resultType="java.lang.String">
        SELECT id
        FROM question q
        WHERE id IN (
        SELECT DISTINCT pq.question_id
        FROM school_coach_book sb, coach_book b, coach_book_paper bp, paper_question pq
        WHERE sb.school_id = #{schId}
        AND sb.grade_level = #{stage}
        AND sb.subject_id = #{subject}
        <if test="semesterId != null and semesterId != 0">
            AND sb.semester_id = #{semesterId}
        </if>
        <if test="term != null and term != 0">
            AND sb.term = #{term}
        </if>
        <if test="gradeId != null and gradeId != 0">
            AND sb.grade_id = #{gradeId}
        </if>
        AND b.id = sb.coach_book_id
        AND bp.coach_book_id = b.id
        <!-- 已到开放时间 -->
        AND bp.open_time &lt; NOW()
        AND pq.paper_id = bp.paper_id
        )
        <include refid="questionConditions"/>
        ORDER BY id LIMIT 1000
    </select>
</mapper>
