<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.CoachBookMapper">

    <select id="querySchoolCoachBooks" resultType="com.sure.question.entity.CoachBook">
        SELECT cb.*
        FROM school_coach_book scb
          JOIN coach_book cb ON scb.coach_book_id = cb.id
        WHERE scb.school_id = #{schoolId}
          AND cb.grade_id = #{gradeId}
          AND scb.semester_id = #{semesterId}
          AND scb.term = #{term}
    </select>

</mapper>
