<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.RenderedQuestionMapper">
	<insert id="insertBatch">
		INSERT INTO rendered_question(question_id, small_question_id, content_html, options, answer_html, explanation, create_time)
		VALUES
		<foreach collection="inserts" item="item" separator=",">
			(#{item.questionId}, #{item.smallQuestionId}, #{item.contentHtml}, #{item.options}, #{item.answerHtml}, #{item.explanation}, #{item.createTime})
		</foreach>
	</insert>
</mapper>