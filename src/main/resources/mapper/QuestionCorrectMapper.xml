<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
	PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.QuestionCorrectMapper">
	<select id="selectQuestionCount" resultType="java.lang.Integer">
		SELECT COUNT(DISTINCT question_id)
		FROM question_correct
		WHERE status = #{status}
		<if test="gradeLevel != null">AND grade_level = #{gradeLevel}</if>
		<if test="subjectId != null">AND subject_id = #{subjectId}</if>
		<if test="beginTime != null and beginTime != ''">AND create_time &gt;= #{beginTime}</if>
		<if test="endTime != null and endTime != ''">AND create_time &lt;= #{endTime}</if>
	</select>

	<select id="selectQuestionIds" resultType="java.lang.String">
		SELECT question_id
		FROM
		(SELECT question_id, MIN(id) id
		FROM question_correct
		WHERE status = #{status}
		<if test="gradeLevel != null">AND grade_level = #{gradeLevel}</if>
		<if test="subjectId != null">AND subject_id = #{subjectId}</if>
		<if test="beginTime != null and beginTime != ''">AND create_time &gt;= #{beginTime}</if>
		<if test="endTime != null and endTime != ''">AND create_time &lt;= #{endTime}</if>
		GROUP BY question_id) t
		ORDER BY id DESC
		LIMIT #{offset}, #{rowCount}
	</select>
</mapper>