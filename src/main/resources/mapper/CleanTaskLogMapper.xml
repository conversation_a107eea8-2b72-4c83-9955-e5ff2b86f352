<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.CleanTaskLogMapper">
    <insert id="insertBatch">
        INSERT INTO clean_task_log (task_id, paper_id, question_id, small_question_id, content, create_time)
        VALUES
        <foreach collection="entities" item="item" separator=",">
            (#{item.taskId}, #{item.paperId}, #{item.questionId}, #{item.smallQuestionId}, #{item.content}, #{item.createTime})
        </foreach>
    </insert>
</mapper>
