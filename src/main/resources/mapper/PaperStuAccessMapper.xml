<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sure.question.mapper.PaperStuAccessMapper">

    <insert id="insertIgnore">
        INSERT IGNORE INTO `paper_stu_access`
            (`student_id`, `parent_id`, `paper_id`, `coach_book_id`,`type`, `create_time`)
        VALUES (#{studentId}, #{parentId}, #{paperId}, #{coachBookId},#{type}, now())
    </insert>

    <select id="selectAccessPaperCount" parameterType="map" resultType="integer">
        SELECT COUNT( DISTINCT paper_id )
        FROM paper_stu_access
        WHERE student_id = #{studentId}
          AND parent_id = #{parentId}
          AND coach_book_id IN <foreach collection="coachBookIds" item="coachBookId" separator="," open="(" close=")">#{coachBookId}</foreach>
    </select>

</mapper>
