<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.sure.question.mapper.PaperShareMapper">

    <select id="getMyShares" resultType="com.sure.question.vo.paperVo.SharePaperVo">
        SELECT t2.`id`,
        t2.`paper_name` AS paperName,
        t2.`grade_level` AS gradeLevel,
        t2.`subject_id` AS subjectId,
        t2.`grade_id` AS gradeId,
        t2.term,
        t2.`year`,
        t2.`region_id` AS regionId,
        t2.flag_coach_book_id AS flagCoachBookId,
        t2.`paper_type_id` AS paperTypeId,
        t2.`paper_bank` AS paperBank,
        t1.`id` AS sharePaperId,
        t1.`create_time` AS createTime,
        t1.`user_id` AS srcUserId,
        t1.`share_id` AS destUserId,
        t1.`share_delete` AS isDelete
        FROM paper_share t1,
        paper_main t2
        WHERE t1.`paper_id` = t2.`id`
        AND t1.`user_id` = #{u}
        <if test="t != null">AND t2.`paper_type_id` = #{t}</if>
        <if test="g != null">AND t2.`grade_id` = #{g}</if>
        <if test="y != null">AND t2.`year` = #{y}</if>
        <if test="r != null and r != ''">AND t2.`region_id` like concat(#{r},'%')</if>
        <if test="sub != null">AND t2.`subject_id` = #{sub}</if>
        <if test="st != null">AND t2.`grade_level` = #{st}</if>
        <if test="bookType == 'coach' and flagCoachBookId != null">AND t2.flag_coach_book_id = #{flagCoachBookId}</if>
        <if test="bookType == 'coach' and flagCoachBookId == null">AND t2.flag_coach_book_id IS NOT NULL</if>
        <if test="bookType == 'normal'">AND t2.flag_coach_book_id IS NULL</if>
        ORDER BY t1.`create_time` DESC
    </select>

    <select id="getShareToMe" resultType="com.sure.question.vo.paperVo.SharePaperVo">
        SELECT t2.`id`,
        t2.`paper_name` AS paperName,
        t2.`grade_level` AS gradeLevel,
        t2.`subject_id` AS subjectId,
        t2.`grade_id` AS gradeId,
        t2.flag_coach_book_id AS flagCoachBookId,
        t2.paper_group_id AS paperGroupId,
        t2.term,
        t2.`year`,
        t2.`region_id` AS regionId,
        t2.`paper_type_id` AS paperTypeId,
        t2.`paper_bank` AS paperBank,
        t1.`id` AS sharePaperId,
        t1.`create_time` AS createTime,
        t1.`user_id` srcUserId,
        t1.`share_id` destUserId,
        t1.`share_delete` isDelete
        FROM paper_share t1,
        paper_main t2
        WHERE t1.`paper_id` = t2.`id`
        AND t1.`share_id` = #{u}
        AND t1.`share_delete` = FALSE
        <if test="t != null">AND t2.`paper_type_id` = #{t}</if>
        <if test="g != null">AND t2.`grade_id` = #{g}</if>
        <if test="y != null">AND t2.`year` = #{y}</if>
        <if test="r != null and r != ''">AND t2.`region_id` like concat(#{r},'%')</if>
        <if test="sub != null">AND t2.`subject_id` = #{sub}</if>
        <if test="st != null">AND t2.`grade_level` = #{st}</if>
        <if test="bank != null">AND t2.`paper_bank` = #{bank}</if>
        <if test="keyword != null and keyword != ''">AND t2.paper_name like CONCAT('%', #{keyword}, '%')</if>
        <if test="bookType == 'coach' and flagCoachBookId != null">AND t2.flag_coach_book_id = #{flagCoachBookId}</if>
        <if test="bookType == 'coach' and flagCoachBookId == null">AND t2.flag_coach_book_id IS NOT NULL</if>
        <if test="bookType == 'normal'">AND t2.flag_coach_book_id IS NULL</if>
        <if test="paperGroupId != null">AND t2.paper_group_id = #{paperGroupId}</if>
        ORDER BY t1.`create_time` DESC
    </select>

    <select id="getMyShareSpecifiedPaperId" resultType="com.sure.question.vo.paperVo.SharePaperVo">
        SELECT t2.`id`,
               t2.`paper_name`,
               t2.`grade_level`,
               t2.`subject_id`,
               t2.`grade_id`,
               t2.term,
               t2.`year`,
               t2.`region_id`,
               t2.`paper_type_id`,
               t2.`paper_bank`,
               t1.`id`           sharePaperId,
               t1.`create_time`,
               t1.`user_id`      srcUserId,
               t1.`share_id`     destUserId,
               t1.`share_delete` isDelete
        FROM paper_share t1,
             paper_main t2
        WHERE t1.`paper_id` = t2.`id`
          AND t1.`user_id` = #{u}
          AND t1.`paper_id` = #{paperId}
        ORDER BY t1.`create_time` DESC
    </select>

    <select id="selectBangPaper" resultType="com.sure.question.entity.PaperMain">
        SELECT id, paper_name, update_time, grade_level, subject_id, grade_id, term, region_id, year
        FROM paper_main
        WHERE subject_id = #{subjectId}
        AND grade_level = #{stage}
        <if test="gradeId != null and gradeId != 0">AND grade_id = #{gradeId}</if>
        <if test="term != null and term != 0">AND term = #{term}</if>
        AND id IN (SELECT paper_id FROM paper_share WHERE share_id = #{uid} AND share_delete = 0)
        AND status = 0
        ORDER BY update_time DESC
        LIMIT 30
    </select>
</mapper>