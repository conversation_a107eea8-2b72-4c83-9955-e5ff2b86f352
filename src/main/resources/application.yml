server:
  tomcat:
    accept-count: 1500
    max-threads: 1500
    min-spare-threads: 50
    max-connections: 1600
  port: 8402

spring:
  application:
    name: question-service

  profiles:
    active: dev

  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# Logger Config 配置
logging:
  level:
    com.sure.*: info

ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 3000
  MaxAutoRetries: 1 #同一台实例最大重试次数,不包括首次调用
  MaxAutoRetriesNextServer: 1 #重试负载均衡其他的实例最大重试次数,不包括首次调用
  OkToRetryOnAllOperations: false  #是否所有操作都重试

# 分页插件
pagehelper:
  helper-dialect: mysql
  params: count=countSql
  reasonable: true
  support-methods-arguments: true

# 题库缓存
cacheKey:
  # 基础信息
  baseMessage: "Ques:Base:Message"
  # 地区
  baseRegion: "Ques:Base:Region"
  # 学段学科知识点
  gradeLevelSubjectKnowledgeTreePre: "Ques:KnowledgeTree:"
  # 教材章节
  bookChapterTreePre: "Ques:ChapterTree:"
  # 试题篮
  basketPre: "Ques:Basket:"
  # 试卷收藏
  favPaperPre: "Ques:Favorite:Paper:"
  # 试题收藏
  favQuesPre: "Ques:Favorite:Ques:"

# 用户信息缓存
UserInfoPre: "User:Info:"

# 各PDF前端页面路径
pdfPath:
  paperH5: paper-h5/index.html

